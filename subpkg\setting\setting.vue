<template>
	<view class="setting-container">

		<!-- 设置选项列表 -->
		<view class="setting-list">
			<!-- 个人资料 -->
			<view class="setting-item" @click="navTo('/subpkg/edit/edit')">
				<text class="item-text">个人资料</text>
				<uni-icons color="#999" type="right" size="20"></uni-icons>
			</view>

			<view class="setting-item" @click="navTo('/subpkg/edit/edit')">
				<text class="item-text">当前版本</text>
				<view class="cache-size">
					<text class="current-size">{{ version }}</text>
					<uni-icons color="#999" type="right" size="20"></uni-icons>
				</view>
			</view>
			<view class="setting-item" @click="clearStorage">
				<text class="item-text">清除缓存</text>
				<view class="cache-size">
					<text class="current-size">{{ currentSize }}</text>
					<uni-icons color="#999" type="right" size="20"></uni-icons>
				</view>

			</view>
			<!--  -->
			<view class="setting-item" @click="navTo('/subpkg/about/about')">
				<text class="item-text">关于我们</text>
				<uni-icons color="#999" type="right" size="20"></uni-icons>
			</view>

			<!-- 新增注销账户选项 -->
			<view class="setting-item delete-account" @click="deleteAccount">
				<text class="item-text">注销账户</text>
				<uni-icons color="#999" type="right" size="20"></uni-icons>
			</view>
		</view>

		<!-- 退出登录按钮 - 现在固定在底部 -->
		<view class="logout-btn-container">
			<view class="logout-btn" @click="logoutApp">
				<text>退出登录</text>
			</view>
		</view>
	</view>
</template>

<script>
import { mapMutations } from "vuex";
import { logout } from "@/api/user";

export default {
	data() {
		return {
			currentSize: '0B',
			materialFilesSize: '0B',
			hasMaterialFiles: false
		};
	},
	onLoad() {
		this.getStorageSize();
	},
	computed: {
		version() {
			return plus.runtime.version
		}
	},
	methods: {
		...mapMutations("user", ["setUser"]),

		// 返回上一页
		navBack() {
			uni.navigateBack();
		},
		// 跳转到指定页面
		navTo(url) {
			uni.navigateTo({
				url
			});
		},

		// 注销账户
		deleteAccount() {
			uni.showModal({
				title: '警告',
				content: '确定要注销账户吗？注销后将永久删除您的所有账户信息，此操作不可撤销！',
				confirmColor: '#ff4d4f',
				success: async (res) => {
					if (res.confirm) {
						// 显示加载提示
						uni.showLoading({
							title: '正在处理...'
						});

						// 先调用后端注销接口
						const result = await logout();
						console.log("result", result)
						if (result) {
							// 清除Vuex中的用户状态
							this.setUser({});

							// 清除本地存储中的用户数据
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');

							// 清除其他应用相关的用户数据
							this.clearUserRelatedData();

							uni.hideLoading();
							uni.showToast({
								title: '账户已注销',
								icon: 'success',
								duration: 1500
							});

							// 延迟后跳转到登录页
							setTimeout(() => {
								uni.reLaunch({
									url: '/subpkg/login/login'
								});
							}, 1500);
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '注销失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},

		// 退出登录
		logoutApp() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: async (res) => {
					if (res.confirm) {
						// 显示加载提示
						uni.showLoading({
							title: '正在退出...'
						});



						// 清除Vuex中的用户状态
						this.setUser({});

						// 清除本地存储中的用户数据
						uni.removeStorageSync('token');
						uni.removeStorageSync('userInfo');

						// 清除其他应用相关的用户数据
						this.clearUserRelatedData();

						uni.hideLoading();
						uni.showToast({
							title: '退出成功',
							icon: 'success',
							duration: 1500
						});

						// 延迟后跳转到登录页
						setTimeout(() => {
							uni.reLaunch({
								url: '/subpkg/login/login'
							});
						}, 1000);
					}
				}
			});
		},

		// 获取本地缓存大小
		getStorageSize() {
			let that = this;

			// 计算资料文件大小
			that.calculateMaterialFilesSize().then(materialSize => {
				that.materialFilesSize = materialSize.formatted;
				that.hasMaterialFiles = materialSize.bytes > 0;

				// 计算总缓存大小
				plus.cache.calculate(function (size) {
					let sizeCache = parseInt(size);
					// 加上资料文件的大小
					sizeCache += materialSize.bytes;

					if (sizeCache == 0) {
						that.currentSize = "0B";
					} else if (sizeCache < 1024) {
						that.currentSize = sizeCache + "B";
					} else if (sizeCache < 1048576) {
						that.currentSize = (sizeCache / 1024).toFixed(2) + "KB";
					} else if (sizeCache < 1073741824) {
						that.currentSize = (sizeCache / 1048576).toFixed(2) + "MB";
					} else {
						that.currentSize = (sizeCache / 1073741824).toFixed(2) + "GB";
					}
				});
			});
		},

		// 计算资料文件大小
		calculateMaterialFilesSize() {
			return new Promise((resolve) => {
				try {
					const cachedFiles = uni.getStorageSync('yanqu_material_files');
					console.log("cachedFiles", cachedFiles);
					if (!cachedFiles) {
						resolve({ bytes: 0, formatted: '0B' });
						return;
					}

					const localFiles = JSON.parse(cachedFiles);
					const fileKeys = Object.keys(localFiles);
					console.log("fileKeys", fileKeys);

					if (fileKeys.length === 0) {
						resolve({ bytes: 0, formatted: '0B' });
						return;
					}

					let totalSize = 0;
					let processedCount = 0;

					fileKeys.forEach(key => {
						const filePath = localFiles[key].path;
						if (filePath) {
							uni.getFileInfo({
								filePath: filePath,
								success: (res) => {
									console.log("文件信息:", res);
									totalSize += res.size;
									processedCount++;

									if (processedCount === fileKeys.length) {
										// 格式化大小
										const formattedSize = this.formatSize(totalSize);
										resolve({ bytes: totalSize, formatted: formattedSize });
									}
								},
								fail: (err) => {
									console.error("获取文件信息失败:", filePath, err);
									processedCount++;
									if (processedCount === fileKeys.length) {
										const formattedSize = this.formatSize(totalSize);
										resolve({ bytes: totalSize, formatted: formattedSize });
									}
								}
							});
						} else {
							processedCount++;
							if (processedCount === fileKeys.length) {
								const formattedSize = this.formatSize(totalSize);
								resolve({ bytes: totalSize, formatted: formattedSize });
							}
						}
					});
				} catch (e) {
					console.error('计算资料文件大小时出错:', e);
					resolve({ bytes: 0, formatted: '0B' });
				}
			});
		},

		// 格式化文件大小
		formatSize(bytes) {
			if (bytes === 0) return '0B';
			if (bytes < 1024) return bytes + 'B';
			if (bytes < 1048576) return (bytes / 1024).toFixed(2) + 'KB';
			if (bytes < 1073741824) return (bytes / 1048576).toFixed(2) + 'MB';
			return (bytes / 1073741824).toFixed(2) + 'GB';
		},

		// 清理缓存
		clearStorage() {
			let that = this;

			// 检查是否有资料文件
			if (that.hasMaterialFiles) {
				uni.showModal({
					title: '提示',
					content: '清除缓存将同时删除已下载的资料文件，是否继续？',
					success: (res) => {
						if (res.confirm) {
							that.performClearStorage();
						}
					}
				});
			} else {
				that.performClearStorage();
			}
		},

		// 执行清理缓存操作
		performClearStorage() {
			let that = this;

			// 先删除资料文件
			that.clearMaterialFiles().then(() => {
				let os = plus.os.name;
				if (os == 'Android') {
					let main = plus.android.runtimeMainActivity();
					let sdRoot = main.getCacheDir();
					let files = plus.android.invoke(sdRoot, "listFiles");
					let len = files.length;

					if (len === 0) {
						uni.showToast({
							title: '清除成功',
							duration: 2000
						});
						that.getStorageSize();
						return;
					}

					let processedCount = 0;
					for (let i = 0; i < len; i++) {
						let filePath = '' + files[i]; // 没有找到合适的方法获取路径，这样写可以转成文件路径  
						plus.io.resolveLocalFileSystemURL(filePath, function (entry) {
							if (entry.isDirectory) {
								entry.removeRecursively(function () { //递归删除其下的所有文件及子目录
									processedCount++;
									if (processedCount === len) {
										uni.showToast({
											title: '清除成功',
											duration: 2000
										});
										that.getStorageSize();
									}
								}, function (e) {
									console.log(e.message);
									processedCount++;
									if (processedCount === len) {
										uni.showToast({
											title: '清除成功',
											duration: 2000
										});
										that.getStorageSize();
									}
								});
							} else {
								entry.remove(() => {
									processedCount++;
									if (processedCount === len) {
										uni.showToast({
											title: '清除成功',
											duration: 2000
										});
										that.getStorageSize();
									}
								}, () => {
									processedCount++;
									if (processedCount === len) {
										uni.showToast({
											title: '清除成功',
											duration: 2000
										});
										that.getStorageSize();
									}
								});
							}
						}, function (e) {
							console.log('文件路径读取失败');
							processedCount++;
							if (processedCount === len) {
								uni.showToast({
									title: '清除成功',
									duration: 2000
								});
								that.getStorageSize();
							}
						});
					}
				} else { // ios  
					plus.cache.clear(function () {
						uni.showToast({
							title: '清除成功',
							duration: 2000
						});
						that.getStorageSize();
					});
				}
			});
		},

		// 清除资料文件
		clearMaterialFiles() {
			return new Promise((resolve) => {
				try {
					const cachedFiles = uni.getStorageSync('yanqu_material_files');
					if (!cachedFiles) {
						resolve();
						return;
					}

					const localFiles = JSON.parse(cachedFiles);
					const fileKeys = Object.keys(localFiles);

					if (fileKeys.length === 0) {
						// 清除资料文件缓存记录
						uni.removeStorageSync('yanqu_material_files');
						resolve();
						return;
					}

					let processedCount = 0;

					fileKeys.forEach(key => {
						const filePath = localFiles[key].path;
						if (filePath) {
							// 使用 uni.removeSavedFile 删除保存的文件
							uni.removeSavedFile({
								filePath: filePath,
								success: () => {
									console.log('成功删除文件:', filePath);
									processedCount++;
									if (processedCount === fileKeys.length) {
										// 清除资料文件缓存记录
										uni.removeStorageSync('yanqu_material_files');
										resolve();
									}
								},
								fail: (err) => {
									console.error('删除文件失败:', filePath, err);
									processedCount++;
									if (processedCount === fileKeys.length) {
										// 清除资料文件缓存记录
										uni.removeStorageSync('yanqu_material_files');
										resolve();
									}
								}
							});
						} else {
							processedCount++;
							if (processedCount === fileKeys.length) {
								// 清除资料文件缓存记录
								uni.removeStorageSync('yanqu_material_files');
								resolve();
							}
						}
					});
				} catch (e) {
					console.error('清除资料文件时出错:', e);
					// 尝试清除资料文件缓存记录
					try {
						uni.removeStorageSync('yanqu_material_files');
					} catch (err) {
						console.error('清除资料文件缓存记录时出错:', err);
					}
					resolve();
				}
			});
		},

		// 清除所有用户相关数据
		clearUserRelatedData() {
			try {
				// 清除可能存在的其他用户相关数据
				const userRelatedKeys = [
					'historySearch',    // 搜索历史
					'recentCourses',    // 最近学习的课程
					'studyProgress',    // 学习进度
					'userSettings',     // 用户设置
					'userPreferences',  // 用户偏好
					'lastPosition',     // 上次学习位置
					'yanqu_material_files' // 资料文件缓存记录
				];

				userRelatedKeys.forEach(key => {
					if (uni.getStorageSync(key)) {
						uni.removeStorageSync(key);
					}
				});

				// 清除已保存的资料文件
				this.clearMaterialFiles();

			} catch (error) {
				console.error('清除用户相关数据失败:', error);
			}
		},
	}
}
</script>

<style lang="scss">
.setting-container {
	padding: 0 30rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	padding-bottom: 40rpx;
	position: relative; // 为固定定位提供参考点

	.header {
		position: relative;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		padding: 20rpx 0;
		margin-bottom: 20rpx;

		.back-icon {
			position: absolute;
			left: 30rpx;
			font-size: 40rpx;
		}

		.title {
			font-size: 36rpx;
			font-weight: 500;
		}
	}

	.setting-list {
		margin-top: 30rpx;

		.setting-item {
			margin-bottom: 20rpx;
			background-color: #fff;
			height: 100rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			padding-top: 10rpx;
			padding-bottom: 10rpx;
			border-radius: 20rpx;

			.cache-size {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #999;

				.current-size {
					margin-right: 10rpx;
				}
			}

			.item-text {
				font-size: 28rpx;
				color: #333;
			}

			.iconfont {
				color: #999;
				font-size: 36rpx;
			}
		}

		// 注销账户按钮样式
		.delete-account {
			.item-text {
				color: #ff4d4f;
			}
		}

		.setting-item:first-child {
			margin-top: 10rpx;
		}

		.setting-item:last-child {
			margin-bottom: 10rpx;
		}
	}

	// 添加容器使按钮固定在底部
	.logout-btn-container {
		position: fixed;
		bottom: 50rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		padding: 0 60rpx;
	}

	.logout-btn {
		width: 100%;
		height: 90rpx;
		background-color: #fff;
		color: #ff4d4f;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 45rpx;
		font-size: 32rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); // 添加轻微阴影效果
	}
}
</style>
