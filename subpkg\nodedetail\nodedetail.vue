<template>
	<view class="container">
		<view class="title">
			<text>第{{num}}天</text>
			<text>{{updatetimeText}}</text>
		</view>
		
		<view class="note-text">
			{{text}}
		</view>
		<view class="img-list">
			<image v-for="(item,index) in imageList" :src="item" :key="index"  @click="previewImage(index)"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imageList:[],
				text:'',
				num:0,
				updatetimeText:''
			};
		},
		onLoad(option){
			let id = option.id;
			this.num = option.num 
			this.getDetail(id)
		},
		methods:{
			previewImage (index) {
				uni.previewImage({
					current:index,
					urls:this.imageList
				})
			},
			getDetail(id) {
				let that = this
				let  data  = uni.$u.http.post('/student/getNote',{id:id}).then(res=>{
					console.log(res)
					if(res.code == 1) {						
						that.imageList = res.data.image;
						that.text  = res.data.data.text;
						that.updatetimeText  = res.data.data.updatetimeText;
						
					} else {
						uni.tip('数据不存在')
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background-color: $container-bg-color;
	height: 100vh;
	padding: 20rpx 30rpx;
	.title{
		padding: 0 18rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 14rpx;
		background-color: #fff;
		height: 72rpx;
		text{
			&:first-child{
				color: #01997A;
				font-size: 30rpx;
			}
			&:last-child{
				color: #6D6D6D;
				font-size: 24rpx;
			}
		}
	}
	.note-text{
		margin-top: 30rpx;
		padding: 34rpx 28rpx;
		background-color: #fff;
		color: #131313;
		border-radius: 20rpx;
	}
	.img-list{
		margin-top: 28rpx;
		image{
			width: 100%;
			margin-top: 30rpx;
		}
	}
}
</style>
