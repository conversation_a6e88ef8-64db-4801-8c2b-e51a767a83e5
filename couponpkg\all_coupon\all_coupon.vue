<template>
	<view class="container">
		<view class="content-container" >		
			<view class="coupon-container">
				<view class="coupon" v-for="(item,key) in couponList" :key="key">
					<view class="left">
						<text>{{item.coupon_price}} <text style="font-size: 32rpx;">元</text></text>
						<text v-if="item.use_min_price > 0">满{{item.use_min_price}}可用</text>
						<text v-else>无门槛</text>
					</view>
					<view class="right">
						<view class="name">
							{{item.coupon_title}}
						</view>
						<view class="detail-info">
							<view class="detail-left">
								<text class="date" v-if="item.coupon_time > 0">领取后{{item.coupon_time}}天有效</text>
								<text class="date" v-else>有效期  {{item.end_use_time_text}}</text>
								<view class="rule" @click="seeRule(item.coupon_desc)">
									<text>使用规则</text>
									<u-icon name="arrow-right"  color="#009c7b"  size="24" ></u-icon>
								</view>
							</view>
							<view class="detail-right">
								<view class="use-btn" @click="getCoupon(item)">
									立即领取
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
	
		<u-popup :show="show" mode="bottom"  @close="close" @open="open">
			<view class='u-popup-slot'>
				<text>使用规则:</text>
				<text>{{coupon_desc}}</text>
			</view>
		</u-popup>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex:0,
				couponNum:"",
				couponList:[],
				show:false,
				coupon_desc:'',
				clickFlag:false
			};
		},
		onLoad(){
			this.getAllCoupon();
		},
		methods:{
			
			seeRule(coupon_desc) {
				this.coupon_desc = coupon_desc
				this.show=true;
			},
			 open() {
			  // console.log('open');
			},
			close() {
			  this.show = false
			  // console.log('close');
			},
			change(item){
				this.currentIndex = item.index
			},
			getAllCoupon(){
				let that = this;
				const  data  = uni.$u.http.get('/coupon/couponList').then(rest=>{
					let res = rest.data
					console.log(data);
					that.couponList = res.rows;
					
				});
			},
			getCoupon(item){
				let that = this;
				if(this.clickFlag) {
					return false;
				} 
				this.clickFlag = true;
				setTimeout(function() {
					this.clickFlag = false;
				},500)
				const  data  = uni.$u.http.post('/coupon/useGetCoupon',{coupon_id:item.id}).then(res=>{
					if(res.code == 1) {
						uni.$u.toast('领取成功')
					}
					
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		view{
			box-sizing: border-box;
		}
		.u-popup-slot {
			width: 200px;
			height: 150px;
			@include flex;
			justify-content: center;
			align-items: center;
			text {
				color:#000;
				font-size: 24rpx;
				
			}
		}
		.content-container{
			padding: 30rpx;
			.exchange{
				height: 84rpx;
				border-radius: 40rpx;
				background-color: #F6F7FB;
				display: flex;
				align-items: center;
				justify-content: center;
					padding-left: 240rpx;
				.input-code{
					flex: 3;
				
				}
				text{
					flex: 1;
					height: 100%;
					border-radius: 0 40rpx 40rpx 0 ;
					background-color: $main-color;
					color: #FFF;
					line-height: 84rpx;
					text-align: center;
				}
				
			}
		
			.wait-for-use{
				height: 120rpx;
				border-radius: 40rpx;
				border: 1rpx solid #AFAFAF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 28rpx;
				.tip{
					display: flex;
					align-items: center;
					justify-content: space-between;
					text{
						color: #5A5A5A;
						font-size: 28rpx;
						margin-left: 16rpx;
					}
				}
				.more{
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						color: #EE7878;
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}
		 .coupon-container{
			 margin-top: 36rpx;
			 .coupon{
				 margin-bottom: 28rpx;
				 height: 170rpx;
				 background-size: contain;
				 background-repeat: no-repeat;
				 display: flex;
				 align-items: center;
				 justify-content: flex-start;
				 .left{
					 width: 192rpx;
					 height: 100%;
					 background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
					 background-repeat: no-repeat;
					 background-size: contain;
					 color: #fff;
					 display: flex;
					 flex-direction: column;
					 align-items: center;
					 justify-content: center;
					 text{
						 &:first-child{ 
							 font-weight: bold;
							font-size: 40rpx;
						 }
						 &:last-child{
							 color: #CDF3E7;
							 font-size: 26rpx;
						 }
					 }
				 }
				 .right{
					 background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
					 background-repeat: no-repeat;
					 background-size: contain;
					 width: 500rpx;
					 height: 100%;
					 padding: 24rpx 34rpx;
					 .name{
						 color: #4C5370;
						 font-size: 30rpx;
						 font-weight: bold;
					 }
					 .detail-info{
						 display: flex;
						 align-items: center;
						 justify-content: space-between;
						 .detail-left{
							 text.date{
								 color: #AFAFAF;
								 font-size: 24rpx;
							 }
							 .rule{
								 margin-top: 16rpx;
								 display: flex;
								 align-items: center;
								 justify-content: flex-start;
								 text{
									 font-size: 24rpx;
									 color: $main-color;
								 }
							 }
						 }
						 .detail-right{
							 height: 100%;
							 display: flex;
							 align-items: center;
							 justify-content: center;
							 .use-btn{
								 width: 132rpx;
								 height: 54rpx;
								 border-radius: 32rpx;
								 border: 1rpx solid #01997A;
								 color: #01997A;
								 font-weight: 500;
								 line-height: 54rpx;
								 text-align: center;
								 font-size: 26rpx;
							 }
						 }
					 }
				 }
			 }
		 }
		}
	}
</style>