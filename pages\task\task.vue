<template>
	<view class="container">
		<view class="head">
			<view class="title">
				<view class="green-block">

				</view>
				<view class="title">
					今日任务
				</view>
			</view>
			<view class="opt">
				<image @click="toHistory" class="opt-icon" src="../../static/png/history.png" mode="widthFix"></image>
				<image @click="toStudyHistory" class="opt-icon" src="../../static/png/note.png" mode="widthFix"></image>
				<image class="opt-icon" src="../../static/png/date.png" mode="widthFix" @click="selectDate"></image>
			</view>
		</view>

		<view class="tabs">
			<u-tabs :list="list" lineWidth="50" lineHeight="8" lineColor="#01997A" @change="changeSubject" :activeStyle="{
				color: '#01997A',
				fontWeight: 'bold',
			}" :inactiveStyle="{
				color: '#777777',
				fontWeight: 'bold',
			}" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;">
			</u-tabs>
		</view>
		<view class="desc" v-if="courseList.length > 0">
			<view class="info">
				<text>{{ name }}</text>
				<view class="complate-bar">
					<u-line-progress :percentage="taskTotal" height="18" activeColor="#10C19D" inactiveColor="#F39898" :showText="false">
					</u-line-progress>
				</view>
				<text class="rate">完成率{{ taskTotal != null ? taskTotal : 0 }}%</text>
			</view>

			<view class="lesson-list">
				<view class="lesson-info" v-if="courseList.length > 0" v-for="(item, index) in courseList">
					<view class="lesson-title">
						{{ item.title }}
					</view>
					<view class="show-text">
						<view v-if="item.task_type == 'professional'">
							<view :dttt="taskRet[item.id]" :class="['btn', (taskRet[item.id] && parseInt(taskRet[item.id].pre) > 97) ? 'btn2' : '']">
								<text v-if="(taskRet[item.id] && parseInt(taskRet[item.id].pre) > 97)" @click="goPro(item.task_id, item.id)">已完成</text>
								<text v-if="(taskRet[item.id] && parseInt(taskRet[item.id].pre) > 0 && parseInt(taskRet[item.id].pre) < 97)" @click="goPro(item.task_id, item.id)">继续考试</text>
								<text v-if="taskRet == '' || taskRet[item.id].pre == 0 || taskRet[item.id] == undefined" @click="goPro(item.task_id, item.id)">进入考试</text>
							</view>
							<text class="course_ddd" v-if="(taskRet[item.id] && taskRet[item.id].pre > 0 && taskRet[item.id].pre < 97)">已学{{ parseInt(taskRet[item.id].pre) }}%</text>

						</view>
						<view v-else>
							<view :class="['btn', parseInt(item.total_ret) > 97 ? 'btn2' : '']">
								<text v-if="parseInt(item.total_ret) > 97" @click="goOther(1, item)">已完成</text>
								<text v-if="parseInt(item.total_ret) > 0 && parseInt(item.total_ret) < 97" @click="goOther(2, item)">继续考试</text>
								<text v-if='item.total_ret == "" || parseInt(item.total_ret) == null || parseInt(item.total_ret) == 0' @click="goOther(2, item)">进入考试</text>
							</view>

						</view>

					</view>
				</view>
				<!-- <view class="lesson-info">
					<view class="lesson-title">
						25考研基础阶段时政考点测试卷
					</view>
					<view class="btn">
						<text>继续学习</text>
						<text class="tip">已学3%</text>
					</view>
				</view>
				<view class="lesson-info">
					<view class="lesson-title">
						25考研基础阶段时政考点测试卷
					</view>
					<view class="btn done">
						已完成
					</view>
				</view> -->
			</view>

		</view>
		<view class="desc" v-else>
			<view class="info">
				<view class="empty-info">
					<u-empty mode="data" text="暂无任务" textSize="24">
					</u-empty>
				</view>
			</view>
		</view>



		<view class="all-course-h1">
			<view class="left">
				<view class="green-block-big">

				</view>
				<text>题库练习</text>
			</view>
			<view class="right">
				<view class="opt">
					<image class="opt-icon" src="../../static/png/star.png" mode="widthFix" @click="toMyStar"></image>

					<image class="opt-icon" src="../../static/png/wrong-mark.png" mode="widthFix" @click="toWrongList">
					</image>

					<image class="opt-icon" src="../../static/png/records.png" mode="widthFix" @click="toRecords">
					</image>
				</view>
			</view>
		</view>


		<view class="practice-list">
			<view class="item">
				<view class="item-left">
					<text>专项题库</text>
				</view>
				<view class="item-right">
					<view class="more" @click="toMoreQuestion(0)">
						<text>全部专项</text>
						<u-icon color="#A2A2A2" name="arrow-right-double" size="14"></u-icon>
					</view>
					<view class="tabs">
						<u-tabs :current="professionalCurrent" :list="professionalTitleList" @change="changeProfessionalTitle" lineWidth="40" lineHeight="7" lineColor="#01997A" :activeStyle="{
							color: '#01997A',
							fontWeight: 'bold',
							fontSize: '30rpx'
						}" :inactiveStyle="{
							color: '#777777',
							fontSize: '30rpx'
						}" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 34px;">
						</u-tabs>
					</view>
					<scroll-view class="lesson-list" scroll-y="true">
						<view v-if="professionalSublist.length > 0">
							<view class="lesson-info" v-for="(item, i) in professionalSublist" :key="item.id">
								<view class="lesson-title">
									{{ item.title }}
								</view>
								<view class="btn" :class="{ 'done': item.doing }" @click="getProfessionalBankDetail(item.id)">
									进入专项
								</view>
							</view>
						</view>

						<view v-else class="empty-info">
							<u-empty mode="list" text="暂无题库" textSize="20">
							</u-empty>
						</view>

					</scroll-view>
				</view>
			</view>



			<view class="item">
				<view class="item-left">
					<text>真题题库</text>
				</view>
				<view class="item-right">
					<view class="more" @click="toMoreQuestion(1)">
						<text>全部真题</text>
						<u-icon color="#A2A2A2" name="arrow-right-double" size="14"></u-icon>
					</view>
					<view class="tabs">
						<u-tabs :current="trueCurrent" :list="trueTitleList" @change="changeTrueTitle" lineWidth="40" lineHeight="7" lineColor="#01997A" :activeStyle="{
							color: '#01997A',
							fontWeight: 'bold',
							fontSize: '30rpx'
						}" :inactiveStyle="{
							color: '#777777',
							fontSize: '30rpx'
						}" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 34px;">
						</u-tabs>
					</view>
					<scroll-view class="lesson-list" scroll-y="true">
						<view v-if="trueSublist.length > 0">
							<view class="lesson-info" v-for="(item, i) in trueSublist" :key="item.id">
								<view class="lesson-title">
									{{ item.title }}
								</view>
								<view class="btn" :class="{ 'done': item.doing }" @click="getProfessionalBankDetail(item.id)">
									进入真题
								</view>
							</view>
						</view>


						<view v-else class="empty-info">
							<u-empty mode="list" text="暂无题库" textSize="20">
							</u-empty>
						</view>
					</scroll-view>
				</view>
			</view>

			<!-- 模拟试卷 -->

			<view class="item">
				<view class="item-left">
					<text>模拟试卷</text>
				</view>
				<view class="item-right">
					<view class="more" @click="toMoreQuestion(2)">
						<text>全部试卷</text>
						<u-icon color="#A2A2A2" name="arrow-right-double" size="14"></u-icon>
					</view>
					<view class="tabs">
						<u-tabs :current="mockExaminationCurrent" :list="mockExaminationTitleList" @change="changeMockExaminationTitle" lineWidth="40" lineHeight="7" lineColor="#01997A" :activeStyle="{
							color: '#01997A',
							fontWeight: 'bold',
							fontSize: '30rpx'
						}" :inactiveStyle="{
							color: '#777777',
							fontSize: '30rpx'
						}" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 34px;">
						</u-tabs>
					</view>
					<scroll-view class="lesson-list" scroll-y="true">
						<view v-if="mockExaminationSublist.length > 0">
							<view class="lesson-info" v-for="(item, i) in mockExaminationSublist" :key="item.id">
								<view class="lesson-title">
									{{ item.title }}
								</view>
								<view class="btn" @click="getProfessionalBankDetail(item.id)">
									进入模考
								</view>
							</view>
						</view>

						<view v-else class="empty-info">
							<u-empty mode="list" text="暂无试卷" textSize="20">
							</u-empty>
						</view>
					</scroll-view>
				</view>
			</view>

		</view>
		<uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true" :insert="false" @confirm="confirm" @close="closeDate" />
	</view>

</template>

<script>
import {
	createNamespacedHelpers,
	mapState
} from "vuex"
import {
	getProfessionalQuestion,
	getTrueQuestion,
	getProgress,
	getQuestionListByPqid,
	getConfig,
	sendTaskSubmitTemplateMessage,
	getQuestionById,
	getTaskRecords,
	getQuestionCateList,
	getQuestionListByCate
} from "@/api/professional/index.js"
import {
	selectTemplate
} from "@/utils/tool.js"

let {
	mapGetters,
	mapActions,
	mapMutations,
} = createNamespacedHelpers('user')
var that;
export default {
	data() {
		const d = new Date()
		const year = d.getFullYear()
		let month = d.getMonth() + 1
		month = month < 10 ? `0${month}` : month
		const date = d.getDate()
		return {
			stuConfig: {},
			professionalCurrent: 0,
			trueCurrent: 0,
			currentIndex: 0,
			mockExaminationCurrent: 0,
			list: [{
				name: '政治'
			},
			{
				name: '英语'
			},
			{
				name: '数学'
			},
			{
				name: '专业课'
			}
			],
			name: '',
			courseList: [],
			allProfessionalList: [], //专项题库所有数据
			professionalTitleList: [], // 专项题库标题列表
			professionalSublist: [], // 专项题库子列表
			taskTotal: 0,
			taskRet: '',
			calendarShow: false,
			date: `${year}-${month}-${date}`,
			customTextDefaultDate: [`${year}-${month}-${date}`],
			listPractice: [{
				name: '政治'
			},
			{
				name: '英语'
			},
			{
				name: '数学'
			},
			{
				name: '专业课'
			}
			],
			listPaper: [{
				name: '政治'
			},
			{
				name: '英语'
			},
			{
				name: '数学'
			},
			{
				name: '专业课'
			}
			],
			name: '',
			courseList: [],
			allProfessionalList: [], //专项题库所有数据
			trueTitleList: [], // 真题题库标题列表
			trueSublist: [], // 真题题库子列表


			mockExaminationList: [], //模考题库所有数据
			mockExaminationTitleList: [], // 模拟试卷标题列表
			mockExaminationSublist: [], // 模拟试卷子列表

			taskTotal: 0,
			taskRet: '',
			arrText: {
				'英语': 0,
				'政治': 1,
				'数学': 2,
				'专业课': 3
			}
		};
	},

	methods: {
		//练习记录
		toRecords() {
			uni.navigateTo({
				url: '/collection_page/practice_record/practice_record'
			})
		},
		toWrongList() {
			uni.navigateTo({
				url: '/collection_page/my_wrong/my_wrong'
			})
		},
		async goPro(index, itemId) {
			const obj = await getQuestionById({
				id: itemId,
				aboutId: index
			})
			if (obj != false) {
				obj['aboutId'] = index
				this.tobank(obj)
			}
		},
		toStudyHistory() {
			uni.navigateTo({
				url: '/subpkg/study_history/study_history?type=professional'
			})
		},
		// 获取历史最后一次观看
		toHistory() {
			uni.$u.http.get('/task/getHistory', {
				params: {
					type: this.arrText[this.name],
					course_type: 'professional'
				}
			}).then(rest => {
				if (rest.code == 1) {
					let tmpData = rest.data
					if (tmpData.quantion_type !== 'professional') {
						tmpData.task_type = tmpData.quantion_type;
						this.goOther(2, tmpData);
					} else {
						this.goPro(tmpData.id, tmpData.aboutId);
					}
				}
			})
		},
		selectDate() {
			this.$refs.calendar.open();
		},
		closeDate() {

		},
		goOther(type, item) {
			if (item.task_type == 'semt') {
				if (type == 1) {
					uni.navigateTo({
						url: '/class_teacher/day_sentence_answer/day_sentence_answer?task_id=' + item.task_id
					})
				} else {
					uni.navigateTo({
						url: '/class_teacher/day_sentence/day_sentence?task_id=' + item.task_id
					})
				}
			} else if (item.task_type == 'word') {
				if (type == 1) {
					uni.navigateTo({
						url: '/english/word_answer/word_answer?task_id=' + item.task_id
					})
				} else {
					uni.navigateTo({
						url: '/class_teacher/day_word/day_word?task_id=' + item.task_id
					})
				}
			} else if (item.task_type == 'read') {
				if (type == 1) {
					uni.navigateTo({
						url: '/class_teacher/reading_analysis_answer/reading_analysis_answer?task_id=' + item
							.task_id
					})
				} else {
					uni.navigateTo({
						url: '/class_teacher/reading_analysis/reading_analysis?task_id=' + item.task_id
					})
				}
			}
		},

		//获取试题配置信息
		async getStuConfig() {
			const config = await getConfig()
			if (config !== false) {
				this.stuConfig = config
				this.$store.commit("professionalConfig/setConfig", config);
			} else {
				this.stuConfig = {}
			}
		},
		changeSubject(e) {
			this.currentIndex = e.index;
			this.name = e.name;
			this.getTaskList()
		},
		toMyStar() {
			uni.navigateTo({
				url: '/collection_page/my_collection/my_collection'
			})
		},
		getCourseType() {
			let that = this;
			const data = uni.$u.http.get('/task/getUserCourse?type=1').then(rest => {
				let res = rest.data.course_text
				that.list = res.map(item => {
					return {
						name: item.name
					}
				})
				that.name = that.list[0].name
				that.getTaskList();
			});
		},
		confirm(e) {
			this.date = e.fulldate;
			this.getTaskList();
			this.calendarShow = false;
		},
		async getTaskList() {
			let that = this;
			const data = await uni.$u.http.get('/task/getUserTask', {
				params: {
					type: that.name,
					qtype: 'professional',
					date: that.date
				}
			}).then(res => {
				if (res.code == 1) {
					let tmp_data = res.data;
					let tmp_total = 0
					if (res.data.length > 0) {
						that.courseList = [];
						res.data.map(item => {
							if (item.total_ret != null) {
								tmp_total += parseInt(item.total_ret)
								if (item.quantion_type == 'professional') {
									that.taskRet = item.alone_ret != null ? item.alone_ret : '';
								}
							}
							that.courseList = [...that.courseList, ...item.task_text];

						});
						that.taskTotal = parseInt(tmp_total / (that.courseList.length))
					} else {
						if (res.data.length > 0) {
							that.courseList = res.data[0].task_text
						} else {
							that.courseList = [];
						}
					}


				}

			});
		},

		/**
		 * 获取题库信息
		 */
		async getProfessionalBankDetail(pqid) {
			const item = await getQuestionById({
				id: pqid,
			})
			if (item != false) {
				this.tobank(item)
			}
		},

		/**
		 * 到题库
		 * @param {Object} item
		 */
		async tobank(item) {
			this.$store.commit("professionalConfig/clearAnswerList")
			//记录进入题库的页面
			this.$store.commit("professionalConfig/setCurrentStartPage")
			//题目列表
			let answeringQuestion = []
			//获取题目列表
			const params = {
				pqid: item.id,
				perPageNum: this.stuConfig.num,
			}
			let questionList = await getQuestionListByPqid(params)
			//如果题目列表为空返回
			if (questionList === false) {
				return
			}
			questionList.forEach(obj => {
				// 构造当前 题目的列表数据
				answeringQuestion.push({
					...obj,
					cateName: item.pname,
					type: item.type,
					pqid: item.id,
					total_time: item.total_time,
					id: obj.qid,
					qtype: obj.qtype,
					professionTitle: item.title,
					aboutId: item?.aboutId,
				})
			})
			let answerList = []
			if (item?.aboutId > 0) {
				let result = await getTaskRecords({
					pqid: item.id,
					aboutId: item.aboutId
				});
				if (result.code == 1) {
					answerList = result.data.map((o, index) => {
						const qindex = answeringQuestion.findIndex(
							(obj) => obj.id == o.qid
						);
						const question = answeringQuestion.splice(qindex, 1);
						answeringQuestion.splice(index, 0, question);
						return {
							answer: JSON.parse(o.answer),
							index,
							submitTime: o.createtime,
						};
					});
				}
				// 更新答题结果

				this.$store.commit("professionalConfig/setAnswerList", answerList);
			}

			this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
			//判断配置信息是否跳过已做题目
			//默认第一道题开始
			let startIndex = 0
			if (this.stuConfig.do_unread == 1) {
				//获取题库该中 已经做了多少道题
				let res = await getProgress({
					pqid: item.id
				})
				if (res !== false) {
					startIndex = res.currentIndex
				}
			}
			//覆盖起始index
			if (item?.aboutId > 0) {
				startIndex = answerList.length;
				if (startIndex >= answeringQuestion.length) {
					return uni.alert("当前任务已经完成")
				}
				//记录之前的答题数量
				this.$store.commit(
					"professionalConfig/setPrevAllAnswerListCount",
					answerList.length
				);
			}

			console.log(answeringQuestion[startIndex])
			this.$store.commit("professionalConfig/setCurrentIndex", startIndex);
			selectTemplate(answeringQuestion[startIndex], item)
		},
		...mapMutations(['setUser']),
		//所有专项
		toMoreQuestion(type) {
			uni.navigateTo({
				url: "/subpkg/allquestion/allquestion?type=" + type
			})
		},
		//专项题库标题更改
		async changeProfessionalTitle(obj) {
			this.professionalCurrent = obj.index;
			// 请求对应分类的题目列表
			const res = await getQuestionListByCate({
				cid: obj.id,
				type: 0 // 专项题库
			});
			if (res.code === 1) {
				this.professionalSublist = res.data || [];
			}
		},
		async changeTrueTitle(obj) {
			this.trueCurrent = obj.index;
			const res = await getQuestionListByCate({
				cid: obj.id,
				type: 1 // 真题题库
			});
			if (res.code === 1) {
				this.trueSublist = res.data || [];
			}
		},

		//模拟试卷标题改变
		async changeMockExaminationTitle(obj) {
			this.mockExaminationCurrent = obj.index;
			const res = await getQuestionListByCate({
				cid: obj.id,
				type: 2 // 模拟试卷
			});
			if (res.code === 1) {
				this.mockExaminationSublist = res.data || [];
			}
		},

		// 新增处理分类数据的方法
		async handleQuestionCateList() {
			const res = await getQuestionCateList();
			if (res.code === 1) {
				// 设置专项题库标题
				this.professionalTitleList = res.data.specializedQuestionCateList.map(item => ({
					name: item.name,
					id: item.id
				}));

				// 设置真题题库标题
				this.trueTitleList = res.data.realExamQuestionCateList.map(item => ({
					name: item.name,
					id: item.id
				}));

				// 设置模拟试卷标题
				this.mockExaminationTitleList = res.data.mockExamQuestionCateList.map(item => ({
					name: item.name,
					id: item.id
				}));

				// 默认加载第一个分类的数据
				if (this.professionalTitleList.length > 0) {
					this.changeProfessionalTitle({
						index: 0,
						id: this.professionalTitleList[0].id
					});
				}

				if (this.trueTitleList.length > 0) {
					this.changeTrueTitle({
						index: 0,
						id: this.trueTitleList[0].id
					});
				}

				if (this.mockExaminationTitleList.length > 0) {
					this.changeMockExaminationTitle({
						index: 0,
						id: this.mockExaminationTitleList[0].id
					});
				}
			}
		}
	},
	computed: {
		...mapState('professionalConfig', ['currentTypes', 'config']),
		...mapState('user', {
			token: state => state.userInfo.token
		})
	},
	onLoad() {
		this.professionalCurrent = 0;
		this.trueCurrent = 0;
		this.mockExaminationCurrent = 0;
		this.getCourseType()
	},
	onShow() {
		this.handleQuestionCateList();
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #F6F7FB;
}

.container {
	padding: 0 30rpx;
	background-color: #F6F7FB;

	.opt {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.stu-note {
			padding: 8rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: bold;
			color: #01997A;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			border: 3rpx solid #01997A;
		}

		.opt-container {
			width: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.opt-text {
				text-align: center;
				font-size: 20rpx;
				color: #777;
			}
		}

		.opt-icon {
			margin: 0 10rpx;
			width: 50rpx;

			&:first-child {}
		}
	}

	.lesson-list {
		.lesson-info {
			padding: 0 30rpx;
			margin-bottom: 40rpx;
			color: #4A4A4C;
			font-size: 28rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.lesson-title {
				font-size: 28rpx;
				font-weight: bold;
				color: #4A4A4C;
				width: 400rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.btn {
				width: 130rpx;
				height: 44rpx;
				line-height: 44rpx;
				font-size: 24rpx;
				color: #fff;
				background-color: #01997A;
				text-align: center;
				border-radius: 365rpx;
				position: relative;

				.tip {
					position: absolute;
					bottom: -40rpx;
					left: 50%;
					transform: translateX(-50%);
					font-size: 20rpx;
					color: #A4A4A4;
					width: 72rpx;
				}
			}

			.btn2 {
				background: linear-gradient(90deg, #F39898 0%, #EE7878 100%);
			}

			.show-text {
				position: relative;
			}

			.course_ddd {
				position: absolute;
				top: 40rpx;
				margin-left: 16rpx;
				font-size: 22rpx;
				;
			}

			.done {
				background-color: #F39898;

			}
		}
	}

	.head {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		justify-content: space-between;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.green-block {
				width: 18rpx;
				height: 38rpx;
				background-color: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;
			}

			.title {
				margin-left: 10rpx;
				color: #1F1F27;
				font-size: 32rpx;
			}
		}
	}

	.desc {
		background-color: #fff;
		border-radius: 20rpx;
		margin-top: 24rpx;
		padding: 36rpx 0;

		.info {
			padding: 0 30rpx;
			margin-bottom: 30rpx;
			color: #4A4A4C;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.complate-bar {
				width: 320rpx;
			}

			.rate {
				color: #878787;
				font-size: 26rpx;
			}
		}
	}

	.all-course-h1 {
		background-color: #F6F7FB;
		padding: 32rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.left {
			display: flex;
			align-items: center;
			justify-content: flex-start;

			.green-block-big {

				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;

			}

			text {
				padding-left: 10rpx;
				width: 128rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}

	}

	.practice-list {
		padding-bottom: 30rpx;

		.item {
			background-color: #fff;
			padding: 8rpx 0;
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 28rpx;
			border-radius: 20rpx;

			.item-left {
				width: 58rpx;
				height: 328rpx;
				background: #CDF3E7;
				border-radius: 16rpx 80rpx 80rpx 16rpx;
				opacity: 0.92;
				writing-mode: vertical-lr;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #01997A;
				font-size: 28rpx;
			}

			.item-right {
				flex: 1;
				margin-left: 20rpx;

				.more {
					margin-top: 18rpx;
					padding-right: 30rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					color: #A2A2A2;
					font-size: 22rpx;

					text {
						margin-right: 8rpx;
					}
				}

				.tabs {
					width: 420rpx;
				}

				.lesson-list {
					height: 224rpx;

					&::-webkit-scrollbar {
						width: 4px;
						background-color: #F5F5F5;
					}

					&::-webkit-scrollbar-thumb {
						border-radius: 10px;
						background-color: #01997A;
					}

					&::-webkit-scrollbar-track {
						border-radius: 10px;
						background-color: #F5F5F5;
					}
				}

				.lesson-info {
					padding-left: 20rpx;
					margin-top: 16rpx;
					margin-bottom: 26rpx;

					&:last-child {
						margin-bottom: 18rpx;
					}

					.lesson-title {
						color: #4A4A4C;
						font-size: 26rpx;
					}
				}
			}

		}
	}
}

.empty-info {
	width: 100%;
	margin-top: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>