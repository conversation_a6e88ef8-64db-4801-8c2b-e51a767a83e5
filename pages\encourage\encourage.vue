<template>
	<view class="container">
		<!-- 头部 -->
		<view class="head" v-if="teacherShow">
			<view class="left" v-if="teacherDetail.id">
				<image class="avatar" :src="teacherDetail.image"></image>
				<view class="overview">
					<text>{{teacherDetail.name}}</text>
					<text>{{teacherDetail.tag_text}}</text>
				</view>
			</view>
			<view class="right">
				<view class="desc">
					<text>班主任评价</text>
					<text>授课老师评价</text>
				</view>
				<view class="action">
					<view class="btn" @click="toComment(1)">进入评价</view>
					<view class="btn" @click="toComment(2)">进入评价</view>
				</view>
			</view>
		</view>

		<!-- 任务标题 -->
		<view class="all-course-h1">
			<view class="left">
				<view class="green-block-big">

				</view>
				<text>今日任务</text>
			</view>
			<view class="opt">
				<image class="opt-icon" src="../../static/png/calendar.png" mode="widthFix"  @click="selectDate"></image>

			</view>
		</view>

		<!-- tab 栏目 -->
		<u-tabs :list="list" lineWidth="50" lineHeight="8" lineColor="#01997A" @click="changeName" :activeStyle="{
					 color: '#01997A',
					  fontWeight: 'bold',
		        }" :inactiveStyle="{
		             color: '#777777',
					  fontWeight: 'bold',
		        }" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;">
		</u-tabs>

		<view class="course">
			<view class="cate" v-if="JSON.stringify(courseList) !== '{}' && (show_index==-1 || show_index == index)" v-for="(item,index)  in courseList" :key="index" >
				<view class="title">
					<view class="tag">
						{{showNameArr[index]}}
					</view>
					<view class="progress">
						<view class="overlay" :style="{width:(((parseInt(item.course.total_per)+parseInt(item.other.total_per))/((item.course.course.length > 0 && item.other.course.length > 0) ? 2 : 1) ) > 95 ? 100 : ((parseInt(item.course.total_per)+parseInt(item.other.total_per))/((item.course.course.length > 0 && item.other.course.length > 0) ? 2 : 1) ))+'%',
					backgroundImage:`url(${progressImg})`
					}">

						</view>
					</view>
					<view class="title-info">
						完成率{{((parseInt(item.course.total_per)+parseInt(item.other.total_per))/((item.course.course.length > 0 && item.other.course.length > 0) ? 2 : 1)) > 95 ? 100 :
						((parseInt(item.course.total_per)+parseInt(item.other.total_per))/((item.course.course.length > 0 && item.other.course.length > 0) ? 2 : 1)) }}%
					</view>
				</view>
				<template  v-if="item && item.course.course.length > 0"  >				
					
					<view class="desc" >
						<view class="info">
							<text>{{showNameArr[index]}}</text>
							<view class="complate-bar">
								<u-line-progress :percentage="(item.course.course.length  > 0 && item.course.total_per?(parseInt(item.course.total_per) >= 95 ? 100: parseInt(item.course.total_per)):0)" height="18" activeColor="#10C19D" inactiveColor="#F39898"
									:showText="false">
								</u-line-progress>
							</view>
							<text class="rate">完成率{{item.course.course.length  > 0 && item.course.total_per?(parseInt(item.course.total_per) >=  95 ? 100 : parseInt(item.course.total_per)):0}}%</text>
						</view>						
						
						<view class="lesson-list" >
							<view class="lesson-info" v-for="(course_item,cindex) in item.course.course" :key="cindex">
								<view class="lesson-title">
									{{course_item.title}}
								</view>
								<view class="show-text">
									<view :class="['btn', item.course.per !=  undefined && item.course.per[course_item.id] != undefined && parseInt(item.course.per[course_item.id].pre) >= 95?'btn2':'']" @click="toListen(item.course.task_id,course_item.id,course_item.video_id,item.course.type)">
										<text v-if="item.course.per != undefined &&  item.course.per[course_item.id] != undefined &&  item.course.per[course_item.id].pre != undefined && parseInt(item.course.per[course_item.id].pre) >= 95">已完成</text>
										<text v-if="item.course.per != undefined  && item.course.per[course_item.id] != undefined && parseInt(item.course.per[course_item.id].pre) > 0 && parseInt(item.course.per[course_item.id].pre)< 95">继续听课</text>
										<text v-if="item.course.per == undefined  || item.course.per[course_item.id] == undefined || parseInt(item.course.per[course_item.id].pre) == 0  ">进入课程</text>
									</view>
									<text class="course_ddd" v-if="item.course.per[course_item.id] != undefined && item.course.per[course_item.id].pre != undefined && item.course.per[course_item.id].pre > 0 && item.course.per[course_item.id].pre< 95">已学{{parseInt(item.course.per[course_item.id].pre)}}%</text>
									
								</view>
							</view>
							
							
						</view>
					</view>
					
				</template>
				<template  v-if="item && item.other.course.length >0"  >
					<view class="desc" >
						<view class="info">
							<text>{{showNameArr[index]}}</text>
							<view class="complate-bar">
								<u-line-progress :percentage="(item.other.total_per?(parseInt(item.other.total_per) >=  95 ? 100 :parseInt(item.other.total_per) ):0)" height="18" activeColor="#10C19D" inactiveColor="#F39898"
									:showText="false">
								</u-line-progress>
							</view>
							<text class="rate">完成率{{item.other.total_per?(parseInt(item.other.total_per) >= 95 ? 100: parseInt(item.other.total_per)):0}}%</text>
						</view>						
						
						<view class="lesson-list">
							<view class="lesson-info"  v-for="(course_item,cindex) in item.other.course" :key="cindex">
								<view class="lesson-title">
									{{course_item.title}}
								</view>
								<template v-if="course_item.task_type ==  'professional'">
									<view :class="['btn',item.other.per != '' && item.other.per !=  undefined && item.other.per[course_item.pro_id] != undefined &&  parseFloat(item.other.per[course_item.pro_id].pre) >= 99?'btn2':'']" :data-ser="item.other.per[course_item.pro_id].pre">
										<text v-if="item.other.per != undefined && item.other.per[course_item.pro_id] != undefined && parseFloat(item.other.per[course_item.pro_id].pre) >= 99" @click="goOthePro(course_item.task_id,course_item)">已完成</text>
										<text v-if=" item.other.per != undefined && item.other.per[course_item.pro_id] != undefined  && parseFloat(item.other.per[course_item.pro_id].pre) > 0 && parseFloat(item.other.per[course_item.pro_id].pre)< 99"  @click="goOthePro(course_item.task_id,course_item)">继续考试</text>
										<text v-if="item.other.per.length == 0 ||item.other.length ==0 || item.other.per[course_item.pro_id] == undefined || parseFloat(item.other.per[course_item.pro_id].pre) == 0 ||　item.other.per[course_item.pro_id]== undefined" @click="goOthePro(course_item.task_id,course_item)">开始考试</text>
									</view>
								</template>
								<template v-else>
									<view :class="['btn',item.other.per != '' && item.other.per !=  undefined && item.other.per[course_item.task_id] != undefined &&  item.other.per[course_item.task_id].pre >= 95?'btn2':'']" :data-ser="item.other.per[course_item.task_id].pre">
										<text v-if="item.other.per != undefined && item.other.per[course_item.task_id] != undefined && item.other.per[course_item.task_id].pre >= 95" @click="goOther(1,course_item)">已完成</text>
										<text v-if=" item.other.per != undefined && item.other.per[course_item.task_id] != undefined && parseFloat(item.other.per[course_item.task_id].pre) > 0 && parseFloat(item.other.per[course_item.task_id].pre)< 95"  @click="goOther(2,course_item)">继续考试</text>
										<text v-if="item.other.per.length == 0 ||item.other.length ==0 || item.other.per[course_item.task_id] == undefined || item.other.per[course_item.task_id].pre == 0 ||　item.other.per[course_item.task_id]== undefined" @click="goOther(3,course_item)">开始考试</text>
									</view>
								</template>
								
							</view>
						</view>
					</view>
				</template>
				
				
			</view>	
			<u-empty v-if="courseList.length ==0 && loaded==true" :marginTop="'20rpx'" class="empty" text="暂无数据" :textSize='"24rpx"' ></u-empty>	
		</view>
		<uni-calendar ref="calendar" class="uni-calendar--hook" clear-date="true"  :insert="false"  @confirm="confirm" @close="closeDate"/>
	
		<view class="content" v-if="remarkList.length > 0">
			
			<view class="text-content">
				<u-cell title="重要通知">
					<u-icon slot="icon" size="32" name="volume"></u-icon>
				</u-cell>
				<u-text  v-for="(item,index) in remarkList" :key="index"
				:text="item" 
				:size="28" 
				color="#5A5A5A"></u-text>

			</view>
		</view>
	</view>
</template>

<script>
	import {
		getProfessionalQuestion,
		getTrueQuestion,
		getProgress,
		getQuestionListByPqid,
		getConfig,
		sendTaskSubmitTemplateMessage,
		getQuestionById
	} from "@/api/professional/index.js"
	import {
		selectTemplate
	} from "@/utils/tool.js"
	import {mapState} from "vuex"
	let progress =
		"data:image/png;base64,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"
		export default {
			watch: {
				userInfo: {
					handler: function(newV, oldV) {
						if (newV) {
							this.getCourseType();
						}
					},
					deep: true
				}
			},
			computed:{
				...mapState('user',['userInfo']),
			},
		data() {
			const d = new Date()
			const year = d.getFullYear()
			let month = d.getMonth() + 1
			month = month < 10 ? `0${month}` : month
			let date = d.getDate()
			date = date < 10 ? `0${date}` : date
			return {
				loaded:false,
				stuConfig: {},
				progressImg: progress,
				processComplete: "60%",
				customTextDefaultDate: `${year}-${month}-${date}`,
				date:`${year}-${month}-${date}`,
				list: [{
						name: '全部'
					}
				],
				name:'全部',
				nameArr :['英语','政治','数学','专业课'],
				showNameArr :{},
				courseList:[],
				show_index:-1,
				allCourse:[],
				courseIds:'',
				calendarShow:false,
				teacherShow:0,
				teacherDetail:'',
				remarkList:[],
			};
		},
		onShow(){
			this.getCourseType();
			this.getCanTeacherConfig();
			this.getRemarkList();
		},
		methods:{
			async getRemarkList() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getNotifyForTask',{params:{date:that.date}}).then(res=>{
					this.loaded = true
					if(res.code == 1){
						this.remarkList = res.data;						
					}
				
				});
			},
			
			
			async goOthePro(index,item){
				const obj = await getQuestionById({id:item.id, aboutId:index})
				if(obj.pname=="计算机408"){
					return uni.navigateToMiniProgram({
						appId: 'wxeddd9b203600c5fe',
					
					})
				}
				if(obj!=false){
					obj['aboutId'] = index
					this.tobank(obj)
				}
			},
			//获取试题配置信息
			async getStuConfig() {
				const config = await getConfig()
				if (config !== false) {
					this.stuConfig = config
					this.$store.commit("professionalConfig/setConfig", config);
				} else {
					this.stuConfig = {}
				}
			},
			
			/**
			 * 到题库
			 * @param {Object} item
			 */
			async tobank(item) {
				this.$store.commit("professionalConfig/clearAnswerList")
				//记录进入题库的页面
				this.$store.commit("professionalConfig/setCurrentStartPage")
				//题目列表
				let answeringQuestion = []
				//获取题目列表
				const params = {
					pqid: item.id,
					perPageNum: this.stuConfig.num,
				}
				let questionList = await getQuestionListByPqid(params)
				//如果题目列表为空返回
				if (questionList === false) {		
					return
				}
				questionList.forEach(obj => {
					// 构造当前 题目的列表数据
					answeringQuestion.push({
						...obj,
						cateName: item.pname,
						type: item.type,
						pqid: item.id,
						total_time: item.total_time,
						id: obj.qid,
						subtitle: obj.qtitle,
						qtype: obj.qtype,
						professionTitle: item.title,
						aboutId: item?.aboutId,
					})
				})
				this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
				//判断配置信息是否跳过已做题目
				//默认第一道题开始
				let startIndex = 0
				if (this.stuConfig.do_unread == 1) {
					//获取题库该中 已经做了多少道题
					let res = await getProgress({
						pqid: item.id
					})
					if (res !== false) {
						startIndex = res.currentIndex
					}
				}
				this.$store.commit("professionalConfig/setCurrentIndex", startIndex);
				selectTemplate(answeringQuestion[startIndex], item)
			},
			//获取教室评论开启与否
			getCanTeacherConfig ()
			{
				let that  = this;
				const  data  =  uni.$u.http.get('/task/getCanTeacherConfig',).then(res=>{
					if(res.code == 1){
						 that.teacherShow = parseInt(res.data.config);
						 if(that.teacherShow) {
							 that.teacherDetail = res.data.tInfo;
						 }
					}
				
				});
			},
			
			toComment(type){
				// console.log()
				uni.navigateTo({
					url:'/subpkg/comment/comment?type='+type
				})
			},
			selectDate(){
				this.$refs.calendar.open();
			},
			closeDate() {
				
			},
			goOther(type,item) {
				
				console.log(type,item);
				if(item.task_type == 'semt') {
					if(type == 1) {
						uni.navigateTo({
							url:'/class_teacher/day_sentence_answer/day_sentence_answer?task_id='+item.task_id
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/day_sentence/day_sentence?task_id='+item.task_id
						})
					}
				} else if(item.task_type == 'word') {
					if(type == 1) {
						uni.navigateTo({
							url:'/english/word_answer/word_answer?task_id='+item.task_id
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/day_word/day_word?task_id='+item.task_id
						})
					}
				} else if(item.task_type == 'read') {
					if(type == 1) {
						uni.navigateTo({
							url:'/class_teacher/reading_analysis_answer/reading_analysis_answer?task_id='+item.task_id
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/reading_analysis/reading_analysis?task_id='+item.task_id
						})
					}
				}
			},
			toListen(taskId,chapter_id,video_id,type){
				uni.navigateTo({
					url:'/choosecoursepkg/study/study?course_type='+type+'&taskId='+taskId+'&chapter_id='+chapter_id+'&video_id='+video_id
				})
			},
			changeName(e){				
			
				if(e.name == '全部') {
					this.show_index  = -1;
				} else  {
					let indexArr  =  this.nameArr.filter(item=>item.name == e.name);
					let index = indexArr[0].index
					console.log(index)
					this.show_index = index;
				}				
			},
			getCourseType() {
				let that = this;
				const  data  = uni.$u.http.get('/task/getUserCourse').then(rest=>{
					let res = rest.data.course_text;
					that.list=  [{
							name: '全部'
						}
					]
					res.map(item=>{
						this.showNameArr[item.index] = item.name
						return that.list.push({name:item.name})
					})
					that.nameArr = res;
					that.sel_name = that.list[0].name
					
					that.getTaskList();
				});
			},
			confirm(e) {
				
				this.date = e.fulldate;
				this.getTaskList();
				this.calendarShow = false;
			},
			async getTaskList (){
				let that = this;
				this.loaded = false
				const  data  = await  uni.$u.http.get('/task/getUserTask',{params:{type:that.sel_name,date:that.date}}).then(res=>{
					this.loaded = true
					if(res.code == 1){
						let tmp_data = res.data;
						
						let tmp_course = [];
						if(that.name == '全部') {
							that.allCourse =  res.data;
							that.courseList =  that.allCourse ;
						}
					}
				
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #F6F7FB;
		padding: 0 30rpx;
		padding-top: 26rpx;
		padding-bottom: 40rpx;
		view {
			box-sizing: border-box;
		}
		

		.head {
			height: 156rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10rpx;

			.left {
				width: 334rpx;
				box-sizing: border-box;
				height: 100%;
				background: linear-gradient(to right, #EBF6F8, #D9F9FF);
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding: 0 24rpx;

				.avatar {
					height: 100rpx;
					width: 100rpx;
					border-radius: 50%;
				}

				.overview {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					text {
						text-align: left;

						&:first-child {
							width: 100%;
							padding-left: 18rpx;
							font-size: 28rpx;
							color: #016A99;
							font-weight: bold;
						}

						&:last-child {
							margin-top: 22rpx;
							width: 100%;
							font-size: 22rpx;
							color: #3D89BC;
						}
					}
				}
			}

			.right {
				width: 334rpx;
				height: 100%;
				background: linear-gradient(to right, #EAF8F3, #D3F9EE);
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 16rpx;
				box-sizing: border-box;

				.desc {
					font-size: 28rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					color: #01997A;
					font-weight: bold;
					text-align: left;

					text {
						width: 100%;

						&:last-child {
							margin-top: 28rpx;
						}
					}
				}

				.action {
					.btn {
						height: 44rpx;
						width: 130rpx;
						line-height: 44rpx;
						text-align: center;
						border-color: #01997A;
						color: #01997A;
						font-size: 24rpx;
						border-radius: 26rpx;
						border: 2rpx solid;

						&:last-child {
							margin-top: 28rpx;
						}
					}
				}
			}
		}

		.all-course-h1 {
			background-color: #F6F7FB;
			padding: 17rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.green-block-big {

					width: 18rpx;
					height: 37rpx;
					background: #01997A;
					border-radius: 0rpx 10rpx 0rpx 10rpx;

				}

				text {
					font-family: PingFang SC, PingFang SC;
					padding-left: 10rpx;
					width: 128rpx;
					height: 45rpx;
					font-weight: 700;
					font-size: 32rpx;
					color: #1F1F27;
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.opt-icon {
					width: 80rpx;
				}
			}


		}


		.course {
			.cate {
				margin-top: 40rpx;
				.title {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.tag {
						height: 48rpx;
						padding: 0 10rpx;
						background: #CDF3E7;
						border-radius: 16rpx;
						font-weight: bold;
						font-size: 26rpx;
						color: #01997A;
						line-height: 48rpx;
						text-align: center;
					}

					.progress {
						width: 420rpx;
						height: 30rpx;
						background: #E6E6E6;
						border-radius: 20rpx;

						.overlay {
							height: 100%;
							border-radius: 20rpx;
							background-repeat: repeat-x;
							background-size: 50% 100%;
						}
					}

					.title-info {
						font-size: 26rpx;
						color: #414141;
					}
				}


				.desc {
					background-color: #fff;
					border-radius: 20rpx;
					margin-top: 24rpx;
					padding: 36rpx 0;

					.info {
						padding: 0 30rpx;
						margin-bottom: 30rpx;
						color: #4A4A4C;
						font-size: 32rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.complate-bar {
							width: 320rpx;
						}

						.rate {
							color: #878787;
							font-size: 26rpx;
						}
					}

					.lesson-list {
						.lesson-info {
							padding: 0 30rpx;
							margin-bottom: 40rpx;
							color: #4A4A4C;
							font-size: 28rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.lesson-title {
								font-size: 28rpx;
								font-weight: bold;
								color: #4A4A4C;
							}

							.btn {
								width: 130rpx;
								height: 44rpx;
								line-height: 44rpx;
								font-size: 24rpx;
								color: #fff;
								background-color: #01997A;
								text-align: center;
								border-radius: 365rpx;
								position: relative;

								.tip {
									position: absolute;
									bottom: -40rpx;
									left: 50%;
									transform: translateX(-50%);
									font-size: 20rpx;
									color: #A4A4A4;
									width: 72rpx;
								}
							}
							.btn2 {
								background:linear-gradient( 90deg, #F39898 0%, #EE7878 100%);
							}
							.show-text{
								position: relative;
							}
							.course_ddd{
								position: absolute;
								top:40rpx;
								margin-left:16rpx;
								font-size: 22rpx;;
							}

							.done {
								background-color: #F39898;

							}
						}
					}


				}
			}
		}
		
		.content{
			background-color: #fff;
			padding: 0 20rpx;
			margin-top: 10rpx;;
			.text-content {
			  margin-top: 20rpx;
			  font-size: 28rpx;
			  line-height: 1.6; /* 设置行高，提升阅读体验 */
			}
		}
	}
</style>