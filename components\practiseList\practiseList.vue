<template>
	<view class="exam-list">
		<view class="item" v-for="(item, i) in list" :key="item.id" @click="tobank(item)">
			<text class="num">{{i+1}}</text>
			<view class="title">
				<text class="title-name" style="width: 100%;">{{item.title}}</text>
				<!-- <text class="date">刷题量&nbsp;{{item.complete}}/{{item.all}} &nbsp;正确率{{item.rate}}</text> -->
				 <text class="date">刷题量&nbsp;{{item.complete}}/{{item.all}}</text>
			</view>
			<view class="exam-progress" v-if="item.doing">
				<text class="do-continue">继续做题</text>
				<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from "vuex"
	import {selectTemplate} from "@/utils/tool.js"
	import {getProgress, getQuestionListByPqid} from "@/api/professional/index.js"
	export default {
		name:"practiseList",
		data() {
			return {
				
			};
		},
		props:{
			list:{
				type:Array,
				default:[]
			}
		},
		methods:{
			async tobank(item){
				
				//记录进入题库的页面
				this.$store.commit("professionalConfig/setCurrentStartPage", '?type='+this.$parent.type)
				//题目列表
				let answeringQuestion = []
				//获取题目列表
				const params = {
					pqid:item.id,
					perPageNum:this.config.num,
				}
				let questionList = await getQuestionListByPqid(params)
				//如果题目列表为空返回
				   if(questionList===false){
					   return
				   }      
																																																																																																							  
					questionList.forEach(obj=>{
						// 构造当前 题目的列表数据
						answeringQuestion.push({
							...obj,
							cateName:item.pname,
							type:item.type,
							pqid:item.id,
							id:obj.qid,
							qtype:obj.qtype,
							professionTitle:item.title,
							total_time:item.total_time,
						})
					})
				
				// Object.keys(item.types).forEach(obj=>{
				// 	item.types[obj].question.forEach(q=>{
				// 		answeringQuestion.push({
				// 			...q,
				// 			qtype:obj,
				// 			cateName:item.pname,
				// 			type:item.type,
				// 			pqid:item.id
				// 		})
				// 	})
				// })
				// return
				//更新到 vuex
				this.$store.commit("professionalConfig/setCurrentTypes",answeringQuestion)
				//判断配置信息是否跳过已做题目
				//默认第一道题开始
				let startIndex = 0
				if(this.config.do_unread ==1){
					//获取题库该中 已经做了多少道题
					let res = await getProgress({pqid:item.id})
					if(res !==false){
						startIndex = res.currentIndex
					}
				}
				this.$store.commit("professionalConfig/setCurrentIndex", startIndex);
				selectTemplate(answeringQuestion[startIndex],item)
			}
		},
		computed:{
			...mapState('professionalConfig', ['currentTypes','config'])
		}
		
	}
</script>

<style lang="scss" scoped>
			.exam-list{
				padding-top: 20rpx;
				padding-bottom: 110rpx;
				
				.item{
					display: flex;
					align-items: center;
					justify-content: flex-start;
					background-color: #fff;
					border-radius: 20rpx;
					padding: 18rpx 20rpx;
					margin-bottom: 20rpx;
					.num{
						width: 40rpx;
						height: 40rpx;
						background-color: $main-color;
						color: #fff;
						border-radius: 50%;
						line-height: 40rpx;
						font-size: 24rpx;
						font-weight: bold;
						text-align: center;
						margin-right: 20rpx;
						
					}
					.title{
						flex: 3;
						margin-right: 20rpx;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						flex-direction: column;
						.title-name{
							text-align: left;
							font-weight: bold;
							font-size: 28rpx;
							color: #4A4A4C;
						}
						.date{
							width: 100%;
							color: #9F9F9F;
							font-size: 22rpx;
						}
					}
					.exam-progress{
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.do-continue{
							
							color: #EE7878;
							font-size: 20rpx;
						}
					}
				}
			}
</style>