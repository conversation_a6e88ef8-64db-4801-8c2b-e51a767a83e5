<template>
  <view class="progress-bar-container">
    <view class="label">{{ title }}</view>
    <view class="progress-bar">
      <view class="progress" :style="{ width: progressWidth }"></view>
    </view>
    <view class="info">{{ date }}</view>
  </view>
</template>

<script>
export default {
  data() {
    return {


    }
  },
  props: {
    title: {
      type: String,
      default: ""
    },
    percentage: {
      type: Number,
      default: 0
    },
    date: {
      type: String,
      default: ""
    }
  },
  computed: {
    // 计算进度条宽度
    progressWidth() {
      console.log('this.percentage', this.percentage)
      return this.percentage + '%';
    }
  }
}
</script>

<style scoped>
.progress-bar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14rpx 54rpx;
}

.label {
  font-size: 24rpx;
  color: #4A4A4C;
  font-weight: bold;
  width: 100rpx;
}

.progress-bar {
  width: 316rpx;
  height: 20rpx;
  background: #EAEDFC;
  border-radius: 40rpx
}

.progress {
  background-color: #10C19D;
  height: 100%;
  border-radius: 4px;
}

.info {
  font-size: 26rpx;
  color: #666;
  width: 155rpx
}
</style>
