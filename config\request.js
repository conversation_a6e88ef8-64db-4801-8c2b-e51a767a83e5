const { getUserInfo } = require("../utils/storage");
const { userLogin } = require("../store/user/index.js");
const tabList = [
	"pages/index/index",
	"pages/listen/listen",
	"pages/task/task",
	"pages/encourage/encourage",
	"pages/center/center",
]
module.exports = (vm) => {
    // 初始化请求配置
    uni.$u.http.setConfig((config) => {
        /* config 为默认全局配置*/
		 config.baseURL =vm.$store.getters.baseUrl
		 console.log('config.baseURL', config.baseURL)
		 config['custom'] = {
			 loading:true
		 }
		 config.timeout = 50000 // 请求超时时间，单位ms
        return config
    })
	// 请求拦截
	uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
	    // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
	    config.data = config.data || {}
		 config.baseURL =vm.$store.getters.baseUrl
		// 根据custom参数中配置的是否需要token，添加对应的请求头
		if(typeof vm.$store.getters.token !== 'undefined') {
			// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
			config.header.token = vm.$store.getters.token
		}
		if(typeof vm.$store.getters.originName !== 'undefined') {
			// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
			config.header['Origin-Name'] = vm.$store.getters.originName
		}
		config.header['Referer-Page'] = "";
		config.header['From-Origin'] = "App";
		let pages = getCurrentPages();
		if(pages && pages.length > 0){
			let currentPage = pages[pages.length - 1];
			console.log("currentPage", currentPage.route)
			if(currentPage &&  typeof currentPage['route']!='undefined'){
				config.header['Referer-Page'] = currentPage.route
			}
		}
		
		if(config.custom.loading && !tabList.includes(config.header['Referer-Page'])) {
			uni.showLoading({
				title:'请求中...'
			})
		} 
	    return config 
	}, config => { // 可使用async await 做异步操作
	    return Promise.reject(config)
	})
	
	// 响应拦截
	uni.$u.http.interceptors.response.use((response) => { /* 对响应成功做点什么 可使用async await 做异步操作*/
		uni.hideLoading()
		const data = response.data	
		return data;
	},  (response) => { 
		uni.hideLoading()
		console.log('响应错误:', response)
		// 对响应错误做点什么 （statusCode !== 200）
		const response_data = response.data	
		// console.log('response_data', response_data);
		if(response_data.code == 401) {
			// #ifdef APP-PLUS
			console.log('APP-PLUS')
			uni.reLaunch({
				url: '/subpkg/login/login'
			})
			// #endif
			
			// #ifdef MP-WEIXIN
			vm.$store.dispatch('user/login').then(() => {
				vm.$store.dispatch('professionalConfig/getStuConfig')
			})
			// #endif
			return Promise.reject(response)
		} else {
			if(response?.data?.msg){
				uni.$u.toast(response.data.msg)
			}
			return Promise.reject(response)
		}
		
			return Promise.reject(response)
	
	})
	
}