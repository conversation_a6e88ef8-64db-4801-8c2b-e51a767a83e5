<template>
	<view class="container">
		<view class="container-top">
			<view class="meterial-list-container">
				<view class="m-top">
					<view class="title-name">
						<view class="left">
							<text class="green-block"></text>
							<text>发放中</text>
						</view>
						<view class="content">
							{{ logArr && logArr.add_time ? logArr.add_time : '暂无发放' }}
						</view>
					</view>
					<view v-if="logArr && logArr.id" class="right-icon" @click="toExpress(logArr.id)">
						<text>查看详情</text>
						<view class="icon-center">
							<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
						</view>
					</view>
				</view>

				<view class="item-list">
					<view class="item" v-for="i in nowSend" :key="i.id">
						<image :src="i.image" mode="widthFix">
						</image>
						<text class="title">{{ i.name }}</text>
					</view>
				</view>

			</view>
			<view class="cate">
				<view class="title-name">
					<view class="left">
						<text class="green-block"></text>
						<text>教材分类</text>
					</view>
					<view class="btn">
						全部发放
					</view>
				</view>
				<view class="add-padding">
					<u-tabs :list="list" lineWidth="50" lineHeight="4" lineColor="#01997A" :activeStyle="{
						color: '#01997A',
						fontWeight: 'bold',
						fontSize: '32rpx'
					}" :inactiveStyle="{
						fontSize: '32rpx',
						fontWeight: 'bold',
						color: '#777777',
					}" itemStyle="padding-left: 12px; padding-right: 12px; height: 40px;" @click="change">
					</u-tabs>
				</view>

			</view>
		</view>
		<view class="bottom-content">
			<view class="left">
				<side-bar :list="sideBarList" :currentSel="sel" @changeItem="changeItem" :height="bottomScroolHeight"></side-bar>
			</view>
			<view class="right">
				<scroll-view scroll-y="true" class="practise-container" :style="{ height: bottomScroolHeight }">

					<view class="list">
						<view class="item" v-for="(i, key) in materialList" :key="key">
							<image :src="i.image" mode="widthFix"></image>
							<text class="title">{{ i.name }}</text>
							<text class="send-status" :class="[hadSend.includes(parseInt(i.id)) ? 'send' : '']">
								{{ hadSend.includes(parseInt(i.id)) ? '已发放' : '未发放' }}
							</text>
						</view>
						<view class="empty-container">
							<u-empty v-if="materialList.length == 0" :marginTop="'20rpx'" class="empty" text="暂无资料" :textSize='"24rpx"'></u-empty>
						</view>

					</view>
				</scroll-view>

			</view>
		</view>
	</view>
</template>

<script>
import sideBar from "../../components/Sidebar/Sidebar.vue"
export default {

	data() {
		return {
			bottomHeight: 0,
			bottomScroolHeight: 0,
			subject: '',
			list: [

			],
			sel: 1,
			sideBarList: [
			],
			materialList: [],
			hadSend: [],
			nowSend: [],
			logArr: ''
		};
	},
	onLoad() {
		this.getAllCategory()
		this.getSendRecord()
	},
	methods: {
		toExpress(id) {
			uni.navigateTo({
				url: '/materialpkg/delivery/delivery?id=' + id
			})
		},
		getAllCategory() {
			let that = this;
			that.courseList = [];
			const data = uni.$u.http.get('/task/getUserCourse').then(rest => {
				let res = rest.data.cate_text;

				res.map(item => {
					return that.list.push({ name: item.name })
				})

				this.subject = this.list[0].name;
				this.getSubjectInfo();
			});
		},
		getSendRecord() {
			let that = this;
			that.courseList = [];
			const data = uni.$u.http.get('/course/userSendInfo').then(rest => {
				that.nowSend = rest.data.data;
				that.logArr = rest.data.logArr;


				let hadSendTmp = rest.data.ids;
				that.hadSend = hadSendTmp.map(item => {
					return parseInt(item)
				})

			});
		},
		changeItem(e) {
			this.sel = e.id
			this.getSubjectInfo();
		},
		change(item) {
			this.subject = item.name
			this.getSubjectInfo();
		},
		getSubjectInfo() {
			let that = this
			const data = uni.$u.http.get('/course/getAllInfo', { params: { subject: this.subject, sel: this.sel } }).then(rest => {
				that.materialList = rest.data.data;
				that.sideBarList = rest.data.cateArrText;

			});
		},
	},
	components: {
		sideBar,
	},
	mounted() {
		uni.getSystemInfo({
			success: (res) => {
				uni.createSelectorQuery().select('.container-top').boundingClientRect(data => {
					this.bottomScroolHeight = res.windowHeight - data.height + 'px'
				}).exec()
			}
		});

	}
}
</script>

<style lang="scss" scoped>
.container {
	view {
		box-sizing: border-box;
	}

	.add-padding {
		padding-left: 150rpx;
	}

	.title-name {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding: 30rpx 0;

		.left {
			color: #1F1F27;
			font-size: 30rpx;
			font-weight: bold;

			.green-block {
				display: inline-block;
				width: 14rpx;
				height: 28rpx;
				line-height: 28rpx;
				background: #01997A;
				border-radius: 0rpx 8rpx 0rpx 8rpx;
				margin-right: 6rpx;
			}
		}

		.content {
			margin-left: 24rpx;
			color: #4C5370;
			font-size: 26rpx;
		}
	}

	.meterial-list-container {
		padding: 0 30rpx;
		background-color: #EEFAF6;
		overflow: hidden;

		.m-top {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.right-icon {
				display: flex;
				align-items: center;
				justify-content: space-between;

				text {
					height: 30rpx;
					line-height: 30rpx;
					color: #01997A;
					font-weight: 500;
					font-size: 24rpx;
					color: #01997A;

				}

				.icon-center {

					height: 30rpx;
					width: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.item-list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 28rpx 0;
			background-color: #EEFAF6;
			border-radius: 16rpx;
			flex-wrap: nowrap;
			height: 240rpx;
		}

		.item {
			padding: 20rpx 2rpx;
			margin-bottom: 30rpx;
			background-color: #fff;
			width: 18%;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			border-radius: 10rpx;

			image {
				max-height: 180rpx;
				width: 80%;
			}

			text.title {
				font-size: 24rpx;
				color: #1F1F27;
				margin-top: 16rpx;
				margin-bottom: 6rpx;
				width: 80%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}

	.cate {
		box-shadow: 0rpx -4rpx 4rpx 1rpx rgba(184, 184, 184, 0.16);
		padding: 0 30rpx;

		.title-name {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			padding-bottom: 0;

			.btn {
				width: 140rpx;
				height: 60rpx;
				border-radius: 16rpx;
				border: 3rpx solid #01997A;
				line-height: 60rpx;
				text-align: center;
				color: #01997A;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}

	.bottom-content {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.right {
			flex: 1;
			background-color: #F6F7FB;

			.list {
				display: flex;
				flex-wrap: wrap;
				align-content: flex-start;
				justify-content: flex-start;
				padding: 30rpx 15rpx;
				padding-bottom: 20rpx;

				.item {
					margin-left: 16rpx;
					padding: 20rpx 2rpx;
					margin-bottom: 30rpx;
					background-color: #fff;
					width: 30%;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					flex-direction: column;
					border-radius: 10rpx;
					padding-bottom: 16rpx;

					image {
						width: 80%;
					}

					text.title {
						width: 80%;
						font-size: 20rpx;
						color: #1F1F27;
						margin-top: 16rpx;
						margin-bottom: 6rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						/* 超出部分隐藏 */
						text-overflow: ellipsis;
					}

					text.date {
						color: #AFAFAF;
						font-size: 22rpx;
					}

					text.send-status {
						margin-top: 10rpx;
						width: 60%;
						height: 33rpx;
						background: #CDF3E7;
						border-radius: 14rpx;
						height: 33rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #01997A;
						line-height: 0rpx;
						text-align: center;
						line-height: 28rpx;
					}

					text.send {
						background: #FFB8B8;
						color: #F25353;
					}
				}
			}
		}
	}

	.empty-container {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
</style>