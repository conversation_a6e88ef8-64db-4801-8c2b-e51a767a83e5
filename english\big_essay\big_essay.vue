<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
						<text>耗时00:45</text>
			</view>
		</u-navbar>
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>1</text>
					<text>/20</text>

				</view>
				<view class="opt">
					<view>
						<u-icon name="close-circle" size="38"></u-icon>
						<text>反馈</text>
					</view>
					<view>
						<u-icon name="star" size="38"></u-icon>
						<text>收藏</text>
					</view>
					<view>
						<u-icon name="order" size="38"></u-icon>
						<text>答题卡</text>
					</view>
				</view>

			</view>


			<view class="q-type">
				大作文
			</view>
			<view class="question">
				<text class="question-content"> 
				  Directions:
				      An art exhibition and a robot show are to be held on Sunday, and your friend  <PERSON> asks you which one he should go to. Write him an email to
				     1)make a suggestion, and
				     2)give your reason(s).
				     You should write about 100 words on the  
				</text>
				<view class="img-list">
					<image style="width: 100%;" src="https://cdn.uviewui.com/uview/swiper/1.jpg" mode="widthFix"></image>
					<image style="width: 100%;" src="https://cdn.uviewui.com/uview/swiper/1.jpg" mode="widthFix"></image>
				</view>
			</view>
		</view>
		
		<!-- 填写答案 -->
		<view class="answer">
			<view class="title">
				我的答案
			</view>
			<view class="a-content">
					<u--textarea  height="290rpx" border="none" v-model="answer" placeholder="请输入答案"  maxlength="-1"></u--textarea>
			</view>
			
			<view class="q-upload">
					<u-upload 
					 height="200rpx"
					 width="200rpx"
					 uploadText="上传照片"
						:fileList="fileList1"
						@afterRead="afterRead"
						@delete="deletePic"
						name="1"
						multiple
						:maxCount="10"
					></u-upload>
			</view>
			
		</view>
		<view class="bottom-btn">
			<view class="prev">
				上一题
			</view>
			<view class="next">
				下一题
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding-bottom: 20rpx;
	.content {
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
	
		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
	
			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;
	
					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
	
			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;
	
				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
	
					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
	}
	.question{
		display: flex;
		flex-direction: column;
		align-content: space-between;
		justify-content: center;
		.title{
			width: 100%;
			color: #5A5A5A;
			font-size: 28rpx;
			font-weight: bold;
			margin-top: 16rpx;
		}
		.question-content{
			color: #5A5A5A;
			font-size: 28rpx;
			margin-top: 24rpx;
			line-height: 40rpx;
		}
	}
	.img-list{
		margin-top: 28rpx;
		image{
			width: 100%;
			margin-top: 30rpx;
		}
	}
	.answer{
		margin-top: 32rpx;
		padding: 0 36rpx;
		margin-bottom: 36rpx;
		.title{
			font-size: 24rpx;
			color: #777777;
			font-weight: bold;
		}
		.a-content{
			margin-top: 12rpx;
			margin-bottom: 36rpx;
			border: 1rpx solid #707070;
			margin-top: 12rpx;
			border-radius: 22rpx;
			padding: 10rpx;
		}
		
	}
	
	.bottom-btn{
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		view{
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}
		.prev{
			background: #FFFFFF;
			color: $main-color;
		}
		.next{
			background-color: $main-color;
			color: #fff;
		}
	}
	
}
</style>
