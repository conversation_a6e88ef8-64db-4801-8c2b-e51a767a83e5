<template>
	<view class="container">
		<view class="opt">
			
			<view
			  @click="showAnswer"
			  v-if="currentTypes[currentIndex].type == 3 && isShowTaskAnswer == false"
			>
			  <!-- <div class="submit-any"> -->
				<u-icon name="eye" size="60"></u-icon>
			  <text>提交</text>
			</view>
			
			
			<view @click="openFeedBack">
				<u-icon name="close-circle" :size="size"></u-icon>
				<text>反馈</text>
			</view>
			<view @click="toggleStar">
				<u-icon v-if="isstar" name="star-fill" :size="size" color="#009c7b"></u-icon>
				<u-icon  v-else  name="star":size="size"></u-icon>
				<text>收藏</text>
			</view>
			<view @click="answerSheet">
				<u-icon name="order" :size="size"></u-icon>
				<text>答题卡</text>
			</view>
		</view>
		<u-popup :show="show" @close="close" mode="bottom">
		    <view class="pop-container">
				<view class="title">
					反馈
				</view>
		        <u--textarea v-model="feedbackValue"  height=240  placeholder="请输入内容" ></u--textarea>
				<view class="btn-submit" @click="submit">
					提交
				</view>
		    </view>
		</u-popup>
	</view>
</template>

<script>
	import{mapState} from "vuex"
	import {addFeedBack, starToggle} from "@/api/professional/index.js"
	import { selectTemplateAnswer } from "@/utils/tool.js";
	export default {
		data(){
			return{
				size:48,
				show:false,
				feedbackValue:'',
			}
		},
		props:{
			isstar:{
				type :Boolean,
				default:false
			},
			isDeep:{
				type :Boolean,
				default:false
			}
		},
		methods:{
			
			    showAnswer() {
					
					uni.showModal({
					  title: '提示',
					  content: '查看答案解析',
					  showCancel: true,  // Whether to show the cancel button
					  cancelText: '取消', // Text for cancel button
					  confirmText: '确定', // Text for confirm button
					  success: (res) => {
					    if (res.confirm) {
							//设置进入 解析页面
							this.$store.commit("professionalConfig/setShowAnswer", true);
							//保存当前答案
							this.$parent.$parent.next();
							//重置答题索引
							const index =
							this.$store.state.professionalConfig.prevAllAnswerListCount;
							this.$store.commit("professionalConfig/setCurrentIndex", index);	
							if(this.currentStartPage =='pages/task/task' || this.currentStartPage =='pages/encourage/encourage'){
								//过滤已经答过的题目
								this.$store.commit("professionalConfig/filterCurrentTypes");
							}
							 if (this.prevAllAnswerListCount == this.currentAnswerList.length) {
								this.$store.commit("professionalConfig/setShowAnswer", false);
							    return uni.tip("当前一题未答，无法提交");
							}
					
										
							//设置提交状态
							this.$store.commit("professionalConfig/changeCurrentAnswerStatus", {
							  isSubmit: true,
							  submitTime: Date.now(),
							});
							

							selectTemplateAnswer(this.currentTypes[this.currentIndex], false);
					    } else if (res.cancel) {
					      console.log('用户点击取消');
					    }
					  },
					  fail: (err) => {
					    console.log('调用失败:', err);
					  }
					});
				
			    },
			
			 close() {
			    this.show = false
			},
			openFeedBack(){
				this.show = true
			},
			async submit(){
				if(this.feedbackValue== ""){
					uni.tip("反馈内容不能为空")
					return
				}
				//添加反馈
				const data = {
					qid:this.currentTypes[this.currentIndex].id,
					content:this.feedbackValue
				}
				console.log(data)
				const ret = await addFeedBack(data)
				if(ret !== false){
					uni.tip("添加成功")
					this.show = false
				}
			},
			async toggleStar(){
				let data = {
					qid:this.currentTypes[this.currentIndex].id,
					pqid:this.currentTypes[this.currentIndex].pqid,
				}
				let ret = await starToggle(data);
				//更改状态管理 当前题目对应的star
				this.$store.commit('professionalConfig/changeCurrentTypesByCurrentIndex', {is_star:ret.add})
				this.$emit("update:isstar", ret.add)
			},
			answerSheet(){
				if(!this.isDeep){
					this.$parent.saveCurrentAnswer()
				}else{
					if(typeof this.$parent.$parent.save=='function'){
						this.$parent.$parent.save()
					}else{
						this.$parent.$parent.saveCurrentAnswer()
					}
				}
				uni.redirectTo({
					url:"/subpkg/answer_sheet/answer_sheet"
				})
			}
		},
		computed:{
			...mapState("professionalConfig",
			['currentTypes', 
			"currentIndex",
			"isShowTaskAnswer",
			"currentStartPage",
			"prevAllAnswerListCount",
			"currentAnswerList",
			]),
		}
	}
</script>

<style lang="scss" scoped>
	
	.container{
		.opt {
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			view {
				margin-left: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			
				text {
					font-size: 18rpx;
					color: #777;
				}
			}
		}
	}
	
	.submit-any {
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  flex-direction: column;
	  position: fixed;
	  bottom: 240rpx;
	  right: 10px;
	  color: #01997a;
	  height: 70px;
	  width: 70px;
	  border-radius: 50%;
	  font-size: 14px;
	  z-index:100;
	}
	.pop-container{
		width:750rpx;
		padding: 20rpx;
		padding-bottom: 40rpx;
		.title{
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 30rpx;
		}
		.btn-submit{
			margin-top: 20rpx;
			background-color: $main-color;
			color: #fff;
			border-radius: 20rpx;
			height: 80rpx;
			margin-top: 30rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 24rpx;
		}

	}
</style>