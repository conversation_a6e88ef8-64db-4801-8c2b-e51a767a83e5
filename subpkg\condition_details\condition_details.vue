<template>
	<view class="container">
		<view class="topic-list" v-if="articleData && articleData.user">
			<!-- 	作者信息 -->
			<view class="auth-info">
				<image class="avatar" :src="articleData.user.avatar"></image>
				<view class="info-detail">
					<view class="auth-name">
						<text>{{ articleData.user.nickname }}</text>
						<text v-if="articleData.is_officle == 1">官方号</text>
					</view>
					<text class="publish-date">{{ articleData.createtime_text }}</text>

				</view>
			</view>
			<!-- 主题 -->
			<view class="subject">
				{{ articleData.title }}
			</view>
			<!-- 内容概述 -->
			<view class="content-overview">
				<!-- <text>如果你想要考研但不知道如何进行复习规划，如果你对专业还院校还了解甚少，你想拥有一个良好的学习环境助你专心去备考，那就来研趣25考研全年集训营吧~可提前入营哟~</text> -->
				<text>{{ articleData.content }} </text>
				<view v-if="articleData.topic_text && articleData.topic_text.length > 0">
					<text class="sel-subject-title" v-for="(aitem, aindex) in articleData.topic_text" :key="aindex"> #{{ aitem }}</text>
				</view>

			</view>
			<!-- 缩略图 -->
			<view class="thumb-list" v-if="articleData.image_arr_text">
				<template v-if="articleData.image_arr_text != null && articleData.image_arr_text.length < 2">
					<image @click="previewImage(imgkey)" v-for="(imageItem, imgkey) in articleData.image_arr_text" class="image_one" :key="imgkey" mode="aspectFill" :src="imageItem"></image>
				</template>
				<template v-else-if="articleData.image_arr_text.length == 2">
					<image @click="previewImage(imgkey)" v-for="(imageItem, imgkey) in articleData.image_arr_text" class="image_two" :key="imgkey" mode="aspectFill" :src="imageItem"></image>
				</template>
				<template v-else-if="articleData.image_arr_text.length > 2 && articleData.image_arr_text.length == 4">
					<image @click="previewImage(imgkey)" v-for="(imageItem, imgkey) in articleData.image_arr_text" class="image_third" :key="imgkey" mode="aspectFill" :src="imageItem"></image>
				</template>
				<template v-else>
					<image @click="previewImage(imgkey)" v-for="(imageItem, imgkey) in articleData.image_arr_text" class="image_four" :key="imgkey" mode="aspectFill" :src="imageItem"></image>
				</template>
			</view>
			<view class="bottom-opt">
				<!-- 阅读数 -->
				<view class="read-num">
					<image src="../../static/img/view.png" mode="widthFix"></image>
					<text>{{ articleData.see_num }}</text>
				</view>
				<!-- 分享 -->
				<view class="share">
					<button @click="appShare(articleData)" :data-item="articleData" style="border:none;display: flex;align-items: center;" plain>
						<image src="../../static/img/share.png" mode="widthFix"></image>
						<text>分享</text>
					</button>
				</view>
				<!-- 评论 -->
				<view class="comment" @click="commentArticle">
					<image src="../../static/img/msg.png" mode="widthFix"></image>
					<text>{{ countComment }}</text>
				</view>
				<!-- 点赞 -->
				<view class="praise" @click="userZan">
					<image :src="praise" mode="widthFix"></image>
					<text>{{ articleData.like_num }}</text>
				</view>
			</view>
		</view>
		<view v-else class="loading">加载中...</view>
		<view class="line"></view>
		<!-- 评论区 -->
		<view class="commentBox">
			<!-- 评论输入框 -->
			<view class="inputBox" v-if="articleData && articleData.user">
				<image class="img" :src="articleData.user.avatar" mode="aspectFit"></image>
				<input type="text" :placeholder="placeholder" v-model="reply_content" :focus="flag" />
				<view class="comfirm" @click="confirm">确认</view>
			</view>
			<!-- 评论详情 -->
			<view class="commentDetails" v-for="(artItem, artIndex) in commentList" :key="artIndex">
				<view class="person">
					<view class="left">
						<image :src="artItem.user.avatar" mode="widthFix"></image>
						<text>{{ artItem.user.nickname }}</text>
					</view>
					<view class="right">{{ artItem.createtime_text }}</view>
				</view>
				<view class="contentBox">
					<view class="content">
						{{ artItem.content }}
					</view>
					<view class="details" v-if="artItem.child && artItem.child.length > 0">
						<view class="list" v-for="(childItem, childIndex) in artItem.child" :key="childIndex" v-show="!clickArr.includes(artIndex) && childIndex > 2 ? false : true" :data-type="1" @click="e => replyContent(e, childItem)">
							<text class="name">{{ childItem.user.nickname }}：</text>
							<text>{{ childItem.reply_tip }}
								<text class="name" v-if="childItem.reply && childItem.reply.nickname"> {{ childItem.reply.nickname }} ：</text>
								<text>{{ childItem.content }}</text>
							</text>
						</view>
						<view class="all" v-if="artItem.child.length > 3">
							<template v-if="clickArr.includes(artIndex)">
								<view @click="closeAll(artIndex)">收起</view>
								<u-icon name="arrow-up" color="#01997A" size="28"></u-icon>
							</template>
							<template v-else>
								<view @click="showAll(artIndex)">查看全部回复</view>
								<u-icon name="arrow-right" color="#01997A" size="28"></u-icon>
							</template>
						</view>
					</view>
				</view>
			</view>
			<view class="commentDetails" v-if="false">
				<view class="person">
					<view class="left">
						<image src="../../static/png/wa.png" mode=""></image>
						<text>小鲤鱼老师</text>
					</view>
					<view class="right">2024-1-19 22:00</view>
				</view>
				<view class="contentBox">
					<view class="content">
						如果你想要考研但不知道如何进行复习规划，如果你对专业还院校还了解甚少，你想拥有一个良好的学习环境助你专心去备考，那就来研趣25考研全年集训营吧~可提前入营哟~
					</view>
					<view class="details">
						<view class="list">
							<text class="name">小鲤鱼老师：</text>
							<text>快来快来快来快来快来快来快来快来快来快来快来快来快来快来快来快来</text>
						</view>
						<view class="list">
							<text class="name">小鲤鱼小鲤鱼：</text>
							<text>一起努力！</text>
						</view>
						<view class="all"></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			praise: '../../static/img/praise.png',
			placeholder: '说点什么',
			clickArr: [],
			showFlag: false,
			articleData: {
				user: { avatar: '', nickname: '' },
				image_arr_text: [],
				topic_text: [],
				article_about: { like: false },
				like_num: 0,
				see_num: 0,
				title: '',
				content: ''
			},
			value: "",
			disflag: false,
			id: '',
			commentList: [],
			flag: false,
			formData: {},
			reply_content: '',
			countComment: 0,
			hadLike: false,
			article_id: 0
		}
	},
	onLoad(option) {
		if (option.id != undefined) {
			this.id = option.id
		}

		this.getArticleDetail();
		this.userSee();
	},
	onShareAppMessage(res) {
		console.log(res.target)
		if (res.from === 'button') {// 来自页面内分享按钮
			let item = res.target.dataset.item;
			return {
				title: item.title,
				path: '/subpkg/condition_details/condition_details?id=' + item.id,
				imageUrl: item.image_arr_text && item.image_arr_text.length > 0 ? item.image_arr_text[0] : '',
				desc: '好老师,好服务',
			}
		} else {
			return {
				title: '研趣智能学习平台',
				path: '/pages/index/index'
			}
		}
	},
	methods: {
		appShare(item) {
			uni.showLoading({
				title: '分享中...',
				mask: true
			});
			uni.share({
				provider: 'weixin',
				scene: "WXSceneSession",
				type: 5,
				imageUrl: "/static/png/share.png",
				title: item.title,
				miniProgram: {
					id: 'gh_bfc6113eb384',
					path: '/subpkg/condition_details/condition_details?id=' + item.id,
					type: 0,
					webUrl: 'https://study.yanqux.com/index/course/index.html'
				},
				fail: (err) => {
					uni.hideLoading();
					console.log(err)
				},
				success: ret => {
					console.log(JSON.stringify(ret));
					uni.hideLoading();
				}
			});



		},
		previewImage(index) {
			let that = this;
			// 调用uni.previewImage API进行图片预览
			console.log(1222);
			if (that.articleData.image_arr_text && that.articleData.image_arr_text.length > 0) {
				uni.previewImage({
					current: index, // 当前显示图片索引
					urls: that.articleData.image_arr_text // 需要预览的图片 URL 列表
				});
			}
		},
		confirm() {
			let that = this
			if (!this.articleData || !this.articleData.id) return;

			this.formData = {
				article_id: this.articleData.id,
				is_reply: 0,
			}
			this.formData.content = this.reply_content
			uni.$u.http.post('/article/commentArticle', this.formData).then(ret => {
				if (ret.code == 1) {
					that.getCommentList()
					uni.tip('评论成功');
					that.flag = false
					that.reply_content = ''
					that.placeholder = '说点什么';
				} else {
					uni.tip(ret.msg);
				}
			})
		},
		showAll(artIndex) {
			this.clickArr.push(artIndex)
		},
		closeAll(artIndex) {
			this.clickArr = this.clickArr.filter(item => item != artIndex)
		},
		commentArticle() {
			if (!this.articleData || !this.articleData.id) return;

			this.flag = true
			this.formData = {
				article_id: this.articleData.id,
				is_reply: 0,
			}
		},
		replyContent(e, replay_id) {
			this.flag = true
			let type = e.currentTarget.dataset.type;

			if (replay_id && replay_id.user && replay_id.user.nickname) {
				this.placeholder = '回复' + replay_id.user.nickname
				this.formData = {
					article_id: replay_id.article_id,
					is_reply: type,
				}
				if (type == 1) {
					this.formData.reply_user = replay_id.user_id
					this.formData.pid = replay_id.id
				}
			}
		},
		async getArticleDetail() {
			let that = this
			try {
				let ret = await uni.$u.http.get('/article/getArticleDetail?id=' + that.id)
				if (ret && ret.data && ret.data.data) {
					that.articleData = ret.data.data
					if (that.articleData.article_about && that.articleData.article_about.like) {
						that.hadLike = true;
						that.praise = '../../static/img/praise-color.png'
					}
					that.getCommentList()
				}
			} catch (error) {
				console.error('获取文章详情失败', error)
			}
		},
		getCommentList() {
			let that = this
			uni.$u.http.get('/article/getCommentList?article_id=' + that.id).then(ret => {
				console.log(ret)
				if (ret && ret.data) {
					that.commentList = ret.data.data || [];
					that.countComment = ret.data.count || 0;
				}
			}).catch(err => {
				console.error('获取评论列表失败', err)
				that.commentList = [];
				that.countComment = 0;
			})
		},
		userSee() {
			if (!this.id) return;

			uni.$u.http.post('/article/likeAndSee', { type: 0, article_id: this.id }).then(ret => {
				if (ret.code == 1) {
					// 处理成功
				}
			})
		},
		userZan() {
			let that = this;
			if (!that.articleData || !that.articleData.id) return;

			if (that.hadLike) {
				uni.$u.http.post('/article/cancelLike', { type: 1, article_id: that.articleData.id }).then(ret => {
					if (ret.code == 1) {
						that.hadLike = false;
						that.praise = '../../static/img/praise.png'
						that.getArticleDetail()
					}
				})
			} else {
				uni.$u.http.post('/article/likeAndSee', { type: 1, article_id: that.articleData.id }).then(ret => {
					if (ret.code == 1) {
						uni.tip('点赞成功');
						that.getArticleDetail()
					}
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	.loading {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 28rpx;
	}

	.topic-list {
		margin-top: 26rpx;
		padding: 0 30rpx;
		box-sizing: border-box;

		.auth-info {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			margin-left: 16rpx;

			image {
				width: 80rpx;
				height: 80rpx;
				margin-right: 14rpx;
				border-radius: 35rpx;
			}

			.auth-name {
				text {
					&:first-child {
						color: #060606;
						font-size: 26rpx;
						margin-right: 8rpx;
					}

					&:last-child {
						width: 92rpx;
						height: 46rpx;
						line-height: 46rpx;
						border-radius: 20rpx;
						font-size: 24rpx;
						color: #10D69A;
						background-color: #EEFAF6;
					}
				}
			}

			.publish-date {
				color: #818181;
				font-size: 24rpx;
			}
		}

		.subject {
			margin-top: 4rpx;
			font-weight: bold;
			color: #060606;
			font-size: 28rpx;
			margin-left: 16rpx;
		}

		.thumb-list {
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
			align-content: center;

			.image_one {
				margin-left: 10rpx;
				margin-top: 10rpx;
				width: 51%;
				border-radius: 16rpx;
			}

			.image_two {
				margin-left: 10rpx;
				margin-top: 10rpx;
				width: 40%;
				border-radius: 16rpx;
			}

			.image_third {
				margin-left: 10rpx;
				margin-top: 10rpx;
				width: 40%;
				border-radius: 16rpx;
				max-height: 260rpx;
			}

			.image_four {
				margin-left: 10rpx;
				margin-top: 10rpx;
				width: 30%;
				border-radius: 16rpx;
				max-height: 260rpx;
			}
		}

		.content-overview {
			margin-top: 10rpx;
			color: #060606;
			font-size: 26rpx;
			line-height: 1.5;
			margin-left: 16rpx;

			.sel-subject-title {
				font-size: 26rpx;
				color: $main-color;
			}
		}

		.bottom-opt {
			padding: 16rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top: 1rpx solid #ebebeb;

			view {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 50rpx;
				}

				text {
					color: #C2C2C2;
					margin-right: 8rpx;
					font-size: 24rpx;
				}
			}
		}
	}

	.line {
		width: 750rpx;
		height: 24rpx;
		background: #F5F5F5;
	}

	.commentBox {
		padding: 30rpx 30rpx 120rpx;
		box-sizing: border-box;

		.inputBox {
			display: flex;
			align-items: center;
			margin-bottom: 15rpx;

			.img {
				flex: 1;
				height: 60rpx;
				margin-right: 10rpx;
			}

			input {
				flex: 4;
				height: 58rpx;
				padding-left: 30rpx;
				font-size: 24rpx;
				box-sizing: border-box;
				background: #F6F7FB;
				border-radius: 28rpx;
			}

			.comfirm {
				flex: 1;
				font-size: 22rpx;
				margin-left: 30rpx;
				background: #01997A;
				color: #fff;
				text-align: center;
				border-radius: 10rpx;
				height: 46rpx;
				line-height: 46rpx;
			}
		}

		.commentDetails {
			padding-top: 30rpx;
			box-sizing: border-box;

			.person {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.left {
					display: flex;
					align-items: center;

					image {
						width: 60rpx;
						height: 60rpx;
						border-radius: 50%;
					}

					text {
						margin-left: 15rpx;
						font-weight: bold;
						font-size: 26rpx;
						color: #060606;
					}
				}

				.right {
					font-weight: 500;
					font-size: 24rpx;
					color: #818181;
				}
			}

			.contentBox {
				width: 616rpx;
				margin-left: 74rpx;
				padding-bottom: 38rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #ebebeb;

				.content {
					line-height: 1.5;
					font-weight: 500;
					font-size: 26rpx;
					color: #060606;
				}

				.details {
					width: 100%;
					margin-top: 15rpx;
					padding: 25rpx 20rpx;
					box-sizing: border-box;
					background: #F6F7FB;
					border-radius: 22rpx;

					.list {
						font-size: 26rpx;
						color: #0A0A0A;
						margin-bottom: 20rpx;

						.name {
							font-weight: bold;
							color: #01997A;
						}
					}

					.all {
						display: flex;
						align-items: center;
						font-size: 26rpx;
						color: #01997A;
					}
				}
			}
		}
	}
}
</style>