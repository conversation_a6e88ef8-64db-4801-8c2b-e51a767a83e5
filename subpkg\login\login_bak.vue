<template>
    <view class="container">
        <!-- 主登录页面，在一键登录拉起前显示 -->
        <view class="main-content" v-if="!showBackup">
            <image class="logo" src="/static/app_logo.png" mode="aspectFit"></image>
            <text class="title">研趣在线</text>
            <view class="login-options">
                <button class="login-btn" @click="showLoginPage">本机号码一键登录</button>
                <view class="code-login" @click="toCodeLogin">手机验证码登录</view>
            </view>
        </view>

        <!-- 仅作为后备的简单界面，通常不会被看到 -->
        <view class="backup-content" v-if="showBackup">
            <image class="logo" src="/static/app_logo.png" mode="aspectFit"></image>
            <text class="title">研趣在线</text>
            <button class="retry-btn" @click="showLoginPage">重试登录</button>
            <view class="code-login" @click="toCodeLogin">手机验证码登录</view>
        </view>
    </view>
</template>

<script>
import { mapMutations } from 'vuex'
import { baseUrl } from "@/utils/setting.js"  // 导入 baseUrl

export default {
    data() {
        return {
            showBackup: false, // 是否显示备用界面
            authCancelled: false // 标记用户是否取消过授权
        }
    },
    onLoad() {
        // 页面加载立即拉起登录页
        setTimeout(() => {
            // 稍微延迟拉起登录页面，确保页面已完全加载
            this.showLoginPage();
        }, 200);
    },
    methods: {
        ...mapMutations('user', ['setUser']),

        // 显示一键登录页面
        showLoginPage() {
            try {
                // 预登录完成后立即拉起授权页
                uni.preLogin({
                    provider: 'univerify',
                    success: () => {
                        console.log('预登录成功');
                        // 立即拉起授权界面
                        this.launchAuthPage();
                    },
                    fail: (err) => {
                        console.log('预登录失败:', err);
                        // 预登录失败直接跳转到验证码登录
                        this.redirectToCodeLogin();
                    }
                });
            } catch (error) {
                console.error('预登录异常:', error);
                // 发生异常也直接跳转到验证码登录
                this.redirectToCodeLogin();
            }
        },

        // 拉起授权页面
        launchAuthPage() {
            uni.login({
                provider: 'univerify',
                univerifyStyle: {
                    fullScreen: true, // 全屏模式
                    backgroundColor: "#ffffff",
                    // 协议勾选框相关
                    checkBoxState: false, // 默认不选中
                    icon: {
                        "path": "/static/app_logo.png", // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
                        "width": "60px",  //图标宽度 默认值：60px
                        "height": "60px"   //图标高度 默认值：60px
                    },
                    // 登录按钮相关
                    authButton: {
                        normalColor: "#009c7b",
                        highlightColor: "#009c7b",
                        disabledColor: "#CCCCCC",
                        textColor: "#ffffff",
                        title: "本机号码一键登录"
                    },
                    // 隐私协议相关
                    privacyTerms: {
                        defaultCheckBoxState: false, // 默认不勾选
                        checkBoxSize: 13, // 勾选框大小
                        textColor: "#999999", // 文本颜色
                        termsColor: "#009c7b", // 协议文字颜色
                        prefix: "我已阅读并同意", // 协议前缀
                        suffix: "并使用本机号码登录", // 协议后缀
                        privacyItems: [ // 自定义协议项
                            {
                                url: "http://1vg96zb02461.vicp.fun/index.php/index/index/userAgreement", // 用户协议地址
                                title: "《用户协议》"
                            },
                            {
                                url: "http://1vg96zb02461.vicp.fun/index.php/index/index/privacyPolicy", // 隐私政策地址
                                title: "《隐私政策》"
                            }
                        ]
                    },
                    // 标语相关
                    slogan: {
                        textColor: "#333333"
                    }
                },
                success: (res) => {
                    console.log('登录成功:', res);
                    if (res.authResult && res.authResult.access_token && res.authResult.openid) {
                        this.getPhoneNumberWithToken(res.authResult.access_token, res.authResult.openid);
                    }
                },
                fail: (err) => {
                    console.error('登录失败:', err);
                    // 登录失败直接跳转到验证码登录
                    this.redirectToCodeLogin();
                }
            });
        },

        // 使用token获取手机号
        getPhoneNumberWithToken(token, openid) {
            uni.showLoading({
                title: '登录中...'
            });

            uniCloud.callFunction({
                name: 'getPhoneNumber',
                data: {
                    access_token: token,
                    openid: openid
                },
                success: (phoneRes) => {
                    console.log("phoneRes", phoneRes);
                    if (phoneRes.result && phoneRes.result.code === 0) {
                        const phoneNumber = phoneRes.result.phoneNumber;
                        this.loginWithPhone(phoneNumber);
                    } else {
                        uni.hideLoading();
                        // 获取手机号失败直接跳转到验证码登录
                        this.redirectToCodeLogin('获取手机号失败');
                    }
                },
                fail: (err) => {
                    uni.hideLoading();
                    console.error('获取手机号失败:', err);
                    // 获取手机号失败直接跳转到验证码登录
                    this.redirectToCodeLogin('登录失败，请重试');
                }
            });
        },

        // 跳转到验证码登录
        toCodeLogin() {
            uni.navigateTo({
                url: '/subpkg/code_login/code_login'
            });
        },

        // 重定向到验证码登录（带提示）
        redirectToCodeLogin(message) {
            if (message) {
                uni.showToast({
                    title: message,
                    icon: 'none',
                    duration: 1500
                });

                // 稍微延迟跳转，让用户能看到提示
                setTimeout(() => {
                    uni.navigateTo({
                        url: '/subpkg/code_login/code_login'
                    });
                }, 1500);
            } else {
                // 直接跳转
                uni.navigateTo({
                    url: '/subpkg/code_login/code_login'
                });
            }
        },

        // 调用后端登录接口
        loginWithPhone(phoneNumber) {
            uni.showLoading({
                title: '登录中...'
            });

            // 调用后端一键登录接口
            uni.$u.http.post('/sendsms/phoneLogin', {
                phone: phoneNumber
            }).then(res => {
                uni.hideLoading();
                if (res.code === 1) {
                    const userData = res.data;

                    // 保存用户信息到 Vuex
                    this.setUser(userData);

                    // 保存 token 到本地存储
                    uni.setStorageSync('token', userData.token);

                    // 也保存整个userInfo对象以便其他地方使用
                    uni.setStorageSync('userInfo', JSON.stringify(userData.userInfo));

                    uni.showToast({
                        title: '登录成功',
                        icon: 'success'
                    });

                    // 延迟返回，让用户看到提示
                    setTimeout(() => {
                        console.log("登陆成功");
                        uni.switchTab({
                            url: '/pages/index/index'
                        });
                    }, 1500);
                } else {
                    // 登录失败直接跳转到验证码登录
                    this.redirectToCodeLogin(res.msg || '登录失败');
                }
            }).catch(err => {
                uni.hideLoading();
                console.error('登录接口异常:', err);
                // 登录接口异常直接跳转到验证码登录
                this.redirectToCodeLogin('网络异常，请重试');
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
}

.main-content,
.backup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .logo {
        width: 150rpx;
        height: 150rpx;
        border-radius: 20rpx;
        margin-bottom: 30rpx;
    }

    .title {
        font-size: 40rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 80rpx;
    }
}

.login-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.login-btn,
.retry-btn {
    width: 400rpx;
    height: 90rpx;
    background-color: #1dd1a1;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
}

.code-login {
    font-size: 28rpx;
    color: #666;
    margin-top: 30rpx;
}
</style>