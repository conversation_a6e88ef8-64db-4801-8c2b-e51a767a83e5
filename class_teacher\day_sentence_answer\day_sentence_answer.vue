<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left" @click="goBack()">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
					<text>耗时{{useTime}}</text>
			</view>
		</u-navbar>
		
		
		<view class="content">
			<view class="q-type">
				每日一句
			</view>
			
			<view class="question">
				<text class="title">一、请根据下列句子找出子难的词并写出汉语意思</text>
				<text class="title">二、写成下列句子结构</text>
				<text class="title">三、翻译下列句子</text>
				<view class="question-content">
					<u-parse :content="content"></u-parse>
				</view>
			</view>
		</view>
		
		<view class="answer">
			<view class="title">
				单词学习
			</view>
			<view class="a-content">
					<u--textarea  autoHeight disabled   v-model="answerData.answerWord" placeholder="请输入答案"  maxlength="-1"></u--textarea>
			</view>
			<view class="img-list" v-if="answerData.imageWord">
				<image style="width: 100%;" :src="item"  mode="widthFix" v-for="item in answerData.imageWord.split(',')"></image>
			</view>
		
		</view>
		
		
		<view class="answer">
			<view class="title">
				句子结构分析
			</view>
			<view class="a-content">
					<u--textarea  autoHeight disabled v-model="answerData.answerSemt" placeholder="请输入答案"  maxlength="-1"></u--textarea>
			</view>
			
			<view class="img-list" v-if="answerData.imageSemt">
				<image style="width: 100%;" :src="item"  mode="widthFix" v-for="item in answerData.imageSemt.split(',')"></image>
			</view>
			
		</view>
		
		
		<view class="answer">
			<view class="title">
				翻译
			</view>
			<view class="a-content">
					<u--textarea  autoHeight disabled v-model="answerData.answerTran" placeholder="请输入答案"  maxlength="-1"></u--textarea>
			</view>
			<view class="img-list" v-if="answerData.imageTran">
				<image style="width: 100%;" :src="item"  mode="widthFix" v-for="item in answerData.imageTran.split(',')"></image>
			</view>
			
			<view class="analysis">
				<view class="title">
					<user-title :title='"正确答案"' :bgcolor="'#fff'"></user-title>
					<view class="correct-answer">
						
						<u-parse :content="correctContent"></u-parse>
						
					</view>
				</view>
			
			</view>
			
		</view>
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	import userTitle from "../../components/user_title/user_title.vue"
	export default {
		data() {
			return {
				taskId:'',
				wordData:[],
				fileList:[],
				fileList1:[],
				fileList2:[],
				answer:'',
				content:'',
				correctContent:'',
				answerTran:'',
				answerWord:'',
				answerSemt:'',
				answerData:{},
				useTime:''
			};
		},
		methods:{
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getSentence',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data;
					that.content = that.wordData.sentence
					that.correctContent = that.wordData.sentence_explain
					
					that.answerData = that.wordData.my_answer
					let tmp_min = parseInt(that.answerData.useTime/60);
					let tmp_second = that.answerData.useTime%60;
					that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
				})
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			}
		},
		onLoad(options) {
			this.taskId = options.task_id
			this.getWordDetail();
		},	
		
		
		components:{
			userTitle
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background-color: $container-bg-color;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
			}
		}
		.q-text{
			
			line-height: 50rpx;
			color: #5A5A5A;
			font-size: 28rpx;
		}
		.q-title{
			margin-top: 36rpx;
			margin-bottom: 50rpx;
		}
		.q-sel-list{
			
			.q-item{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;
				text{
					width: 590rpx;
				}
				.q-check-normal{
					width: 70rpx;
					height: 70rpx;
					border: 1rpx solid #AFAFAF;
					color: #5A5A5A;
					background-color: #fff;
					border-radius: 16rpx;
					line-height: 70rpx;
					text-align: center;
				}
				.q-check-sel{			
					background: #01997A;
					border: 0;
					color: #fff;
					
				}
			}
		}
	
	}
	.answer{
		background-color: #fff;
		padding: 37rpx 36rpx;
		margin-bottom: 36rpx;
		.correct-answer{
			line-height: 48rpx;
			color: FF5A5A5A;
			font-size: 28rpx;;
		}
		.title{
			color: 28rpx;
			color: #777777;
			font-weight: bold;
		}
		.a-content{
			margin-top: 12rpx;
			margin-bottom: 36rpx;
		}
		.img-list{
			margin-top: 28rpx;
			image{
				width: 100%;
				margin-top: 30rpx;
			}
		}
		.bottom-btn{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 56rpx;
			view{
				width: 170rpx;
				height: 70rpx;
				border-radius: 16rpx;
				border: 1rpx solid #01997A;
				line-height: 70rpx;
				text-align: center;
				font-size: 28rpx;
			}
			.prev{
				background: #FFFFFF;
				color: $main-color;
			}
			.next{
				background-color: $main-color;
				color: #fff;
			}
		}
	}

}
</style>
