<template>
	<view class="container">
		<view class="head">
			<view class="avatar">
				<image :src="userDetail.avatar" mode="widthFix"></image>
			</view>
			<view class="info">
				<text>{{userDetail.nickname}}</text>
				<text>恭喜你坚持听课笔记打卡{{total}}天!</text>
			</view>
			<view class="opt" @click="toAddNote">
				添加笔记
			</view>
		</view>

		<view class="all-course-h1">
			<view class="green-block-big">

			</view>
			<text>所有笔记</text>
		</view>
		
		<view class="note-list">
			<view class="item" v-for="(i,k) in list" :key="i">
				<text class="num">第{{total - k }}天</text>
				<text class="date">{{i.createtime | happenTimeFun}}</text>
				<view class="show" @click="detail(i.id,total-k )">
					查看
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				total:0,
				list:[],
				page:1,
				userDetail:''
			};
		},
		onShow(){
			this.list =  [];
			this.page  = 1;
			this.getUserInfo();
			this.getNoteList();
			
		},
		filters : {
			happenTimeFun(num){//时间戳数据处理
				let date = new Date(num*1000);
				 //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;//月补0
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;//天补0
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;//小时补0
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;//分钟补0
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;//秒补0
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m;
			},
		},
		methods:{
			getUserInfo(){
				let that = this;
				uni.$u.http.get('/mini_user/getUserInfo').then(rest=>{
					that.userDetail  = rest.data;				
				});
			},
			getNoteList() {
				let that = this
				console.log(that.page)
				let  data  = uni.$u.http.post('/student/getNoteList',{page:that.page}).then(res=>{
					if(res.code == 1) {		
						that.total = res.data.data.total;
						that.list = [...that.list, ...res.data.data.rows]
					} else {
						
					}
				});
			},		
			
			toAddNote(){
				if(this.userDetail.is_student != 1) {
					uni.tip('学习笔记权限暂未对所有人开放');
					return false;
				}
				
				uni.navigateTo({
					url:"/subpkg/addnote/addnote"
				})
			},
			detail(i,num){
				uni.navigateTo({
					url:"/subpkg/nodedetail/nodedetail?id="+i+'&num='+num
				})
			}
		},
		onReachBottom() {
			if (this.list.length >= this.total) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			}
			this.page += 1
			this.getNoteList()
		},
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: $container-bg-color;
	}

	.container {
		padding: 0 30rpx;
		padding-top: 22rpx;
		padding-bottom: 30rpx;
		background-color: $container-bg-color;
		height: 100vh;

		.head {
			background-color: #fff;
			padding: 22rpx 26rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #fff;
			border-radius: 20rpx;

			.avatar {
				width: 100rpx;
				height: 100rpx;
				background: #F2F2F2;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 94rpx;
					// height: 66rpx;
					border-radius: 50rpx;

				}
			}

			.info {
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				text {
					&:first-child {
						color: #201E2E;
						font-size: 32rpx;
						font-weight: bold;
						width: 100%;
					}

					&:last-child {
						color: #6D6D6D;
						font-size: 24rpx;
					}
				}
			}

			.opt {
				border: 1px solid #01997A;
				border-radius: 20rpx;
				color: #01997A;
				text-align: center;
				padding: 10rpx;
				font-size: 28rpx;
				font-weight: bold;
			}
		}

		.all-course-h1 {
			background-color: #F6F7FB;
			padding: 32rpx 0;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			.green-block-big {

				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;

			}

			text {
				padding-left: 10rpx;
				width: 128rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}
		
		.note-list{
			background-color: #fff;
			border-radius: 20rpx;
			padding: 0 30rpx;
			padding-bottom: 50rpx;
			
			.item{
				padding:30rpx 0;
				border-bottom: 1rpx solid #D6D6D6;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.num{
					color: #201E2E;
					font-size: 30rpx;
					font-weight: bold;
				}
				.date{
					font-size: 24rpx;
					color: #6D6D6D;
					
				}
				.show{
					background-color: #01997A;
					border-radius: 14rpx;
					text-align: center;
					height: 38rpx;
					line-height: 38rpx;
					font-size: 26rpx;
					width: 86rpx;
					color: #fff;
				}
			}
		}
	}
</style>