<template>
	<view class="container">

		<view class="content">
			<question-content></question-content>
			<view class="q-sel-list">
				<view class="q-item" v-for="(value, key) in question.options" :key="key">
					<view class="q-check-normal" :class='{ "q-check-sel": answer.includes(key) }'>
						{{ key }}
					</view>
					<text class="q-text">{{ value }}</text>
				</view>
			</view>
		</view>
		<!-- 标题 -->
		<view class="q-answer">
			<view class="title">
				<user-title :title='"参考答案"' :bgcolor="'#fff'"></user-title>
			</view>

			<view class="q-answer-info">
				<text>正确答案:</text>
				<text>{{ currentType.answer }}</text>
				<text>我的答案:</text>
				<text>{{ answer }}</text>
			</view>
			<question-board :item='statisticsInfo'></question-board>

		</view>

		<!-- 解析 -->
		<view class="analysis">
			<view class="title">
				<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
			</view>
			<view class="answer-info">
				<rich-text :nodes="currentType.explain"></rich-text>
			</view>
		</view>

		<!-- 知识点 -->
		<view class="knowledge-points" v-if="currentType.knowledgeInfo">
			<view class="title">
				<user-title :title='"知识点"' :bgcolor="'#fff'"></user-title>
			</view>

			<view class="point">
				<view class="point-tag" v-for="(item, index) in currentType.knowledgeInfo" :key="index">
					{{ item.knowledge_name }}
				</view>
			</view>
		</view>


		<!-- 补漏 -->
		<view class="traps" v-if="currentType.knowledgeInfo">
			<view class="title">
				<user-title :title='"精准补漏"' :bgcolor="'#fff'"></user-title>
			</view>
			<enter-course :list="currentType.knowledgeInfo"></enter-course>
		</view>

		<view class="knowledge-origin" v-if="currentType.origin">
			<view class="title">
				<user-title :title='"来源"' :bgcolor="'#fff'"></user-title>
			</view>
			<view class="knowledge-origin-title">
				{{ currentType.origin }}
			</view>


		</view>
		<bottom-btn @prev="answerPrev" @next="answerNext"></bottom-btn>
	</view>
</template>

<script>
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
import enterCourse from "../../components/enter_course/enter_course.vue"
import questionBoard from "../../components/question_board/question_board.vue"
import userTitle from "@/components/user_title/user_title.vue"
import { selectTemplate, selectTemplateAnswer } from "@/utils/tool.js"
import bottomBtn from "@/components/bottomBtn/bottomBtn.vue"
import QuestionContent from "../component/content/content.vue"
export default {
	data() {
		return {
			safeTopHeight: 0,
			answer: "",
			question: "",//具体问题
			questionOption: {},//选项内容
		};
	},
	mixins: [mixins],
	mounted() {
		this.safeTopHeight = uni.$safeTopHeight
	},
	onLoad(option) {
		const {
			id
		} = option
		this.id = id
	},
	onBackPress(e) {
		uni.showModal({
			title: "提示",
			content: "返回上级页面",
			success: (res) => {
				if (res.confirm) {
					this.$store.commit("professionalConfig/enterCurrentStartPage")
				}
			}
		})

		return false
	},
	components: {
		star,
		enterCourse,
		questionBoard,
		userTitle,
		bottomBtn,
		QuestionContent
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: $container-bg-color;
	padding-bottom: 120rpx;

	.center {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}

	.content {
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;

		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;

					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}

		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}

		.q-text {

			line-height: 50rpx;
			color: #5A5A5A;
			font-size: 28rpx;
		}

		.q-title {
			margin-top: 36rpx;
			margin-bottom: 50rpx;
		}

		.q-sel-list {

			.q-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;

				text {
					width: 590rpx;
				}

				.q-check-normal {
					width: 70rpx;
					height: 70rpx;
					border: 1rpx solid #AFAFAF;
					color: #5A5A5A;
					background-color: #fff;
					border-radius: 15rpx;
					line-height: 70rpx;
					text-align: center;
				}

				.q-check-sel {
					background: #01997A;
					border: 0;
					color: #fff;

				}
			}
		}

	}

	.q-answer,
	.analysis,
	.knowledge-points,
	.traps,
	.knowledge-origin {
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 60rpx;
		padding-top: 60rpx;
	}

	.q-answer-info {
		margin-bottom: 30rpx;

		text {
			font-size: 28rpx;
			color: #5A5A5A;
			font-weight: bold;
			letter-spacing: 4rpx;

			&:nth-child(2) {
				margin-left: 10rpx;
				color: $main-color;
			}

			&:last-child {
				margin-left: 10rpx;
				color: rgb(206, 97, 117);
			}
		}
	}

	.analysis {
		.answer-info {
			color: #5A5A5A;
			font-size: 28rpx;
		}
	}

	.knowledge-points {
		.point {
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;

			.point-tag {
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}

	.knowledge-origin {
		.knowledge-origin-title {
			color: #5A5A5A;
			font-size: 28rpx;
		}


	}


}
</style>
