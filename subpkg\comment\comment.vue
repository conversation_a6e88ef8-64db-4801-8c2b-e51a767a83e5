<template>
	<view class="container">
		<view class="head">
		 <uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button" activeColor="#009c7b"></uni-segmented-control>
		</view>
		<view class="teacher-list">
			<template>
				<view class="u-page__tag-item" v-for="(item, index) in list1" :key="index">
					<u-tag :bgColor="!item.checked?'#F6F7FB':'#01997A'" :color="!item.checked?'#5A5A5A':'#fff'" borderColor="#fff" :text="item.name" :plain="!item.checked"  :name="item.id"
						@click="clickTeacher">
					</u-tag>
				</view>
			   <!-- <u-tabs lineBgSize="" :list="list1" @click="clickTeacher"></u-tabs> -->
			</template>
		</view>
		
		<view class="comment-container">
			<view class="rate">
				<text class="left-title">课程评价</text>
				<view class="rate-icon">
					<template v-for="i in 5" >
						<image ref="rateItem" :src="rateImgs[i]" :key="i" @click="rate(i)" ></image>
					</template>
				<text class="tip">课程如何？点点评价一下！</text>
				</view>
			
			</view>
			
			<view class="rate">
				<text class="left-title">评价维度</text>
				<view class="rate-tags">
					<view :class="[formData.definition?'tag':'not_tag']" @click="commontShow(1)">
						<template v-if="type != 1">
							课堂氛围							
						</template>
						<template v-else>
							日常督学
						</template>
					</view>
					<view :class="[formData.know?'tag':'not_tag']" @click="commontShow(2)">
						
						<template v-if="type != 1">
							内容实用							
						</template>
						<template v-else>
							问题沟通
						</template>
					</view>
					<view :class="[formData.atmosphere?'tag':'not_tag']" @click="commontShow(3)">
						<template v-if="type != 1">
							知识点梳理							
						</template>
						<template v-else>
							学习计划
						</template>
						
						
					</view>
				</view>
			
			</view>
			
			<view class="bottom-text">
				<view class="comment-item" v-if="formData.definition">
					<text>
						<template v-if="type != 1">
							课堂氛围	:						
						</template>
						<template v-else>
							日常督学:
						</template>
					</text>
					<u--input border="none"  v-model="formData.definitionText"  placeholder="展开说说吧" ></u--input>
				</view>
				<view class="comment-item" v-if="formData.know">
					<text>
						<template v-if="type != 1">
							内容实用:						
						</template>
						<template v-else>
							问题沟通:
						</template>
					</text>
					<u--input border="none"  v-model="formData.knowText" placeholder="展开说说吧" ></u--input>
				</view>
				<view class="comment-item" v-if="formData.atmosphere">
					<text>
						<template v-if="type != 1">
							知识点梳理:						
						</template>
						<template v-else>
							学习计划:
						</template>
					</text>
					<u--input border="none"  v-model="formData.atmosphereText" placeholder="展开说说吧" ></u--input>
				</view>
			</view>
			<view class="btn-container">
				<view class="btn" @click="submit">
					提交
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import rateImgSel from "@/static/img/head.png"
	import rateImg from "@/static/img/praise.png"
import user from "../../store/user";
	export default {
		data() {
			return {
				list1:[],
				definitionText:'',
				knowText:'',
				atmosphereText:'',
				definition:true,
				know:true,
				atmosphere:true,
				rateImg,
				rateImgs:[rateImg,rateImg,rateImg,rateImg,rateImg],
				 rateImgSel,
				items: ['班主任评价', '授课老师评价'],
				current: 0,
				evaluate:0,
				type:1,
				teacherId:0,
				formData:{
					evaluate :0,
					type: 2,
					about_id:0,
					definitionText:'',
					knowText:'',
					atmosphereText:'',
					definition:true,
					know:true,
					atmosphere:true,
					
				},
				allData:{},
				userData:{},
			};
		},
		onLoad(option){
			this.getUserInfo();
			this.type = option.type 
			if(this.type == 1) {
				this.items = ['班主任评价']
			} else {
				this.items = ['授课老师评价']
				this.getAllTeacher();
			}
		},
		methods:{
			
			getUserInfo(){
				let that = this;
				let  data  = uni.$u.http.post('/mini_user/getUserInfo',{}).then(res=>{
					that.userData  =  res.data;
					if(that.type == 1 && that.userData.teacher_id == null) {
						uni.tip('您还未分配班主任,请分配后评价')
						setTimeout(function(){
							uni.navigateBack({delta:1})
						},1000)
					}
				})
			},
			getAllTeacher() {
				let that = this;
				let  data  = uni.$u.http.post('/student/getAllTeacher',{}).then(res=>{
					
					if(res.code == 1) {				
						that.list1 = res.data.teacher_arr
						if(that.list1.length < 1) {
							uni.tip('未找到授课老师,请稍后评价')
							setTimeout(function(){
								uni.navigateBack({delta:1})
							},1000)
						}
						that.teacherId  = that.list1[0].id
						that.list1[0].checked = true;
					} else {
						
					}
				});
			},
			clickTeacher(name){
				this.list1.map((item, index) => {
					item.checked = item.id === name ? true : false
				})
				console.log(name);
				this.teacherId = name;
				
				if(this.allData[this.teacherId] == undefined) {
					this.formData = {
						evaluate :0,
						type: this.type,
						about_id:this.teacherId,
						definitionText:'',
						knowText:'',
						atmosphereText:'',
						definition:true,
						know:true,
						atmosphere:true,
						
					}
					this.rateImgs =  this.rateImgs.map((item, i)=>{
						
						if(i<this.formData.evaluate){
							return this.rateImgSel
						}else{
							return this.rateImg
						}
					})
				} else {
					this.formData  = this.allData[this.teacherId];
					this.rateImgs =  this.rateImgs.map((item, i)=>{
						if(i<this.formData.evaluate){
							return this.rateImgSel
						}else{
							return this.rateImg
						}
					})
					console.log(this.rateImgs)
					
				}
				
			},
			submit() {
						// definition:true,
				// know:true,
				// atmosphere:true,
				// if(this.definition) {
				// 	this.formData.definitionText = this.definitionText
				// }
				// if(this.know) {
				// 	this.formData.knowText = this.knowText
				// }
				// if(this.atmosphere) {
				// 	this.formData.atmosphereText = this.atmosphereText
				// }
				if(this.formData.about_id == 0) {
					this.formData.about_id =  this.teacherId;
				}
				this.allData[this.teacherId] = this.formData;
				let  data  = uni.$u.http.post('/student/recordVideo',this.formData).then(res=>{
					console.log(res)
					if(res.code == 1) {		
						uni.tip('评价成功')
					} else {
						
					}
				});
			
			},		
			commontShow(type){
				if(type == 1) {
					this.formData.definition = !this.formData.definition					
				} else if(type==2) {
					this.formData.know = !this.formData.know
				} else {
					this.formData.atmosphere = !this.formData.atmosphere
				}
			},
			onClickItem(item){
				this.evaluate = 0;
				this.definitionText =''
				this.knowText = ''
				this.atmosphereText=''
				this.rate(0)
			},
			rate(index){
				this.formData.evaluate = index+1
				this.rateImgs =  this.rateImgs.map((item, i)=>{
					if(i<index+1){
						return this.rateImgSel
					}else{
						return this.rateImg
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	padding-top: 30rpx;
	height: calc(100vh - 44px);
	::v-deep .segmented-control__item{
		&:first-child{
			border-top-left-radius: 60rpx;
			border-bottom-left-radius: 60rpx;
			
		}
		&:last-child{
			border-top-right-radius: 60rpx;
			border-bottom-right-radius: 60rpx;
		}
	}
	.teacher-list{
		display: flex;
		margin-top: 26rpx;
	}
	::v-deep .u-page__tag-item  {
		padding:0 20rpx;
	}
	::v-deep .u-tag-wrapper  {
		flex-direction: row;
	}
	.comment-container{
		margin-top: 90rpx;
		min-height: 200rpx;
		padding: 0 30rpx;
		padding-bottom: 120rpx;
		.head{
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			margin-top: 52rpx;
			.h1-title{
				color: #0A0A0A;
				font-size: 32rpx;
				font-weight: bold;
			}
			.close{
				position: absolute;
				top:50%;
				transform: translateY(-50%);
				right: 62rpx;
			}
			
		}
		.rate{
			margin-top: 40rpx;
			margin-bottom: 60rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			min-height: 50rpx;
			text.left-title{
				width: 120rpx;
			}
			.rate-tags{
				display: flex;
				flex-wrap: wrap;
				align-content: space-between;
				justify-content: flex-start;
				.tag{
					color: #01997A;
					font-size: 26rpx;
					padding: 10rpx;
					border: 1rpx solid $main-color;
					border-radius: 8rpx;
					margin-left: 20rpx;
					margin-top: 20rpx;
				}
				.not_tag {
					color: #ccc;
					font-size: 26rpx;
					padding: 10rpx;
					border: 1rpx solid #eee;
					border-radius: 8rpx;
					margin-left: 20rpx;
					margin-top: 20rpx;
				}
			}
			text{
				color: #0A0A0A;
				font-size: 28rpx;
				
			}
			.rate-icon{
				position: relative;
				margin-left: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				image{
					margin-right: 26rpx;
					width: 60rpx;
					height: 60rpx;
				}
				.tip{
					height: 22rpx;
					line-height: 22rpx;
					position: absolute;
					font-size: 22rpx;
					color: #AFAFAF;
					bottom: -22rpx;
					left: 0;
				}
			}
	
		}
		.bottom-text{
			background-color: #F6F7FB;
			border-radius: 16rpx;
			padding: 28rpx;
			.comment-item{
				margin-top: 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				text{
					color: #0A0A0A;
					font-size: 28rpx;
					min-width: 120rpx;
					margin-right: 20rpx;
				}
			}
		}
		.btn-container{
			margin-top: 90rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.btn{
			width: 530rpx;
			height: 90rpx;
			background: #01997A;
			border-radius: 61rpx;
			margin-top: 90rpx auto;
			color: #fff;
			text-align: center;
			line-height: 90rpx;
			font-size: 32rpx;
		}
	}
}
</style>
