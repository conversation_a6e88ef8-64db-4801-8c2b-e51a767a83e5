<template>
	<view class="quick-search-tags">
		<view class="tag"  v-for="tag in tagList" :key="tag.id" :class="{'active-tag':currentsel==tag.id}" @click="changeItem(tag)">
			{{tag.name}}
		</view> 
	</view>
</template>

<script>
	export default {
		name:"tagList",
		data() {
			return {
				currentTagId:"all"
			};
		},
		props:{
			tagList:{
				type:Array,
				default:[]
			},
			current:{
				type:String,
				default:"all"
			}
		},
		computed:{
			currentsel(){
				return this.current
			}
		},
		methods:{
			changeItem(tag){
			 this.$emit("changeCurrentSel", tag.id)
			}
		}
	}
</script>

<style lang="scss" scoped>
			.quick-search-tags{
				display: flex;
				flex-wrap: wrap;
				align-content: space-between;
				justify-content: flex-start;
				padding:  0 16rpx;
				.tag{
					margin-top: 22rpx;
					margin-left: 22rpx;
					padding: 12rpx 28rpx;
					color: #2E3634;
					font-size: 26rpx;
					background-color: #fff;
					border-radius: 8rpx;
				}
				.active-tag{
					background-color: #DCF6ED;
					color: #01997A;
				}
			}
</style>