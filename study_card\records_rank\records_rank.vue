<template>
	<view class="container-p">
		<view class="container">
			<!-- 头部 -->
			<view class="head">
				<view class="title">
					<view class="left">
						<view class="avatar">
							<image :src="userDetail.avatar"></image>
						</view>
						<view class="info">
							<view class="top-text">
								<text>{{ userDetail.nickname ? userDetail.nickname : '小蛙00' + userDetail.user_id }}</text>
								<text class="extra-info">{{ levelName }}</text>
							</view>
							<view class="bottom-text">您已坚持打卡{{ count }}天！<text>当前排名:{{ rankNum }}</text></view>
						</view>
					</view>
					<view class="right" @click="getRecord">
						<image src="../../static/png/last_records.png" mode="widthFix"></image>
					</view>
				</view>


			</view>

			<view class="info-user">
				<view class="left-circle" @click="clockTo(1)">
					早打卡
				</view>
				<view class="mid">
					<view class="mid-top">
						<text class="small-circle"></text>
						<view class="tip">
							<text>{{ mornData ? '上午' + mornData : "上午未打卡" }} </text>
							<view class="icon-container" v-if="mornData">
								<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
							</view>
						</view>
					</view>
					<view class="mid-bottom">
						<text class="small-circle"></text>
						<view class="tip">
							<text>{{ nightData ? '下午' + nightData : "下午未打卡" }} </text>
							<view class="icon-container" v-if="nightData">
								<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="right-circle" @click="clockTo(2)">
					晚打卡
				</view>
			</view>

			<!-- 排名 -->
			<view class="rank">
				<view class="top-icon">
					打卡排行榜
				</view>
				<view class="rank-bottom">

					<view class="rank-item" v-if="rankList.length > 1">
						<view class="level">
							<text class="small-circle"></text>
							<text class="level-num">第二名</text>
							<text class="small-circle"></text>
						</view>
						<view class="rank-avatar">
							<image class="rank-flag" mode="widthFix" src="https://website-1300870289.cos.ap-nanjing.myqcloud.com/564f2a8182c83e738197a5309f1742c4bbb.png">
							</image>
							<image class="rank-avatar-img" :src="rankList[1].user.avatar ? rankList[1].user.avatar : 'https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/default_avatar.png'">
							</image>
							<text>{{ rankList[1].user.username }}同学</text>
						</view>
						<view class="num">
							{{ rankList[1].day_num }}天
						</view>
					</view>
					<view class="rank-item" v-if="rankList.length > 0">
						<view class="level">
							<text class="small-circle"></text>
							<text class="level-num">第一名</text>
							<text class="small-circle"></text>
						</view>
						<view class="rank-avatar">
							<image class="rank-flag" mode="widthFix" src="https://website-1300870289.cos.ap-nanjing.myqcloud.com/438609216ecc2fb0eafeb91cfd68b3dbe9d.png">
							</image>
							<image class="rank-avatar-img" :src="rankList[0].user.avatar ? rankList[0].user.avatar : 'https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/default_avatar.png'">
							</image>
							<text>{{ rankList[0].user.username }}同学</text>
						</view>
						<view class="num">
							{{ rankList[0].day_num }}天
						</view>
					</view>
					<view class="rank-item" v-if="rankList.length > 2">
						<view class="level">
							<text class="small-circle"></text>
							<text class="level-num">第三名</text>
							<text class="small-circle"></text>
						</view>
						<view class="rank-avatar">
							<image class="rank-flag" mode="widthFix" src="https://website-1300870289.cos.ap-nanjing.myqcloud.com/229fe8c12dd1877aeeda0e738f864af60f5.png">
							</image>
							<image class="rank-avatar-img" :src="rankList[2].user.avatar ? rankList[2].user.avatar : 'https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/default_avatar.png'">
							</image>
							<text>{{ rankList[2].user.username }}同学</text>
						</view>
						<view class="num">
							{{ rankList[2].day_num }}天
						</view>
					</view>

				</view>
			</view>

		</view>
		<!-- 底部排名 -->
		<view class="bottom-rank-list">
			<view class="list-item u-border-bottom" v-for="(item, key) in rankList" v-if="key > 2" :key="key">
				<view class="rank-user-info">
					<text>{{ key + 1 }}</text>
					<image :src="item.user.avatar ? item.user.avatar : 'https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/default_avatar.png'">
					</image>
					<text>{{ item.user.username ? item.user.username : '小蛙00' + item.user.user_id }}</text>
				</view>
				<text>{{ item.day_num }}天</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			levelName: "蝌蚪级",
			userDetail: '',
			mornData: '',
			nightData: '',
			rankList: [],
			rankNum: '50+',
			count: 0,
			charText: {
				0: '一',
				1: '二',
				2: '三',
			}
		};
	},
	components: {},
	onLoad() {
		this.getUserInfo();
		this.getUserRecord();
	},
	methods: {
		clockTo(type) {

			//判断今日是否已打卡
			uni.$u.http.get('/student/userIsClock?type=' + type).then(rest => {
				if (rest.code == 0) {
					uni.navigateTo({
						url: '/study_card/clock_in/clock_in?type=' + type + '&text=' + rest.msg
					})
				} else {
					uni.tip(rest.msg);
				}
			})

		},
		getUserRecord() {
			let that = this;
			uni.$u.http.get('/student/getUserRecord').then(rest => {
				let tmpData = rest.data.user_data;
				tmpData.map(item => {
					if (item.type == 1) {
						that.mornData = item.date
					} else {
						that.nightData = item.date
					}
				})
				that.rankList = rest.data.all
				that.count = rest.data.count;
				that.levelName = rest.data.levelName;
				that.rankNum = rest.data.num;
			});
		},
		getRecord() {
			uni.navigateTo({
				url: '/study_card/records/records'
			})
		},
		getUserInfo() {
			let that = this;
			uni.$u.http.get('/mini_user/getUserInfo').then(rest => {
				that.userDetail = rest.data;
			});
		},
	}
}
</script>

<style lang="scss" scoped>
.container {
	background: linear-gradient(to bottom, #01997A 0%, #16AC8E 31%, #CDF3E7 58%, #F6F7FB 100%);
	padding: 0 30rpx;
	padding-bottom: 40rpx;

	.head {
		box-sizing: border-box;
		padding-top: 130rpx;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.avatar {
					width: 100rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 100rpx;
						height: 100rpx;
						border-radius: 50%;

					}


				}

				.info {
					margin-left: 4rpx;

					.top-text {
						font-weight: bold;
						font-size: 30rpx;
						color: #fff;
						display: flex;
						align-items: center;
						justify-content: flex-start;

						.extra-info {
							margin-left: 8rpx;
							color: #CDF3E7;
						}
					}

					.bottom-text {
						margin-top: 12rpx;
						color: #fff;
						font-size: 28rpx;
					}
				}
			}

			.right {
				image {
					width: 60rpx;
				}
			}
		}

	}

	.info-user {
		margin-top: 30rpx;
		height: 164rpx;
		background: #FFFFFF;
		border-radius: 147rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10rpx;

		.left-circle,
		.right-circle {
			width: 150rpx;
			height: 150rpx;
			background: #16AC8E;
			border-radius: 50%;
			box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 0, 0, 0.16);
			border: 1rpx solid #F6EDED;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFF;
			line-height: 150rpx;
			text-align: center;
		}

		.mid {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			height: 100%;

			.mid-top,
			.mid-bottom {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.small-circle {
					width: 10rpx;
					height: 10rpx;
					border-radius: 50%;
					background: #01997A;
					margin-right: 6rpx;
				}

				.tip {
					width: 232rpx;
					height: 45rpx;
					background: #CDF3E7;
					border-radius: 26rpx 26rpx 26rpx 26rpx;
					border: 1rpx solid #01997A;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 12rpx;

					text {
						display: inline-block;
						height: 45rpx;
						line-height: 45rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #01997A;

						text-align: left;
					}

					.icon-container {
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #01997A;
						height: 24rpx;
						width: 24rpx;
						border-radius: 50%;
					}

				}
			}
		}
	}

	.rank {
		margin-top: 40rpx;
		width: 690rpx;
		padding-bottom: 20rpx;
		background: #C2F3E8;
		border-radius: 22rpx 22rpx 0rpx 0rpx;

		.top-icon {
			margin: 0 auto;
			width: 300rpx;
			background: url('https://website-1300870289.cos.ap-nanjing.myqcloud.com/7054777b70c136214c7324ca33faf40ecad.png');
			background-repeat: no-repeat;
			background-size: contain;
			height: 66rpx;
			text-align: center;
			line-height: 66rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
		}

		.rank-bottom {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14rpx;
			margin-top: 48rpx;

			.rank-item {
				width: 214rpx;
				height: 286rpx;
				padding-top: 40rpx;

				.level {
					display: flex;
					align-items: center;
					justify-content: center;

					.small-circle {
						width: 10rpx;
						height: 10rpx;
						border-radius: 50%;
						background: #6C71A5;
						margin-right: 6rpx;
					}

					.level-num {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 26rpx;
						color: #6C71A5;
					}

				}

				.rank-avatar {
					margin-top: 40rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					flex-direction: column;
					position: relative;

					.rank-flag {
						position: absolute;
						width: 40rpx;
						top: -34rpx;
					}

					.rank-avatar-img {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
					}

					text {
						width: 90px;
						display: inline-block;
						text-align: center;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						margin-top: 10rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #6C71A5;

					}
				}

				.num {
					margin: 10rpx auto;
					width: 105rpx;
					height: 34rpx;
					background: linear-gradient(180deg, #BFBCE3 0%, #7185D3 100%);
					border-radius: 36rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 22rpx;
					color: #E6E9FF;
					text-align: center;
					line-height: 34rpx;
				}

				&:first-child {
					background: url('https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/two.png');
					background-size: 100% 100%;
				}

				&:nth-child(2) {
					height: 354rpx;
					background: url('https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/one.png');
					background-size: 100% 100%;

					.level-num {
						color: #AC7532;
					}

					.num {
						background: linear-gradient(180deg, #D39E6C 0%, #B2482D 100%);
						color: #FED4B7;
					}
				}

				&:nth-child(3) {
					background: url('https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/three.png');
					background-size: 100% 100%;

					.level-num {
						color: #803A13;
					}

					.num {
						background: linear-gradient(180deg, #ECC2A0 0%, #C57A5A 100%);
						color: #FFEAE5;
					}
				}
			}
		}
	}

}

.bottom-rank-list {
	position: relative;
	top: -50rpx;
	box-sizing: border-box;
	width: 100%;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	background-color: #fff;
	padding-bottom: 20rpx;

	.list-item {
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.rank-user-info {
			font-size: 30rpx;

			display: flex;
			align-items: center;
			justify-content: center;

			text {
				color: #4A4A4C;
			}

			image {
				margin: 0 10rpx;
				height: 60rpx;
				width: 60rpx;
				border-radius: 50%;
			}
		}

		text {
			text-align: right;
			color: #01997A;
			font-weight: bold;
			font-size: 30rpx;
		}
	}
}
</style>