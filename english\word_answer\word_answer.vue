<template>
	<view class="answer-container u-border-top">
		<view class="list ">
			
			<user-title  :title='"批改答案"' :bgcolor="'#fff'"></user-title>
			<image @click="toErrorWord"  class="opt-icon" src="../../static/png/note.png" mode="widthFix"></image>
				
			
			<view class="item" v-for="(item, index) in list" :key="index">
				<view class="item-title" >
					<view class="left-info">
						{{index+1}}.{{item.word}} <text>{{item.prop}}</text>
					</view>
					<view class="right-action">
						<view class="wrong-icon" :style="{backgroundColor: item.userMark=='wrong'?'#EE7878':''}" @click="list[index].userMark='wrong'">
							<u-icon name="close"   :color="item.userMark=='wrong'?'#fff':'#EE7878'" size="44"></u-icon>
						</view>
						<view class="right-icon" :style="{backgroundColor: item.userMark=='right'?'#009c7b':''}"  @click="list[index].userMark='right'">
							<u-icon name="checkmark"   :color="item.userMark=='right'?'#fff':'#009c7b'" size="44"></u-icon>
						</view>
					</view>
				</view>
				<view class="item-detail">
					<view class="line">
						<text class="answerreal">正确答案:</text>
						<text class="mark ">{{item.rightAnswer}}</text>
					</view>
					<view class="line">
						<text>来自:</text>
						<text>{{item.from}}</text>
					</view>
					
					<view class="line eg">
						<text>eg:</text>
						<text>{{item.eg}}</text>
					</view>
					<u-line color="#01997A"></u-line>
					<view class="line line-bottom">
						<text>我的答案:</text>
						<text class="mark">{{item.selfAnswer}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="bottom-btn" v-if="!userRecord">
						
			<view class="btn" @click="submit()">
				提交批改
			</view>
		</view>
	</view>
</template>

<script>
	import userTitle from "@/components/user_title/user_title.vue";
	export default {
		data() {
			return {
				taskId:'',
				list:[],
				myAnswer:[],
				userRecord:0,
			};
		},
		components:{
			userTitle
		},
		async onLoad(options) {
			this.taskId = options.task_id
			this.getWordDetail();
		},
		methods:{
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getUnitWord',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data.tmplist;
					that.myAnswer = res.data.word_list;
					that.secondTime = res.data.useTime
					that.userRecord = res.data.user_record
					that.wordData.map(item=>{
						let tmpData  =  {
							word : item.word,
							prop : item.pos_text,
							rightAnswer:item.simple_meaning,
							from:(item.day_unit.book_id == 1?'必考词':'基础词')+item.day_unit.name,
							eg:item.example,
							selfAnswer:that.filteredList(item.word),
							userMark:'wrong'
						};
						
						that.list.push(tmpData);
					})
					console.log(that.answer);

				})
			},
			toErrorWord() {
				uni.navigateTo({
					url:'/english/word_wrong_list/word_wrong_list'
				})
			},
			filteredList(word) {
				let  tmp = this.myAnswer.filter((item, index, arr) => {
					if(item.word == word) {
						return item.uanswer;
					} else  {
						return  ''
					}
				}).map(item=>item.uanswer).toString();
				if(tmp != undefined) {
					return tmp;
				} else {
					return '';
				}
			},
			submit() {
				
				let colList = [];
				this.list.map(item=>{
					console.log(item.userMark);
					if(item.userMark == 'wrong') {
						colList.push(item.word)
					}
				})
				uni.$u.http.post('/task/wordRecord',{task_id:this.taskId,list:colList}).then(res=>{
					if(res.code == 1) {
						uni.tip('操作成功');
						uni.switchTab({
							url:'/pages/task/task'
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
.answer-container{
	padding: 0 20rpx;
	padding-bottom: 100rpx;
}
.item{
	margin-bottom: 34rpx;
}
.list{
	position: relative;
	.opt-icon {
			position: absolute;
			width: 50rpx;
			padding-left: 30rpx;
			right:30rpx;
			top:30rpx
			
		}
}
.item-title{
	display: flex;
	align-items: center;
	justify-content: space-between;
	.left-info{
		font-weight: 400;
		font-size: 26rpx;
		color: #5A5A5A;
		text{
			margin-left: 20rpx;
		}
	}
	.right-action{
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 148rpx;
		.wrong-icon, .right-icon{
			display: flex;
			align-items: center;
			justify-content: center;
			height: 54rpx;
			width: 54rpx;
			border-radius: 50%;
		}
		.wrong-icon{
			border: 1rpx solid #EE7878;
		}
		.right-icon{
			border: 1rpx solid #009c7b;
		}
	}

}
.item-detail{
	margin-top: 10rpx;
	border: 1rpx solid #01997A;
	border-radius: 15rpx;
	font-weight: 500;
	font-size: 28rpx;
	color: #1F1F27;
	.line{
		padding: 0 24rpx;
		margin-top: 10rpx;
		display: flex;
		align-items: flex-start;
		justify-content: flex-start;
		text{
			&:first-child{
				width:130rpx;
			}
			&:last-child{
				flex: 1;
				margin-left: 10rpx;
				text-align: left;
			}
		}
	}
	.eg{
		
		margin-bottom: 10rpx;
		align-items: flex-start;
	}
	.line-bottom{
		height: 80rpx;
	}
}
.mark{
	color: #009c7b;
}
.bottom-btn{
	position: fixed;
	bottom: 0;
	height: 90rpx;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #FFFFFF;
	.btn{
		
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
		line-height: 80rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: #FFFFFF;
		text-align: center;
	}
	
}

</style>
