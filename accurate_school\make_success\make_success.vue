<template>
	<view class="container">
		<view class="head" :style="{backgroundImage:'url(/static/img/submit-bg.png)','paddingTop':topHeight}">
			<u-icon size="40" name="arrow-left" color="#fff" @click="back"></u-icon>
			<text>精准择校</text>
		</view>
		<view class="bottom-bg" :style="{backgroundImage:'url(/static/img/submit-bg2.png)'}">
			<view>恭喜您，</view>
			<view>您的专属《院校专业选择分析报告》已经完成！</view>
		</view>
		<u-button shape="square" size="mini">下载报告</u-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				topHeight: 0,
			}
		},
		mounted() {
			let m = uni.getMenuButtonBoundingClientRect()
			console.log(m)
			this.topHeight = m.top + 'px'

		},
	}
</script>

<style lang="scss" scoped>
	.container {

		.head {
			height: 485rpx;
			background-repeat: no-repeat;
			background-size: contain;

			::v-deep .u-icon {
				display: flex;
				align-items: center;
				float: left;
				height: 32rpx;
				color: #fff;
			}

			text {
				display: inline-block;
				float: left;
				width: 90%;
				margin-top: -10rpx;
				text-align: center;
				font-weight: bold;
				font-size: 34rpx;
				color: #fff;
			}
		}

		.bottom-bg {
			width: 690rpx;
			height: 480rpx;
			padding-top: 200rpx;
			box-sizing: border-box;
			background-repeat: no-repeat;
			background-size: contain;
			margin: -50rpx auto;
			text-align: center;
			font-weight: 400;
			font-size: 30rpx;
			color: #4C5370;
		}

		::v-deep .u-button {
			height: 81rpx;
			width: 400rpx;
			font-size: 30rpx;
			color: #FFFFFF;
			border-radius: 50rpx;
			border: none;
			background-color: #01997A;
			margin-top: 318rpx;
		}
	}
</style>