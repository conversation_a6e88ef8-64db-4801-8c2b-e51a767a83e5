<template>
    <view class="about-container">

        <!-- 选项列表 -->
        <view class="about-list">
            <!-- 用户协议 -->
            <view class="about-item" @click="goToPage('userAgreement')">
                <text class="item-text">用户协议</text>
                <uni-icons color="#999" type="right" size="20"></uni-icons>
            </view>

            <!-- 隐私政策 -->
            <view class="about-item" @click="goToPage('privacyPolicy')">
                <text class="item-text">隐私政策</text>
                <uni-icons color="#999" type="right" size="20"></uni-icons>
            </view>

            <!-- 资质证照公示
            <view class="about-item" @click="goToPage('certificate')">
                <text class="item-text">资质证照公示</text>
                <uni-icons color="#999" type="right" size="20"></uni-icons>
            </view> -->
        </view>

        <!-- 底部版权信息 -->
        <view class="copyright">
            <text>© 2025 安徽中未教育科技有限公司 版权所有</text>
        </view>
    </view>
</template>

<script>
import { baseUrl } from "@/utils/setting.js";

export default {
    data() {
        return {

        };
    },
    computed: {
        version() {
            // 获取当前应用版本号
            return plus.runtime.version;
        }
    },
    methods: {
        // 返回上一页
        navBack() {
            uni.navigateBack();
        },

        // 跳转到相关页面
        goToPage(type) {
            const urls = {
                privacyPolicy: baseUrl.replace("/api", "") + "/index/index/privacyPolicy",
                userAgreement: baseUrl.replace("/api", "") + "/index/index/userAgreement",
                certificate: baseUrl.replace("/api", "") + "/index/index/certificate", // 资质证照公示页面
            };

            if (urls[type]) {
                uni.navigateTo({
                    url: `/subpkg/scan_login/webview?url=${encodeURIComponent(urls[type])}`,
                });
            } else {
                // 如果没有对应的URL，显示开发中提示
                uni.showToast({
                    title: '功能开发中',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss">
.about-container {
    padding: 0 30rpx;
    min-height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    padding-bottom: 40rpx;

    .header {
        position: relative;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        padding: 20rpx 0;
        margin-bottom: 20rpx;

        .back-icon {
            position: absolute;
            left: 0;
            font-size: 40rpx;
        }

        .title {
            font-size: 36rpx;
            font-weight: 500;
        }
    }

    .company-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60rpx 0;

        .logo {
            width: 160rpx;
            height: 160rpx;
            border-radius: 30rpx;
            margin-bottom: 20rpx;
        }

        .app-name {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 10rpx;
        }

        .version {
            font-size: 28rpx;
            color: #999;
        }
    }

    .about-list {
        margin-top: 30rpx;

        .about-item {
            margin-bottom: 20rpx;
            background-color: #fff;
            height: 100rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30rpx;
            border-bottom: 1rpx solid #f5f5f5;
            padding-top: 10rpx;
            padding-bottom: 10rpx;
            border-radius: 20rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

            .item-text {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }
        }

        .about-item:first-child {
            margin-top: 10rpx;
        }

        .about-item:last-child {
            margin-bottom: 10rpx;
        }
    }

    .copyright {
        margin-top: auto;
        text-align: center;
        padding: 30rpx 0;
        font-size: 24rpx;
        color: #999;
    }
}
</style>