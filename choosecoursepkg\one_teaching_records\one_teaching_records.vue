<template>
	<view class="container">
		<view class="top">
			<text>总课时68</text>
			<text>已节课20.4</text>
			<text>剩余课时47.6</text>
		</view>
		<view class="list">
			<view class="live-broadcast" v-for="i in 4":key=i>
				<view class="broadcast-info">
					<view class="broadcast-title">
						<text>{{i+1}}</text>
						<text>2025.12.13 13:00-15:00(2.5课时）</text>
					</view>
					<view class="broadcast-detail">
						<text>进入回放</text>
					</view>
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
.container{
	min-height: 100vh;
	background-color: $container-bg-color;
	.top{
		height: 90rpx;
		padding: 22rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		text{
			color: #4C5370;
			font-size: 30rpx;
			font-weight: bold;
		}
	}
	.list{
		padding: 0 30rpx;
		padding-top: 30rpx;
		.live-broadcast{
			
			background: linear-gradient( 180deg, #CDF3E7 0%, #FFFFFF 100%);
			border-radius: 20rpx;
			
			
			
			margin-bottom: 34rpx;
			box-sizing: border-box;
			padding:28rpx;
			padding-bottom: 35rpx;
				.broadcast-title{
					color: #4A4A4C;
					font-size: 30rpx;
					font-weight: bold;
					text{
						display: inline-block;
						&:first-child{
							height: 40rpx;
							width: 40rpx;
							border-radius: 50%;
							background-color: #01997A;
							color: #fff;
							line-height: 40rpx;
							text-align: center;
							font-size: 24rpx;
							margin-right: 23rpx;
						}
					}
				}
				.broadcast-detail{
					box-sizing: border-box;
					margin-top: 20rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					text{
		
							height: 44rpx;
							padding: 0 20rpx;
							background: linear-gradient( 90deg, #10C19D 0%, #14B494 63%, #01997A 100%);
							border-radius: 40rpx;
							line-height: 44rpx;
							text-align: center;
							font-size: 24rpx;
							font-weight: bold;
							color: #fff;
						
					}	
				}
		}
	}
}
</style>
