<template>
	<view class="container">
		<uni-segmented-control :current="current" :values="list" @clickItem="onClickItem" styleType="button"
			activeColor="#01997A" inActiveColor="#fff">


		</uni-segmented-control>
		<view v-show="current==0" class="content-action">
<!-- 				<u-datetime-picker :minDate="start" :show = "showStart" @cancel="showStart=false" class="show-tm" v-model="startDate" @confirm="showStart=false">
				</u-datetime-picker> -->
				
				<uni-datetime-picker :start="start" class="show-tm" v-model="startDate">
				<view class="form-item">
					<text class="label">
						开始时间
					</text>

					<view class="show-tm">
						{{startDate}}
					</view>
				</view>
				</uni-datetime-picker>
			
<!-- 			<u-datetime-picker :minDate="startDate" :show = "showEnd" @cancel="showEnd=false" class="show-tm" v-model="endDate" @confirm="showEnd=false">
			</u-datetime-picker> -->
			<uni-datetime-picker :start="startDate"  class="show-tm" v-model="endDate" @change='changeEnd'>
				<view class="form-item">
					<text class="label">
						结束时间
					</text>

					<view class="show-tm">
						{{endDate}}
					</view>
				</view>
				</uni-datetime-picker>
			<view class="form-item">
				<text class="label">
					请假时长
				</text>

				<view class="show-tm">
					{{leaveTime}}
				</view>
			</view>


			<view class="form-item-textarea">
				<text class="label">
					请假原因
				</text>

				<view class="show-tm">
					<u--textarea :border='"none"' v-model="reason" placeholder="请输入内容"></u--textarea>
				</view>
			</view>

			<view class="form-item photo">
				<u-upload height="140rpx" width="140rpx" uploadText="上传照片" :fileList="fileList" @afterRead="afterRead"
					@delete="deletePic" name="1" multiple :maxCount="10"></u-upload>

			</view>
			<view class="btn-container">
				<view class="btn" @click="submit" :style="{opacity:btnLoading?0.4:1}">
					<text v-if="!btnLoading"> 提交</text>
					<u-loading-icon v-else size=20 mode="circle" inactive-color="white"></u-loading-icon>
				</view>
			</view>
		</view>
		<view v-show="current==1" class="content-list">

			<view class="awaitBoxs" v-for="item in unList" :key="item.id">
				<view class="status-lable"
				:class='{"status-lable-success":item.status==1,"status-lable-fail":item.status==2,"status-lable-pending":item.status==0}'
				>
					{{item.status==0?'待审批':item.status==1?'已通过':'未通过'}}
				</view>
				<view class="remarkDetail">
					<view class="remark">请假原因：{{item.reason}}</view>
					<view class="start">开始时间：{{item.start_time_text}}</view>
					<view class="end">结束时间：{{item.end_time_text}}</view>
					<view class="start">请假时长：{{item.duration}}小时</view>
				</view>
				<view class="btnBox" v-if="item.note">
					<text>驳回原因</text>、
					<text>{{item.note}}</text>
				</view>
			</view>
			<u-empty textSize='28' text="暂无请假列表" v-if="unList.length==0 && listLoading==false"></u-empty>
			<u-loadmore v-if="listLoading==false" fontSize='28' iconSize='30' :status="status" @loadmore='loadMore' />
		</view>
	</view>
</template>

<script>
	import {
		uploadFile
	} from "@/api/common/common.js"
	import {
		askForLeave,
		getLeaveList
	} from "@/api/camp";
	
	import moment from "../module/moment/moment"
	export default {
		data() {
			return {
				showStart:false,
				showEnd:false,
				status:'loadmore',
				pageInfo: {
					page:1,
					total:0,
					pageSize:5,
				},
				startDate: '',
				endDate: '',
				start: Date.now(),
				reason: '',
				fileList: [],
				loading: false,
				btnLoading: false,
				list: ['请假', '请假记录'],
				current: 0,
				listLoading : false,
				unList: [
			]
			};
		},
		methods: {
			changeStart(e) {
			},
			 loadMore() {
				this.pageInfo.page++
				this.getList()
			},
			openEndDate(){
				if(this.startDate-0 <=0){
					return uni.tip("请先选择开始时间")
				}
				this.showEnd = true
			},
			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
					if(this.current == 1){
						this.unList = []
						//获取请假列表
						this.getList()
					}
				}
			},
			async getList(){
				const params = {
					page:this.pageInfo.page,
					pageSize:this.pageInfo.pageSize
				}
				this.listLoading = true
				const unList =   await getLeaveList({params})
				this.listLoading = false
				this.status = 'loading'
				if(unList != false){
					this.pageInfo.total = unList.total
					this.unList = [...unList.list, ...this.unList]
				}
				this.status = 'loadmore'
				//判断是否有下一页
				if(this.pageInfo.total<= this.unList.length){
					this.status = 'nomore'
				}
			},
			changeEnd() {
				if ((this.endTimeStamp - this.startTimeStamp) < 0) {
					uni.tip("结束时间应大于开始时间")
				}
				return
			},
			async submit() {
				if (this.btnLoading) {
					return
				}
				if (this.startDate == "") {
					return uni.tip("请输入请假开始时间")
				}
				if (this.endDate == "") {
					return uni.tip("请输入请假结束时间")
				}

				if (this.reason == "") {
					return uni.tip("请输入请假原因")
				}
				if ((this.endTimeStamp - this.startTimeStamp) < 0) {
					return uni.tip("结束时间应大于开始时间")
				}
				const images = this.fileList.map(item => {
					return item?.url
				}).join(",")
				const duration = this.leaveTime
				const postData = {
					start_time: this.startTimeStamp / 1000,
					end_time: this.endTimeStamp / 1000,
					reason: this.reason,
					images,
					duration
				}
				this.btnLoading = true
				const data = await askForLeave(postData)
				if (data != false) {
					uni.tip('请假成功')
				}
				this.btnLoading = false
				this.resetForm()
			},
			resetForm() {
				this.startDate = '';
				this.endDate = '';
				this.start = Date.now();
				this.reason = '';
				this.fileList = [];
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading = true;
				for (let i = 0; i < lists.length; i++) {
					try {
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					} catch (err) {
						//移除图片
						setTimeout(() => {
							this.fileList.splice(fileListLen, 1)
						}, 1500)
					}
				}
				this.loading = false
			},
			// 删除图片
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
		},
		computed: {
			leaveTime() {
				if ((this.endTimeStamp - this.startTimeStamp) > 0) {
					return parseInt((this.endTimeStamp - this.startTimeStamp) / 1000 / 60 / 60) + '小时';
				}
				return ""
			},
			//请假开始时间戳
			endTimeStamp() {
				return new Date(this.endDate).getTime()
			},
			//请假结束时间戳
			startTimeStamp() {
				return new Date(this.startDate).getTime()
			},
		},
		filters:{
			formater(val){
				if(val == ""){
					return ""
				}
				return moment(val).format("YYYY-MM-DD HH:mm:ss")
			}
		}
	}
</script>

<style lang="scss">
	.content-list,
	.content-action {
		margin-top: 22rpx;
	}

	.container {
		min-height: 100vh;
		background-color: #F3F4F6;
		padding: 30rpx;
	}

	.form-item,
	.form-item-textarea {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		height: 80rpx;
		border-radius: 16rpx;
		background-color: #fff;
		margin-bottom: 18rpx;
	}

	.label {
		font-size: 28rpx;
		color: #1E1E1E;
		width: 120rpx;
	}

	.show-tm {
		flex: 1;
		text-align: right;
	}

	.form-item-textarea {
		flex-direction: column;
		height: 210rpx;
		padding: 20rpx;

		.label {
			width: 100%;
			text-align: left;

		}

		.show-tm {
			width: 100%;
			text-align: left;
		}
	}

	.photo {
		height: 210rpx;

		justify-content: flex-start;
	}

	.btn-container {
		position: fixed;
		bottom: 60rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100rpx;
	}

	.btn {
		width: 518rpx;
		height: 82rpx;
		background: #06A177;
		border-radius: 67rpx;
		color: #fff;
		text-align: center;
		line-height: 82rpx;
		display: flex;
		align-items: center;
		justify-content: center;

	}

	.content-list {
		.awaitBoxs {
			width: 690rpx;
			font-size: 28rpx;
			color: #1E1E1E;
			background: #FFFFFF;
			border-radius: 14rpx;
			margin: 0 auto 25rpx;
			padding-top: 34rpx;
			box-sizing: border-box;
			position: relative;
			.status-lable{
				position: absolute;
				right: 0;
				top: 0;
				width: 104rpx;
				height: 60rpx;
				border-radius: 0rpx 14rpx 0rpx 14rpx;
				font-weight: bold;
				font-size: 24rpx;
				text-align: center;
				line-height: 60rpx;
			}
			.status-lable-success{
				background: #CDF3E7;
				color: #01997A;
			}
			.status-lable-fail{
				background: #FFD1D1;
				color: #EE7878;

			}
			.status-lable-pending{
				background: #AFAFAF;
				color: #5A5A5A;	
			}
			.remarkDetail {
				margin-left: 29rpx;

				.studentName {
					font-weight: bold;
					font-size: 30rpx;
					color: #1E1E1E;
					margin-bottom: 36rpx;
				}

				.start {
					margin-top: 26rpx;
					margin-bottom: 20rpx;
				}
			}

			.btnBox {
				height: 91rpx;
				margin-top: 28rpx;
				padding: 0 27rpx;
				font-size: 28rpx;
				color: #1E1E1E;
				box-sizing: border-box;
				border-top: 1rpx solid #CFCDCD;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
		}
	}
</style>