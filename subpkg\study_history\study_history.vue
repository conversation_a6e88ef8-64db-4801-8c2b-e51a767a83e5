<template>
	<view class="container">
		<view class="all-course-h1">
			<view class="green-block-big">

			</view>
			<text>任务记录</text>
			<view class="opt">
				<image class="opt-icon" src="../../static/png/date.png" mode="widthFix" @click="selectDate"></image>
			</view>
		</view>
		<view class="note-list" v-for="(i,k) in list" :key="k">
			<view>
				{{k}}
			</view>	
			<template  v-for="(item,key) in i">
				<view class="item"  v-for="(item_t,key_k) in item.task_text">
					<text class="num" :key="key_k">{{item_t.title}}</text>
					<text class="date">{{item.type_text}}</text>
					<view class="show" @click="detail(item.quantion_type,item.id,item.type,item_t)">
						查看
					</view>					
				</view>
			</template>
		</view>
		<view class="more" v-if="show">
			<text>没有更多数据</text>
		</view>
		<uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true"  :range="true" :insert="false"	  @confirm="confirm" @close="closeDate"/>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show:false,
				total:0,
				list:{},
				page:1,
				userDetail:'',
				qtype:'course',
				begin_date:'',
				end_date:''
			};
		},
		onLoad(option){
			this.qtype = option.type;
			this.sevenDayLater(7)
			this.getUserInfo();
			this.getNoteList();
		},
		filters : {
			happenTimeFun(num){//时间戳数据处理
				let date = new Date(num*1000);
				 //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;//月补0
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;//天补0
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;//小时补0
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;//分钟补0
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;//秒补0
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m;
			},
		},
		methods:{
			confirm(e) {
				this.date = e.fulldate;
				console.log(e)
				this.begin_date = e.range.before
				this.end_date = e.range.after
				if(e.range.data.length > 7) {
					uni.tip('最多不可超过7天');
					return  false
				}
				this.getNoteList();
				this.calendarShow = false;
			},
			sevenDayLater(days){
				let timestamp = new Date().getTime();
				let nowDay = new Date();
				let one_day = 86400000; // 24 * 60 * 60 * 1000;
				let addVal = timestamp - days * one_day;
				//7天后的日期
				let date = new Date(addVal);
				//格式化日期
				let filters = n => {
					return n < 10 ? (n = '0' + n) : n;
				};
				let month = filters(date.getMonth() + 1);
				let day = filters(date.getDate());
				let nowMonth = filters(nowDay.getMonth() + 1);
				let nowTime = filters(nowDay.getDate());
				this.begin_date = `${date.getFullYear()}-${month}-${day}`;
				this.end_date = `${nowDay.getFullYear()}-${nowMonth}-${nowTime}`;
				
			},
			
			selectDate(){
				this.$refs.calendar.open();
			},
			closeDate() {
				
			},
			detail(quantion_type,taskId,type,item) {
				if(quantion_type == 'course') {
					uni.navigateTo({
						url:'/choosecoursepkg/study/study?course_type='+type+'&taskId='+taskId+'&chapter_id='+item.id+'&video_id='+item.video_id
					})
					
				} else if(quantion_type == 'semt') {
					if(item.total_ret > 0) {
						uni.navigateTo({
							url:'/class_teacher/day_sentence_answer/day_sentence_answer?task_id='+taskId
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/day_sentence/day_sentence?task_id='+taskId
						})
					}
				} else if(quantion_type == 'word') {
					if(item.total_ret > 0) {
						uni.navigateTo({
							url:'/english/word_answer/word_answer?task_id='+taskId
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/day_word/day_word?task_id='+taskId
						})
					}
				} else if(quantion_type == 'read') {
					if(item.total_ret >  0) {
						uni.navigateTo({
							url:'/class_teacher/reading_analysis_answer/reading_analysis_answer?task_id='+taskId
						})
					} else {
						uni.navigateTo({
							url:'/class_teacher/reading_analysis/reading_analysis?task_id='+taskId
						})
					}
				}
			},
	
			getUserInfo(){
				let that = this;
				uni.$u.http.get('/mini_user/getUserInfo').then(rest=>{
					that.userDetail  = rest.data;				
				});
			},
			getNoteList() {
				let that = this
				that.list  = [];
				let  data  = uni.$u.http.post('/Task/getUserHistoryTask',{start_date:that.begin_date,end_date:that.end_date,qtype:that.qtype,page:that.page}).then(res=>{
				
					if(res.code == 1) {						
							that.total = res.data.total;
							that.list = {...that.list, ...res.data.list}
							let arr = Object.keys(that.list);  
							if(arr.length  >= that.total) {
								that.show = true;
							}
					} else {
						
					}
				});
			},		
			
			toAddNote(){
				uni.navigateTo({
					url:"/subpkg/addnote/addnote"
				})
			}
		},
		onReachBottom() {
			if ((Object.keys(this.list)).length >= this.total) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			}
			this.page += 1
			this.getNoteList()
		},
	}
</script>

<style lang="scss" scoped>
	page{
		background-color:red;
	}

	.container {
		padding: 0 30rpx;
		padding-top: 22rpx;
		padding-bottom: 30rpx;
		background-color: $container-bg-color;
		min-height: 100vh;
		.opt {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		
			.opt-icon {
				width: 50rpx;
				padding-left: 30rpx;
			}
		}
		.head {
			background-color: #fff;
			padding: 22rpx 26rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #fff;
			border-radius: 20rpx;

			.avatar {
				width: 100rpx;
				height: 100rpx;
				background: #F2F2F2;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 94rpx;
					// height: 66rpx;
					border-radius: 50rpx;

				}
			}

			.info {
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				text {
					&:first-child {
						color: #201E2E;
						font-size: 32rpx;
						font-weight: bold;
					}

					&:last-child {
						color: #6D6D6D;
						font-size: 24rpx;
					}
				}
			}

			.opt {
				border: 1px solid #01997A;
				border-radius: 20rpx;
				color: #01997A;
				text-align: center;
				padding: 10rpx;
				font-size: 28rpx;
				font-weight: bold;
			}
		}

		.all-course-h1 {
			background-color: #F6F7FB;
			padding: 32rpx 0;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			.green-block-big {

				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;

			}

			text {
				padding-left: 10rpx;
				width: 128rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}
		
		.note-list{
		
			margin-top: 30rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			padding-bottom: 0;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			.item{
				padding:30rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.num{
					flex:2;
					color: #201E2E;
					font-size: 30rpx;
					font-weight: bold;
				}
				.date{
					flex:1 ;
					font-size: 24rpx;
					color: #6D6D6D;
					
				}
				.show{
					background-color: #01997A;
					border-radius: 14rpx;
					text-align: center;
					height: 38rpx;
					line-height: 38rpx;
					font-size: 26rpx;
					width: 86rpx;
					color: #fff;
				}
			}
		}
		.more{
			margin-top: 30rpx;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			image{
				width: 20rpx;
			}
			text{
				margin-left: 6rpx;
				font-size: 30rpx;
				color: #A2A2A2;
				font-weight: bold;
			}
			
		} 
	}
</style>