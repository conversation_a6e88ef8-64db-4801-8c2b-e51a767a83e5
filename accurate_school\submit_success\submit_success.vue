<template>
	<view class="container">
		<view class="head" :style="{paddingTop:topHeight}">
			<u-icon size="40" color="#5A5A5A" name="arrow-left" @click="back"></u-icon>
			<text>精准择校</text>
		</view>
		<image src="/static/img/submit.png" mode=""></image>
		<view class="submit">提交成功</view>

		<view class="text">《院校专业选择分析报告》排队制作中，</view>
		<view class="text">预计24h内完成，请耐心等待！</view>
	</view>
</template>

<script>
	export default {
		data() {

			return {
				topHeight: 0,
			}
		},
		mounted() {
			let m = uni.getMenuButtonBoundingClientRect()
			console.log(m)
			this.topHeight = m.top + 'px'

		},
		methods: {
			back() {
				uni.navigateBack()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.head {
			height: 150rpx;
			border-bottom: 1rpx solid #D6D6D6;

			::v-deep .u-icon {
				display: flex;
				align-items: center;
				float: left;
				height: 32rpx;
			}

			text {
				display: inline-block;
				float: left;
				width: 90%;
				margin-top: -10rpx;
				text-align: center;
				font-weight: bold;
				font-size: 34rpx;
				color: #2D2D2D;
			}
		}

		image {
			width: 332rpx;
			height: 318rpx;
			margin-top: 220rpx;
			margin-left: 50%;
			transform: translateX(-50%)
		}

		.submit {
			margin-top: 40rpx;
			text-align: center;
			font-weight: 800;
			font-size: 34rpx;
			color: #4C5370;
			margin-bottom: 57rpx;
		}

		.text {
			text-align: center;
			font-weight: 400;
			font-size: 30rpx;
			color: #5A5A5A;
		}
	}
</style>