<template>
  <view class="container">
    <view class="c-switch" >
      <view class="c-toggle"></view>
      <view class="c-names">
        <view :animation="animationData"
          style="
            background: #009c7b;
            position: absolute;
			top:-1px;
            border-radius: 44px;
            width:38px;
            height: 24px;">
        </view>
        <view
          class="left"
          :style="{
            color: current == false ? '#fff' : ' #009c7b',
          }"
          @click="leftButton('left')"
          >{{ left }}
		  </view>
        <view
          class="right"
          @click="rightButton('right')"
          :style="{
            color: current == true ? '#fff' : ' #009c7b',
          }"
          >{{ right }}
		  </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "mimicry-switch",
  data() {
    return {
      animationData: {},
      thiscuren: Boolean,
    };
  },
  props: {
    left: {
      type: String,
      default: "最近",
    },
    right: {
      type: String,
      default: "全部",
    },

    //指当前状态
    current: {
      type: Boolean,
      default: true,
    },
  },
  mounted() {
    if (this.current) {
      this.clickright();
    } else {
      this.clickleft();
    }
  },
  methods: {
    leftButton(e) {
      console.log(e);
      if (e == "left" && this.current == true) {
        this.clickleft();
      }
      console.log(this.current);
    },
    rightButton(e) {
      console.log(e);
      if (e == "right" && this.current == false) {
        this.clickright();
        console.log(this.current);
      }
    },
    clickleft() {
      var animation = uni.createAnimation({
        duration: 400,
        timingFunction: "ease",
      });
      this.animation = animation;
      this.animationData = animation.export();

      setTimeout(
        function () {
          animation.translate(0).step();
          this.animationData = animation.export();
        }.bind(this),
        100
      );
      this.$emit("update:current", false);
    },
    clickright() {
      var animation = uni.createAnimation({
        duration: 400,
        timingFunction: "ease",
      });
      this.animation = animation;
      this.animationData = animation.export();
      setTimeout(
        function () {
          animation.translate(34).step();
          this.animationData = animation.export();
        }.bind(this),
        100
      );
      this.$emit("update:current", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.container{
	.c-switch {
	position: relative;
	  width: 71px;
	  height: 24px;
	  display: block;
	  background: #fff;
	  text-align: center;
	  border: 1rpx solid #009c7b;
	  border-radius: 44px;
	  .c-names {
	    font-size: 9px;
	    color:  #009c7b;
	    display: flex;
		height: 24px;
	    line-height: 24px;
	    .left {
		line-height: 23px;
			z-index: 100;
			width: 50%;
	    }
	    .right {
			line-height: 23px;
			z-index: 100;
			width: 50%;
	    }
	  }
	}
}

</style>
