import{getUserInfo, setUserInfo, removeUserInfo} from "@/utils/storage.js"
let user =  {
	namespaced: true,
	state:{
		userInfo:getUserInfo()
	},
	mutations:{
		setUser(state, payload){
			state.userInfo = {...state.userInfo, ...payload}
			setUserInfo(state.userInfo)
		},
		removeUser(state,  payload){
			state.userInfo = {}
			removeUserInfo()
		},
	},
	actions:{
		async login(context){						
			// #ifdef MP-WEIXIN
			//获取用户信息
			const loginInfo = await uni.login({
				provider: "weixin"
			});
			let rest = await uni.$u.http.get("/mini_user/wxLogin",  {params:{
				code: loginInfo[1].code
			}})
			console.log(rest)
			let ret = rest.data
			if (ret.token) {
				let rets =  {
					token: ret.token,
					phone: ret.phone,
					nickName: ret.nickName,
					avatarUrl: ret.avatarUrl,
					is_camp:ret?.is_camp
				}
				context.commit('setUser',rets)
			}
			// #endif			
		}
	}
}
export default user