<template>
	<view class="container">
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>{{ currentIndex - 0 + 1 }}</text>
					<text>/{{ currentTypes.length }}</text>
				</view>
				<star :isDeep='true' :isstar.sync="question.is_star"></star>
			</view>

			<view class="q-type">
				{{ currentType.subtitle }}
			</view>

			<!-- <view class="question" :style="{paddingBottom:isInMiddle?'calc(100vh - 200px)':'100px'}"> -->
			<view class="question">
				<scroll-view class="question-content" scroll-y="true">
					<rich-text :nodes="questionContent"></rich-text>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
//import {mapState} from "vuex"
export default {
	name: 'QuestionContent',
	data() {
		return {

		};
	},
	components: {
		star
	},
	methods: {
		saveCurrentAnswer() {

		}
	},
	mixins: [mixins],
	computed: {
		questionContent() {
			if (!this.currentType || !this.currentType.title) {
				return '';
			}
			return this.currentType.title.replace(/_+(\d+)_+/g, (match, num) => {
				return `__${num}__`
			})
		},
		paddingBottomVal() {
			console.log(this.isInMiddle)
			return this.isInMiddle
		},

		//...mapState('cover', ['isInMiddle'])
	}
}
</script>

<style lang="scss" scoped>
.content {
	background-color: #fff;
	padding: 0 36rpx;
	height: 100%;
	padding-top: 78rpx;

	.top-opt {
		width: 100%;
		padding: 10rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.progress-text {
			text {
				color: #777777;
				font-size: 26rpx;

				&:first-child {
					font-size: 34rpx;
					color: #2D2D2D;
					font-weight: bold;
				}
			}
		}

		.opt {
			display: flex;
			align-items: center;
			justify-content: space-between;

			view {
				margin-left: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					font-size: 18rpx;
					color: #777;
				}
			}
		}
	}

	.q-type {
		display: inline-block;
		margin-top: 12rpx;
		background-color: #CDF3E7;
		border-radius: 12rpx;
		color: $main-color;
		font-size: 24rpx;
		min-width: 120rpx;
		height: 46rpx;
		text-align: center;
		line-height: 46rpx;
		padding: 0 10rpx;
	}

	.question {
		padding-top: 20rpx;

		.question-content {
			height: 30vh;
			color: #5A5A5A;
			font-size: 28rpx;
			line-height: 40rpx;
			background-color: #fff;

			.question-text {
				padding: 20rpx 0;
				word-break: break-all;
			}
		}
	}
}
</style>