(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.markdownitTaskLists = f()}})(function(){var define,module,exports;return (function(){function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s}return e})()({1:[function(require,module,exports){
    // Markdown-it plugin to render GitHub-style task lists; see
    //
    // https://github.com/blog/1375-task-lists-in-gfm-issues-pulls-comments
    // https://github.com/blog/1825-task-lists-in-all-markdown-documents
    
    var disableCheckboxes = true;
    var useLabelWrapper = false;
    var useLabelAfter = false;
    
    module.exports = function(md, options) {
        if (options) {
            disableCheckboxes = !options.enabled;
            useLabelWrapper = !!options.label;
            useLabelAfter = !!options.labelAfter;
        }
    
        md.core.ruler.after('inline', 'github-task-lists', function(state) {
            var tokens = state.tokens;

            // 用于寻找关闭标签的数组
            let tagPaired = (index)=>{
                let targetItem,
                    item = tokens[index],
                    tag = item.tag,
                    level = item.level,
                    targetType = item.type === 'list_item_open' ? 'list_item_close' : 'bullet_list_close';
                for(let i = index,len=tokens.length; i<len; i++){
                    let _item = tokens[i];

                    if(_item.tag === tag && level === _item.level && _item.type === targetType){
                        targetItem = _item;
                        break;
                    };
                };
                return targetItem;

            };

            for (var i = 2; i < tokens.length; i++) {
                if (isTodoItem(tokens, i)) {
                    todoify(tokens[i], state.Token);
                    attrSet(tokens[i-2], 'class', 'task-list-item' + (!disableCheckboxes ? ' enabled' : ''));
                    attrSet(tokens[parentToken(tokens, i-2)], 'class', 'contains-task-list');

                    tagPaired(parentToken(tokens, i-2)).tag = 'todogroup';
                    tokens[parentToken(tokens, i-2)].tag = 'todogroup';

                    tagPaired(i-2).tag = 'todolist';
                    tokens[i-2].tag = 'todolist';
                };
            };
        });
    };
    
    function attrSet(token, name, value) {
        var index = token.attrIndex(name);
        var attr = [name, value];
    
        if (index < 0) {
            token.attrPush(attr);
        } else {
            token.attrs[index] = attr;
        }
    }
    
    function parentToken(tokens, index) {
        var targetLevel = tokens[index].level - 1;
        for (var i = index - 1; i >= 0; i--) {
            if (tokens[i].level === targetLevel) {
                return i;
            }
        }
        return -1;
    }
    
    function isTodoItem(tokens, index) {
        return isInline(tokens[index]) &&
               isParagraph(tokens[index - 1]) &&
               isListItem(tokens[index - 2]) &&
               startsWithTodoMarkdown(tokens[index]);
    }
    
    function todoify(token, TokenConstructor) {
        token.children.unshift(makeCheckbox(token, TokenConstructor));
        token.children[1].content = token.children[1].content.slice(3);
        token.content = token.content.slice(3);
    
        if (useLabelWrapper) {
            if (useLabelAfter) {
                token.children.pop();
    
                // Use large random number as id property of the checkbox.
                var id = 'task-item-' + Math.ceil(Math.random() * (10000 * 1000) - 1000);
                token.children[0].content = token.children[0].content.slice(0, -1) + ' id="' + id + '">';
                token.children.push(afterLabel(token.content, id, TokenConstructor));
            } else {
                token.children.unshift(beginLabel(TokenConstructor));
                token.children.push(endLabel(TokenConstructor));
            }
        }
    }
    
    function makeCheckbox(token, TokenConstructor) {
        var checkbox = new TokenConstructor('html_inline', '', 0);
        var disabledAttr = disableCheckboxes ? ' disabled="" ' : '';
        var value = ' value="' + token.content + '" ';
        if (token.content.indexOf('[ ] ') === 0) {
            checkbox.content = '<checkbox class="h2w__todoCheckbox task-list-item-checkbox"' + disabledAttr + value + '/>';
        } else if (token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0) {
            checkbox.content = '<checkbox class="h2w__todoCheckbox task-list-item-checkbox" checked="true"' + disabledAttr + value + '/>';
        }
        return checkbox;
    }
    
    // these next two functions are kind of hacky; probably should really be a
    // true block-level token with .tag=='label'
    function beginLabel(TokenConstructor) {
        var token = new TokenConstructor('html_inline', '', 0);
        token.content = '<label>';
        return token;
    }
    
    function endLabel(TokenConstructor) {
        var token = new TokenConstructor('html_inline', '', 0);
        token.content = '</label>';
        return token;
    }
    
    function afterLabel(content, id, TokenConstructor) {
        var token = new TokenConstructor('html_inline', '', 0);
        token.content = '<label class="task-list-item-label" for="' + id + '">' + content + '</label>';
        token.attrs = [{for: id}];
        return token;
    }
    
    function isInline(token) { return token.type === 'inline'; }
    function isParagraph(token) { return token.type === 'paragraph_open'; }
    function isListItem(token) { return token.type === 'list_item_open'; }
    
    function startsWithTodoMarkdown(token) {
        // leading whitespace in a list item is already trimmed off by markdown-it
        return token.content.indexOf('[ ] ') === 0 || token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0;
    }
    
    },{}]},{},[1])(1)
    });