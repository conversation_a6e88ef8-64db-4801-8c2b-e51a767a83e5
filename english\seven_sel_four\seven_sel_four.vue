<template>
	<view class="container">
		<template v-if="currentType && questionList.length">
			<cheader :initTimeVal="timeval" ref="cheader" style="height: 44px;"></cheader>
			<view class="content-wrapper">
				<question-content :currentType="currentType"></question-content>
			</view>
			<cover>
				<view class="drawer-container">
					<view class="title-container">
						<scroll-view class="title-list" :scroll-into-view="scrollToTitle" scroll-x>
							<text :id="'title-list-' + (i - 1)" @click="currentChoose = i - 1" :class="[{ done: hasDone(i - 1) }, { sel: currentChoose == i - 1 }]" class="title" v-for="i in questionList.length">第{{ i }}题</text>
						</scroll-view>
					</view>
					<scroll-view class="scroll-content" scroll-y="true">
						<view class="q-container">
							<view class="q-title q-text" v-if="questionTitle">
								<image v-if="questionTitle.includes('http')" :src="questionTitle" style="width: 100%" mode="widthFix"></image>
								<text v-else>{{ questionTitle }}</text>
							</view>
							<view class="q-sel-list">
								<swiper :current="currentChoose" :duration="100" :style="{ height: swiperHeight }" @change="CurrentIndexChange">
									<swiper-item v-for="(item, index) in questionList" :key="index">
										<view class="q-item">
											<view class="q-item-option" v-for="(v, k) in item" @click="setSel(index, k)">
												<view class="q-check-normal" :class='{ "q-check-sel": selected(index, k) }'>{{ k }}</view>
												<text class="q-option-text">{{ v }}</text>
											</view>
										</view>
									</swiper-item>
								</swiper>
							</view>
						</view>
						<view class="bottom-btn">
							<view class="prev" @click="prevAction">上一题</view>
							<view class="next" @click="next">下一题</view>
						</view>
					</scroll-view>
				</view>
			</cover>
		</template>
		<template v-else>
			<view class="loading">加载中...</view>
		</template>
	</view>
</template>

<script>
import questionContent from "../components/content/content.vue"
import cover from "../../components/zhong-cover/zhong-cover.vue"
import cheader from "@/components/c_head/c_head.vue"
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
import { mapState } from "vuex"
export default {
	data() {
		return {
			isShowAll: false,
			swiperHeight: 0,
			scrollToTitle: '',
		};
	},
	mixins: [mixins],
	onShow() {
		console.log('this.currentChoose', this.currentChoose)
	},
	methods: {
		questionListTitleClick(index) {
			this.currentChoose = index
			this.scrollToTitle = `title-list-${this.currentChoose}`;
		},
		CurrentIndexChange(e) {
			this.currentChoose = e.detail.current
			this.scrollToTitle = `title-list-${this.currentChoose}`;
		},
		setSel(index, selOption) {
			if (this.currentChoose < this.questionList.length - 1) {
				this.currentChoose++
			}
			//保存选择结果
			let selData = this.currentSelArr?.filter(item => item.index != index)
			selData.push({
				index,
				selOption
			})
			this.currentSelArr = selData
		},
		selected(index, selOption) {
			if (this.currentSelArr.length > 0) {
				let sel = this.currentSelArr?.find(item => item.index == index)
				if (sel) {
					return sel.selOption == selOption
				}
				return false
			}
			return false
		},
		hasDone(i) {
			if (Array.isArray(this.currentSelArr)) {
				return this.currentSelArr.some(item => item.index == i)
			} else {
				return false
			}
		},
		prevAction() {
			this.save()
			this.prev(false)
		},
		next() {
			this.save()
			//清空答题记录
			this.whetherToNext()
		},
		save() {
			//保存答题结果
			let answer = {
				answer: this.currentSelArr,
				url: [],
				timeval: this.$refs.cheader.currentTime,
			}

			const data = {
				answer: answer,
				index: this.currentIndex,//保存当前 题库的进度
			}
			//当前结果保存本地
			this.$store.commit("professionalConfig/setAnswerList", data);
		}
	},
	computed: {
		...mapState("professionalConfig", ['currentTypes', "currentIndex", 'config', 'currentAnswerList',
			'currentAnswerStatus', "isShowTaskAnswer"
		]),
		currentType() {
			return this.currentTypes[this.currentIndex] || null;
		},
		questionTitle() {
			if (!this.currentType || !this.currentType.material_questions) {
				return "";
			}

			if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length > 0) {
				let questions = JSON.parse(this.currentType.material_questions)
				let result = questions.find(item => item.num == this.currentChoose + 1)

				if (result !== undefined) {
					return result.question
				}
			}
			return ""
		},
		questionList() {
			if (!this.currentType || !this.currentType.material_questions) {
				return [];
			}

			if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length > 0) {
				let questions = JSON.parse(this.currentType.material_questions)
				return questions.map((item, index) => {
					let returnOpt = {}
					Object.keys(item).forEach(key => {
						if (key.indexOf('q') != -1 && key.length == 2) {
							returnOpt[key.slice(1).toUpperCase()] = item[key]
						}
					})
					return returnOpt
				})
			}
			return []
		},
		questionListTitle() {
			return this.questionList.map((item, index) => {
				return {
					name: `第${index + 1}题`,
				}
			})
		}
	},
	watch: {
		questionTitle(newval) {
			console.log(newval)
		},
		currentIndex: {
			handler(newVal) {
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.q-item').boundingClientRect(data => {
						if (data) {
							console.log(data)
							// data.height 就是元素的高度
							console.log(data.height)
							this.swiperHeight = data.height + 'px'
						}
					}).exec();
				})
			},
			immediate: true,
			deep: true
		},
	},
	components: {
		cheader,
		star,
		questionContent,
		cover
	},
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.content-wrapper {
	flex: 1;
	min-height: 200rpx;
	overflow: hidden;
	background: #fff;
}

.drawer-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	background-color: #F7F7F7;
}

.title-container {
	padding: 0 30rpx;
	background-color: #F7F7F7;
	flex-shrink: 0;

	.title-list {
		height: 78rpx;
		white-space: nowrap;
		width: 100%;

		.title {
			width: 25%;
			height: 78rpx;
			display: inline-block;
			color: #777777;
			font-size: 28rpx;
			text-align: center;
			line-height: 78rpx;
		}
	}

	.done {
		color: $main-color !important;
	}

	.sel {
		color: #0A0A0A;
		font-weight: bold;
	}
}

.scroll-content {
	flex: 1;
	overflow-y: auto;
}

.q-container {
	padding: 0 36rpx;
	background-color: #fff;
	margin-top: 30rpx;
}

.q-text {
	line-height: 50rpx;
	color: #5A5A5A;
	font-size: 28rpx;
}

.q-title {
	margin-top: 20rpx;
	margin-bottom: 28rpx;
}

.q-sel-list {
	padding-bottom: 12rpx;

	.q-item {
		margin-bottom: 40rpx;

		.q-item-option {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;

			.q-check-normal {
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}

			.q-option-text {
				width: 590rpx;
				font-size: 24rpx;
			}

			.q-check-sel {
				background: #01997A;
				border: 0;
				color: #fff;
			}
		}
	}
}

.bottom-btn {
	padding: 0 36rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	padding-bottom: 100rpx;
	padding-top: 30rpx;

	view {
		width: 170rpx;
		height: 70rpx;
		border-radius: 16rpx;
		border: 1rpx solid #01997A;
		line-height: 70rpx;
		text-align: center;
		font-size: 28rpx;
	}

	.prev {
		background: #FFFFFF;
		color: $main-color;
	}

	.next {
		background-color: $main-color;
		color: #fff;
	}
}

.loading {
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #666;
	font-size: 28rpx;
}
</style>