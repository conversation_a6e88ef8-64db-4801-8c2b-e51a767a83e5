<template>
	<view class="container">
		<view class="form-container">
			<view class="form-item">
				<text>收货人</text>
				<u--input border="none" placeholder="请输入收货人姓名" v-model="formData.recieveName"></u--input>
			</view>
			<view class="form-item">
				<text>联系电话</text>
				<u--input border="none" type="digit" placeholder="请输入联系电话" v-model="formData.mobile"></u--input>
			</view>
			<view class="form-item">
				<text>所在地区</text>
				<picker  id="picker" mode="multiSelector" :range="range" :value="value"  @columnchange="columnchange"
					@cancel="pickerCancel"
					@confirm="confirm"
					>
					<view class="sel-addr">
						<u--input border="none" placeholder="请选择所在地区" readonly v-model="formData.area"></u--input>
						<u-icon name="arrow-right" size="38"></u-icon>
					</view>
					
				</picker>
				
			</view>

			<view class="form-item">
				<text class="top-align">详细地址</text>
				<u--textarea class="rich-text" v-model="formData.detail" placeholder="请输入详细地址" autoHeight
					border="none"></u--textarea>
			</view>
			<view class="form-item">
				<text>设为默认地址</text>
				<u-switch v-model="formData.isDefault" :activeValue="actval"  :inactiveValue="inactval" inactiveColor="#fff"  activeColor="#009c7b" size="40"></u-switch>
			</view>

		</view>
		<view class="btn-container">
			<view class="btn" @click="submit()">
				确认
			</view>
		</view>
	</view>
</template>

<script>
	import {
		area
	} from '../lisenh-cityPicker/area.js'
	export default {
		data() {
			return {
				formData: {
					recieveName: '',
					id:0,
					area:"",
					detail:'',
					mobile:'',
					isDefault:0,
				},
				selected: '请选择',
				id:0,
				range: [
					[''],
					[''],
					['']
				],
				actval:1,
				inactval:0,
				provinceCodes: [],
				cityCodes: [],
				value: [0, 0, 0],
				province:'',
				city:'',
				area:'',
			};
		},
		watch:{
			area(newval){
				if (newval!==""){
					this.formData.area = `${this.province}/${this.city}/${this.area}`
				}else{
					this.formData.area = ""
				}
			}
		},
		methods: {
			submit() {
				if(this.formData.area == "") {
					uni.tip('请选择地址')
					return false;
				}
				if(this.formData.detail == "") {
					uni.tip('请地址')
					return false;
				}
				if(this.formData.mobile == "") {
					uni.tip('收件人电话不能为空')
					return false;
				}
				if(this.formData.recieveName == "") {
					uni.tip('收件人姓名不能为空')
					return false;
				}
				const  data  = uni.$u.http.post('/order/addAddress',this.formData).then(res=>{
					uni.navigateBack()
				});
			},
			pickerCancel() {
				console.log('pickerCancel')
			},
			confirm(e){
				console.log(e)
			},
			columnchange: function(e) {
				this.value[e.detail.column] = e.detail.value
				if (0 == e.detail.column) {
					let provinceCode = this.provinceCodes[e.detail.value - 1]
					this.range[1] = ['']
					this.range[2] = ['']
					let cities = ['']
					this.cityCodes = []
					for (let cityCode in area.city_list) {
						if (Number(cityCode) >= Number(provinceCode) && Number(cityCode) <= Number(provinceCode) +
							9900) {
							cities.push(area.city_list[cityCode])
							this.cityCodes.push(cityCode)
						}
					}
					this.range[1] = cities
					this.value.splice(1, 1, 0)
					this.value.splice(2, 1, 0)
				} else if (1 == e.detail.column) {
					this.value[2] = 0
					let cityCode = this.cityCodes[e.detail.value - 1]
					this.range[2] = ['']
					let counties = ['']
					for (let countyCode in area.county_list) {
						if (Number(countyCode) >= Number(cityCode) && Number(countyCode) <= Number(cityCode) + 99) {
							counties.push(area.county_list[countyCode])
						}
					}
					this.range[2] = counties
					this.value.splice(2, 1, 0)
				}
				this.$forceUpdate()
				if (this.range[2][this.value[2]]) {
					this.selected = this.range[2][this.value[2]]
					this.area = this.range[2][this.value[2]]
				} else if (this.range[1][this.value[1]]) {
					this.selected = this.range[1][this.value[1]]
					this.city = this.range[1][this.value[1]]
					this.area = ""
				} else if (this.range[0][this.value[0]]) {
					this.selected = this.range[0][this.value[0]]
					this.province = this.range[0][this.value[0]]
					this.city = ""
					this.area = ""
				}
		
				
			},
			getAddress(){
				let that = this;
				const  data  = uni.$u.http.get('/order/getAddress',{params:{id:this.id}}).then(rest=>{
					let res = rest.data
					console.log(res)
					that.formData.recieveName = res.name;
					that.formData.area=res.province_name+'/'+res.city_name+'/'+res.region_name;
					that.formData.detail=res.detail;
					that.formData.mobile=res.phone;
					that.formData.isDefault = res.is_default;
					that.formData.id = res.address_id
				});
				
			}
		},
		onLoad: function(options) {
			for (let provinceCode in area.province_list) {
				this.range[0].push(area.province_list[provinceCode])
				this.provinceCodes.push(provinceCode)
			}
			
			if(options.id) {
				if(options.id > 0) {
					this.id = options.id;
					this.getAddress()
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	page{
		background-color: $container-bg-color;
	}
	.container {
		box-sizing: border-box !important;
		padding-top: 30rpx;
		background: #eee;
		min-height: 1300rpx;
		overflow: hidden;
		.sel-addr{
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		.form-container {
			padding: 0 30rpx;

			.form-item {
				padding: 20rpx 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background-color: #fff;
				border-radius: 12rpx;
				#picker{
					flex: 1;
				}
				text {
					min-width: 120rpx;
					color: #060606;
					font-size: 28rpx;
					margin-right: 60rpx;
				}
				

				margin-bottom: 30rpx;

				.rich-text {
					min-height: 120rpx;
				}
				.top-align{
					min-height: 120rpx;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
		.btn-container{
			position: fixed;
			bottom: 0;
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			.btn{
				width: 400rpx;
				height: 80rpx;
				background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
				border-radius: 40rpx;
				color: #fff;
				font-size: 30rpx;
				line-height: 80rpx;
				text-align: center;
			}
		}
	}
</style>