/**
 * 获取banner信息
 * @return Array|bool
 */
export  const  getBannerList = async(type)=>{
	const ret = await uni.$u.http.get("/exam_point/getBanner/type/"+type)
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取分类信息和推荐列表
 * @return Array|bool
 */
export  const  getTopCateListAndRecommendList = async()=>{
	const ret = await uni.$u.http.get("/exam_point/getListenCategory", {params:{pid:0}})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取子分类列表
 * @return Array|bool
 */
export  const  getSubCatList = async(pid)=>{
	const ret = await uni.$u.http.get("/exam_point/getChildCategory", {params:{pid}})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取 分类下的 试题列表
 */
export  const  getPointListByCateId = async(params)=>{
	const ret = await uni.$u.http.get(`/exam_point/getPointListByCateId`, {params})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 提交收藏
 */
export  const  setStar = async(data)=>{
	const ret = await uni.$u.http.post('/course/userCollectStudyPoints', data)
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 取消收藏
 */
export  const  cancelStar = async(data)=>{
	const ret = await uni.$u.http.post('/course/userCancelCollectStudyPoints', data)
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 添加播放次数
 */
export  const  addListenCount = async(data)=>{
	const ret = await uni.$u.http.post(`/exam_point/addListenCount`, data)
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取顶级分类
 */
export  const  getTopCate = async()=>{
	const ret = await uni.$u.http.get('/exam_point/getTopCategory')
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取统计数据
 */
export  const getStatisticsData = async (params)=>{
	const ret = await uni.$u.http.get("/Course/getUserStatistics", {params})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取收藏列表
 */
export  const getAnyStarList = async (params)=>{
	const ret = await uni.$u.http.get("/exam_point/collectionList",{params})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取随身学顶级分类及推荐信息
 * @return Array|bool
 */
export  const  getStudyCateList = async()=>{
	const ret = await uni.$u.http.get("/index/getFirstCate")
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
 /*
 * 获取随身学二级分类及三级分类
 * @return Array|bool
 */
export  const  getStudySecondCateList = async(data)=>{
	const ret = await uni.$u.http.get("/index/getSecondCate",{params:data})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
