<template>
	<view class="container">
		<view class="head">
			<view class="title">
				<view class="green-block">
					
				</view>
				<view class="title">
					今日任务
				</view>
			</view>
			<view class="opt">
				<text class="stu-note" @click="toNote">
					听课笔记
				</text>
		
				<image @click="toHistory" class="opt-icon" src="../../static/png/history.png" ></image>
				<image @click="toStudyHistory"  class="opt-icon" src="../../static/png/note.png" ></image>
				<image class="opt-icon" src="../../static/png/date.png" @click="selectDate"></image>
			</view>
		</view>
		
		
		<view class="tabs">
			<u-tabs
					:current="courseCurrent"
			        :list="list"
			        lineWidth="50"
					lineHeight="8"
			        lineColor="#01997A"
					@click="changeSubject"
			        :activeStyle="{
			           
						 color: '#01997A',
						  fontWeight: 'bold',
			        }"
			        :inactiveStyle="{
			             color: '#777777',
						  fontWeight: 'bold',
			        }"
			        itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
			    >
			    </u-tabs>
		</view>
		<view class="desc" v-if="courseList.length > 0">
			<view class="info">
				<text class="course_text">{{sel_name}}</text>
				<view class="complate-bar">
					<u-line-progress :percentage="taskList[0].total_ret ?(parseInt(taskList[0].total_ret) > 95 ? 100 : parseInt(taskList[0].total_ret)):0" height="18"
						activeColor="#10C19D"
						inactiveColor="#F39898">
					</u-line-progress>
				</view>
				<text class="rate">完成率{{taskList[0].total_ret ?(parseInt(taskList[0].total_ret) > 95 ? 100 : (parseInt(taskList[0].total_ret)) ):0}}%</text>
			</view>
			
			<view class="lesson-list">
				<view  class="lesson-info" v-for="(item,index) in courseList" :key="index">
					<view class="lesson-title">
						{{item.title}}
					</view>
					<view class="show-text">
						<view :class="['btn',taskRet != '' && taskRet[item.id].pre > 97?'btn2':'']" @click="toListen(taskList[0].id,item.id,item.video_id,item)">
							<text v-if="taskRet!=''  &&  taskRet[item.id].pre > 97">已完成</text>
							<text v-if="taskRet != '' && taskRet[item.id].pre > 0 && taskRet[item.id].pre< 97">继续听课</text>
							<text v-if="taskRet== '' || taskRet[item.id].pre == 0">进入课程</text>
						</view>
						<text class="course_ddd" v-if="taskRet != '' && taskRet[item.id].pre > 0 && taskRet[item.id].pre< 97">已学{{parseInt(taskRet[item.id].pre)}}%</text>
						
					</view>
				</view>
			</view>

		</view>
		<view class="desc" v-else>
			<view class="info empty" >
				<u-empty  mode="list" text="暂无任务"
					textSize="24">
				</u-empty>
			</view>
		</view>
		<!-- 最近直播 -->
		<view class="all-course-h1" v-if='false'>
			<view class="green-block-big">
				
			</view>
			<text>最近直播</text>
		</view>
		
		<view class="live-broadcast"  v-if='false'>
			<view class="broadcast-info">
				<view class="broadcast-title">
					政治精讲最新时政题型总结
				</view>
				<view class="broadcast-detail">
					<text>孙老师</text>
					<text>2025年12月12日20:00-21:00</text>
					<text>预约直播</text>
				</view>
			</view>
			
		</view>
		
		<!-- <view class="live-broadcast">
			<view class="broadcast-info">
				<view class="broadcast-title">
					政治精讲最新时政题型总结
				</view>
				<view class="broadcast-detail">
					<text>孙老师</text>
					<text>2025年12月12日20:00-21:00</text>
					<text>预约直播</text>
				</view>
			</view>
			
		</view> -->
		
		
		
		
		<view class="all-course-h1">
			<view class="green-block-big">
				
			</view>
			<text>全部课程</text>
		</view>
		
		<!-- 授课信息 -->
		<view class="course-list" v-if="onlineCourse.length > 0" v-for="(item,index) in onlineCourse" :key="item.id">
			<view @click="toOtherListen(item.id)">				
				<view class="top">
					<view class="info">
						<text>{{item.title.length>30?item.title.substring(0,29)+'...':item.title}}</text>
					
					</view>
					<view class="btn-test">
						<button class="btn" >听课</button>
					</view>
				</view>
				<view class="mid">
					<view class="tag">
						{{item.cate_text}}
					</view>
					<text class="date">{{item.start_time | happenTimeFun}}-{{item.end_time | happenTimeFun}} | 共{{item.time_hour}}课时</text>
					
				</view>
				<view class="bottom">
					<view class="teacher-list">
						<view class="teacher-info" v-for="te_item in item.teacher_list" :key="te_item.image_text">
							<image class="avatar" :src="te_item.image_text"/>
							<text>{{te_item.name}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="all-course-h1"  v-if="downCourse.length > 0">
			<view class="green-block-big">
				
			</view>
			<text>过期课程</text>
		</view>
		
		
		<!-- 授课信息 -->
		<view class="course-list"  v-if="downCourse.length > 0" v-for="(item,index) in downCourse" :key="index">
			
			<view class="top">
				<view class="info">
					<text>{{item.title.length>30?item.title.substring(0,29)+'...':item.title}}</text>
				</view>
				<view class="btn-test">
					<!-- <button class="btn">听课</button> -->
				</view>
			</view>
			<view class="mid">
				<view class="tag">
					{{item.cate_text}}
				</view>
				<text class="date">{{item.start_time | happenTimeFun}}-{{item.end_time | happenTimeFun}} | 共{{item.time_hour}}课时</text>
				
			</view>
			<view class="bottom">
				<view class="teacher-list">
					<view class="teacher-info" v-for="te_item in item.teacher_list" :key="te_item.image_text">
						<image class="avatar" :src="te_item.image_text"/>
						<text>{{te_item.name}}</text>
					</view>
				</view>
			</view>
		</view>
		<uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true"  :insert="false"  @confirm="confirm" @close="closeDate"/>
	</view>
</template>

<script>
	import {mapState} from "vuex"
	export default {
		data() {
			const d = new Date()
			const year = d.getFullYear()
			let month = d.getMonth() + 1
			month = month < 10 ? `0${month}` : month
			const date = d.getDate()
			return {
				courseCurrent:0,
				calendarShow:false,
				customTextDefaultDate: [`${year}-${month}-${date}`],
				list:[
					
				],
				date:`${year}-${month}-${date}`,
				sel_name:'',
				courseList:[],
				courseIds:'',
				onlineCourse:[],
				downCourse:[],
				taskList :'',
				taskRet:'',
				arrText :{'英语': 0, '政治' : 1, '数学' : 2, '专业课': 3}
			};
		},
		filters : {
			happenTimeFun(num){//时间戳数据处理
				let date = new Date(num*1000);
				 //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;//月补0
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;//天补0
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;//小时补0
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;//分钟补0
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;//秒补0
				return y + '-' + MM + '-' + d ;
			},
		},
		watch: {
			userInfo: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getCourseType();
					}
				},
				deep: true
			}
		},
		computed:{
			...mapState('user',['userInfo']),
		},
		onShow(){
			this.getCourseType();
			//this.courseCurrent = 0
			this.$nextTick(()=>{    //（重要2）
				this.courseCurrent = 0;
				
			});
		},
		methods:{
			selectDate(){
				this.$refs.calendar.open();
			},
			closeDate() {
				
			},
			// 获取历史最后一次观看
			toHistory(){
				uni.$u.http.get('/task/getHistory',{params:{type:this.arrText[this.sel_name]}}).then(rest=>{
					if(rest.code == 1) {
						let tmpData = rest.data
						uni.navigateTo({
							url:'/choosecoursepkg/study/study?course_type='+this.arrText[this.sel_name]+'&taskId='+tmpData.id+'&chapter_id='+tmpData.chapter_id+'&video_id='+tmpData.video_id
						})
					}
				})
			},
			toStudyHistory() {
				uni.navigateTo({
					url:'/subpkg/study_history/study_history?type=course'
				})
			},			
			toListen(taskId,chapter_id,video_id){
				
				uni.navigateTo({
					url:'/choosecoursepkg/study/study?course_type='+this.taskList[0].type+'&taskId='+taskId+'&chapter_id='+chapter_id+'&video_id='+video_id
				})
			},
			toOtherListen(goods_id){
				uni.navigateTo({
					url:'/choosecoursepkg/courseDetail/courseDetail?goods_id='+goods_id
				})
			},
			changeSubject(e) {
				
				this.courseCurrent = e.index
				this.sel_name = e.name;
				this.getTaskList()
			},
			toNote(){
				uni.navigateTo({
					url:"/subpkg/note/note"
				})
			},
			confirm(e) {
				this.date = e.fulldate;
				this.getTaskList();
				this.calendarShow = false;
			},
			getCourseType() {
				console.log(this.courseCurrent)
				let that = this;
				const  data  = uni.$u.http.get('/task/getUserCourse').then(rest=>{
					let res = rest.data.course_text
					let caterest = rest.data.cate_text		
					that.arrText  = rest.data.courseArrText
					if(caterest.length > 0) {
						if(res.length > 0) {
							that.list =  res.map(item=>{
								return {name:item.name}
							})							
							that.sel_name = that.list[0].name
						
						}
						that.getTaskList();
						that.courseIds = rest.data.course_id
						if(that.courseIds != '') {
							that.getGoodsList();
						}
					}
					
					
				});
			},
			async getGoodsList(){
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getGoodsList',{params:{ids:that.courseIds,'page_size':20}}).then(res=>{
					that.onlineCourse  = res.data.onlineCourse;
					that.downCourse  = res.data.downCourse;
				})
			},
			async getTaskList (){
				let that = this;
				that.courseList =  [];
				const  data  = await  uni.$u.http.get('/task/getUserTask',{params:{type:that.sel_name,qtype:'course',date:that.date}}).then(res=>{
					if(res.code == 1){
						let tmp_data = res.data;
						that.taskList = tmp_data;
						let tmp_course = [];
						if(tmp_data.alone_ret != undefined &&  tmp_data.alone_ret != '') {
							that.taskRet =  JSON.parse(tmp_data[0].alone_ret)
						}
						if(res.data.length > 0) {
							that.courseList = res.data[0].task_text

						} else {
							that.courseList =  [];
						}
					}
				
				});
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	page{
		background-color: #F6F7FB;
	}
.container{
	min-height: 100vh;
	padding: 0 30rpx;
	background-color: #F6F7FB;
	.head{
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		justify-content: space-between;
		.title{
			display: flex;
			align-items: center;
			justify-content: space-between;
			.green-block{
				width: 18rpx;
				height: 38rpx;
				background-color: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;
			}
			.title{
				margin-left: 10rpx;
				color: #1F1F27;
				font-size: 32rpx;
			}
		}
		.opt{
			display: flex;
			align-items: center;
			justify-content: space-between;
			.stu-note{
				padding: 8rpx;
				text-align: center;
				font-size: 28rpx;
				font-weight: bold;
				color: #01997A;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				border: 3rpx solid #01997A;
			}
			.opt-icon{
				width: 50rpx;
				height: 50rpx;
				padding-left: 30rpx;
				&:first-child{
					
				}
			}
		}
	}
	.desc{
		background-color: #fff;
		border-radius: 20rpx;
		margin-top: 24rpx;
		padding: 36rpx 0;
		.info{
			padding: 0 30rpx;
			margin-bottom: 30rpx;
			color: #4A4A4C;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.complate-bar{
				width: 320rpx;
			}
			.rate{
				color: #878787;
				font-size: 26rpx;
			}
			.course_text{
				margin-right:20rpx;
			}
		}
		.empty{
			justify-content: center;
		}
		.lesson-list{
			.lesson-info{
				padding: 0 30rpx;
				margin-bottom: 40rpx;
				color: #4A4A4C;
				font-size: 28rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.lesson-title{
					font-size: 28rpx;
					font-weight: bold;
					color: #4A4A4C;
				}
				.btn{
					width: 130rpx;
					height: 44rpx;
					line-height: 44rpx;
					font-size: 24rpx;
					color: #fff;
					background-color: #01997A;
					text-align: center;
					border-radius: 365rpx;
					position: relative;
					.tip{
						position: absolute;
						bottom: -40rpx;
						left: 50%;
						transform: translateX(-50%);
						font-size: 20rpx;
						color: #A4A4A4;
						width: 72rpx;
					}
				}
				.btn2 {
					background:linear-gradient( 90deg, #F39898 0%, #EE7878 100%);
				}
				.show-text{
					position: relative;
				}
				.course_ddd{
					position: absolute;
					top:40rpx;
					margin-left:16rpx;
					font-size: 22rpx;
					text-align: center;
					padding: 10rpx;
				}
				.done{
					background-color: #F39898;
					
				}
			}
		}

	}
	
	.live-broadcast{
		margin-bottom: 36rpx;
		box-sizing: border-box;
		background: linear-gradient( to bottom, #CDF3E7 0%, #FFFFFF 100%);
		border-radius: 20rpx;
		padding:28rpx;
			.broadcast-title{
				color: #4A4A4C;
				font-size: 30rpx;
				font-weight: bold;
			}
			.broadcast-detail{
				box-sizing: border-box;
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				text{
					color: #A2A2A2;
					font-size: 24rpx;
					&:nth-child(3){
						height: 44rpx;
						padding: 0 20rpx;
						background: linear-gradient( 90deg, #10C19D 0%, #14B494 63%, #01997A 100%);
						border-radius: 40rpx;
						line-height: 44rpx;
						text-align: center;
						font-size: 24rpx;
						font-weight: bold;
						color: #fff;
					}
				}	
			}
	}
	
	.all-course-h1{
		 background-color:#F6F7FB;
			 padding: 32rpx 0;
			 display: flex;
			 align-items: center;
			 justify-content: flex-start;
			 .green-block-big{
				
				 width: 18rpx;
				 height: 37rpx;
				 background: #01997A;
				 border-radius: 0rpx 10rpx 0rpx 10rpx;
	
			 }
			 text{
				 padding-left: 10rpx;
			 					 width: 128rpx;
			 					 height: 45rpx;
			 					 font-weight: bold;
			 					 font-size: 32rpx;
			 					 color: #1F1F27;
			 }
	}
	
	
	.course-list{
		margin-bottom: 22rpx;
		background-color: #fff;
		border-radius: 16rpx;
		.top{
			padding:16rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.info{
				font-size: 26rpx;
				font-weight: bold;
				color: $uni-text-main-black;
				display: flex;
				flex-direction: column;
			}
			.btn-test{
				width: 120rpx;
				height: 50rpx;
				.btn{
					height: 100%;
					width: 100%;
					text-align: center;
					line-height: 50rpx;
					border-radius: 40rpx;
					background-color: $main-color;
					font-size: 24rpx;
					color: #fff;
				}
			}
		}
	
		.mid{
			margin-top: 6rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			padding-left: 36rpx;
			.tag{
				padding: 0 8rpx;
				height: 48rpx;
				background-color: #EEFAF6;
				color: $main-color;
				line-height: 48rpx;
				font-size: 22rpx;
				font-weight: bold;
				border-radius: 10rpx;
			}
			.date{
				margin-left: 8rpx;
				font-size: 22rpx;
				color: #A4A4A4;
			}
		}
	
		.bottom{
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.teacher-list{
				padding-left: 36rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding-bottom: 20rpx;
				.teacher-info{
					margin-right: 24rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					color: #818181;
					.avatar{
						width: 60rpx;
						height: 60rpx;
						border-radius: 100%;
						margin-bottom: 2rpx;
					}
					
				}
			}
		
			.money{
				color: #E16965;
				font-size: 38rpx;
				margin-right: 34rpx;
			}
		}
	
	}
}
</style>
