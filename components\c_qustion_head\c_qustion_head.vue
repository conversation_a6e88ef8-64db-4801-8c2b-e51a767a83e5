<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
			     <view class="u-nav-slot" slot="left">
			                <u-icon 
			                    name="arrow-left"
			                    size="38"
								@click="back"
			                ></u-icon>
			    </view>
				
				<view  slot="center">
		
							<slot name="hcenter"></slot>  
				</view>
			</u-navbar>
		
			
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>1</text>
					<text>/20</text>
					
				</view>
		
					<slot name="opt"></slot>
			</view>
		
		
		<view class="q-type">
			{{question.subtitle}}
		</view>
		<slot name="qcontent"></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name:"c_qustion_head",
		data() {
			return {
				
			};
		},
		props:{
			question:{
				type:Object,
				default:{
	
				}
			}
		},
		methods:{
			back(){
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
	
	}
}
</style>