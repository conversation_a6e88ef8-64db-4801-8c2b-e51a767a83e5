<template>
	<view class="container">
		<view class="tabs">
			<u-tabs  :list="list" lineWidth="50" lineHeight="4" lineColor="#01997A" :activeStyle="{
			           
						 color: '#01997A',
						
			        }" :inactiveStyle="{
			             color: '#777777',
						
			        }" itemStyle="padding-left: 20px;width:33%; padding-right: 20px; height: 40px;"
					@click="change"
					>
			</u-tabs>
		</view>
		<view class="content">
			<template v-if="orderInfo.length" v-for="(item,index) in orderInfo">
				<view class="order-list" @click="orderDetail(item.id)">
					 <view class="top-info">
						<text>订单编号：{{item.order_no}}</text>
						<text>{{item.is_pay==1?"已支付":"未支付"}}</text>
					 </view>
					<view class="order-detail">
						<view class="title u-border-top" >
							{{item.goods.title}}
						</view>
						<!-- 授课信息 -->
						<view class="course-list u-border-bottom">
						
							<view class="mid">
								<view class="tag">
									{{item.cate_text}}
								</view>
								<text class="date" v-if="item.goods.time_type == 1">{{item.goods.start_time_text}}-{{item.goods.end_time_text}} | 共{{item.goods.time_hour}}课时</text>
								<text class="date" v-else>有效期 <text class="time_day">{{item.goods.time_day}}</text>年 | 共{{item.goods.time_hour}}课时</text>		
							</view>
						
							<view class="bottom">
								<view class="teacher-list">
									<view class="teacher-info" v-for="(row,tindex) in item.teacher_list">
										<image class="avatar" :src="row.image_text" />
										<text>{{row.name}}</text>
									</view>
								</view>
								<view class="course-money">
									￥{{item.goods.price}}
								</view>
							</view>
						</view>
						
						<view class="real-pay">
							实际支付  <text>￥{{item.price}}</text>
						</view>
					
					</view>
				</view>
			</template>
		</view>

		
	</view>
</template>

<script>
	import {mapState} from "vuex"
	export default {
		data() {
			return {
				list: [{
						name: '全部'
					},
					{
						name: '待支付',
					},
					{
						name: '已支付'
					}
				],
				orderInfo:[],
				total:0,
				page:1
			};
		},
		watch: {
			userInfo: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getOrderList();
					}
				},
				deep: true
			}
		},
		computed:{
			...mapState('user',['userInfo']),
		},
		onLoad(){
			this.getOrderList();
		},
		methods:{
			change(item){
				this.currentIndex = item.index
				this.orderInfo  =  [];
				this.getOrderList();
			},
			orderDetail(orderId) {
				uni.navigateTo({
					url:'/choosecoursepkg/order_detail/order_detail?type=2&order_id='+orderId
				})
			},
			async getOrderList() {
				let  that  = this;
				let ret =  await uni.$u.http.get('/order/getOrderList',{params:{type:this.currentIndex,page:this.page }}).then(rest=>{
					let res = rest.data
					that.orderInfo = [...that.orderInfo,...res.data];
					that.total = res.total
					
				})
			},
			onPullDownRefresh(){
				this.page = 1;  
				this.orderInfo = []
				this.total = 0
				this.getOrderList(uni.stopPullDownRefresh)
				
			},
		
			onReachBottom(){
				if(this.orderInfo.length >= this.total){
					return uni.$u.toast("没有更多数据")
				} 
				this.page +=1;
				this.getOrderList();
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	 background-color: $container-bg-color;
	 min-height: 100vh;
	.tabs {
		background-color: #fff;
		width: 100%;
		margin-top: 16rpx;
	
	}
	.content{
		padding: 0 30rpx;
		.order-list{
		
			padding-top: 30rpx;
			padding-bottom: 14rpx;
			margin-top: 36rpx;
			background-color: #fff;
			border-radius: 20rpx;
			.top-info{
				padding: 0 28rpx;
				padding-bottom: 13rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #A2A2A2;
				font-size: 24rpx;
			}
			.order-detail{
				.title{
					padding: 20rpx 28rpx;
					font-weight: bold;
					font-size: 26rpx;
					color: #060606;
					line-height: 30rpx;
				}
				
				.course-list {
					padding: 0 28rpx;
					margin-bottom: 22rpx;
					background-color: #fff;
		
					width: 100%;
				
					.mid {
						margin-top: 6rpx;
						display: flex;
						align-items: center;
						justify-content: flex-start;
				
						.tag {
							padding: 0 8rpx;
							height: 48rpx;
							background-color: #EEFAF6;
							color: $main-color;
							line-height: 48rpx;
							font-size: 22rpx;
							font-weight: bold;
							border-radius: 10rpx;
						}
				
						.date {
							margin-left: 8rpx;
							font-size: 22rpx;
							color: #A4A4A4;
							.time_day {
								font-weight: 700;
								font-size: 24rpx;
								
							}
						}
					}
				
					.bottom {
						margin-top: 20rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
				
						.teacher-list {
							padding-left: 36rpx;
							display: flex;
							align-items: center;
							justify-content: flex-start;
							padding-bottom: 20rpx;
				
							.teacher-info {
								margin-right: 24rpx;
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								font-size: 22rpx;
								color: #818181;
				
								.avatar {
									width: 60rpx;
									height: 60rpx;
									border-radius: 100%;
									margin-bottom: 2rpx;
								}
				
							}
						}
				
						.course-money {
							color: #4C5370;
							font-size: 30rpx;
							margin-right: 34rpx;
						}
					}
				
				}
				
			.real-pay{
				padding: 0 28rpx;
				font-size: 24rpx;
				color: #A4A4A4;
				text-align: right;
				text{
					 font-weight: bold;
					color: #E16965;
					font-size: 32rpx;
					
				}
			}
			}
		}
	}

}
</style>
