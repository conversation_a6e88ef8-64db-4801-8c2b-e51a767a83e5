<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left" @click="goBack()">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
			            <u-icon
							color="#009c7b"
			                name="clock"
			                size="38"
			            ></u-icon>
						<text>耗时{{useTime}}</text>
			</view>
		</u-navbar>
		
		<view class="content">
			<view class="q-type">
				单词带背
			</view>
			
			<view class="question">
				<text class="title">一、请写出下列单词的词性和意思</text>
				<view class="words-list">
					<view class="word" v-for="(item, i) in wordData">
						<text>{{i+1}}.{{item.word}}</text>
						<text>
							
							<text class="order-num">{{i+1}}</text>
						</text>
					</view>
				</view>
			</view>
		</view>
		
		
		
		<view class="answer">
			<view class="title">
				我的答案
			</view>
			<!-- <view class="a-content">
					<u--textarea  height="290rpx" disabled  v-model="taskDetail.answer" placeholder="请输入答案"  ></u--textarea>
			</view> -->
			
			<!-- <view class="img-list" v-if="taskDetail.imageList">
				<image style="width: 100%;" :src="item" mode="widthFix" v-for="item in taskDetail.imageList"></image>
				
			</view> -->
			<view class="analysis">
				<view class="title">
					<user-title :title='"正确答案"' :bgcolor="'#fff'"></user-title>
					<view class="qanswer">
						<view class="word" v-for="(item, i) in wordData">
							<view class="wlist">
								<text class="word_title">{{i+1}}.{{item.word}}</text>								
								<text class="pos_text">&nbsp;&nbsp;&nbsp;&nbsp;{{item.pos_text}}</text>
								<view class="form_word">
										来自({{item.day_unit.book_id == 1?'必考词':'基础性'}}{{item.day_unit.name}})
								</view>
							</view>
							<view>								
								<view class="meaning" v-html="item.simple_meaning"></view>
								<text>eg:</text>
								<view class="example" v-html="item.example"></view>
							</view>
						</view>
					</view>
				</view>
			
			</view>
			
		</view>
		
		
		
		<view class="answer">
			<view class="title">
				订正答案
			</view>
			<view class="a-content">
					<u--textarea  height="290rpx"  v-model="ret_answer" placeholder="请输入答案"  ></u--textarea>
			</view>
			
			<view class="q-upload">
					<u-upload 
					 height="200rpx"
					 width="200rpx"
					 uploadText="上传照片"
						:fileList="fileList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="1"
						multiple
						:maxCount="10"
					></u-upload>
			</view>
			
		</view>
		
		<view class="bottom-btn" @click="submitTask">
			提交
		</view>
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	import userTitle from "../../components/user_title/user_title.vue"
	export default {
		data() {
			return {
				taskId:'',
				unitId:'',
				wordData:[],
				fileList:[],
				ret_answer:'',
				taskDetail:'',
				useTime:''
				
			};
		},
		components:{
			userTitle
		},
		onLoad(options) {
			this.taskId = options.task_id
			this.getWordDetail();
			this.getTaskDetail();
		},
		methods:{
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			submitTask() {
				let pData  = {
					ret_answer:this.ret_answer,
					ret_image:this.fileList,
					about_id:this.taskId,
					type:'word'
				}
				let  data  = uni.$u.http.post('/task/wordModify',pData).then(res=>{
					if(res.code == 1) {
						uni.$u.toast('操作成功')
					} else {
						uni.$u.toast(res.msg)
					}
				});
				
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getUnitWord',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data;
				})
			},
			async getTaskDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getTaskDetail',{params:{task_id:that.taskId}}).then(res=>{
					that.taskDetail  = res.data;
					console.log(that.taskDetail)
					let tmp_min = parseInt(that.taskDetail.useTime/60);
					let tmp_second = that.taskDetail.useTime%60;
					that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
					that.ret_answer  = ''
					this.fileList = []
				})
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						console.log(err);
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)

					}
				}
				this.loading = false
			},
		},

	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	padding-bottom: 20rpx;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.analysis{
		.word{
			margin-top: 10rpx;;
			.word_title{
				font-weight: 700;
				font-size: 36rpx;
				color:#000;
			}
			.wlist{
				.pos_text{
					margin-left: 40rpx;;
				}
				.form_word{
					font-size: 24rpx;
				}
			}
			.meaning{
				font-size: 26rpx;
			}
			.example{
				font-size: 24rpx;
			}
		}
	}
	.content{
		background-color: #fff;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			color: #5A5A5A;
			font-size: 28rpx;
			.title{
				width: 100%;
				margin-top: 16rpx;
			}
			.words-list{
				.word{
					margin-top: 24rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					
					text{
						&:last-child{
							width: 66rpx;
							line-height: 38rpx;
							height: 38rpx;
							border-bottom: 3rpx solid #5A5A5A;
							margin-left: 10rpx;
							text-align: center;
							
							.order-num{
								
								display: inline-block;
								height: 33rpx;
								width: 33rpx;
								border-radius: 50%;
								background-color: #01997A;
								text-align: center;
								line-height: 33rpx;
								color: #fff;
							}
						}
					}
					
				}
			}
		}
	}
	
	
	
	.answer{
		background-color: #fff;
		padding: 37rpx 0;
		margin-bottom: 36rpx;
		
		.title{
			color: 28rpx;
			color: #777777;
			font-weight: bold;
		}
		.a-content{
			margin-top: 12rpx;
			margin-bottom: 36rpx;
		}
		
	}
	
	.bottom-btn{
		margin: 0 auto;
		width: 400rpx;
		height: 80rpx;
		color: #fff;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
	}
}
</style>
