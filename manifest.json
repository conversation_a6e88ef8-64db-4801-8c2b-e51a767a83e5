{
    "name" : "研趣在线",
    "appid" : "__UNI__7D420D0",
    "description" : "研趣在线学员听课、做题、服务、学习数据分析平台",
    "versionName" : "1.0.0",
    "versionCode" : 1,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "Payment" : {},
            "VideoPlayer" : {},
            "Webview-x5" : {},
            "Share" : {},
            "OAuth" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "targetSdkVersion" : 30
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:study.yanqux.com" ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "android" ],
                        "appid" : "wxd6ca6513a7824adc",
                        "UniversalLinks" : "https://study.yanqux.com/uni-universallinks/__UNI__7D420D0/"
                    }
                },
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxd6ca6513a7824adc",
                        "UniversalLinks" : "https://study.yanqux.com/uni-universallinks/__UNI__7D420D0/"
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxd6ca6513a7824adc",
                        "UniversalLinks" : "https://study.yanqux.com/uni-universallinks/__UNI__7D420D0/"
                    },
                    "univerify" : {}
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            },
            "screenOrientation" : [
                "portrait-primary",
                "landscape-primary",
                "portrait-secondary",
                "landscape-secondary"
            ]
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx1373c1a3f614f1e4",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "es6" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "locale" : "auto",
    "_spaceID" : "mp-5df20212-b0ba-4f75-99ce-12acf135fc96"
}
/* 应用发布信息 *//* android打包配置 *//* ios打包配置 *//* SDK配置 */

