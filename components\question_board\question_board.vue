<template>
	<view class="container">
		<view class="item">
			<view class="top-text">
				<text>{{item.usingTime}}</text>
				<text>秒</text>
			</view>
			<text>答题用时</text>
		</view>
		<view class="item u-border-left">
			<view class="top-text">
				<text>{{item.rightRate}}</text>
				<text>%</text>
			</view>
			<text>全站正确率</text>
		</view>
		<view class="item u-border-left">
			<view class="top-text">
				<text>{{item.easyWrongOption}}</text>
				<text></text>
			</view>
			<text>易错项</text>
		</view>
	</view>
</template>

<script>
	export default {
		name:"question_board",
		data() {
			return {
				
			};
		},
		props:{
			item:{
				type:Object,
				default:{
					usingTime:130,
					rightRate:70,
					easyWrongOption:"abc"
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background: #F6F7FB;
	border-radius: 18rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 130rpx;
	.item{
		text-align: center;
		flex: 1;
		color: #777;
		font-size: 18rpx;
		.top-text{
			text{
			font-size: 32rpx;
			color: #201E2E;
			
			&:last-child{
				color: #777;
				font-size: 18rpx;
			}
			}
		}
	}
}
</style>