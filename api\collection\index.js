/**
 * 获取所有的错题数据
 */
export  const getAllWrongQuestionData = async ()=>{
	const ret = await uni.$u.http.get("/Question/getWrongList")
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 根据科目获取所有的错题
 */
export  const getWrongQuestionListBySubject = async (params)=>{
	const ret = await uni.$u.http.get("/Question/getWrongQuestionListBySubject",{params})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取所有的错题数据
 */
export  const getAllStarQuestionData = async ()=>{
	const ret = await uni.$u.http.get("/Question/getAllStarQuestionData")
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取收藏列表
 */
export  const getStarList = async (params)=>{
	const ret = await uni.$u.http.get("/Question/getStarQuestionListBySubject",{params})
	if(ret.code ==1){
		return ret.data
	}else{
		uni.tip(ret.msg)
		return false
	}
}
