<template>
	<view class="container" v-if="!showloading">
		<view class="userAvatar">
			<template v-if="list.length>0">
				<view class="list"  v-for="item in list" :key="item.id">
					<view @click="details(item)">
						<view class="publish">
							<view class="pBox">
								<image :src="item.user.avatar" mode="widthFix"></image>
							</view>
							<view class="right">
								<view class="teacher">{{item.user.nickname}}</view>
								<view class="time">{{item.time}}</view>
							</view>
						</view>
						<view class="content">{{item.content}}</view>
						<view class="topicBox">
							<view class="topic">
								<text class="sel-subject-title" v-if="item.topic_id > 0">#{{item.topic.name}}</text>

							</view>
							<u-icon name="arrow-down" size="30"></u-icon>
						</view>
						<view class="thumb-list">
							<template v-if="item.image_arr_text.length < 2"  >
								<image v-for="imageItem in item.image_arr_text" class="image_one" mode="aspectFill" :src="imageItem"></image>
							</template>							
							<template v-else-if="item.image_arr_text.length == 2" >
								<image  v-for="imageItem in item.image_arr_text" class="image_two" mode="aspectFill" :src="imageItem"></image>
							</template>							
							<template v-else-if="item.image_arr_text.length > 2 && item.image_arr_text == 4" >
								<image  v-for="imageItem in item.image_arr_text" class="image_third" mode="aspectFill" :src="imageItem"></image>
							</template>
							<template v-else  >
								<image v-for="imageItem in item.image_arr_text" class="image_four" mode="aspectFill" :src="imageItem"></image>
							</template>
						</view>
					</view>
					<view class="bottom-opt">
						<!-- 阅读数 -->
						<view class="read-num">
							<image src="../../static/img/view.png" mode="widthFix"></image>
							<text>{{item.see_num}}</text>
						</view>
						<!-- 分享 -->
						<view class="share">
							<image src="../../static/img/share.png" mode="widthFix"></image>
							<text>分享</text>
						</view>
						<!-- 评论 -->
						<view class="comment" @click="details(item)">
							<image src="../../static/img/msg.png" mode="widthFix"></image>
							<text>{{item.reviewNum}}</text>
						</view>
						<!-- 点赞 -->
						<view class="praise" @click="userZan(item.id)">
							<image :src="zanList.includes(item.id)?unpraise:praise"  mode="widthFix"></image>
							<text>{{item.like_num}}</text>
						</view>
					</view>
				</view>				
			</template>
			<template v-else>
				<view class="no-data" >
					还没发布任何动态哦~
				</view>
			</template>
			
		</view>

	</view>
	<view  class="container" v-else>
		<u-loading-page loading="true" loading-text="加载中..." font-size="32"></u-loading-page>
	</view>
</template>

<script>
	import {mapState} from "vuex"
	export default {
		data() {
			return {
				showloading:true,
				current: 0,
				tabList: [{
						name: '动态'
					},
					{
						name: '点赞'
					}
				],
				zanPage:1,
				comPage:1,
				list: [],
				lists: [],
				type:-3,
				pageSize:6,
				comTotal:0,
				userDetail:'',
				zanTotal:0,
				zanList:[],
				praise:'../../static/img/praise.png',
				unpraise:'../../static/img/praise-color.png',
			}
		},
		watch: {
			userInfo: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getArticleList()
						this.getUserInfo()
					}
				},
				deep: true
			}
		},
		computed:{
			...mapState('user',['userInfo']),
		},
		onShow() {
			this.getArticleList()
			this.getArticleListZan();
			this.getUserInfo()
		},
		methods: {
			userZan(articleId){
				if (typeof this.userInfo.token == "undefined" || this.userInfo.tken == "") {
					uni.navigateTo({
						url:'/pages/login/login'
					})
				} else {
					let that = this;
					if(that.zanList.includes(articleId)) {					
						uni.$u.http.post('/article/cancelLike',{type:1,article_id:articleId}).then(ret=>{
							if(ret.code == 1) {							
								that.zanList =  that.zanList.filter(item=>item!=articleId)
								that.articleList  =  that.articleList.map(item=>{
									if(item.id == articleId) {
										item.like_num -=1;
									}
									return item
								})
								console.log(that.articleList)
								
							}
						})
					} else  {
						uni.$u.http.post('/article/likeAndSee',{type:1,article_id:articleId}).then(ret=>{
							if(ret.code == 1) {
								uni.tip('点赞成功');
								that.zanList.push(articleId)
								
								that.articleList  =  that.articleList.map(item=>{
									if(item.id == articleId) {
										item.like_num +=1;
									}
									return item
								})
							}
						})
					}
				}
			},
			getUserInfo(){
				let that = this;
				uni.$u.http.get('/mini_user/getUserInfo').then(rest=>{
					that.userDetail  = rest.data;				
				});
			},
			getArticleListZan(){
				let that = this;
				uni.$u.http.get('/article/getArticleListZan').then(rest=>{
					that.zanList  = rest.data;				
				});
			},
			async getArticleList() {
				
				let that = this;
				let params  = {
					type: that.type,
					pageSize:6
				}
				if(that.type == -3) {
					params.page= that.comPage
				} else  {
					params.page= that.zanPage;
					
				}				
				let ret = await uni.$u.http.get('/article/getArticleListData', {
					params: params
				}).then(rest => {
					that.showloading = false
					let res = rest.data
					that.comTotal = res.total
					that.list = [...that.list,...res.data]; 
					
				})
			},
			details(e) {
				console.log(e)
				uni.navigateTo({
					url: "/subpkg/condition_details/condition_details?id="+e.id
				})
			},
			changemenu(val) {
				console.log(val)
				
				this.current = val.index
				if(this.current == 0) {
					this.type  = -1
				} else {
					this.type = -2
				}
				this.getArticleList()
			}
		},
		onReachBottom() {
			
			if (this.list.length >= this.comTotal) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			}
			this.comPage += 1
			this.getArticleList()
			
			
			
		},
	}
</script>
<style>
	page {
		background-color: #f5f5f5;
	}
</style>
<style lang="scss" scoped>
	.container {
		.userAvatar {
			width: 750rpx;
			height: 326rpx;
			background: linear-gradient(180deg, #b5e4db 0%, #d6edeb 60%, #F6F7FB 100%);

			.unameBox {
				display: flex;
				align-items: center;
				padding-top: 35rpx;
				padding-left: 30rpx;
				box-sizing: border-box;

				.imgBox {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
					background-color: #fff;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.uname {
					font-weight: bold;
					font-size: 32rpx;
					color: #201E2E;
					margin-left: 10rpx;
				}
			}

			.tabList {
				padding-top: 50rpx;
				margin-top: 46rpx;
				background-color: #fff;
				box-sizing: border-box;
				border-radius: 28rpx 28rpx 0rpx 0rpx;
			}

			.list {
				width: 100%;
				background-color: #fff;
				padding: 0 30rpx;
				box-sizing: border-box;
				margin-bottom: 25rpx;

				&:last-child {
					padding-bottom: 100rpx;
					margin-bottom: 0;
				}
				.thumb-list{
					display: flex;
					justify-content: flex-start;
					flex-wrap: wrap;
					align-content: center;
					
					.image_one {
						margin-left: 10rpx;
						margin-top: 10rpx;
						width: 51%;
						border-radius: 16rpx;
					}
					.image_two {
						margin-left: 10rpx;
						margin-top: 10rpx;
						width: 40%;
						border-radius: 16rpx;
					}
					.image_third {
						margin-left: 10rpx;
						margin-top: 10rpx;
						width: 40%;
						border-radius: 16rpx;
						max-height:260rpx;
					}
					.image_four {
						margin-left: 10rpx;
						margin-top: 10rpx;
						width: 28%;
						border-radius: 16rpx;
						max-height:260rpx;
					}
				}

				

				.publish {
					display: flex;
					align-items: center;
					padding-top: 24rpx;
					box-sizing: border-box;

					.pBox {
						width: 60rpx;
						height: 60rpx;
					

						image {
							width: 100%;
							height: 100%;
							border-radius: 30rpx;
						}
					}

					.right {
						margin-left: 10rpx;

						.teacher {
							font-size: 26rpx;
							color: #060606;
							margin-bottom: 8rpx;
						}

						.time {
							font-weight: 500;
							font-size: 24rpx;
							color: #818181;
						}
					}
				}

				.content {
					margin-top: 20rpx;
					font-weight: 500;
					line-height: 1.5;
					font-size: 26rpx;
					color: #060606;
				}

				.topicBox {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;

					.topic {
						flex: 4;
						display: flex;
						flex-wrap: wrap;
						font-size: 26rpx;
						color: #01997A;

						text {
							margin-right: 20rpx;
							margin-bottom: 5rpx;
						}
					}
				}

				.bottom-opt {
					padding: 16rpx 0;
					margin-top:20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-top: 1rpx solid #ebebeb;

					view {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width: 50rpx;
						}

						text {
							color: #C2C2C2;
							margin-right: 8rpx;
							font-size: 24rpx;
						}
					}
				}
			}

			.no-data {
				width: 100%;
				height: 500rpx;
				line-height: 200rpx;
				text-align: center;
				background-color: #fff;
			}
		}
	}
</style>