<template>
	<view class="container">
		<view class="head">
			<view class="left-sel-seg">
				<uni-segmented-control :values="list" @clickItem="chooseItem" activeColor="#06A177" styleType="text"></uni-segmented-control>
			</view>
			<view class="right">
				<quick-search :showCalendar='true' @chooseDate='chooseDate' @calendarChange='calendarChange'>
				</quick-search>
			</view>
		</view>

		<!-- vip教室选择 -->

		<view class="select-vip-room" v-if="index == 0">
			<view class="title">
				{{ list[index] }}
			</view>
			<view class="vip-room-list">
				<view class="vip-room-item" :class="[item.isFull == 1 ? 'gray-color' : '']" :style="{ backgroundColor: currentSelRoomId == item.id ? '#E16965' : '' }" v-for="(item) in roomList" :key="item.id" @click="chooseRoom(item)">
					{{ item.name }}
				</view>
			</view>

			<view class="sub-title">
				预定时段
			</view>
			<view class="time-segment">
				<view class="segment-item" v-for="key in Object.keys(timeSegment)" :key="key" :style="{ backgroundColor: selectedTimeSegmentList.includes(key - 0) || isOutOfDate(timeSegment[key]) ? '#333' : '' }" :class="[currentRoomSelectedSegs.includes(key - 0) ? 'sel-color' : '']" @click="chooseTimeSeg(key - 0)">
					{{ timeSegment[key] }}
				</view>
			</view>
		</view>


		<!-- 按时间段选择 -->
		<view class="sel-container">

			<view class="select-vip-room-by-time-segment" v-if="index == 1">
				<view class="title">
					预定时段
				</view>
				<view class="sel-time-segment">
					<view class="start-time" @click="showStartTime = true">
						{{ startTimeStr }}
					</view>
					<text class="text-split">至</text>
					<view class="end-time" @click="showEndTime = true">
						{{ endTimeStr }}
					</view>
				</view>

				<view class="title">
					教室选择
				</view>
				<view class="vip-room-list">
					<view class="vip-room-item" v-for="(item) in roomList" :class="[selectedRoomIds.includes(item.id) ? 'gray-color' : '']" :style="{ backgroundColor: currentSelRoomId == item.id ? '#E16965' : '' }" :key="item.id" @click="chooseRoomByTimeSegs(item)">
						{{ item.name }}
					</view>
				</view>
			</view>


		</view>

		<view class="tag-label">
			<view class="label-item">
				<text>当前已选</text>
				<text class="color-label color-label-red"></text>
			</view>
			<view class="label-item">
				<text>可选</text>
				<text class="color-label color-label-green"></text>
			</view>
			<view class="label-item">
				<text>不可选</text>
				<text class="color-label color-label-black"></text>
			</view>
		</view>


		<view class="sel-list" v-show="AppointmentList.length > 0">
			<view class="header">
				<text class="head-item">教室</text>
				<text class="head-item-time">时间</text>
				<text class="head-item">操作</text>
			</view>
			<view class="order-list u-border-bottom" v-for="AppointmentItem in AppointmentList" :key="AppointmentItem.id">
				<text class="list-item">{{ AppointmentItem.roomName }}</text>
				<text class="list-item-time">{{ AppointmentItem.date }}&nbsp;{{ timeSegment[AppointmentItem.time_in] }}</text>
				<view class="btn-opt">
					<text class="btn" @click="cancleAppointment(AppointmentItem.id)">取消预约</text>
				</view>
			</view>
		</view>



		<view class="order-btn">

			<button class="btn" v-if="!hasPhone" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">立即预定</button>

			<u-button text="立即预定" :loading="loading" :customStyle="btnStyle" v-else class="btn" @click="submit"></u-button>
		</view>

		<u-picker @cancel='showStartTime = false' confirmColor="#01997A" :show="showStartTime" ref="uPickerStartTime" :columns="startTimeColumns" @confirm="confirmStart">
		</u-picker>
		<u-picker @cancel='showEndTime = false' confirmColor="#01997A" :show="showEndTime" ref="uPickerEndTime" :columns="columnsEnd" @confirm="confirmEnd">
		</u-picker>
	</view>
</template>

<script>
import quickSearch from "../component/quick-search/quick-search.vue"
import {
	getVipRoomList,
	getVipRoomTimeSegmentApi,
	addAppointmentList,
	getAppointmentListApi,
	cancleAppointmentApi,
	getClassRoomInfoByTimeSegApi,
	getAppointmentListByTimeSegsApi
} from "@/api/camp/index.js"
import moment from "../module/moment/moment"
import {
	mapState
} from "vuex"
export default {
	data() {
		return {
			showStartTime: false,//显示开始时间 picker
			showEndTime: false,//显示结束时间 picker	
			loading: false,
			currentSelRoomId: -1,
			list: ["教室选择", "时间段选择"],
			index: 0,
			time_index: [],
			roomList: [],
			//时间区间
			timeSegment: {},
			//已经选择的时间区域
			selectedTimeSegmentList: [],
			//搜索日期 默认当天
			selectDate: moment().format('YYYY-MM-DD'),
			currentSelTimeSegIds: [], //选择的时间段
			AppointmentList: [], //预约的列表
			btnStyle: {
				width: '520rpx',
				height: '86rpx',
				background: '#01997A',
				borderRadius: '68rpx',
				textAlign: 'center',
				lineHeight: '86rpx',
				fontSize: '32rpx',
				fontWeight: 'bold',
				color: '#fff',

			},
			columnsStart: [],
			columnsEnd: [],
			//时间段 非区间
			hourarr: {},
			startTimeStr: '',
			endTimeStr: '',
			limit_times: 0,
			selectedRoomIds: [],//某个时间段下已经被选择的房间号
			regionData: [],//根据时间段选择选择 时间段数据
		};
	},
	async onShow() {
		const params = {
			date: this.selectDate
		}
		//获取教室列表
		const data = await getVipRoomList(params)
		if (data != false) {
			this.roomList = data.list;
			this.timeSegment = data.timeSegment.time_arr;
			this.hourarr = data.timeSegment.hour_arr;
			// 每日次数限制
			this.limit_times = data.timeSegment.limit_times;
		}
		//获取预约列表
		this.getAppointmentList()
	},
	methods: {
		async decryptPhoneNumber(e) {
			if (e.detail.errMsg.indexOf('ok') != -1) {
				const {
					code,
					data
				} = await uni.$u.http.post("/mini_user/getMobile", {
					code: e.detail.code
				})
				if (code == 1) {
					this.$store.commit('user/setUser', {
						phone: data.phone
					})
					this.submit()
				}
			}
		},
		//tabs更改
		chooseItem(e) {
			if (this.index != e.currentIndex) {
				this.index = e.currentIndex
			}
			//清空
			this.resetSelected()
			//this.selectDate = moment().format('YYYY-MM-DD') //重置日期
		},

		//房间更改
		async chooseRoom(item) {
			if (item.isFull == 0) {
				this.currentSelRoomId = item.id;
			}
			//清空时间段的选择
			//this.currentSelTimeSegIds = [];
			this.getVipRoomTimeSegment(item)
		},
		//根据时间段选择房间
		chooseRoomByTimeSegs(item) {
			if (this.selectedRoomIds.includes(item.id)) {
				return;
			}
			if (this.regionData.length == 0 || this.startTimeStr == "" || this.endTimeStr == "") {
				return uni.tip("请先选择时间段")
			}
			this.currentSelRoomId = item.id;
		},


		//获取已经预约的时间段
		async getVipRoomTimeSegment() {
			if (this.currentSelRoomId == -1) {
				return
			}
			//搜索时间段
			const params = {
				date: this.selectDate,
				id: this.currentSelRoomId
			}
			//获取已经预约的时间段
			const data = await getVipRoomTimeSegmentApi(params)
			if (data != false) {
				//查询其他房间已经选择的时间点
				const otherTimeSeg = this.currentSelTimeSegIds.map(item => {
					if (item.roomId != this.currentSelRoomId) {
						return item.timeSegId
					}
				})
				//合并 防止当日 在不同的房间选择相同的时间点
				this.selectedTimeSegmentList = [...data.list, ...otherTimeSeg]
			}
		},
		//选择时间段
		chooseTimeSeg(key) {
			//已经被选 直接返回
			if (this.selectedTimeSegmentList.includes(key)) {
				return
			}

			// 过时返回
			if (this.isOutOfDate(this.timeSegment[key])) {
				return
			}
			if (this.currentSelRoomId <= 0) {
				return uni.tip("请先选择教室")
			}


			//已经在该房间选择了 剔除
			if (this.currentRoomSelectedSegs.includes(key)) {
				this.currentSelTimeSegIds = this.currentSelTimeSegIds.filter(item => {
					if (item.timeSegId == key && item.roomId == this.currentSelRoomId) {
						return false;
					}
					return true
				})
				return;
			}
			//添加时间段到已选列表
			//超过限制次数 直接返回

			const count = this.currentSelTimeSegIds.length + this.selectedDateAppointmentNum()
			if (count > this.limit_times) {
				return uni.tip("超过最大预约时段")
			}
			//选择的信息 房间号 和时间段
			const selectInfo = {
				roomId: this.currentSelRoomId,
				timeSegId: key,
			}
			this.currentSelTimeSegIds.push(selectInfo)
		},
		//今天，明天，后天 日期变化
		chooseDate(e) {
			if (e == 1) {
				this.selectDate = moment().add(1, 'days').format('YYYY-MM-DD');
			} else if (e == 2) {
				this.selectDate = moment().add(2, 'days').format('YYYY-MM-DD');
			} else if (e == 0) {
				this.selectDate = moment().format('YYYY-MM-DD')
			}
			//日期变化重置 选择的房间和 时间段,和已选择列表
			this.resetSelected()
		},
		//确认开始时间
		confirmStart(e) {
			this.startTimeStr = e.value[0]

			//this.endTimeStr = this.hourarr[[e.indexs[0]+2]]

			let columnsEndColunm = this.startTimeColumns[0].slice(e.indexs[0] + 1);
			//把 hourarr 最后一条数据追加上
			columnsEndColunm.push(Object.values(this.hourarr).pop());
			//结束时间段
			this.columnsEnd = [columnsEndColunm]
			//时间变化 清空选中的教室
			this.currentSelRoomId = -1;
			//清空已选时间段
			this.endTimeStr = ""
			this.showStartTime = false

		},
		//确认结束时间
		async confirmEnd(e) {

			if (this.startTimeStr.length == 0) {
				return uni.tip("请选择开始时间")
			}

			//获取时间段的 区间数据
			this.regionData = this.getRegionData(e.value[0])
			const count = this.regionData.length + this.selectedDateAppointmentNum()
			if (count > this.limit_times) {
				return uni.tip("超过最大预约时段")
			}

			//查询该时间段的可选教室	
			const params = {
				date: this.selectDate,
				period: this.regionData,
			}
			const data = await getAppointmentListByTimeSegsApi(params)
			if (data != false) {
				this.selectedRoomIds = data.list
			}
			this.endTimeStr = e.value[0]
			//时间变化 清空选中的教室
			this.currentSelRoomId = -1;
			this.showEndTime = false
		},




		//获取区间的时间段
		getRegionData(endTimeStr) {

			let region = [];
			let keys = Object.keys(this.hourarr)
			keys.forEach(key => {
				if (this.hourarr[key] == this.startTimeStr || this.hourarr[key] == endTimeStr) {
					region.push(key)
				}
			})

			region = region.sort();
			let start = region[0];
			let end = region[region.length - 1];
			let startIndex = keys.indexOf(start);
			let endIndex = keys.indexOf(end);

			return keys.slice(startIndex, endIndex);
		},
		calendarChange(e) {
			this.selectDate = e.fulldate
			this.resetSelected()
		},
		//重置选择的房间和时间段
		resetSelected() {
			//日期变化重置 选择的房间和 时间段,和已选择列表
			this.currentSelRoomId = -1
			this.currentSelTimeSegIds = [];//提交的数据 {roomId:this.currentSelRoomId,timeSegId:key}
			this.selectedTimeSegmentList = [] //选择房间后 该房间下当前 已选择的时间段列表
			this.startTimeStr = ''	//时间段开始时间
			this.endTimeStr = '' //时间段结束时间
			this.regionData = [] //时间段数据 按时间段选择
			this.selectedRoomIds = [] //时间段选择  某个时间段已经被选的教室列表
			//this.selectDate = moment().format('YYYY-MM-DD')//重置日期
		},
		//提交预约记录
		submit() {
			const phone = this.userInfo.phone
			const date = this.selectDate
			if (this.index == 0) {

				if (this.currentSelRoomId == -1) {
					return uni.tip("请选择教室")
				}
				if (this.currentSelTimeSegIds.length == 0) {
					return uni.tip("请选择正确的时间段")
				}

			} else {
				if (this.regionData.length == 0) {
					return uni.tip("请选择时间段")
				}
				if (this.currentSelRoomId == -1) {
					return uni.tip("请选择教室")
				}
				this.currentSelTimeSegIds = this.regionData.map(item => {
					return { roomId: this.currentSelRoomId, timeSegId: item }
				})
			}

			const data = {
				phone,
				date,
				selectedData: this.currentSelTimeSegIds
			}
			this.addData(data)
		},
		//提交数据
		async addData(data) {
			try {
				this.loading = true
				const result = await addAppointmentList(data)
			} catch (e) {
				console.log(e)
			} finally {
				this.loading = false
				//提交后 重新刷新
				this.resetSelected();

				//重置日期
				//this.selectDate = moment().format('YYYY-MM-DD')
				this.getAppointmentList()
				this.getVipRoomTimeSegment()
			}
		},
		//获取我的预约列表
		async getAppointmentList() {
			const data = await getAppointmentListApi()
			if (data != false) {
				this.AppointmentList = data.list
			}
		},
		//取消预约
		async cancleAppointment(id) {
			const data = {
				id
			}
			const result = await cancleAppointmentApi(data)
			this.getAppointmentList()
			this.getVipRoomTimeSegment()
		},
		//是否过时
		isOutOfDate(datePeriod) {
			const dateString = datePeriod.split('-')[0]
			return Date.now() > moment(this.selectDate + ' ' + dateString, 'YYYY-MM-DD HH:mm:ss').valueOf();
		},
		//获取当前选择的日期已经预约的数量
		selectedDateAppointmentNum() {
			const count = this.AppointmentList.filter(item => {
				return item.date == this.selectDate
			})
			return count.length
		}
	},
	components: {
		quickSearch
	},
	computed: {
		...mapState('user', ['userInfo']),
		hasPhone() {
			return typeof this.userInfo.phone !== "undefined"
		},
		//当前 房间已选的时间段
		currentRoomSelectedSegs() {
			return this.currentSelTimeSegIds.map(item => {
				if (item.roomId == this.currentSelRoomId) {
					return item.timeSegId
				}
			})
		},

		//按时间段选择计算开始时间段
		startTimeColumns() {
			let columns = []
			Object.values(this.hourarr).forEach(value => {
				if (!this.isOutOfDate(value)) {
					columns.push(value)
				}
			})

			if (columns.length >= 1) {
				columns.pop()
			}
			return [columns]
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #F3F4F6;
	padding-bottom: 120rpx;
}

.head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 26rpx;

	.left-sel-seg {
		flex: 2;
		height: 100rpx
	}

	.right {
		flex: 3;
		height: 100rpx
	}
}

::v-deep .segmented-control__text {
	font-size: 28rpx !important;
}

.select-vip-room {
	padding: 0 24rpx;

}


.title,
.sub-title {
	margin: 16rpx 0;
	padding-left: 16rpx;
	font-size: 28rpx;
	color: #333;
}

.sub-title {
	margin-top: 0;
	margin-bottom: 16rpx;
}

.sel-container {
	padding: 0 24rpx;
	margin-top: 24rpx;
}

.select-vip-room-by-time-segment {
	padding-top: 18rpx;
	border-radius: 16rpx;

	.title {
		padding-left: 28rpx;
	}

	.vip-room-list {
		padding: 0;
	}
}

.sel-time-segment {
	margin-left: 28rpx;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;

	.start-time,
	.end-time {
		width: 200rpx;
		height: 54rpx;
		background: #FFFFFF;
		border-radius: 18rpx;
		text-align: center;
		line-height: 54rpx;
		border: 1rpx solid #01997A;
		color: #01997A;
		font-size: 28rpx;
	}

	.text-split {
		margin: 0 14rpx;
	}
}

.vip-room-list {
	padding: 0 16rpx;
	display: flex;
	justify-content: flex-start;
	align-content: flex-start;
	flex-wrap: wrap;

	.vip-room-item {
		margin-left: 26rpx;
		margin-bottom: 30rpx;
		width: 108rpx;
		height: 52rpx;
		background: #E16965;
		border-radius: 14rpx;
		text-align: center;
		line-height: 52rpx;
		font-size: 22rpx;
		background-color: #01997A;
		color: #fff;
	}

}

.time-segment {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	flex-wrap: wrap;
	padding: 30rpx 28rpx;
	padding-bottom: 18rpx;
	border-radius: 14rpx;

	.segment-item {
		margin-bottom: 28rpx;
		width: 158rpx;
		height: 52rpx;
		background: #01997A;
		line-height: 52rpx;
		border-radius: 12rpx;
		font-size: 20rpx;
		color: #fff;
		text-align: center;
	}
}

.gray-color {
	background-color: #333 !important;
}

.sel-color {
	background-color: #E16965 !important;
}

.tag-label {
	padding: 24rpx 0;
	display: flex;
	align-items: center;
	justify-content: center;

	.label-item {
		margin-right: 44rpx;
		color: #555;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.color-label {
		display: inline-block;
		width: 30rpx;
		height: 30rpx;
		margin-left: 10rpx;

	}

	.color-label-red {
		background-color: #E16965;
	}

	.color-label-green {
		background-color: #01997A;
	}

	.color-label-black {
		background-color: #555;
	}
}

.sel-list {
	padding: 0 24rpx;

	.header,
	.order-list {
		height: 74rpx;
		background: #01997A;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.head-item {
			flex: 1;
			text-align: center;
			color: #fff;
			font-size: 28rpx;
		}

		.head-item-time {
			text-align: center;
			color: #fff;
			font-size: 28rpx;
			width: 320rpx;
		}
	}

	.order-list {
		height: 108rpx;
		border-radius: 0;
		background-color: #fff;

		.list-item,
		.btn-opt {
			flex: 1;
			text-align: center;
			color: #2D2D2D;
			font-size: 22rpx;
		}

		.list-item-time {
			text-align: center;
			color: #2D2D2D;
			font-size: 22rpx;
			width: 320rpx;
		}

		.btn-opt {
			display: flex;
			align-items: center;
			justify-content: center;

			.btn {
				width: 120rpx;
				background-color: #01997A;
				border-radius: 12rpx;
				color: #fff;
				height: 50rpx;
				line-height: 50rpx;
				font-size: 20rpx;
			}
		}
	}
}

.order-btn {
	position: fixed;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding-bottom: 20rpx;
	background-color: #F3F4F6;
}
</style>