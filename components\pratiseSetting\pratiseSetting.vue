<template>
	<view class="container">
		<view class="head">
			<text>自定义模式</text>
			<view class="close-icon">
				<u-icon @click="close" name="close" size="28" color="#4A4853"></u-icon>
			</view>
		</view>
		<view class="sel-mode">
			<view class="left" @click="settingdata.type=0" :class="{sel:settingdata.type==0}">
				<text>练习模式</text>
				<text class="bottom-text">先做题最后查看解析</text>
				<view class="show-sel" v-if="settingdata.type==0">
					<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
				</view>
			</view>
			<view class="right" @click="settingdata.type=1" :class="{sel:settingdata.type==1}">
				<text>背题模式</text>
				<text class="bottom-text">边做题，边查看解析</text>
				<view class="show-sel" v-if="settingdata.type==1">
					<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 题目数量 -->
		<view class="h1-title">
			题目数量
		</view>
		<view class="slider">
			<view class="slider-container">
				<view id="bar" class="bar" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
					<view class="bg-color" :style="{width: bgWidth}">
					</view>
					<view ref="ball" class="ball" :style="{left: ballLeft}">
						<view class="line"></view>
						<view class="line"></view>
						<view class="line"></view>
					</view>
					<view class="bar-bg">
						<view v-for="item in defaultConfig" :key="item.id" :id="`v-${item.value}`" @click="detail($event, item.value)">
							<text class="dot"></text>
						</view>
					</view>
				</view>
				<view class="scale">
					<text v-for="item in defaultConfig" :key="item.id">{{item.value}}</text>
				</view>
			</view>
		</view>
		
		<view class="opt-switch">
			<text>仅练习未做题目</text>
			<u-switch v-model="settingdata.do_unread" @change="switchChange" size="48" activeColor="#01997A"></u-switch>
		</view>
		
		<view class="bottom-btn">
			<button @click="save">保存</button>
		</view>
	</view>
</template>

<script>
	export default {
		name:"pratiseSeting",
		data() {
			return {
				settingdata:{
					type:0,
					num:0,
					do_unread:false,
				},
				// 滑块相关数据
				ballLeft: "36rpx",
				bgWidth: "37rpx",
				defaultConfig: [
					{ id: 1, value: 5, distance: 36 },
					{ id: 2, value: 10, distance: 124 },
					{ id: 3, value: 15, distance: 232 },
					{ id: 4, value: 20, distance: 320 },
					{ id: 5, value: 30, distance: 428 },
					{ id: 6, value: 60, distance: 516 },
					{ id: 7, value: 90, distance: 614 }
				],
				dragging: false,
				barWidth: 688,
				barLeft: 0,
				startX: 0,
				startLeft: 0,
				isReady: false
			};
		},
		props:['config'],
		mounted() {
			// 初始化滑块位置
			this.$nextTick(() => {
				const initialItem = this.defaultConfig.find(item => item.value === (this.settingdata.num || this.config.num));
				if (initialItem) {
					this.ballLeft = `${initialItem.distance + 20}rpx`;
					this.bgWidth = `${initialItem.distance}rpx`;
					this.isReady = true;
				}
			});
		},
		methods:{
			// 滑块相关方法
			detail(e, num) {
				const item = this.defaultConfig.find(item => item.value === num);
				if (item) {
					this.$nextTick(() => {
						this.ballLeft = `${item.distance + 20}rpx`;
						this.bgWidth = `${item.distance}rpx`;
						this.settingdata.num = num;
					});
				}
			},
			onTouchStart(e) {
				this.dragging = true;
				const touch = e.touches[0];
				this.startX = touch.pageX;
				this.startLeft = parseFloat(this.ballLeft);
			},
			onTouchMove(e) {
				if (!this.dragging) return;
				e.preventDefault();

				const touch = e.touches[0];
				const deltaX = touch.pageX - this.startX;
				const deltaRpx = deltaX * (750 / uni.getSystemInfoSync().windowWidth);
				let newLeft = this.startLeft + deltaRpx;

				newLeft = Math.max(36, Math.min(newLeft, 624));

				const nearestPoint = this.getNearestPoint(newLeft);
				if (nearestPoint) {
					this.ballLeft = `${nearestPoint.distance + 20}rpx`;
					this.bgWidth = `${nearestPoint.distance}rpx`;
					this.settingdata.num = nearestPoint.value;
				}
			},
			onTouchEnd(e) {
				if (!this.dragging) return;
				this.dragging = false;
				
				const touch = e.changedTouches[0];
				const deltaX = touch.pageX - this.startX;
				const deltaRpx = deltaX * (750 / uni.getSystemInfoSync().windowWidth);
				let newLeft = this.startLeft + deltaRpx;

				newLeft = Math.max(36, Math.min(newLeft, 624));

				const nearestPoint = this.getNearestPoint(newLeft);
				if (nearestPoint) {
					this.ballLeft = `${nearestPoint.distance + 20}rpx`;
					this.bgWidth = `${nearestPoint.distance}rpx`;
					this.settingdata.num = nearestPoint.value;
				}
			},
			getNearestPoint(position) {
				let nearest = null;
				let minDistance = Infinity;

				this.defaultConfig.forEach(point => {
					const distance = Math.abs(position - point.distance);
					if (distance < minDistance) {
						minDistance = distance;
						nearest = point;
					}
				});

				return nearest;
			},
			// 原有方法
			switchChange(){
				//this.switchVal = !this.switchVal
			},
			close(){
				this.$emit("update:show", false)
			},
			save(){
				let postData =  {...this.settingdata, do_unread:this.settingdata['do_unread']?1:0}
				this.$emit("save", postData)
			}
		},
		watch:{
			config:{
				handler(newVal, oldVal){
					this.settingdata = {...newVal, do_unread:newVal.do_unread==0?false:true}
					// 更新滑块位置
					if (this.isReady) {
						const item = this.defaultConfig.find(item => item.value === newVal.num);
						if (item) {
							this.ballLeft = `${item.distance + 20}rpx`;
							this.bgWidth = `${item.distance}rpx`;
						}
					}
				},
				immediate:true,
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	height: 820rpx;
	background-color: #fff;
	.head{
		margin-top: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		text{
			font-size: 32rpx;
			color: #0A0A0A;
			font-weight: bold;
		}
		.close-icon{
			position: absolute;
			right: 64rpx;
		}
	}
	.sel-mode{
		margin-top: 66rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		.left,.right{
			position: relative;
			box-sizing: border-box;
			width: 336rpx;
			border-radius: 13rpx;
			border: 1rpx solid #A4A4A4;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 16rpx 26rpx; 
			text{
				color: #0A0A0A;
				font-size: 32rpx;
				font-weight: bold;
			}
			.bottom-text{
				margin-top: 20rpx;
				color: #A2A2A2;
				font-size: 24rpx;
				font-weight: normal;
			}
		}
		.sel{
			background-color: #CDF3E7;
			color: #01997A;
			.show-sel{
				position: absolute;
				top: 0;
				right: 0;
				width: 52rpx;
				height: 40rpx;
				background: #01997A;
				border-radius: 0rpx 12rpx 0rpx 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	.h1-title{
		margin-top: 70rpx;
		padding: 0 30rpx;
		color: #2D2D2D;
		font-size: 30rpx;
		font-weight: bold;
	}
	.slider{
		box-sizing: border-box;
		margin-top: 58rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 120rpx;

		.slider-container {
			box-sizing: border-box;
			width: 688rpx;
			.bar {
				box-sizing: border-box;
				background-color: #EBEFF8;
				width: 688rpx;
				border-radius: 22rpx;
				height: 16rpx;
				position: relative;
				.bg-color {
					position: absolute;
					top: 0;
					left: 0;
					width: 36rpx;
					height: 16rpx;
					border-radius: 22rpx;
					background-color: #16AC8E;
					z-index: 1;
				}
				.ball {
					box-sizing: border-box;
					height: 40rpx;
					width: 40rpx;
					border-radius: 50%;
					background-color: #fff;
					position: absolute;
					display: flex;
					align-items: center;
					justify-content: space-around;
					top: 50%;
					padding: 0 10rpx;
					transform: translateY(-50%) translateX(-50%);
					box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(70, 70, 70, 0.16);
					z-index: 2;
					.line {
						width: 2rpx;
						background-color: #16AC8E;
						height: 24rpx;
					}
				}
				.bar-bg {
					box-sizing: border-box;
					background-color: #EBEFF8;
					width: 100%;
					height: 100%;
					border-radius: 22rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 36rpx;
					z-index: 0;
					view {
						z-index: 100;
						width: 40rpx;
						height: 40rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						text {
							height: 8rpx;
							width: 8rpx;
							border-radius: 50%;
							background-color: #fff;
						}
					}
				}
			}
			.scale {
				box-sizing: border-box;
				width: 688rpx;
				color: #2D2D2D;
				font-size: 22rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 36rpx;
				margin-top: 20rpx;
				text {
					width: 40rpx;
					text-align: center;
				}
			}
		}
	}
	.opt-switch{
		font-size: 30rpx; 
		color: #2D2D2D;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		margin-top: 48rpx;
		font-weight: bold;
	}
	.bottom-btn{
		margin-top: 56rpx;
		padding: 0 100rpx;
		button{
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			border-radius: 60rpx;
			background-color: #01997A;
			color: #fff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}
</style>