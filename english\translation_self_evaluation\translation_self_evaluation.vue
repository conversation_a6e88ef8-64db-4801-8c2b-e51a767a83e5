<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
						<text>耗时00:45</text>
			</view>
		</u-navbar>
		
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>1</text>
					<text>/20</text>
				</view>
				<view class="opt">
					<view>
						<u-icon name="close-circle" size="38"></u-icon>
						<text>反馈</text>
					</view>
					<view>
						<u-icon name="star"  size="38"></u-icon>
						<text>收藏</text>
					</view>
					<view>
						<u-icon name="order"  size="38"></u-icon>
						<text>答题卡</text>
					</view>
				</view>
			</view>
			
			<view class="q-type">
				翻译
			</view>
			
			<view class="question">
				<text class="question-content"> 
				  In the quest for the perfect lawn, homeowners across the country are taking a shortcut - 
				  and it is the environment that is paying the price. 
				  About eight million square metres of plastic grass is sold each year but opposition has now spread to the highest
				</text>
			</view>
		</view>
		 <cover>
		      <scroll-view class="drawer-container" scroll-y="true">
				  <view class="title-container">
				  	<view class="title-list" :style="{height:isShowAll? Math.ceil(16/4)*80+'rpx' :'80rpx'}">
				  		<text @click="currentChoose=i" :class="[{done:i<4},{sel:currentChoose==i}]"  class="title" v-for="i in 16"> 第{{i+1}}题</text>
				  	</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up"  @click="isShowAll=true" size="44"></u-icon>
					</view>
				  </view>
				  <view class="q-container">
				  	<view class="q-title q-text">
				  		我的答案
				  	</view>
					<view class="a-content">
						<u--textarea  height="290rpx" border="none" v-model="answer" placeholder="请输入答案"  ></u--textarea>
					</view>
					<view class="img-list">
						<image style="width: 100%;" src="https://cdn.uviewui.com/uview/swiper/swiper3.png" mode="widthFix"></image>
					</view>
				  </view>
				  <!-- 标题 -->
				  <view class="q-answer">
				  	<view class="title">
				  		<user-title :title='"正确答案"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	
				  	<view class="q-answer-info">
				  		<text>
							开头的介词结构in many countries of Western作地点状语，直接译为"在西欧很多国家里"。再看主干部分，根据中文逻辑，利用时间关系安排翻译顺
序，英原文the numbers of students..doubled...inthe 1960, and doubled again... by the middlle of
1970s
</text>  		
		  	</view>
				  	
				  </view>
				  
				  
				  <!-- 解析 -->
				  <view class="analysis">
				  	<view class="title">
				  		<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	<view class="answer-info">
				  		<rich-text :nodes="answerInfo"></rich-text>
				  	</view>
				  </view>
				  
				  
				  
				  <!-- 自测评分 -->
				  <view class="comment">
				  	<view class="title">
				  		<user-title :title='"自测评分"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	<view class="edit-score">
				  		<u--input :disabled='true' :disabledColor="'#fff'" type="number" border="surround"
				  			v-model="questionInfo.selfScore"></u--input>
				  	</view>
					
					<view class="bottom-btn">
						<view class="prev">
							上一题
						</view>
						<view class="next">
							下一题
						</view>
					</view>
				  </view>
				
			  </scroll-view>
		</cover>
	</view>
</template>

<script>
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	import userTitle from "../../components/user_title/user_title.vue"
	import questionBoard from "../../components/question_board/question_board.vue"
	import enterCourse from "../../components/enter_course/enter_course.vue"
	export default {
		data() {
			return {
				isShowAll:false,
				currentSel:"",
				currentChoose:0,
				
				trapList:[{
						id:1,
						title:'考点精讲1-英语语法知识'
					},
					{
						id:2,
						title:'考点精讲1-英语语法知识'
					}],
				qData:{
					title:'21. It was a way to tell stories and pass down history.',
					seloption:[
						{
							optionName:"A",
							text:"Urge legislation to restrict its use."
						},
						{
							optionName:"B",
							text:"the necessity to lower the cost of fake grass"
						},
						{
							optionName:"C",
							text:"Urge legislation to restrict its use."
						},
						{
							optionName:"D",
							text:"Urge legislation to restrict its use."
						}
					]
				},
				answerInfo:`开头的介词结构in many countries of Western作地点状语，直接译为"在西欧很多国家里"。再看主干部分，根据中文逻辑，利用时间关系安排翻译顺
序，英原文the numbers of students..doubled...inthe 1960, and doubled again... 
`,
				teacherAnswer:`在揭示人类社会发展一般规律的基础上指明未来社会发展的方向`
			}
		},
		methods: {
			
		},
		components:{
		    cover,
			userTitle,
			questionBoard,
			enterCourse
		  }
	}
</script>

<style lang="scss" scoped>
.container{
	box-sizing: b;
	background-color: $container-bg-color;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
			}
		}
	
	}
	.drawer-container{
		height: 100%;
		background-color:rgb(247, 247, 247);
	}
	.title-container{
		display: flex;
		align-items: flex-start;
		justify-content: center;
		background-color: rgb(247, 247, 247);
		.title-list{
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			transition: height 0.3s ease;
			overflow: hidden;
			background-color: rgb(247, 247, 247);
			.title{
				width: 100rpx;
				color: #777777;
				font-size: 28rpx;
				padding: 15rpx 30rpx;
			}
		}
		.right-icon{
			width: 140rpx;
			padding-top: 15rpx;
			padding-right: 30rpx;
			::v-deep .uicon-arrow-up{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
			::v-deep .uicon-arrow-down{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
		}
		.done{
			color: $main-color !important;
		}
		.sel{
			color: #0A0A0A;
			font-weight: bold;
		}
	}

	.q-container{
		padding: 0 36rpx;
		background-color: #fff;
		padding-top: 1rpx;
		
		.a-content{
			margin-bottom: 36rpx;
			border: 1rpx solid #707070;
			margin-top: 24rpx;
			border-radius: 22rpx;
			padding: 10rpx;
		}
		
	}
	.q-text{
		
		line-height: 50rpx;
		color: #5A5A5A;
		font-size: 28rpx;
	}
	.q-title{
		margin-top: 36rpx;
		margin-bottom: 20rpx;
	}
	.q-sel-list{
		padding-bottom: 12rpx;
		.q-item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;
			text{
				width: 590rpx;
			}
			.q-check-normal{
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}
			.q-check-sel{			
				background: #01997A;
				border: 0;
				color: #fff;
				
			}
		}
	}
		
	.q-answer,.analysis,.knowledge-points, .traps, .knowledge-origin{
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 30rpx;
	}
	.q-answer-info{
		margin-bottom: 30rpx;
		text{
			font-size: 28rpx;
			color: #5A5A5A;
		}
	}
	
	.analysis{
		.answer-info{
			color: #5A5A5A;
			font-size: 28rpx;
		}
		.img-list{
			margin-top: 28rpx;
			image{
				width: 100%;
				margin-top: 30rpx;
			}
		}
		
	}
	.comment {
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 60rpx;
	
		.qanswer {
			font-size: 28rpx;
			color: #5A5A5A;
		}
	
		.edit-score {
		}
	}
	.knowledge-points{
		.point{
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			.point-tag{
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				 font-weight: bold;
			}
		}
	}
	.knowledge-origin{
		.knowledge-origin-title{
			color: #5A5A5A;
			font-size: 28rpx;
		}
		}
	.bottom-btn{
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		margin-bottom: 100rpx;
		view{
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}
		.prev{
			background: #FFFFFF;
			color: $main-color;
		}
		.next{
			background-color: $main-color;
			color: #fff;
		}
	}
	
}
</style>
