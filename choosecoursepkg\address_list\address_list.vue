<template>
	<view class="container">
		<view class="content-container">
			<view class="address" v-for="(item,index) in addressList" :key="index">
				<view class="info">
					<view class="user-address">
						<text>{{item.name}}  {{item.phone}}</text>
						<template v-if="item.is_default==1">
							
						
						<text class="tag">默认</text>
						<view class="bg-arrow-down">
							<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
						</view>
						</template>
					</view>
					<text class="detail">{{item.province_name+item.city_name+item.region_name+item.detail}}</text>
				</view>
				<view class="opt">
					<text @click="editAddress(item)">编辑</text>
					<text @click="del(item)">删除</text>
				</view>
			</view>
		</view>
		<view class="btn-container">
			<view class="btn" @click="editAddress({address_id:0})">
				新增收货地址
			</view>
		</view>
		<u-modal
			content="确认删除该地址"
			:show="show"
			showCancelButton
			closeOnClickOverlay
			@confirm="confirm"
			@cancel="cancel"
		></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentDefault:3,
				addressList:[],
				show:false,
				delId:0,
			};
		},
		onShow(){
			this.getList();
		},
		methods:{
			del(item){
				this.delId = item.address_id;
				this.show= true
				// const  data  = uni.$u.http.post('/order/delAddress',{id:item.address_id}).then(res=>{
				// 	that.addressList = res;
					
				// });
			},
			confirm(){
				let that = this;
				const  data  = uni.$u.http.post('/order/delAddress',{id:this.delId}).then(rest=>{
					that.show = false;
					that.getList()
					
				});
			},
			cancel() {
				this.show = false
			},
			editAddress(item){
				uni.navigateTo({
					url:'/choosecoursepkg/add_address/add_address?id='+item.address_id
				})
			},
			getList() {
				let that = this;
				const  data  = uni.$u.http.get('/order/addressList').then(rest=>{
					let res = rest.data
					that.addressList = res;
					
				});
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	page{
		background-color: $container-bg-color;
	}
.content-container{
	padding: 30rpx;
	.address {
		margin-top: 36rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 34rpx 28rpx;
		border-radius: 12rpx;
	
		.info {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			text-align: left;
	
			.user-address {
				height: 40rpx;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				color: #060606;
				font-weight: bold;
				font-size: 28rpx;
	
				.tag {
					width: 67rpx;
					height: 40rpx;
					background: #EEFAF6;
					border-radius: 10rpx;
					line-height: 40rpx;
					text-align: center;
					color: $main-color;
					font-size: 22rpx;
					margin-left: 18rpx;
				}
				.bg-arrow-down{
					width: 30rpx;
					height: 30rpx;
					background: #01997A;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 10rpx;
				}
			}
	
			text.detail {
				width: 100%;
				margin-top: 14rpx;
				color: #5A5A5A;
				font-weight: bold;
				font-size: 24rpx;
			}
		}
		.opt{
			color: #5A5A5A;
			font-size: 24rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 30%;
			text{
				padding: 0 10rpx;
			}
		}
	}
}
		.btn-container{
			position: fixed;
			bottom: 0;
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			.btn{
				width: 400rpx;
				height: 80rpx;
				background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
				border-radius: 40rpx;
				color: #fff;
				font-size: 30rpx;
				line-height: 80rpx;
				text-align: center;
			}
		}
</style>
