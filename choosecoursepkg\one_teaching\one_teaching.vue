<template>
	<view class="container">
		<view class="top">
			<text>总课时68</text>
			<text>已节课20.4</text>
			<text>剩余课时47.6</text>
		</view>
		<view class="content">
			<view class="title-name">
				<view class="left">
					<text class="green-block"></text>
					<text>基本信息</text>
				</view>
				<view class="content">
				
				</view>
			</view>
			
			<view class="panel">
				<view class="list-info">
					<text>本科院校及专业：</text>
					<text>黄山学院 市场营销</text>
				</view>
				<view class="list-info">
					<text>目标院校：</text>
					<text>中国科学技术大学(300分）</text>
				</view>
				<view class="list-info">
					<text>专业课：</text>
					<text>0761西方经济学     0845宏观经济学</text>
				</view>
			</view>
			
			<view class="title-name">
				<view class="left">
					<text class="green-block"></text>
					<text>课程计划</text>
				</view>
				<view class="btn">
					课程记录
				</view>
			</view>
			
			<view class="live-broadcast">
				<view class="broadcast-info">
					<view class="broadcast-title">
						<text>1</text>
						<text>2025.12.13 13:00-15:00(2.5课时）</text>
					</view>
					<view class="broadcast-detail">
						<text>请同学提前做好预习准备！</text>
						<text>进入直播</text>
					</view>
				</view>
				
			</view>
			
			<view class="live-broadcast">
				<view class="broadcast-info">
					<view class="broadcast-title">
						<text>2</text>
						<text>2025.12.13 13:00-15:00(2.5课时）</text>
					</view>
					<view class="broadcast-detail">
						<text>请同学提前做好预习准备！</text>
						<text>进入直播</text>
					</view>
				</view>
				
			</view>
			
			<view class="live-broadcast">
				<view class="broadcast-info">
					<view class="broadcast-title">
						<text>3</text>
						<text>2025.12.13 13:00-15:00(2.5课时）</text>
					</view>
					<view class="broadcast-detail">
						<text>请同学提前做好预习准备！</text>
						<text>进入直播</text>
					</view>
				</view>
				
			</view>
			
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
.container{
	min-height: 100vh;
	background-color: $container-bg-color;
	.top{
		height: 90rpx;
		padding: 22rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		text{
			color: #4C5370;
			font-size: 30rpx;
			font-weight: bold;
		}
	}
	.content{
		padding: 0 30rpx;
		.title-name {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
		
			.left {
				color: #1F1F27;
				font-size: 32rpx;
				font-weight: bold;
				
				.green-block {
					display: inline-block;
					width: 14rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #01997A;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
					margin-right: 6rpx;
				}
			}
		
			.content {
				margin-left: 24rpx;
				color: #4C5370;
				font-size: 26rpx;
			}
			.btn {
					width: 140rpx;
					height: 60rpx;
					border-radius: 16rpx;
					border: 3rpx solid #01997A;
					line-height: 60rpx;
					text-align: center;
					color: #01997A;
					font-size: 28rpx;
					font-weight: bold;
				}
		}
		.panel{
			background: #FFFFFF;
			border-radius: 20rpx;
			border: 1rpx solid #01997A;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			text-align: left;
			padding-top: 28rpx;
			.list-info{
				width: 100%;
				margin-bottom: 28rpx;
				margin-left: 28rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				text{
					color: #201E2E;
					font-size: 28rpx;
					font-weight: bold;
					&:last-child{
						color: $main-color;
						font-weight: normal;
					}
				}
			}
		}
		
		.live-broadcast{
			margin-bottom: 34rpx;
			box-sizing: border-box;
			background: linear-gradient( to bottom, #CDF3E7 0%, #FFFFFF 100%);
			border-radius: 20rpx;
			padding:28rpx;
				.broadcast-title{
					color: #4A4A4C;
					font-size: 30rpx;
					font-weight: bold;
					text{
						display: inline-block;
						&:first-child{
							height: 40rpx;
							width: 40rpx;
							border-radius: 50%;
							background-color: #01997A;
							color: #fff;
							line-height: 40rpx;
							text-align: center;
							font-size: 24rpx;
							margin-right: 8rpx;
						}
					}
				}
				.broadcast-detail{
					box-sizing: border-box;
					margin-top: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					text{
						color: #A2A2A2;
						font-size: 24rpx;
						&:nth-child(2){
							height: 44rpx;
							padding: 0 20rpx;
							background: linear-gradient( 90deg, #10C19D 0%, #14B494 63%, #01997A 100%);
							border-radius: 40rpx;
							line-height: 44rpx;
							text-align: center;
							font-size: 24rpx;
							font-weight: bold;
							color: #fff;
						}
					}	
				}
		}
		
	}
}
</style>
