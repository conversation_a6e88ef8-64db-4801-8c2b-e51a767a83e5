<template>
	<view class="text">
		<rich-text :nodes="content"></rich-text>
	</view>
</template>

<script>
	
	export default {
		data() {
			return {
				content: 0
			}
		},
		
		onLoad() {
			this.getHtml()
		
		},
		methods: {
			
			getHtml () {
				uni.$u.http.get('/index/getHtml').then(ret=>{
					this.content  = ret.data.text
				})
			}
			
		}
	}
</script>



<style>
	.text{
		width: 95%;
		margin-left: 2%;
		padding:20rpx;
	}
</style>