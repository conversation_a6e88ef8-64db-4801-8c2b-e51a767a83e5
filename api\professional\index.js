import store from "@/store"
/**
 * 获取专项题库分类信息
 * @return Array|bool
 */
export const getProfessionalQuestion = async (type) => {
	const ret = await uni.$u.http.get("/Question/getProfessionalQuestion", {
		params: {
			type
		}
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

export const getTrueQuestion = async () => {
	const ret = await uni.$u.http.get("/Question/getProfessionalQuestion", {
		params: {
			type: 1
		}
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取专项题库信息
 * @params 请求参数
 * @return object|bool
 */
export const getQuestionList = async (params) => {
	const ret = await uni.$u.http.get("/Question/getQuestionList", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取所有分类
 * @return Array
 */
export const getAllTypes = async (params) => {
	const ret = await uni.$u.http.get("/Question/getAllTypes",  {params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取装箱题库的统计信息
 * @return object
 */
export const getStatistics = async (params) => {
	const ret = await uni.$u.http.get("/Question/getStatistics", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取做题配置信息
 * @return object
 */
export const getConfig = async () => {
	const ret = await uni.$u.http.get("/Question/getConfig")
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 更新配置信息
 * @method post
 * @return bool
 */
export const updateConfig = async (data) => {
	const ret = await uni.$u.http.post("/Question/updateConfig", data)
	if (ret.code == 1) {
		return true
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取题目信息
 */
export const getQuestionDetail = async (params) => {
	const ret = await uni.$u.http.get("/Question/getQuestionDetail", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 添加反馈
 * @param data obejct
 * @return object
 */
export const addFeedBack = async (data) => {
	const ret = await uni.$u.http.post("/Question/addFeedBack", data)
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 点赞/取消点赞
 * @param data obejct
 * @return object
 */
export const starToggle = async (data) => {
	const ret = await uni.$u.http.post("/Question/starToggle", data)
	if (ret.code == 1) {
		uni.tip(ret.msg)
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取我的答题结果
 * @method get
 * @param params
 * @return object
 */
export const getQuestionAnswer = async (params) => {
	const ret = await uni.$u.http.get("/Question/getUserQuestionAnswer", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 更新答题结果
 * @method post
 * @param data
 * @return object
 */
export const updateAnswer = async (data) => {
	const ret = await uni.$u.http.post("/Question/addQuestionAnswer", data)
	if (ret.code == 1) {
		uni.tip(ret.msg)
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取答案
 * @method get
 * @param params
 * @return object
 */
export const getSystemAnswer = async (params) => {
	const ret = await uni.$u.http.get("/Question/getSystemAnswer", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取做题的进度
 * @method get
 * @param params 
 * @return object
 */
export const getProgress = async (params) => {
	const ret = await uni.$u.http.get("/Question/getProgress", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取题目列表
 */
export const getQuestionListByPqid = async (params) => {
	//任务题库标志重置
	store.commit('professionalConfig/setShowAnswer', false)
	const ret = await uni.$u.http.get("/Question/getquestionByProfessionId", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 添加答题记录
 */
export const addAnswerRecords = async (data) => {
	const ret = await uni.$u.http.post("/Question/addAnswerRecords", data)
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 添加答题报告数据
 */
export const addAnswerAndReportData = async (data) => {
	const ret = await uni.$u.http.post("/Question/addAnswerAndReportData", data)
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取答题报告的数据
 */
export const getReportData = async (params) => {
	const ret = await uni.$u.http.get("/Question/getReportData", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取用户的收藏数据
 */
export const getStarData = async (params) => {
	const ret = await uni.$u.http.get("/Question/getAllStarQuestionData", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 *  模板消息通知批改
 */
export const sendTaskSubmitTemplateMessage = async (data) => {
	const ret = await uni.$u.http.post("/Question/sendTaskSubmitTemplateMessage", data)
	if (ret.code == 1) {
		return true
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 *  获取批改结果
 */
export const getCorrectionResult = async (params) => {
	const ret = await uni.$u.http.get("/Question/getCorrectionResult", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		//uni.tip(ret.msg)
		return false
	}
}

/**
 * 根据id 获取题库信息
 */
export const getQuestionById = async (params) => {
	const ret = await uni.$u.http.get("/Question/getQuestionBankInfoById", {
		params
	})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取答题的记录列表
 */
export const getAnswerRecordsList = async (params) => {
	const ret = await uni.$u.http.get("/Question/getAnswerRecordsList",{params})
		if (ret.code == 1) {
			return ret.data
		} else {
			uni.tip(ret.msg)
			return false
		}
}
/**
 * @param data:
 * {
 *  about_id 任务id
 *	type 'professional'
 *	useTime 时间
 *	total_ret:100%
 *	professional_id:试卷id
 * }
 */
export const taskOtherRecord = async (data)=>{
	const ret = await uni.$u.http.post("/task/taskOtherRecord",data)
		if (ret.code == 1) {
			return ret.data
		} else {
			uni.tip(ret.msg)
			return false
		}
}

export const getTaskRecords = async (params) => {
  const ret = await uni.$u.http.get("/Question/getTaskRecords", {
    params,
  });
  if (ret.code == 1) {
    return ret;
  } else {
	console.log(ret.msg)
   //uni.tip(ret.msg)
    return false;
  }
};
/**
 * 根据report_id获取答题记录
 */
export const getRecordsByReportId = async (params) => {
  const ret = await uni.$u.http.get("/Question/getRecordsByReportId", {
    params,
  });
  if (ret.code == 1) {
    return ret;
  } else {
	console.log(ret.msg)
   //uni.tip(ret.msg)
    return false;
  }
  }
  /**
   * 获取题库分类
   */
  export const getQuestionCateList = async (params) => {
    const ret = await uni.$u.http.get("/Question/getQuestionCateList", {
      params,
    });
    if (ret.code == 1) {
      return ret;
    } else {
     //uni.tip(ret.msg)
      return false;
    }
}

/**
 * 根据分类和类型获取具体题库
 */
  export const getQuestionListByCate = async (params) => {
    const ret = await uni.$u.http.get("/Question/getQuestionListByCate", {
      params,
    });
    if (ret.code == 1) {
      return ret;
    } else {
     //uni.tip(ret.msg)
      return false;
    }
}
