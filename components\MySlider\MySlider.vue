<template>
    <view class="container">
        <view id="bar" class="bar" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
            <view class="bg-color" :style="{width: bgWidth}">
            </view>
            <view ref="ball" class="ball" :style="{left: ballLeft}">
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
            </view>
            <view class="bar-bg">
                <view v-for="item in defaultConfig" :key="item.id" :id="`v-${item.value}`" @click="detail($event, item.value)">
                    <text class="dot"></text>
                </view>
            </view>
        </view>
        <view class="scale">
            <text v-for="item in defaultConfig" :key="item.id">{{item.value}}</text>
        </view>
    </view>
</template>

<script>
export default {
    name: "MySlide",
    data() {
        return {
            ballLeft: "36rpx",
            bgWidth: "37rpx",
            num: 5,
            defaultConfig: [
                { id: 1, value: 5, distance: 36 },
                { id: 2, value: 10, distance: 124 },
                { id: 3, value: 15, distance: 232 },
                { id: 4, value: 20, distance: 320 },
                { id: 5, value: 30, distance: 428 },
                { id: 6, value: 60, distance: 516 },
                { id: 7, value: 90, distance: 614 }
            ],
            dragging: false,
            barWidth: 688,
            barLeft: 0,
            startX: 0,
            startLeft: 0,
            isReady: false
        };
    },
    props: {
        value: {
            type: Number,
            default: 5
        }
    },
    mounted() {
        // 在mounted时初始化滑块位置
        this.$nextTick(() => {
            const initialItem = this.defaultConfig.find(item => item.value === this.value);
            if (initialItem) {
                this.ballLeft = `${initialItem.distance + 20}rpx`;
                this.bgWidth = `${initialItem.distance}rpx`;
                this.isReady = true;
            }
        });
    },
    watch: {
        value: {
            immediate: true,
            handler(newVal) {
                if (this.isReady) {
                    const item = this.defaultConfig.find(item => item.value === newVal);
                    if (item) {
                        this.ballLeft = `${item.distance + 20}rpx`;
                        this.bgWidth = `${item.distance}rpx`;
                    }
                }
            }
        }
    },
    methods: {
        detail(e, num) {
            const item = this.defaultConfig.find(item => item.value === num);
            if (item) {
                this.$nextTick(() => {
                    this.ballLeft = `${item.distance + 20}rpx`;
                    this.bgWidth = `${item.distance}rpx`;
                    this.$emit("valChange", num);
                });
            }
        },
        onTouchStart(e) {
            this.dragging = true;
            const touch = e.touches[0];
            this.startX = touch.pageX;
            this.startLeft = parseFloat(this.ballLeft);
            if (!this.barWidth) {
                this.getBarDimensions();
            }
        },
        onTouchMove(e) {
            if (!this.dragging) return;
            e.preventDefault();

            const touch = e.touches[0];
            const deltaX = touch.pageX - this.startX;
            const deltaRpx = deltaX * (750 / uni.getSystemInfoSync().windowWidth);
            let newLeft = this.startLeft + deltaRpx;

            newLeft = Math.max(36, Math.min(newLeft, 624));

            const nearestPoint = this.getNearestPoint(newLeft);
            if (nearestPoint) {
                this.ballLeft = `${nearestPoint.distance + 20}rpx`;
                this.bgWidth = `${nearestPoint.distance}rpx`;
                this.$emit("valChange", nearestPoint.value);
            }
        },
        onTouchEnd(e) {
            if (!this.dragging) return;
            this.dragging = false;
            
            const touch = e.changedTouches[0];
            const deltaX = touch.pageX - this.startX;
            const deltaRpx = deltaX * (750 / uni.getSystemInfoSync().windowWidth);
            let newLeft = this.startLeft + deltaRpx;

            newLeft = Math.max(36, Math.min(newLeft, 624));

            const nearestPoint = this.getNearestPoint(newLeft);
            if (nearestPoint) {
                this.ballLeft = `${nearestPoint.distance + 20}rpx`;
                this.bgWidth = `${nearestPoint.distance}rpx`;
                this.$emit("valChange", nearestPoint.value);
            }
        },
        getNearestPoint(position) {
            let nearest = null;
            let minDistance = Infinity;

            this.defaultConfig.forEach(point => {
                const distance = Math.abs(position - point.distance);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = point;
                }
            });

            return nearest;
        },
        updateSlider(value) {
            const item = this.defaultConfig.find(item => item.value === value);
            if (!item) return;
            this.ballLeft = `${item.distance}rpx`;
            this.bgWidth = `${item.distance}rpx`;
        },
        calculateDistances() {
            const segmentWidth =  this.barWidth / (this.defaultConfig.length - 1);
            this.defaultConfig.forEach((item, index) => {
                item.distance =index * segmentWidth;
            });
        },
        getBarDimensions() {
            const initialItem = this.defaultConfig.find(item => item.value === this.value);
            if (initialItem) {
                this.ballLeft = `${initialItem.distance + 20}rpx`;
                this.bgWidth = `${initialItem.distance}rpx`;
            }
        }
    },
    onReady() {
        this.getBarDimensions();
    },
}
</script>

<style lang="scss" scoped>
.container {
    box-sizing: border-box;
    .bar {
        box-sizing: border-box;
        background-color: #EBEFF8;
        width: 688rpx;
        border-radius: 22rpx;
        height: 16rpx;
        position: relative;
        .bg-color {
            position: absolute;
            top: 0;
            left: 0;
            width: 36rpx;
            height: 100%;
            border-radius: 22rpx;
            height: 16rpx;
            background-color: #16AC8E;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1;
        }
        .ball {
            box-sizing: border-box;
            height: 40rpx;
            width: 40rpx;
            border-radius: 50%;
            background-color: #fff;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: space-around;
            top: 50%;
            padding: 0 10rpx;
            transform: translateY(-50%) translateX(-50%);
            box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(70, 70, 70, 0.16);
            z-index: 2;
            .line {
                width: 2rpx;
                background-color: #16AC8E;
                height: 24rpx;
            }
        }
        .bar-bg {
            box-sizing: border-box;
            background-color: #EBEFF8;
            width: 100%;
            height: 100%;
            border-radius: 22rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 36rpx;
            z-index: 0;
            view {
                z-index: 100;
                width: 40rpx;
                height: 40rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                text {
                    height: 8rpx;
                    width: 8rpx;
                    border-radius: 50%;
                    background-color: #fff;
                }
            }
        }
    }
    .scale {
        box-sizing: border-box;
        margin-top: 20rpx;
        width: 688rpx;
        color: #2D2D2D;
        font-size: 22rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 36rpx;
        text {
            width: 40rpx;
            text-align: center;
        }
    }
}
</style>
