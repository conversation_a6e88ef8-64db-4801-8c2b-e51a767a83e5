<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left" @click="goBack()">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
			            <u-icon
							color="#009c7b"
			                name="clock"
			                size="38"
			            ></u-icon>
						<text>{{useTime}}</text>
			</view>
		</u-navbar>
		
		
		<view class="content">
			<view class="top-opt">
				<view class="opt">
					<view>
						<u-icon name="close-circle" size="38"></u-icon>
						<text>反馈</text>
					</view>
					<view>
						<u-icon name="star"  size="38"></u-icon>
						<text>收藏</text>
					</view>
					<view>
						<u-icon name="order"  size="38"></u-icon>
						<text>答题卡</text>
					</view>
				</view>
			</view>
			
			<view class="q-type">
				综合题
			</view>
			
			<view class="question">
				<text class="title">一、请根据下列句子找出子难的词并写出汉语意思</text>
				<text class="title">二、写成下列句子结构</text>
				<text class="title">三、翻译下列句子</text>
				<view class="question-content">
					<u-parse :content="content"></u-parse>
				</view>
				
			</view>
		</view>
		
		<view class="answer">
			<view class="title">
				单词学习
			</view>
			<view class="a-content">
					<u--textarea  height="290rpx"  v-model="answerWord" placeholder="请输入答案" maxlength="-1"  ></u--textarea>
			</view>
			
			<view class="q-upload">
					<u-upload 
					 height="200rpx"
					 width="200rpx"
					 uploadText="上传照片"
						:fileList="fileList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="1"
						multiple
						:maxCount="10"
					></u-upload>
			</view>
			
		</view>
		
		
		<view class="answer">
			<view class="title">
				句子结构分析
			</view>
			<view class="a-content">
					<u--textarea  height="290rpx"  v-model="answerSemt" placeholder="请输入答案"  maxlength="-1"></u--textarea>
			</view>
			
			<view class="q-upload">
					<u-upload 
					 height="200rpx"
					 width="200rpx"
					 uploadText="上传照片"
						:fileList="fileList1"
						@afterRead="afterRead1"
						@delete="deletePic1"
						name="1"
						multiple
						:maxCount="10"
					></u-upload>
			</view>
			
		</view>
		
		
		<view class="answer">
			<view class="title">
				翻译
			</view>
			<view class="a-content">
					<u--textarea  height="290rpx"  v-model="answerTran" placeholder="请输入答案" maxlength="-1" ></u--textarea>
			</view>
			
			<view class="q-upload">
					<u-upload 
					 height="200rpx"
					 width="200rpx"
					 uploadText="上传照片"
						:fileList="fileList2"
						@afterRead="afterRead2"
						@delete="deletePic2"
						name="1"
						multiple
						:maxCount="10"
					></u-upload>
			</view>
			
		</view>
		<view class="bottom-btn" @click="submitData">
			提交
		</view>
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	export default {
		data() {
			return {
				taskId:'',
				wordData:[],
				fileList:[],
				fileList1:[],
				fileList2:[],
				answer:'',
				content:'',
				answerTran:'',
				answerWord:'',
				answerSemt:'',
				useTime:'00:00',
				secondTime:0,
			};
		},
		methods:{
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getSentence',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data;
					that.content = that.wordData.sentence
					
				})
			},
			submitData() {
				let pData  = {
					useTime:this.secondTime,
					answerTran:this.answerTran,
					answerWord:this.answerWord,
					answerSemt:this.answerSemt,
					imageWord:this.fileList,
					imageSemt:this.fileList1,
					imageTran:this.fileList2,
					about_id:this.taskId,
					type:'semt'
				}
				let that = this
				let  data  = uni.$u.http.post('/task/taskOtherRecord',pData).then(res=>{
					console.log(res)
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						setTimeout(function(){
							uni.navigateTo({
								url:'/class_teacher/day_sentence_answer/day_sentence_answer?task_id='+that.taskId
							})
						},1000)	
					} else {
						uni.$u.toast(res.msg)
					}
				});
				
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
			deletePic1(event) {
				this.fileList1.splice(event.index, 1)
			},
			//图片上传
			async afterRead1(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList1.length
				lists.map((item) => {
					this.fileList1.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList1[fileListLen]
						this.fileList1.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList1.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
			deletePic2(event) {
				this.fileList2.splice(event.index, 1)
			},
			//图片上传
			async afterRead2(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList2.length
				lists.map((item) => {
					this.fileList2.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList2[fileListLen]
						this.fileList2.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList2.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
		},
		async onLoad(options) {
			this.taskId = options.task_id
			this.getWordDetail();
			let that = this;
			await setInterval(function(){
				that.secondTime++;
				let tmp_min = parseInt(that.secondTime/60);
				let tmp_second = that.secondTime%60;
				that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
			},1000)
		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding-bottom: 20rpx;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				color: #5A5A5A;
				font-size: 32rpx;
				margin-top: 24rpx;
				line-height: 50rpx;
			}
		}
		.q-text{
			
			line-height: 50rpx;
			color: #5A5A5A;
			font-size: 28rpx;
		}
		.q-title{
			margin-top: 36rpx;
			margin-bottom: 50rpx;
		}
		.q-sel-list{
			
			.q-item{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;
				text{
					width: 590rpx;
				}
				.q-check-normal{
					width: 70rpx;
					height: 70rpx;
					border: 1rpx solid #AFAFAF;
					color: #5A5A5A;
					background-color: #fff;
					border-radius: 16rpx;
					line-height: 70rpx;
					text-align: center;
				}
				.q-check-sel{			
					background: #01997A;
					border: 0;
					color: #fff;
					
				}
			}
		}
	
	}
	.answer{
		padding: 0 36rpx;
		margin-bottom: 36rpx;
		.title{
			color: 28rpx;
			color: #777777;
			font-weight: bold;
		}
		.a-content{
			margin-top: 12rpx;
			margin-bottom: 36rpx;
		}
		
		.bottom-btn{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 56rpx;
			view{
				width: 170rpx;
				height: 70rpx;
				border-radius: 16rpx;
				border: 1rpx solid #01997A;
				line-height: 70rpx;
				text-align: center;
				font-size: 28rpx;
			}
			.prev{
				background: #FFFFFF;
				color: $main-color;
			}
			.next{
				background-color: $main-color;
				color: #fff;
			}
		}
	}
	.bottom-btn{
		margin: 0 auto;
		width: 400rpx;
		height: 80rpx;
		color: #fff;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
	}
}
</style>
