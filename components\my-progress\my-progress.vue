<template>
	<view id="bar" class="container" style="height: 6px;" >
		<view class="progress" :style="{backgroundColor: fontColor, width:moving?movingPercent*100+'%':percentage+'%'}"></view>
		<view class="logo-container" @touchstart="start" @touchmove="move" @touchend="end">
			<image  class="logo" :class='{"imageFull":isFull}'  :src="logo"></image>
		</view>
	</view>
</template>

<script>
	import logo from "@/static/img/play-bar-head.png"
	export default {
		name:"my-progress",
		data() {
			return {
				logo,
				barWith:0,
				startMovePosX:0,
				endMovePosX:0,
				moving:false,
				movingPercent:0,
				winInfo:{}
			};
		},
		props:{
			percentage:{
				type:Number,
				default:0
			},
			fontColor:{
				default:"#009c7b"
			},
			isFull:{
				type:Boolean,
				default:false
			},
		},
		methods:{
			async getPoint(e){
				if(typeof e.target.x !=="undefined" && this.barWith>0){
					const pos =  (parseInt(e.target.x)/this.barWith).toFixed(2)
					this.$emit('posChange', pos)
				}
				
			},
			start(e){
				//是否移动中
				this.moving = true;
			},
			move(e){
				let distance = parseFloat(e.touches[0].clientX/this.barWith).toFixed(2)
				if(parseInt(distance)*100>=100){
					distance = 1;
				}
				this.movingPercent = distance
			},
			end(e){
				this.moving = false
				this.$emit('posChange', this.movingPercent)
				
			}
		},
		watch:{
			isFull(newVal){
				//计算横屏时候的 播放条宽度
				this.barWith = newVal?this.winInfo.screenHeight:this.winInfo.screenWidth
			}
		},
		async mounted() {
			this.winInfo = await uni.getSystemInfoSync();
			//默认竖屏处理
			this.barWith = this.winInfo.screenWidth
		}
	}
</script>

<style lang="scss" scoped>
.container{
	width: 100%;
	height: 6px;
	background-color: #ccc;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	.progress{
		height: 90%;
		width: 10%;
	}
	.logo-container{
		height: 36rpx;
		width: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.logo{
		height: 18px;
		width: 18px;
		position: relative;
		right: 2px;
		object-fit: contain;
	}
	.imageFull{
		height: 24px;
		width: 24px;
		right: 4px;
		object-fit: contain;
	}
}
</style>