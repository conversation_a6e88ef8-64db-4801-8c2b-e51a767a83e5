<template>
	<view class="container u-border-top">
		<!-- 	简介 -->
		<view class="introduction-container">
		</view>
		<!-- 中间部分tab -->
		<view class="mid-tabs">
			<view class="all-course-h1">
				<view class="left-title">
					<view class="green-block-big">

					</view>
					<view class="head">
						<u-tabs :list="mlist" lineWidth="50" @change="changeMlist" lineHeight="10" :lineColor="`url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/bottom_line_green.png) 100% 100%`" :activeStyle="{
							color: '#01997A',
							fontWeight: 'bold',
							transform: 'scale(1.1)'
						}" :inactiveStyle="{
							fontSize: '30rpx',
							color: '#2D2D2D',
							fontWeight: 'bold',
							transform: 'scale(1)'
						}" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 82rpx;">
						</u-tabs>
					</view>
				</view>
			</view>
			<view class="tabs" style="margin-left: 92rpx;">
				<u-tabs @change="changeName" :list="list" lineWidth="50" lineHeight="8" :current="current" lineColor="#01997A" :activeStyle="{
					color: '#01997A',
					fontSize: '32rpx'
				}" :inactiveStyle="{
					color: '#777777',
					fontSize: '32rpx'
				}" itemStyle="padding-left: 37rpx; padding-right: 37rpx; height: 34px;">
				</u-tabs>
			</view>

		</view>
		<view class="content" :style="{ height: bottomHeight }">
			<view class="left">
				<side-bar :list="sideBarList" :currentSel="sel" @changeItem="changeItem" :height="bottomHeight"></side-bar>
			</view>
			<view class="right">
				<tag-list class="tag-items" :tagList="tagList" v-if="!mIndex" :current="currentTagId" @changeCurrentSel="chooseTag"></tag-list>
				<scroll-view scroll-y="true" class="practise-container" :style="{ height: bottomScroolHeight }">

					<view class="exam-list">
						<view class="item" v-for="(item, i) in pratiseList" :key="item.id" @click="downDoc(item)">
							<text class="num">{{ i + 1 }}</text>
							<view class="title">
								<text class="title-name">{{ item.name }}</text>
								<text class="date">&nbsp;已下载{{ item.down_num }}次</text>
							</view>
							<view class="exam-progress" v-if="item.hasDown">
								<text class="do-continue" style="color: #01997A;">直接打开</text>
								<u-icon name="play-right-fill" color="#01997A" size="18"></u-icon>
							</view>
							<view class="exam-progress" v-else-if="item.wasDownloaded">
								<text class="do-continue">再次下载</text>
								<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
							</view>
							<view class="exam-progress" v-else>
								<text class="do-continue" style="color: #01997A;">立即下载</text>
								<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
							</view>
						</view>
					</view>
				</scroll-view>

			</view>
		</view>
	</view>
</template>

<script>
import sideBar from "../../components/Sidebar/Sidebar.vue"
import tagList from "../../components/tagList/tagList.vue"
export default {
	data() {
		return {
			//弹出层
			currentTagId: '0',
			show: false,
			//底部高度 动态获取
			bottomHeight: 0,
			bottomScroolHeight: 0,
			mName: '',
			mIndex: 0,
			mlist: [{
				name: '课堂资料'
			},
			{
				name: '历年真题'
			},
			],

			list: [
			],
			sel: 0,
			selName: '',
			sideTmpBarList: [{
				id: 0,
				name: "全部"
			}
			],
			current: 0,
			sideBarList: [],
			tagList: [{ id: 0, name: '全部' }, { id: 1, name: '基础阶段' }, { id: 2, name: '强化阶段' }, { id: 3, name: '冲刺阶段' }],
			pratiseList: [
			],
			// 添加本地文件缓存映射
			localFiles: {}
		};
	},
	components: {
		sideBar,
		tagList
	},
	methods: {
		chooseTag(id) {
			this.currentTagId = String(id);
			this.getAllInfoByCategory();
		},
		changeName(e) {
			if (!e || typeof e !== 'object') return;

			this.selName = e.name || '';
			this.getSecond();
			this.current = e.index || 0;
		},
		changeMlist(e) {
			if (!e || typeof e !== 'object') return;

			console.log(e);
			this.mName = e.name || '';
			this.current = 0;
			if (e.index == 1) {
				this.mIndex = 1;
				this.tagList = [{ id: 0, name: '全部' }];
				this.list = [{ id: 0, name: "政治" }, { id: 1, name: "英语" }, { id: 2, name: "数学" }, { id: 3, name: "专业课" }];
				this.selName = '政治';
				this.getSecond();
			} else {
				this.mIndex = 0;
				this.list = [];
				this.tagList = [{ id: 0, name: '全部' }, { id: 1, name: '基础阶段' }, { id: 2, name: '强化阶段' }, { id: 3, name: '冲刺阶段' }];
				this.getUserCourse();
			}
		},
		downDoc(item) {
			// 如果文件已下载，直接打开
			if (item.hasDown) {
				const localFile = this.localFiles[item.id];
				if (localFile && localFile.path) {
					this.openLocalFile(localFile.path);
					return;
				}
			}

			uni.showLoading({
				title: "资料下载中..."
			})

			// 通用下载逻辑
			uni.downloadFile({
				url: item.file_url,
				success: (res) => {
					if (res.statusCode === 200) {
						const tempFilePath = res.tempFilePath;

						// 记录下载次数
						uni.$u.http.post('/course/dowmLoadNum', { id: item.id }, { custom: { loading: false } });

						// #ifdef APP-PLUS
						try {
							// 使用uni.saveFile替代复杂的文件系统操作
							uni.saveFile({
								tempFilePath: tempFilePath,
								success: (saveRes) => {
									const savedFilePath = saveRes.savedFilePath;

									// 更新本地文件缓存
									this.localFiles[item.id] = {
										path: savedFilePath,
										name: item.name,
										timestamp: new Date().getTime()
									};

									// 保存缓存到本地存储
									uni.setStorageSync('yanqu_material_files', JSON.stringify(this.localFiles));

									// 更新UI显示
									this.updateItemDownloadStatus(item.id, true, false);

									uni.hideLoading();

									// 打开文件
									this.openLocalFile(savedFilePath);
								},
								fail: (err) => {
									console.error('保存文件失败:', err);
									uni.hideLoading();
									// 直接打开临时文件
									this.openLocalFile(tempFilePath);
								}
							});
						} catch (err) {
							console.error('保存文件过程中出错:', err);
							uni.hideLoading();
							// 直接打开临时文件
							this.openLocalFile(tempFilePath);
						}
						// #endif
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '下载失败',
							icon: 'none'
						});
					}
				},
				fail: function (err) {
					uni.hideLoading();
					uni.showToast({
						title: '文件下载失败',
						icon: 'none'
					});
				}
			});
		},
		// 打开本地文件
		openLocalFile(filePath) {
			uni.openDocument({
				filePath: filePath,
				showMenu: true,
				success: function () {
					console.log('文档打开成功');
				},
				fail: function (err) {
					console.error('打开文档失败:', err);
					uni.showToast({
						title: '无法打开此文件',
						icon: 'none'
					});
				}
			});
		},
		// 简化的检查文件是否存在方法
		checkFileExists(filePath) {
			return new Promise((resolve) => {
				// #ifdef APP-PLUS
				uni.getFileInfo({
					filePath: filePath,
					success: () => {
						resolve(true);
					},
					fail: () => {
						resolve(false);
					}
				});
				// #endif

				// #ifndef APP-PLUS
				resolve(false);
				// #endif
			});
		},
		// 更新列表项的下载状态
		updateItemDownloadStatus(itemId, hasDown, wasDownloaded = true) {
			const index = this.pratiseList.findIndex(item => item.id === itemId);
			if (index !== -1) {
				this.pratiseList[index].hasDown = hasDown;
				this.pratiseList[index].wasDownloaded = wasDownloaded;
				// 强制视图更新
				this.$forceUpdate();
			}
		},
		// 检查文件是否已下载
		async checkLocalFiles() {
			try {
				// 从本地存储加载文件缓存
				const cachedFiles = uni.getStorageSync('yanqu_material_files');
				if (cachedFiles) {
					this.localFiles = JSON.parse(cachedFiles);

					// 更新列表项的下载状态
					for (const item of this.pratiseList) {
						const localFile = this.localFiles[item.id];
						if (localFile) {
							// 检查文件是否仍然存在
							const exists = await this.checkFileExists(localFile.path);
							if (exists) {
								// 文件存在
								this.$set(item, 'hasDown', true);
								this.$set(item, 'wasDownloaded', false);
							} else {
								// 文件不存在
								this.$set(item, 'wasDownloaded', true);
								this.$set(item, 'hasDown', false);
							}
						} else {
							this.$set(item, 'hasDown', false);
							this.$set(item, 'wasDownloaded', false);
						}
					}

					// 保存更新后的缓存
					uni.setStorageSync('yanqu_material_files', JSON.stringify(this.localFiles));
				}
			} catch (e) {
				console.error('检查本地文件时出错:', e);
			}
		},
		changeItem(e) {
			this.sel = e.id
			this.getAllInfoByCategory();
		},
		open() {
			// console.log('open');
		},
		close() {
			this.show = false
			// console.log('close');
		},
		getUserCourse() {
			let that = this;
			uni.$u.http.get('/task/getUserCourse').then(rest => {
				try {
					const res = rest.data && rest.data.course_text ? rest.data.course_text : [];

					if (Array.isArray(res) && res.length > 0) {
						that.list = [];
						res.forEach(item => {
							if (item && item.name) {
								that.list.push({ name: item.name });
							}
						});

						if (that.list.length > 0) {
							that.selName = that.list[0].name;
							that.getSecond();
						} else {
							// 处理空列表情况
							that.list = [{ name: '默认分类' }];
							that.selName = '默认分类';
							that.getSecond();
						}
					} else {
						// 处理空数据情况
						that.list = [{ name: '默认分类' }];
						that.selName = '默认分类';
						that.getSecond();
					}
				} catch (err) {
					console.error('处理用户课程数据时出错:', err);
					// 设置默认数据
					that.list = [{ name: '默认分类' }];
					that.selName = '默认分类';
					that.getSecond();
				}
			}).catch(err => {
				console.error('获取用户课程失败:', err);
				// 设置默认数据
				that.list = [{ name: '默认分类' }];
				that.selName = '默认分类';
				that.getSecond();
			});
		},
		getAllInfoByCategory() {
			let that = this;
			uni.$u.http.get('/course/getAllInfoByCategory', {
				params: {
					cateIndex: that.mIndex || 0,
					name: that.selName || '',
					id: that.sel || 0,
					stage: that.currentTagId || '0'
				}
			}).then(res => {
				if (res.data && Array.isArray(res.data.data)) {
					that.pratiseList = res.data.data;
					// 检查本地文件状态
					that.$nextTick(() => {
						that.checkLocalFiles();
					});
				} else {
					that.pratiseList = [];
				}
			}).catch(err => {
				console.error('获取课程分类信息失败:', err);
				that.pratiseList = [];
			});
		},
		getSecond() {
			let that = this;
			that.sideBarList = [...that.sideTmpBarList];
			uni.$u.http.get('/course/infoCategory', {
				params: { name: that.selName || '' }
			}).then(res => {
				try {
					if (res.data && Array.isArray(res.data.data)) {
						const tmpData = res.data.data;
						if (!that.mIndex) {
							tmpData.forEach(vo => {
								if (vo && vo.id !== undefined && vo.name) {
									that.sideBarList.push({ id: vo.id, name: vo.name });
								}
							});
						}
					}
					that.getAllInfoByCategory();
				} catch (err) {
					console.error('处理课程分类信息时出错:', err);
					that.getAllInfoByCategory();
				}
			}).catch(err => {
				console.error('获取二级分类失败:', err);
				that.getAllInfoByCategory();
			});
		},
		//计算右侧滚动区域高度
		caculateRightScrolledContentHeight() {
			try {
				uni.getSystemInfo({
					success: (res) => {
						if (!res || !res.windowHeight) return;

						uni.createSelectorQuery().select('.introduction-container').boundingClientRect(data => {
							if (!data) return;

							//计算右侧课程 列表高度
							uni.createSelectorQuery().select('.mid-tabs').boundingClientRect(midData => {
								if (!midData) return;

								this.bottomHeight = res.windowHeight - data.height - midData.height + 'px';

								//课程列表高度
								uni.createSelectorQuery().select('.tag-items').boundingClientRect(tagsData => {
									if (!tagsData) {
										// 如果找不到tag-items，使用一个合理的默认值
										this.bottomScroolHeight = res.windowHeight - data.height - midData.height - 50 + 'px';
										return;
									}
									this.bottomScroolHeight = res.windowHeight - data.height - midData.height - tagsData.height + 'px';
								}).exec();
							}).exec();
						}).exec();
					},
					fail: (err) => {
						console.error('获取系统信息失败:', err);
					}
				});
			} catch (err) {
				console.error('计算滚动高度时出错:', err);
			}
		}
	},
	onLoad() {
		// 添加try-catch以防止初始化错误
		try {
			this.getUserCourse();
		} catch (err) {
			console.error('页面加载时出错:', err);
		}
	},
	computed: {
		rightScrolledContentHeight() {
			// 这个计算属性没有返回值，可能需要实现或删除
			return '0px';
		}
	},
	mounted() {
		try {
			this.caculateRightScrolledContentHeight();
		} catch (err) {
			console.error('组件挂载时出错:', err);
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	box-sizing: border-box;
	height: 100vh;
	overflow: hidden;

	.introduction-container {
		box-sizing: border-box;
		padding: 0 30rpx;
		background-color: #01997A;
	}

	.introduction {
		height: 120rpx;
		margin-top: 32rpx;
		background-color: #F6F7FB;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 22rpx 22rpx 0rpx 0rpx;

		.item {
			width: 25%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;

			.title {
				text {
					&:first-child {
						font-weight: bold;
						color: #201E2E;
						font-size: 32rpx;
					}

					&:last-child {
						font-size: 22rpx;
						color: #777777;
					}
				}
			}

			text {
				font-size: 18rpx;
				color: #777777;
			}
		}

	}

	.mid-tabs {
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 4rpx 1rpx rgba(184, 184, 184, 0.16);
		padding: 0 30rpx;

		.all-course-h1 {
			padding-bottom: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.green-block-big {
					width: 18rpx;
					height: 37rpx;
					background: #01997A;
					border-radius: 0rpx 10rpx 0rpx 10rpx;

				}

				text {
					padding-left: 10rpx;
					width: 128rpx;
					height: 45rpx;
					font-weight: bold;
					font-size: 32rpx;
					color: #1F1F27;
				}
			}


		}
	}

	.content {
		margin-top: 26rpx;
		display: flex;
		align-items: flex-start;
		box-sizing: border-box;
		background-color: $container-bg-color;

		.left {
			width: 140rpx;
			background-color: #fff;
		}

		.right {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;
			padding: 0 40rpx;

			.practise-container {
				margin-top: 22rpx;

				.exam-list {
					padding-top: 20rpx;
					padding-bottom: 50rpx;

					.item {
						display: flex;
						align-items: center;
						justify-content: flex-start;
						background-color: #fff;
						border-radius: 20rpx;
						padding: 18rpx 20rpx;
						margin-bottom: 20rpx;
						padding-right: 10rpx;

						.num {
							width: 40rpx;
							height: 40rpx;
							background-color: $main-color;
							color: #fff;
							border-radius: 50%;
							line-height: 40rpx;
							font-size: 24rpx;
							font-weight: bold;
							text-align: center;
							margin-right: 20rpx;

						}

						.title {
							margin-right: 20rpx;
							display: flex;
							flex: 2;
							align-items: center;
							justify-content: center;
							flex-direction: column;
							text-align: left;

							.title-name {
								width: 100%;
								font-weight: bold;
								font-size: 28rpx;
								color: #4A4A4C;
							}

							.date {
								color: #9F9F9F;
								font-size: 22rpx;
								width: 100%;
							}
						}

						.exam-progress {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.do-continue {
								width: 120rpx;
								color: #EE7878;
								font-size: 20rpx;
								margin-right: 10rpx;
								text-align: right;
							}
						}
					}
				}
			}
		}
	}
}
</style>