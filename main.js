import Vue from 'vue'
import App from './App'

// 假设您项目中已使用VueX
import store from './store'
// 引入全局uView
import uView from 'uview-ui'
import request from  '@/config/request.js'
import { addPermisionInterceptor, removePermisionInterceptor } from '@/uni_modules/x-perm-apply-instr/js_sdk/index.js'
addPermisionInterceptor('chooseImage', '为了修改个人头像和发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('chooseVideo', '为了发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('saveImageToPhotosAlbum', '为了保存推广海报到手机相册, 我们需要申请您设备的存储权限')
addPermisionInterceptor('getLocation', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('makePhoneCall', '为了联系客服/用户/咨询等, 我们需要申请您设备的拨打电话权限')
addPermisionInterceptor('getRecorderManager', '为了使用语言消息功能等, 我们需要申请您设备的麦克风权限')
addPermisionInterceptor('startLocationUpdate', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('scanCode', '为了识别二维码信息, 我们需要申请您设备的相机权限')
Vue.prototype.$store = store
Vue.config.productionTip = false
App.mpType = 'app'
// #ifdef APP-PLUS
// 重写 showModal
uni.showModal = function(options = {}) {
	
	return new Promise((resolve) => {
	  plus.nativeUI.confirm(
		options.content || '', 
		(e) => {
		  const result = { confirm: e.index === 0, cancel: e.index === 1 };
		  options.success && options.success(result);
		  resolve(result);
		}, 
		{
		  title: options.title || '提示',
		  buttons: [options.confirmText || '确定', options.cancelText || '取消'],
		  verticalAlign: 'center'
		}
	  );
	});

}
// #endif

uni.tip = (title, cb)=>{
	uni.showToast({
		title,
		duration:1500,
		icon:'none'
	})
	typeof cb!=='undefined' && setTimeout(()=>cb(), 1500)
}

uni.alert = (content)=>{
	return uni.showModal({
		title: '提示',
		content,
		showCancel: false, // Whether to show the cancel button
	});
}
//注册全局过滤器 处理null 类型
Vue.filter('handleNullValue', function (value) {
	if(value==null){
		return ''
	}
	return value
})
Vue.use(uView)
uni.$u.setConfig(
	{
		props: {
			 sizeType:"compressed",
		}
	},
)
uni.$u.config.unit = 'rpx'
const app = new Vue({
	store,
	...App
})
request(app)
// 引入请求封装，将app参数传递到配置中
//import('@/config/request.js').then(({default:req})=>req(app))
app.$mount()