<template>

	<view class="container" v-if="goodsDetail != ''">
		<view class="top-banner">
			<image :src="goodsDetail.image" mode="widthFix"></image>
		</view>
		<view class="title">
			<text class="title-name">[{{ goodsDetail.cate_name[0].name }}]{{ goodsDetail.title }}</text>
			<view class="center">
				<text v-if="goodsDetail.time_type == 1">开课时间：{{ goodsDetail.start_time_text }}-{{ goodsDetail.end_time_text }}</text>
				<text v-else>课程有效期：{{ goodsDetail.time_day }} 年</text>
				<text class="u-border-left">课时：{{ goodsDetail.time_hour }}</text>
			</view>
			<text class="money  u-border-bottom">￥{{ goodsDetail.price }}</text>
			<view class="extra-info u-border-bottom">
				<text v-for="(eitem, eindex) in equity" :key="eindex">{{ eitem }} </text>
				<!-- <text>教材包邮</text> -->
			</view>
			<view class="h1-title">
				包含以下课程
			</view>
			<!-- 授课信息 -->
			<view class="course-list u-border-bottom" v-for="(item, index) in goodsDetail.course" :key="index">
				<view class="mid">
					<view class="tag">
						{{ item.cate_text }}
					</view>
					<text class="date" v-if="goodsDetail.time_type == 1">{{ goodsDetail.start_time_text }}-{{ goodsDetail.end_time_text }} | 共{{ item.goods.time_hour }}课时</text>
					<text class="date" v-else>有效期 <text class="time_day">{{ goodsDetail.time_day }}</text>年| 共{{ item.goods.time_hour }}课时</text>
				</view>

				<view class="bottom">
					<view class="teacher-list">
						<view class="teacher-info" v-for="(titem, tindex) in item.teacher_list" :key="tindex">
							<image class="avatar" :src="titem.image_text" />
							<text>{{ titem.name }}</text>
						</view>
					</view>
					<view class="course-money">
						￥{{ item.goods.price }}
					</view>
				</view>
			</view>


			<view class="teachers">
				<view class="h1-title">
					授课老师
				</view>
				<scroll-view class="all-teacher-info" show-scrollbar="false" enhanced="true" :scroll-x="true">

					<view class="teacher-item" v-for="(i, index) in goodsDetail.teacher_detail" :key="index">
						<view class="top-info">
							<image :src="i.image_text"></image>
							<view class="teacher-extra">
								<text>{{ i.name }}</text>
								<text>{{ i.subject_text }}</text>
							</view>
						</view>
						<view class="bottom-info" v-if="i.tag_text">
							<text v-for="(tt, tin) in i.tag_text.split(',')" :key="tin">{{ tt.length > 10 ? tt.substring(0, 10) + '...' : tt }}</text>
						</view>
					</view>

				</scroll-view>


			</view>
		</view>
		<view class="tabs">
			<u-tabs :list="list" lineWidth="50" lineHeight="10" :lineColor="`url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/bottom_line_green.png) 100% 100%`" :activeStyle="{
				color: '#1F1F27',
				fontWeight: 'bold',
				transform: 'scale(1.25)'
			}" :inactiveStyle="{
				fontSize: '30rpx',
				color: '#777777',
				fontWeight: 'bold',
				transform: 'scale(1)'
			}" itemStyle="padding-left: 20rpx;width:33%; box-sizing: border-box; padding-right: 20rpx; height: 82rpx;" @click="chooseItem">
			</u-tabs>
			<view class="course-detail" v-show="currentIndex == 0">
				<view class="dir-tab m-t-30" v-if="course_length > 1">
					<text class="course-name" v-for="(courseInfo, index) in goodsDetail.course" :key="index" :class="[descIndex == index ? 'sel' : '']" @click="descIndex = index">{{ courseInfo.cate_text }}</text>
				</view>
				<rich-text :nodes="goodsDetail.course[descIndex].goods.desc"></rich-text>
			</view>

			<view class="course-dir" v-show="currentIndex == 1">
				<!-- 知识点 -->
				<!-- 直播信息 -->
				<view class="live-info try_info" v-if="auditFlag">
					<view class="s_btn" v-if="course_length > 1">
						<text v-if="goodsDetail.course[cin].chapter_audite.length > 0" :class="[audite_index == cin ? 'choose_this' : '']" @click="choose(cin, 'audite_index')" v-for="(cit, cin) in goodsDetail.cate_all_arr" :key="cin">{{ cit }}</text>
					</view>
					<template v-if="goodsDetail.course[audite_index].chapter_audite.length > 0">
						<view class="live-title">
							<text class="green-block"></text>
							<text>试听课</text>
						</view>
						<view class="live-date" v-for="(citem, cindex) in goodsDetail.course[audite_index].chapter_audite" :key="cindex">
							<view class="try">
								<text><text class="circle">●</text>{{ citem.alias_name ? (citem.alias_name.length > 18 ? citem.alias_name.substring(0, 17) + '...' : citem.alias_name) : (citem.name.length > 18 ? citem.name.substring(0, 17) + '...' : citem.name) }} </text>
							</view>
							<view class="btn-enter" @click="toAuditListen(citem)">
								立即试听
							</view>
						</view>


					</template>
				</view>




				<!-- <view class="dir-tab" v-if="course_length > 1">
					<text @click="choose(cin,'live_index')" :class="[live_index==cin ?'sel' :'']"  v-for="(cit,cin) in goodsDetail.cate_all_arr" :key="cin">{{cit}}</text>
				</view>
				<view v-else class="dir-tab">
					 <text class="sel">所有</text>
				</view> -->
				<!-- 直播信息 -->
				<!-- <view class="live-info">
					<view class="live-title">
						<text class="green-block"></text>
						<text>最近直播</text>
					</view>
					<view class="live-name">
						精讲最新时政题型总结
					</view>
					<view class="live-date">
						<view class="date">
							<text>孙老师 </text>
							<text>2025年12月12日20:00-21:00</text>
						</view>
						<view class="btn-enter">
							预约直播
						</view>
					</view>
				</view> -->
				<view class="dir-tab m-t-30" v-if="course_length > 1">
					<text :class="[course_index == cate_index ? 'sel' : '', 'course-name']" @click="choose(cate_index, 'course_index')" v-for="(cate_item, cate_index) in goodsDetail.cate_all_arr" :key="cate_index">{{ cate_item }}</text>
					<!-- <text>政治</text>
					<text>英语</text> -->
				</view>

				<view class="dir-tabs">
					<u-tabs :list="tempList" lineWidth="50" lineHeight="10" :lineColor="`url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/bottom_line_green.png) 100% 100%`" :activeStyle="{
						color: '#009c7b',
						fontWeight: 'bold',
						transform: 'scale(1.1)'
					}" :inactiveStyle="{
						fontSize: '26rpx',
						color: '#777777',
						fontWeight: 'bold',
						transform: 'scale(1)'
					}" itemStyle="padding-left: 20rpx;box-sizing: border-box; padding-right: 20rpx; height: 82rpx;" :current="coursecurrent" @click="chooseDirItem">
					</u-tabs>
				</view>

				<!-- 课程列表 -->

				<view class="course-container">
					<block v-if="showCourse.length > 0">
						<view v-for="(sitem, sindex) in showCourse" :key="sindex">
							<view class="title-name" @click="rotate(sindex)">
								<view class="left">
									<text class="green-block"></text>
									<text>{{ sitem.name }}</text>
								</view>
								<view class="expand" :animation="sindex == showIndex ? animationData : animationDataNew">
									<u-icon name="arrow-down" color="#FFF" size="22"></u-icon>
								</view>
							</view>
							<u-transition :show="sindex == showIndex ? true : false" mode="slide-left">
								<view class="course-list" v-if="sitem.selData.length > 0" v-for="(ssitem, ssindex) in sitem.selData" :key="ssindex">
									<view class="list-item u-border-top" v-if="ssitem.id > 0">
										<view class="title-text">
											<text>{{ ssitem.alias_name ? ssitem.alias_name : ssitem.name }}</text>
										</view>
										<view class="enter" v-if="goodsDetail.is_pay && goodsDetail.valid_time == 1" @click="toListen(sitem.id, ssitem.id)">
											进入课堂
										</view>
									</view>
								</view>
							</u-transition>
						</view>
					</block>
				</view>


			</view>

			<view class="course-detail course_detail" v-show="currentIndex == 2">
				<rich-text :nodes="courseEquity"></rich-text>
			</view>

		</view>
		<template v-if="goodsDetail.status == 1">
			<view class="bottom-opt" v-if="!goodsDetail.is_pay || !goodsDetail.valid_time">
				<view class="left">
					<text v-if="goodsDetail.is_free != 1" class="price">￥{{ goodsDetail.price }}</text>
					<text v-else class="price">免费</text>
					<!-- 	<text class="price">￥{{goodsDetail.price}}</text> -->
					<u-icon @click="toConsult" name="server-man" color="#777777" size="38" label='咨询' labelSize="12px" :labelPos="'bottom'"></u-icon>
				</view>
				<!-- ios移除购买功能 -->
				<!-- <template v-if="(userInfo.phone == '' || userInfo.phone == undefined)">
					<button class="btn btn-auth" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">{{ goodsDetail.is_free == 1 ? '立即领取' : '立即购买' }}</button>
				</template>
				<template v-else>
					<button class="btn  btn-auth" @click="buy">
						{{ goodsDetail.is_free == 1 ? '立即领取' : '立即购买' }}
					</button>
				</template> -->
			</view>
		</template>
		<template v-else-if="goodsDetail.status == 2">
			<view class="bottom-opt">
				<view class="left">
					<text class="price">--</text>
				</view>
				<button class="btn-gery  btn-auth">
					课程已下架
				</button>

			</view>
		</template>
		<template v-else-if="goodsDetail.status == 3">
			<view class="bottom-opt">
				<view class="left">
					<text class="price">--</text>
				</view>

				<button class="btn-gery  btn-auth">
					课程已停止销售
				</button>

			</view>
		</template>
	</view>
	<view class="container" v-else>
		<u-loading-page :loading="true" loading-text="加载中..." font-size="32"></u-loading-page>
	</view>
</template>

<script>
import { mapState } from "vuex"
export default {
	data() {
		return {
			try_index: '',
			equity: [],
			audite_index: 0,
			live_index: 0,
			course_index: 0,
			show: true,
			currentIndex: 0,
			dirIndex: 0,
			height: 0,
			sel_index: 0,
			coursecurrent: 0,
			turnOver: false, //折叠动画
			showIndex: -1,
			list: [{
				name: '课程详情'
			},
			{
				name: '课程目录'
			},
			{
				name: "课程权益"
			}
			],
			dirListRev: {},
			dirList: [],
			dirListCommon: [],
			tempList: [],
			animation: {},
			animationData: {},
			animationDataNew: {},
			goodsId: '',
			goodsDetail: '',
			course_length: 0,
			nowCourse: [],
			showCourse: [],
			auditFlag: false,
			desc: '',
			courseEquity: '',
			isPre: 0,
			descIndex: 0,

		};
	},
	watch: {
		userInfo: {
			handler: function (newV, oldV) {
				if (newV) {
					this.getGoodsDetail();
				}
			},
			deep: true
		}
	},
	computed: {
		...mapState('user', ['userInfo']),
	},
	async onLoad(option) {
		let that = this;
		if (option.scene != undefined) {
			const scene = decodeURIComponent(option.scene);
			let goodsString = scene.split("&")[0];

			that.goodsId = goodsString.split("=")[1];
			that.isPre = 1;

		} else {

			that.goodsId = option.goods_id;
		}

		that.getGoodsDetail();



	},
	onShareAppMessage(res) {
		return {
			title: '[' + this.goodsDetail.cate_name[0].name + ']' + this.goodsDetail.title,
			path: '/choosecoursepkg/courseDetail/courseDetail?goods_id=' + this.goodsId,
			imageUrl: this.goodsDetail.image,
			desc: '好老师,好服务',
		}

	},
	methods: {
		toConsult() {
			// 在按钮点击事件中触发
			let wxService = null;
			plus.share.getServices(res => {
				// 查找微信服务
				wxService = res.find(i => i.id === 'weixin');
				if (wxService) {
					wxService.openCustomerServiceChat({
						corpid: 'wwfb16515cd00205d9', // 企业 ID
						url: 'https://work.weixin.qq.com/kfid/kfc8d7614d20b955751' // 客服链接
					});
				} else {
					uni.showToast({ title: '当前环境不支持微信客服', icon: 'none' });
				}
			}, err => {
				uni.showToast({ title: '服务获取失败: ' + JSON.stringify(err), icon: 'none' });
			});
		},
		async decryptPhoneNumber(e) {
			//await getUserProfile()
			console.log(e);

			if (e.detail.errMsg.indexOf('ok') != -1) {
				const {
					code,
					data
				} = await uni.$u.http.post("/mini_user/getMobile", {
					code: e.detail.code
				})
				if (code == 1) {
					this.$store.commit('user/setUser', { phone: data.phone })
					this.buy()
				}
			}
		},
		toListen(chap_id, video_id) {
			let course_id = this.goodsDetail.course[this.course_index].course_goods_id;
			let course_in_type = this.goodsDetail.course[this.course_index].course_in_type;

			uni.navigateTo({
				url: '/choosecoursepkg/study/study?course_type=' + course_in_type + '&course_id=' + course_id + '&chapter_id=' + chap_id + '&video_id=' + video_id + '&conly_couse_id=' + (this.dirIndex + 1)
			})
		},
		toAuditListen(item) {
			let course_id = this.goodsDetail.course[this.course_index].course_goods_id;
			uni.navigateTo({
				url: '/choosecoursepkg/study/study?course_id=' + course_id + '&chapter_id=' + item.id + '&video_id=' + item.video_id + '&conly_couse_id=-1'
			})
		},
		buy() {
			uni.navigateTo({
				url: '/choosecoursepkg/confirm_order/confirm_order?goods_id=' + this.goodsId
			})
		},
		async getGoodsDetail() {
			let that = this;

			let ret = await uni.$u.http.get('/index/courseDetail', { params: { goods_id: this.goodsId } }).then(rest => {

				if (rest.code == 0) {
					uni.tip(rest.msg)
					setTimeout(function () {
						uni.navigateBack({
							delta: 1,
							fail: () => {
								uni.switchTab({
									url: '/pages/index/index'
								})
							}
						})
					}, 1000)
					return false;
				}
				let res = rest.data
				that.goodsDetail = res
				console.log(that.goodsDetail)
				that.course_length = that.goodsDetail.course.length
				that.equity = that.goodsDetail.equity.split(',')
				that.nowCourse = that.goodsDetail.allCourse[that.course_index]

				//获取所有课程模块
				that.dirListCommon = res.stageList;

				that.dirListRev = res.stageListRev;
				console.log(that.nowCourse);

				Object.keys(that.nowCourse).forEach((value, key) => {
					that.dirList.push(that.dirListCommon[value - 1]);
				});
				//that.tempList = [...that.dirList]
				let tmpCourse = that.goodsDetail.course
				that.desc = that.goodsDetail.desc
				that.desc = this.desc.replace(
					/<img/gi,
					'<img style="max-width:100%;height:auto;float:left;display:block" '
				);
				that.courseEquity = that.goodsDetail.course_equity;
				that.courseEquity = this.courseEquity.replace(
					/<img/gi,
					'<img style="max-width:100%;height:auto;float:left;display:block" '
				);
				tmpCourse.forEach((item, index) => {
					if (item.chapter_audite.length > 0) {
						that.audite_index = index;
						that.auditFlag = true;
						return

					}
				})

			})
		},
		choose(index, name) {
			console.log(1111)
			console.log(index, name)
			this[name] = index;
			if (name == 'course_index') {
				this.nowCourse = this.goodsDetail.allCourse[this.course_index]
				this.dirList = [];
				Object.keys(this.nowCourse).forEach((value, key) => {
					this.dirList.push(this.dirListCommon[value - 1]);
				});
				this.tempList = this.dirList
				this.showCourse = this.nowCourse[this.dirListRev[this.dirList[0].name]]
				this.coursecurrent = 1;
				this.$nextTick(() => {    //（重要2）
					this.coursecurrent = 0;
				});
				//console.log(this.coursecurrent)
			}
		},
		chooseItem(item) {
			this.currentIndex = item.index
			if (item.index == 1) {
				//重新计算 子tab
				this.dirList = [];
				Object.keys(this.nowCourse).forEach((value, key) => {
					this.dirList.push(this.dirListCommon[value - 1]);
				});

				this.tempList = this.dirList
				this.showCourse = this.nowCourse[this.dirListRev[this.dirList[0].name]]
				this.coursecurrent = 1;
				this.$nextTick(() => {    //（重要2）
					this.coursecurrent = 0;
				});
			}
		},
		chooseDirItem(item) {
			this.dirIndex = item.index
			let t_index = this.dirListRev[item.name]
			this.showCourse = this.nowCourse[t_index] ? this.nowCourse[t_index] : [];
			this.showIndex = -1;
			this.animation.rotate(0).step()
			this.animationData = this.animation.export()
			this.turnOver = false


		},
		rotate(e) {
			console.log(this.turnOver, e, this.showIndex)
			if (e == this.showIndex) {
				if (this.turnOver) {
					this.showIndex = -1;
					this.animation.rotate(-180).step()
				} else {
					this.animation.rotate(0).step()
					this.showIndex = e;
				}
				this.animationData = this.animation.export()
				this.turnOver = !this.turnOver
				console.log(this.turnOver, this.showIndex)

			} else {
				this.animationData = this.animation.rotate(-180).step().export()
				this.animation.rotate(0).step()
				this.animationDataNew = this.animation.export()
				this.showIndex = e;
				console.log("showindex", this.showIndex)
				this.turnOver = !this.turnOver
			}



		}

	},
	created() {
		var animation = uni.createAnimation({
			duration: 500,
			timingFunction: 'ease',
		})
		this.animation = animation
	}
}
</script>

<style lang="scss" scoped>
.container {


	view {
		box-sizing: border-box;
	}

	.m-t-30 {
		margin-top: 30rpx;
	}

	padding-bottom: 120rpx;

	.h1-title {
		width: 100%;
		padding: 26rpx 0;
		color: #201E2E;
		text-align: left;
		font-weight: bold;
		font-size: 32rpx;
		text-align: left;
	}

	background-color: $container-bg-color;
	min-height: 100vh;
	padding-top: 20rpx;

	.top-banner {
		display: flex;
		align-items: center;
		justify-content: center;

		image {
			width: 625rpx;
		}
	}

	.title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: column;
		background-color: #fff;
		padding: 0 30rpx;
		padding-top: 22rpx;
		width: 100%;
		text-align: left;
		color: #060606;
		font-size: 32rpx;
		font-weight: bold;

		.title-name {
			width: 100%;

		}

		.center {
			font-weight: normal;
			margin-top: 12rpx;
			color: #777777;
			font-size: 26rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			text-align: left;

			text {
				&:first-child {
					padding-right: 13rpx;
				}

				&:last-child {
					padding-left: 13rpx;
				}
			}
		}

		.money {
			margin-top: 25rpx;
			display: inline-block;
			color: #E16965;
			font-size: 34rpx;
			font-weight: bold;
			width: 100%;
			text-align: left;
			padding-bottom: 22rpx;
		}

		.extra-info {
			width: 100%;
			font-size: 26rpx;
			color: #706E6E;
			font-weight: normal;
			padding: 26rpx 0;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			text {
				margin-right: 8rpx;
			}
		}

		.course-list {
			margin-bottom: 22rpx;
			background-color: #fff;
			border-radius: 16rpx;
			width: 100%;

			.mid {
				margin-top: 6rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.tag {
					padding: 0 8rpx;
					height: 48rpx;
					background-color: #EEFAF6;
					color: $main-color;
					line-height: 48rpx;
					font-size: 22rpx;
					font-weight: bold;
					border-radius: 10rpx;
				}

				.date {
					margin-left: 8rpx;
					font-size: 22rpx;
					color: #A4A4A4;

					.time_day {
						font-weight: 700;
						font-size: 24rpx;

					}
				}
			}

			.bottom {
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.teacher-list {
					padding-left: 36rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					padding-bottom: 20rpx;

					.teacher-info {
						margin-right: 24rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						font-size: 22rpx;
						color: #818181;

						.avatar {
							width: 60rpx;
							height: 60rpx;
							border-radius: 100%;
							margin-bottom: 2rpx;
						}

					}
				}

				.course-money {
					color: #E16965;
					font-size: 38rpx;
					margin-right: 34rpx;
				}
			}

		}


		.teachers {
			width: 100%;
			padding-bottom: 26rpx;

			.all-teacher-info {
				width: 100%;
				white-space: nowrap;

				.teacher-item {
					width: 30%;
					display: inline-block;
					align-items: center;
					justify-content: space-between;
					flex-direction: column;

					.top-info {
						display: flex;
						align-items: center;
						justify-content: center;

						image {
							width: 80rpx;
							height: 80rpx;
							border-radius: 50%;
							margin-right: 6rpx;
						}

						.teacher-extra {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: space-between;

							text {
								font-size: 22rpx;

								&:first-child {
									color: #818181;

								}

								&:last-child {
									color: #201E2E;
								}
							}
						}
					}

					.bottom-info {
						margin-top: 6rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
						flex-direction: column;

						text {
							color: #201E2E;
							font-size: 16rpx;
						}
					}
				}
			}
		}
	}

	.tabs {
		background-color: #fff;
		width: 100%;
		margin-top: 10rpx;

	}

	.dir-tab {
		display: flex;
		align-items: center;
		justify-content: flex-start;

		.course-name {
			padding: 8rpx;
			margin-left: 10rpx;
			border-radius: 12rpx;
			text-align: center;
			line-height: 60rpx;
			font-size: 26rpx;
			font-weight: bold;
			color: #201E2E;
			background-color: #fff;
			margin-right: 20rpx;

		}

		.sel {
			background: #01997A !important;
			color: #fff !important;
		}
	}

	.course-detail {
		width: 95%;
		margin: 10rpx auto;

		.course-name {
			background-color: $container-bg-color;
		}

		.dir-tab {
			padding-left: 30rpx;
		}
	}

	.course-dir {
		background-color: $container-bg-color;
		padding: 0 30rpx;
		padding-top: 16rpx;


		.try_info {
			position: relative;

			.s_btn {
				position: absolute;
				top: -45rpx;
				right: 0;
				background: #fff;

				text {
					padding: 10rpx;

					&:first-child {
						border-radius: 20rpx 0 0 0;
					}

					&:last-child {
						border-radius: 0 20rpx 0 0;
					}

				}


				.choose_this {
					color: #fff;
					background: rgb(0, 156, 123);
				}
			}

		}

		.live-info {
			margin-top: 70rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx 28rpx;

			.live-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				color: #0A0A0A;
				font-size: 28rpx;
				font-weight: bold;

				.green-block {
					width: 14rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #01997A;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
					margin-right: 6rpx;
				}

			}

			.live-name {
				color: #4A4A4C;
				font-size: 30rpx;
				text-align: left;
				margin-top: 24rpx;
			}

			.live-date {
				margin-top: 20rpx;

				display: flex;
				align-items: center;
				justify-content: space-between;

				.date {
					color: #A2A2A2;
					font-size: 24rpx;

					text {
						margin-right: 16rpx;
					}
				}

				.try {
					font-size: 26rpx;
					font-weight: bold;

					text {
						margin-right: 24rpx;
					}

					.circle {
						color: #CDF3E7;
					}
				}

				.btn-enter {
					width: 129rpx;
					height: 44rpx;
					background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
					border-radius: 28rpx;
					line-height: 44rpx;
					text-align: center;
					color: #fff;
					font-size: 24rpx;
					font-weight: bold;
				}

			}
		}

		.dir-tabs {
			margin-top: 32rpx;
		}

		.course-container {
			margin-top: 46rpx;
			background-color: transparent;
			border-radius: 20rpx;

			.title-name {

				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 28rpx;
				margin-top: 28rpx;
				background-color: #fff;
				border-radius: 20rpx;

				// border-top-right-radius: 20rpx;
				// border-top-left-radius: 20rpx;
				.left {
					color: #4A4A4C;
					font-size: 28rpx;
					font-weight: bold;

					.green-block {
						display: inline-block;
						width: 14rpx;
						height: 28rpx;
						line-height: 28rpx;
						background: #01997A;
						border-radius: 0rpx 8rpx 0rpx 8rpx;
						margin-right: 6rpx;
					}
				}

				.expand {
					width: 30rpx;
					height: 30rpx;
					background: #01997A;
					border-radius: 50%;
					display: flex;
					align-items: center;
					transition: transform 0.5s ease;
					justify-content: center;

					.rotate {
						// transform: rotate(180deg);
					}
				}
			}
		}

		.course-list {
			height: auto;
			overflow: hidden;
			transition: height 0.8s ease;

			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 28rpx;
				background-color: #fff;

				&:last-child {
					border-bottom-left-radius: 20rpx;
					border-bottom-right-radius: 20rpx;
				}

				.title-text {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					font-size: 26rpx;
					color: #4A4A4C;

				}

				.enter {
					width: 130rpx;
					height: 44rpx;
					background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
					border-radius: 60rpx;
					color: #fff;
					text-align: center;
					line-height: 44rpx;
					font-weight: bold;
					font-size: 22rpx;
				}
			}
		}
	}

	.bottom-opt {
		width: 100%;
		padding: 0 22rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: fixed;
		bottom: 0;
		height: 120rpx;
		background-color: #fff;

		.left {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.price {
				font-size: 32rpx;
				color: #E16965;
				margin-right: 24rpx;
			}

		}

		.btn {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			text-align: center;
			border-radius: 40rpx;
			background-color: $main-color;
		}

		.btn-gery {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			text-align: center;
			border-radius: 40rpx;
			background-color: grey;
		}

		.btn-auth {
			margin-right: 20rpx;
		}
	}
}
</style>