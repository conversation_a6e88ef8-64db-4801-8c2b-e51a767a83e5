<template>
    <view class="wx-login-container">
        <view class="logo-box">
            <image src="/static/png/logo.png" mode="aspectFit" class="logo"></image>
        </view>

        <view class="welcome">
            <view class="title">欢迎使用研趣在线</view>
            <view class="subtitle">登录后体验更多功能</view>
        </view>

        <view class="btn-group">
            <button class="wx-btn" @click="handleWxLogin">
                <text class="iconfont icon-weixin"></text>
                微信一键登录
            </button>
            <view class="phone-login" @click="goPhoneLogin">手机号登录</view>
        </view>

        <view class="agreement">
            <u-checkbox-group @change="checkboxChange">
                <u-checkbox size="10px" activeColor="#009c7b" iconSize="10px" name="1" shape="circle"></u-checkbox>
            </u-checkbox-group>
            <text>我已认真阅读并同意 </text>
            <text class="link" @click="privacy(4)">《用户协议》</text>
            和
            <text class="link" @click="privacy(3)">《隐私政策》</text>
        </view>
    </view>
</template>

<script>
import { mapMutations } from 'vuex'

export default {
    data() {
        return {
            check: 0
        }
    },
    computed: {
        canLogin() {
            return this.check == 1
        }
    },
    methods: {
        ...mapMutations('user', ['setUser']),

        // 处理微信登录
        async handleWxLogin() {
            if (!this.canLogin) {
                uni.showToast({
                    title: '请同意用户协议和隐私政策',
                    icon: 'none'
                })
                return
            }

            try {
                const [error, loginRes] = await uni.login({
                    provider: 'weixin'
                })

                if (error) {
					console.log(error)
                    throw new Error('微信登录失败')
                }

                // 调用后端登录接口
                const res = await uni.$u.http.post('/api/wx/login', {
                    code: loginRes.code
                })

                if (res.code === 200) {
                    const userData = res.data
                    if (!userData.phone) {
                        // 未绑定手机号，跳转到手机号绑定页面
                        uni.navigateTo({
                            url: '/subpkg/login/login'
                        })
                        return
                    }

                    // 已绑定手机号，保存用户信息
                    this.setUser(userData)
                    uni.setStorageSync('token', userData.token)

                    uni.showToast({
                        title: '登录成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.navigateBack({
                            delta: 1,
                            fail: () => {
                                uni.switchTab({
                                    url: '/pages/index/index'
                                })
                            }
                        })
                    }, 1500)
                } else {
                    throw new Error(res.msg)
                }
            } catch (error) {
                uni.showToast({
                    title: error.message || '登录失败，请重试',
                    icon: 'none'
                })
            }
        },

        // 跳转到手机号登录
        goPhoneLogin() {
            uni.navigateTo({
                url: '/subpkg/login/login'
            })
        },

        // 复选框变化
        checkboxChange(n) {
            this.check = n[0] == 1 ? n[0] : 0
        },

        // 查看协议
        privacy(type) {
            uni.navigateTo({
                url: "/subpkg/scan_login/agree_index?type=" + type
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.wx-login-container {
    padding: 40rpx;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;

    .logo-box {
        margin: 60rpx 0;

        .logo {
            width: 240rpx;
            height: 240rpx;
        }
    }

    .welcome {
        text-align: center;
        margin: 40rpx 0;

        .title {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 20rpx;
        }

        .subtitle {
            font-size: 28rpx;
            color: #666;
        }
    }

    .btn-group {
        width: 80%;
        margin: 60rpx 0;

        .wx-btn {
            width: 100%;
            height: 90rpx;
            line-height: 90rpx;
            background: $main-color;
            color: #fff;
            border-radius: 45rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .iconfont {
                margin-right: 10rpx;
                font-size: 40rpx;
            }
        }

        .phone-login {
            text-align: center;
            margin-top: 30rpx;
            color: $main-color;
            font-size: 28rpx;
        }
    }

    .agreement {
        position: fixed;
        bottom: 60rpx;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #999;

        .link {
            color: $main-color;
            margin: 0 4rpx;
        }
    }
}
</style>