<template>
  <view class="cover">
    <view class="identify">
      <view class="border">
        <view class="border-flat"></view>
      </view>
    </view>
    <slot></slot>
  </view>
</template>

<script>
export default {
  data() {
    return {
      coverHeight: '50vh' // 固定高度为屏幕一半
    }
  }
}
</script>

<style lang="scss" scoped>
.cover {
  position: fixed;
  width: 100%;
  height: 50vh;
  top: 50vh;
  background: #F7F7F7;
  box-shadow: 0rpx 0rpx 12rpx 0rpx rgba(19, 55, 150, 0.08);
  border-radius: 50rpx 50rpx 0 0;
  z-index: 999;
  display: flex;
  flex-direction: column;

  .identify {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50rpx;
    flex-shrink: 0;
  }

  .border-flat {
    height: 10rpx;
    width: 65rpx;
    background-color: #e1e0e3;
    border-radius: 20%;
  }
}
</style>
