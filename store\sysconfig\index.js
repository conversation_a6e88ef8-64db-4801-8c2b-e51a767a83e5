import { webviewUrl, baseUrl } from "@/utils/setting"
const sysConfig = {
	namespaced: true,
	state:{
		webviewUrl:webviewUrl,
		baseUrl:baseUrl
	},
	mutations:{
		setRequestConfig(state, payload){
			state.webviewUrl = payload.webviewUrl
			state.baseUrl = payload.requestUrl
		}
	},
	actions:{
		async getRequestConfig(context, payload){
			console.log('getRequestConfig start')
			const data = await uni.$u.http.get('/index/getSysConfig',{custom:{loading:false}})
			console.log('getRequestConfig end', data)
			if(data.code ==1){
				context.commit('setRequestConfig', data.data)
			}
		}
	}
}
export default sysConfig