<template>
	<view class="container">

		<view class="container-info">
			<!-- 地址信息 -->
			<view class="address" v-if="showAdress">
				<view class="info">
					<view class="user-address">
						<text>{{ address.name }} {{ address.phone }}</text>
						<text class="tag" v-if="address.is_default">默认</text>
					</view>
					<text class="detail">{{ address.province_name + address.city_name + address.region_name + address.detail }}</text>
				</view>
				<u-icon @click="modifyAddress" name="arrow-right" size="38"></u-icon>
			</view>
			<view class="add-address" v-else>
				<view class="add-address-btn" @click="addAddress">
					+添加地址
				</view>
			</view>
			<!-- 课程信息 -->
			<view class="course-info">
				<view class="title">
					课程信息
				</view>
				<view class="info-container u-border-bottom" v-for="(item, index) in goodsDetail.course" :key="index">
					<view class="price">
						<text>【{{ item.cate_text }}】{{ item.title }}</text>
						<text>￥{{ item.goods.price }}</text>
					</view>
					<view class="course-detail">
						<text class="tag">{{ item.cate_text }}</text>

						<text class="date" v-if="item.goods.time_type == 1">{{ item.goods.start_time_text }}-{{ item.goods.end_time_text }} | 共{{ item.goods.time_hour }}课时</text>
						<text class="date" v-else>有效期 <text class="time_day">{{ item.goods.time_day }}</text>年 | 共{{ item.goods.time_hour }}课时</text>

					</view>
					<view class="teacher-info">
						<text>主讲老师:</text>
						<text v-for="(i, teacherIndex) in item.teacher_list" :key="teacherIndex">{{ i.name + ',' }}</text>
					</view>
				</view>

			</view>

			<!-- 付款信息 -->
			<view class="pay-info">
				<view class="h1-title">
					付款信息
				</view>
				<view class="info">
					<text>商品金额</text>
					<text>￥{{ goodsDetail.price }}</text>
				</view>
				<view class="info" v-if="goodsDetail.sale_price > 0">
					<text>活动优惠</text>
					<text class="color-red">￥{{ goodsDetail.sale_price }}</text>
				</view>
				<!-- <view class="info ">
					<text>抵扣优惠</text>
					<text class="color-red">￥0</text>
				</view> -->
				<view class="info" v-if="couponList.length > 0">
					<text>优惠券优惠</text>
					<view class="choose-coupon" @click="open">
						<text class="color-red">￥{{ couponMoney }}</text>
						<u-icon name="arrow-right" size="28"></u-icon>
					</view>
				</view>
			</view>

			<!-- 支付方式 -->
			<view class="pay-info other-info">
				<view class="h1-title">
					支付方式
				</view>
				<view class="pay-type">
					<u-icon name="weixin-fill" color="rgb(40  183 97)" size="48" label="微信" labelSize="14px"></u-icon>
					<u-checkbox-group>
						<u-checkbox size="16px" activeColor="#009c7b" checked v-model="payCheck" shape="circle">

						</u-checkbox>
					</u-checkbox-group>
				</view>
			</view>

			<!-- <view class="author-info">
				<u-checkbox-group>
					<u-checkbox size="10px" activeColor="red" v-model="readCheck" shape="circle"></u-checkbox>
					<text>我已阅读 </text>
					<text>备案内容承诺公式、退款协议</text>
				</u-checkbox-group>
			</view>
			<view class="tip">
				*涉及优惠活动的订单发起退款时，实际退费金额需要重新核算
			</view> -->


		</view>
		<view class="bottom-opt">
			<view class="left">
				<text class="real-money" v-if="goodsDetail.is_free != 1">实付金额
					<text>￥{{ lastGoodsPrice }}</text>
				</text>
				<text class="real-money" v-else>
					<text>免费</text>
				</text>
				<text class="preferential">已优惠￥{{ goodsDetail.is_free != 1 ? lastSalePrice : goodsDetail.price }}</text>
			</view>

			<view class="btn" @click="buy">
				{{ goodsDetail.is_free ? '确认领取' : '立即购买' }}
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
		<u-loading-page :loading="loading"></u-loading-page>
		<!-- 优惠券底部弹出层 -->

		<u-popup :show="showCoupon" mode="bottom" @close="close" :safeAreaInsetTop="true">
			<view class="coupon-conainer">
				<view class="title">
					<text>优惠券详情</text>
					<u-icon class="close" @click="close" top="10" name="close"></u-icon>
				</view>
				<view class="coupon-list">
					<view class="num">
						<view>可用优惠券({{ couponList.length }})</view>
						<view @click='cancelCoupon'>不使用优惠</view>
					</view>
					<view class="coupon-container">
						<u-radio-group placement="column" @change="groupChange" v-model="couponDeafultSel">
							<view class="coupon" v-for="item in couponList" :key="item.id">
								<view class="left">
									<text>{{ item.coupon_price }} <text style="font-size: 32rpx;">元</text></text>
									<text>满{{ parseFloat(item.use_min_price) }}元可用</text>
								</view>
								<view class="right">
									<view class="name">
										{{ item.coupon_title }}
									</view>
									<view class="detail-info">
										<view class="detail-left">
											<text class="date">有效期 {{ item.end_time_text }}</text>
											<view class="rule">
												<text>使用规则</text>
												<u-icon name="arrow-right" color="#009c7b" size="24"></u-icon>
											</view>
										</view>
										<view class="detail-right">
											<u-radio :name="item.id" size="20px" activeColor="#009c7b" iconSize="18px">
											</u-radio>

										</view>
									</view>
								</view>
							</view>
						</u-radio-group>
					</view>

				</view>
				<view class="bottom">
					<view class="btn" @click="close">
						确认
					</view>
				</view>

			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showCoupon: false,
			showAdress: true,
			payCheck: true,
			goodsId: 0,
			goodsDetail: {},
			address: {},
			coupon_price: 0.00,
			loading: true,
			readCheck: true,
			couponvalue: "",
			num: 0,
			couponList: [],
			couponDeafultSel: 0, //默认选中的优惠券
			selcoupon: {}, //选中的优惠券
			couponMoney: 0, //优惠金额
			lastGoodsPrice: 0,
			lastSalePrice: 0,
			goodsFormPrice: {},

		};
	},
	filters: {
		happenPirceFun(price) { //时间戳数据处理
			return price > 0.01 ? price : 0.01;
		},
	},
	onShow() {
		this.getAddress();
	},

	onLoad(option) {
		this.checkOrderStatus();
		this.goodsId = option.goods_id;
		this.getGoodsDetail();
	},
	methods: {
		async checkOrderStatus() {
			let ret = await uni.$u.http.get('/order/changeOrderStatus', {})
		},
		close() {
			this.showCoupon = false
		},
		open() {
			this.showCoupon = true
		},
		cancelCoupon() {
			let that = this;
			that.couponDeafultSel = 0;
			that.selcoupon = {};
			that.couponMoney = 0;
			that.showCoupon = false
			that.goodsFormPrice.coupon_price = 0;
			that.changePrice();
		},
		async getCouponList() {
			let that = this;
			let ret = await uni.$u.http.get('/order/getUserCoupon', {
				params: {
					goods_id: this.goodsId
				}
			}).then(rest => {
				let res = rest.data
				that.couponList = res

				if (res.length > 0) {
					let couponPrice = 0
					let couponTmp = 0
					res.forEach(function (item, index) {
						if (item.coupon_price > couponPrice) {
							couponPrice = item.coupon_price;
							couponTmp = item;
							that.couponDeafultSel = item.id;
						}

					});
					that.selcoupon = couponTmp
					that.couponMoney = couponPrice
					that.goodsFormPrice.coupon_price = that.couponMoney;

				}
				that.changePrice();
			})
		},
		groupChange(n) {
			//选中的优惠券
			this.selcoupon = this.couponList.find(item => item.id == n)
			this.couponMoney = this.selcoupon.coupon_price
			this.showCoupon = false
			this.goodsFormPrice.coupon_price = this.couponMoney;
			this.changePrice();
		},
		async getGoodsDetail() {
			let that = this;

			let ret = await uni.$u.http.get('/index/courseDetail', {
				params: {
					goods_id: this.goodsId
				}
			}).then(res => {
				// that.loading = false;
				that.goodsDetail = res.data;

				that.goodsFormPrice.goods_price = that.goodsDetail.price;
				that.goodsFormPrice.sale_price = that.goodsDetail.sale_price;

				if (that.goodsDetail.is_free == 1) {
					this.cancelCoupon()
				} else {
					that.getCouponList();
				}

				//that.changePrice();

			})
		},
		changePrice() {
			//this.lastGoodsPrice = ((parseInt(this.goodsDetail.price *100) - parseInt(goodsDetail.sale_price * 100) - parseInt(couponMoney * 100))/100) | happenPirceFun}}
			uni.$u.http.post('/order/calcGoodsPrice', this.goodsFormPrice).then(res => {
				this.loading = false;
				this.lastGoodsPrice = res.data.goods_price;
				this.lastSalePrice = res.data.lastSalePrice;
			})

			if (this.goodsDetail.is_free == 1) {
				this.lastSalePrice = this.goodsDetail.price;
			}

		},
		addAddress() {
			uni.navigateTo({
				url: '/choosecoursepkg/add_address/add_address'
			})
		},
		modifyAddress() {
			uni.navigateTo({
				url: '/choosecoursepkg/add_address/add_address?id=' + this.address.address_id
			})
		},
		async getAddress() {
			let that = this;
			let ret = await uni.$u.http.get('/order/getAddress').then(res => {

				that.address = res.data;
				if (that.address != null) {
					that.showAdress = true;
				} else {
					that.showAdress = false;
				}

			})
		},
		buy() {
			let that = this;
			if (!that.showAdress) {
				uni.tip('请填写地址')
				return false;
			}
			let req_data = {
				goods_id: this.goodsDetail.id,
				address_id: this.address.address_id,
				discount_activity_id: this.goodsDetail.activity_id,
				deduction_price: this.goodsDetail.sale_price,
				coupon_id: this.couponDeafultSel
			};

			let ret = uni.$u.http.post('/order/createOrder', req_data).then(res => {
				if (res.data.isZeroBuy == 1) { // 免费  or 0 元购
					uni.$u.http.post('/order/userZeroBuyOrFree', {
						order_id: res.data.order_id
					}).then(payret => {
						if (payret.code == 1) {
							uni.navigateTo({
								url: '/choosecoursepkg/order_detail/order_detail?type=1&order_id=' +
									res.data.order_id
							})
						} else {
							uni.tip('购买失败,请稍后再试')
							return false;
						}
					})
				} else {
					uni.$u.http.post('/order/wxAppPay', {
						order_id: res.data.order_id
					}).then(paymsg_data => {
						let paymsg = paymsg_data.data
						
						uni.requestPayment({
							provider: "wxpay",
							"orderInfo": paymsg,
							success(rest) {
								if (rest.errMsg == 'requestPayment:ok') {
									uni.navigateTo({
										url: '/choosecoursepkg/order_detail/order_detail?type=1&order_id=' +
											res.data.order_id
									})
								}
							},
							fail(e) {
								console.log(e);
							},
							complete(e) {
								console.log(e);
							}
						})
					});
				}
			})
		}

	}
}
</script>

<style lang="scss" scoped>
.container {
	padding-bottom: 120rpx;

	view {
		box-sizing: border-box;
	}

	.coupon-conainer {
		height: 800rpx;
		overflow-y: auto;

		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #0A0A0A;
			font-size: 32rpx;
			font-weight: bold;
			position: relative;
			padding: 10rpx 0;

			::v-deep .uicon-close {
				position: absolute;
				top: 18rpx;
				transform: translateY(-50%);
				right: 60rpx;
			}

		}

		.bottom {
			width: 100%;
			display: flex;
			justify-content: center;

			.btn {
				width: 400rpx;
				height: 80rpx;
				line-height: 80rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				text-align: center;
				border-radius: 40rpx;
				background-color: $main-color;
				position: fixed;
				bottom: 16rpx;
			}
		}

		::v-deep .u-radio-group {
			padding-bottom: 100rpx;
		}

		.coupon-list {
			.num {
				font-size: 28rpx;
				color: #0A0A0A;
				text-align: left;
				padding: 0 30rpx;
				display: flex;
				justify-content: space-between;
			}

			.coupon-container {
				margin-top: 36rpx;
				padding: 0 30rpx;

				.coupon {
					margin-bottom: 28rpx;
					height: 170rpx;
					background-size: contain;
					background-repeat: no-repeat;
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.left {
						width: 192rpx;
						height: 100%;
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
						background-repeat: no-repeat;
						background-size: contain;
						color: #fff;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						text {
							&:first-child {
								font-weight: bold;
								font-size: 40rpx;
							}

							&:last-child {
								color: #CDF3E7;
								font-size: 26rpx;
							}
						}
					}

					.right {
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
						background-repeat: no-repeat;
						background-size: contain;
						width: 500rpx;
						height: 100%;
						padding: 24rpx 34rpx;

						.name {
							color: #4C5370;
							font-size: 30rpx;
							font-weight: bold;
						}

						.detail-info {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.detail-left {
								text.date {
									color: #AFAFAF;
									font-size: 24rpx;
								}

								.rule {
									margin-top: 16rpx;
									display: flex;
									align-items: center;
									justify-content: flex-start;

									text {
										font-size: 24rpx;
										color: $main-color;
									}
								}
							}

							.detail-right {
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: center;

								.use-btn {
									width: 132rpx;
									height: 54rpx;
									border-radius: 32rpx;
									border: 1rpx solid #01997A;
									color: #01997A;
									font-weight: 500;
									line-height: 54rpx;
									text-align: center;
									font-size: 26rpx;
								}
							}
						}
					}
				}
			}
		}
	}

	.container-info {
		background-color: $container-bg-color;
		padding: 0 30rpx;
		padding-top: 36rpx;

		padding-bottom: 130rpx;

		.address {
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 34rpx 28rpx;
			border-radius: 12rpx;

			.info {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;
				text-align: left;

				.user-address {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					color: #060606;
					font-weight: bold;
					font-size: 28rpx;

					.tag {
						width: 67rpx;
						height: 40rpx;
						background: #EEFAF6;
						border-radius: 10rpx;
						line-height: 40rpx;
						text-align: center;
						color: $main-color;
						font-size: 22rpx;
						margin-left: 18rpx;
					}
				}

				text.detail {
					width: 100%;
					margin-top: 14rpx;
					color: #5A5A5A;
					font-weight: bold;
					font-size: 24rpx;
				}
			}
		}

		.add-address {
			height: 150rpx;
			background: #FFFFFF;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.add-address-btn {
				width: 570rpx;
				height: 76rpx;
				background: #CDF3E7;
				border-radius: 12rpx;
				color: #01997A;
				line-height: 76rpx;
				text-align: center;
			}
		}

		.course-info {
			margin-top: 30rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 22rpx 28rpx;

			.title {
				font-size: 30rpx;
				color: #201E2E;
				font-weight: bold;
			}

			.info-container {
				padding-bottom: 24rpx;

				.price {
					margin-top: 30rpx;
					color: #060606;
					font-size: 26rpx;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: space-between;

					text {
						&:last-child {
							font-weight: normal;
							font-size: 30rpx;
						}
					}
				}

				.course-detail {
					margin-top: 24rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;

					text {
						&:first-child {
							display: inline-block;
							background-color: #EEFAF6;
							color: #09CC8C;
							font-size: 22rpx;
							font-weight: bold;
							padding: 10rpx 2rpx;
							border-radius: 10rpx;
						}

						&:last-child {
							margin-left: 8rpx;
							color: #A4A4A4;
							font-size: 24rpx;
						}
					}
				}

				.teacher-info {
					margin-top: 16rpx;
					color: #818181;
					font-size: 22rpx;

				}
			}

		}

		.pay-info {
			margin-top: 30rpx;
			padding: 22rpx 28rpx;
			border-radius: 22rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;

			.h1-title {
				width: 100%;
				color: #201E2E;
				font-size: 30rpx;
				font-weight: bold;
				text-align: left;
			}

			.info {
				margin-top: 30rpx;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #201E2E;
				font-size: 26rpx;
				font-weight: bold;

				.choose-coupon {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-right: -28rpx;
				}
			}

			.color-red {
				color: #E62E2E;
			}

			.pay-type {
				margin-top: 30rpx;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

		}

		.other-info {
			border-radius: 0 0 30rpx 30rpx;
		}

		.author-info {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			text {
				font-size: 20rpx;
				margin-left: 10rpx;

				&:first-child {
					color: #F6F7FB;
				}

				&:last-child {
					color: $main-color;
				}
			}
		}

		.tip {
			margin-top: 6rpx;
			color: #E62E2E;
			font-size: 20rpx;
			padding-left: 38rpx;
		}


	}

	.bottom-opt {
		position: fixed;
		bottom: 0;
		padding: 0 22rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 120rpx;
		background-color: #fff;

		.left {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;

			.real-money {
				font-size: 26rpx;
				color: #201E2E;
				font-weight: bold;

				text {
					color: #E62E2E;
					font-size: 32rpx;
				}
			}

			.preferential {
				margin-top: 6rpx;
				width: 100%;
				color: #201E2E;
				font-size: 22rpx;
				text-align: left;

			}

		}

		.btn {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			text-align: center;
			border-radius: 40rpx;
			background-color: $main-color;
		}
	}
}
</style>