<template>
	<view class="container">
		<cc-dropDownMenu :showTopBottom='true' activeColor='#009c7b' :titleArr="titleArr" :dropArr="dropArr"
			@finishDropClick="finishClick"></cc-dropDownMenu>

		<!-- 列表信息 -->
		<view class="list">
			<view class="item" v-for="item in praticeList" :key="item.id" @click="toBank(item)">
				<view class="item-title">
					<text>{{item.subject}} </text>
					<text>用时：{{item.used_time|formatTime}}
						<text style="color: #01997A; margin-left: 8rpx;">已完成</text>
					</text>
					<text>提交时间： {{item.submit_time_str}}</text>
				</view>

				<view class="enter">
					<text>进入试卷</text>
					<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
				</view>
			</view>
		</view>
		<view v-if="praticeList.length==0 && loading==false">
			<u-empty mode="list" text="暂无结果" textSize="24">
			</u-empty>
		</view>
	</view>
</template>

<script>
	import {
		getAnswerRecordsList,
		getQuestionById,
		getQuestionListByPqid,
		getRecordsByReportId
	} from "@/api/professional/index.js"
	import {
		formatLocalTime,
		selectTemplate,
		selectTemplateAnswer
	} from "@/utils/tool.js"
	export default {
		data() {
			return {
				loading: true,
				titleArr: ['做题类型', '时间不限', '已完成'],
				pageInfo: {
					page: 1,
					perPageNum: 10,
					total: 0
				},
				stuConfig: {},
				praticeList: [],
				dropArr: [

					[{
							text: '专题题库',
							value: "0"
						},
						{
							text: '真题题库',
							value: "1"
						},
						{
							text: '任务题库',
							value: "2"
						},
						{
							text: '模考试卷',
							value: "3"
						},
					],

					[{
						text: '不限',
						value: ""
					}],

					[{
						text: '不限',
						value: ""
					}]

				],
				filterResultData: []
			}
		},
		onLoad() {
			this.getList();
			this.stuConfig = this.$store.state.professionalConfig.config
		},
		methods: {

			//进入题库
			async toBank(item) {
				this.$store.commit("professionalConfig/setCurrentStartPage")
				//题目列表
				let answeringQuestion = []
				//获取题目列表
				const params = {
					pqid: item.pqid,
				}
				let questionList = await getQuestionListByPqid(params)
				//如果题目列表为空返回
				if (questionList === false) {
					return
				}
				questionList.forEach(obj => {
					// 构造当前 题目的列表数据
					answeringQuestion.push({
						...obj,
						cateName: item.pname,
						type: item.type,
						professionTitle: item.subtitle,
						pqid: item.pqid,
						total_time: item.used_time,
						id: obj.qid,
						subtitle: obj.qtitle,
						qtype: obj.qtype,
						report_id: item.id
					})
				})
				//清空之前的答题记录
				this.$store.commit("professionalConfig/clearAnswerList")
				//获取本次的答案记录
				const answerInfo = await getRecordsByReportId({
					report_id: item.id
				})
				
				if (answerInfo.code == 1) {
										
					let  answerData = answeringQuestion.map((item, index) => {
						return {
							index,
							answer: answerInfo.data[item.id]?JSON.parse(answerInfo.data[item.id]) : ""
						}
					})
					answerData =  answerData.filter(item => item.answer !=="")				
					this.$store.commit("professionalConfig/setAnswerList", answerData)
				}

				this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
				//判断配置信息是否跳过已做题目
				//默认第一道题开始
				this.$store.commit("professionalConfig/setCurrentIndex", 0);
				selectTemplateAnswer(answeringQuestion[0], false)
			},
			//获取筛选结果
			finishClick(resultData) {
				this.filterResultData = resultData;
				this.praticeList = []
				this.pageInfo.page = 1;
				this.pageInfo.total = 0;
				this.getList()
			},
			//获取列表
			async getList(callback) {
				const params = {
					page: this.pageInfo.page,
					perPageNum: this.pageInfo.perPageNum,
					type: this.filterResultData[0],
				}
				this.loading = true
				const res = await getAnswerRecordsList(params)
				this.loading = false
				if (res !== false) {
					this.pageInfo.total = res.total
					this.praticeList = [...this.praticeList, ...res.list]
					callback && callback()
				}
			}
		},
		onPullDownRefresh() {
			this.praticeList = []
			this.pageInfo.page = 1;
			this.pageInfo.total = 0;
			this.getList(() => uni.stopPullDownRefresh())
		},
		onReachBottom() {
			if (this.praticeList.length >= this.pageInfo.total) {
				return uni.tip('没有更多数据了')
			}
			this.pageInfo.page++
			this.getList()
		},
		filters: {
			formatTime(seconds) {
				const minutes = Math.floor((seconds % 3600) / 60);
				const remainingSeconds = seconds % 60;

				// 使用padStart方法保证时间部分总是两位数字
				const formattedMinutes = String(minutes).padStart(2, '0');
				const formattedSeconds = String(remainingSeconds).padStart(2, '0');

				return `${formattedMinutes}分钟${formattedSeconds}秒`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: $container-bg-color;
		min-height: 100vh;
		padding-bottom: 80rpx;

		.list {
			padding: 0 36rpx;

			.item {
				background-color: #fff;
				margin-top: 26rpx;
				border-radius: 18rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				box-shadow: 2rpx 2rpx 10rpx rgba(0, 0, 0, 0.1);

				.item-title {
					display: flex;
					align-items: center;
					justify-content: space-between;
					flex-direction: column;

					text {
						width: 100%;
						text-align: left;
						margin-top: 4rpx;
						color: #9F9F9F;
						font-size: 22rpx;
						padding: 0 4rpx;

						&:first-child {
							width: 100%;
							text-align: left;
							color: #4A4A4C;
							font-size: 28rpx;
							font-weight: bold;
							padding: 0 4rpx;
						}

					}
				}

				.enter {
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						color: #EE7878;
						font-size: 20rpx;
						margin-right: 10rpx;
					}
				}
			}
		}
	}
</style>