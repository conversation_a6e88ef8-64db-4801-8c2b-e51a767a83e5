<template>
	<view class="container">
		<question-content></question-content>
		<cover>
			<scroll-view class="drawer-container" scroll-y="true" style="height: 100%; background-color:#fff">
				<view style="height: 100%; background-color:#fff">
					<view class="title-container">
						<!-- <view class="title-list"
							:style="{height:isShowAll? Math.ceil(questionList.length/4)*70+'rpx' :'80rpx'}">
					
							<text v-for="i in questionList.length" @click="currentChoose=i"
								:class="[{done:hasDone(i)},{sel:currentChoose==i}]" class="title"> 第{{i+1}}题</text>
						</view>
						<view class="right-icon">
							<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false"
								size="44"></u-icon>
							<u-icon v-else name="arrow-up" @click="isShowAll=true" size="44"></u-icon>
						</view> -->
						<scroll-view  class="title-list" :scroll-into-view="scrollToTitle" scroll-x >
							<text @click="currentChoose=i" :class="[{done:hasDone(i)},{sel:currentChoose==i}]"  class="title" v-for="i in questionList.length"> 第{{i+1}}题</text>
						</scroll-view>
						
					</view>


					<view class="answer">
						<swiper :current="currentChoose" :duration="100" :style={height:swiperHeight}
							@change="CurrentIndexChange">
							<swiper-item :scroll-y='true' v-for="(item,index) in questionList" :key="index">
								<scroll-view class="swiper-item-content">
									<view class="q-text q-title">
										{{questionTitle}}
									</view>
									<view class="title">
										我的答案
									</view>
									<view class="a-content">
										<u--textarea height="290rpx" border="none"
											:value="answer[currentChoose]"></u--textarea>
									</view>
									<view class="img-list" >
										<view class="img-container">
										<image v-for="item in fileList[currentChoose]" :key="item.url" v-if="item.url.length>0" :src="item.url" @click="showImage(item.url)"
											mode="heightFix"></image>
										</view>
									</view>
									<!-- 答案 -->
									<view class="analysis" v-if="currentAnalysis">
										<view class="title">
											<user-title :title='"答案"' :bgcolor="'#fff'"></user-title>
										</view>
										<view class="answer-info">
											<rich-text :nodes="currentAnalysis"></rich-text>
										</view>
									</view>
									
									<view class="knowledge-origin" v-if="currentType.origin">
										<view class="title">
											<user-title :title='"来源"' :bgcolor="'#fff'"></user-title>
										</view>
										<view class="knowledge-origin-title">
											{{currentType.origin}}
										</view>
									</view>
									
									
									<!-- 知识点 -->
									<view class="knowledge-points" v-if="item.knowledge_name">
										<view class="title">
											<user-title :title='"知识点"' :bgcolor="'#fff'"></user-title>
										</view>
									
										<view class="point">
											<view class="point-tag" v-for="(item, index) in currentType.knowledgeInfo" :key="index">
												{{item.knowledge_name|handleNullValue}}
											</view>
										</view>
									</view>
									
									<!-- 补漏 -->
									<view class="traps" v-if="currentType.knowledgeInfo">
										<view class="title">
											<user-title :title='"精准补漏"' :bgcolor="'#fff'"></user-title>
										</view>
										<enter-course :list='currentType.knowledgeInfo'></enter-course>
									</view>
									
									<!-- 	老师点评 -->
									<view class="comment" v-if="currentType.answer_records">
										<view class="title">
											<user-title :title='"老师点评"' :bgcolor="'#fff'"></user-title>
											<view class="a-content" v-if="currentType.answer_records.marking_comment[currentChoose]">
												<text>{{currentType.answer_records.marking_comment[currentChoose]}}</text>
											</view>
										</view>
										<view class="img-list" v-if="currentType.answer_records.marking_pic[currentChoose].length>0">
											<user-title :title='"老师批改"' :bgcolor="'#fff'"></user-title>
											<view class="img-container">
												<image @click="showImage(imgsrc)" v-for="(imgsrc) in currentType.answer_records.marking_pic[currentChoose]"
												:key="imgsrc"  
												style="margin-top: 6rpx;" 
												:src="imgsrc" mode="heightFix"></image>
											</view>
		
										
										</view>
									
										<view class="edit-score" style="margin-top: 46rpx;" v-if="currentType.answer_records.marking_score[currentChoose]">
											<user-title :title='"老师打分"' :bgcolor="'#fff'"></user-title>
											<u--input :disabled='true' :disabledColor="'#fff'" type="number" border="surround"
												v-model="currentType.answer_records.marking_score[currentChoose]"></u--input>
										</view>
									</view>
									
									
									<view class="analysis" v-if="isShowTaskAnswer == true">
											<view class="title">
												<user-title :title='"判断对错"' :bgcolor="'#fff'"></user-title>
											</view>
										<view class="qanswer">
									
											<u-radio-group activeColor="#01997A" v-model="isRight[index]" placement="row">
												<u-radio iconSize='20' labelSize="30" size='30' :name="1" label="对"></u-radio>
												<text style="margin: 0 10rpx;"></text>
												<u-radio iconSize='20' labelSize="30" size='30' :name="0" label="错"></u-radio>
											</u-radio-group>
										</view>
									</view>
									
									<!-- 试卷显示自测评分 -->
									<view class="comment"
										v-if="currentType.type==2">
										<view class="title">
											<user-title :title='"自测评分(满分"+score+")"' :bgcolor="'#fff'"></user-title>
										</view>
										<view class="edit-score">
											<u--input  :disabledColor="'#fff'" type="number" border="surround"
											  placeholder="请输入分数"
											v-model="selfScore[currentChoose]"></u--input>
										</view>
									</view>
									
								</scroll-view>
							</swiper-item>
						</swiper>
					</view>
					<view class="bottom-btn">
						<view class="prev" @click="answerPrev">
							上一题
						</view>
						<view class="next"  @click="answerNext">
							下一题
						</view>
					</view>
				</view>
			</scroll-view>
		</cover>
	</view>
</template>

<script>
	import questionContent from "../components/content/content.vue"
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	import cheader from "@/components/c_head/c_head.vue"
	import star from "@/components/star/star.vue"
	import mixins from "@/mixins/index.js"
	import userTitle from "@/components/user_title/user_title.vue"
	import enterCourse from "@/components/enter_course/enter_course.vue"
	import {
		uploadFile
	} from "@/api/common/common.js"
	export default {
		mixins: [mixins],
		data() {
			return {
				isShowAll: false,
				swiperHeight: 0,
				loading: false,
				scrollToTitle:'',
			}
		},
		methods: {
			CurrentIndexChange(e) {
				this.currentChoose = e.detail.current
				this.scrollToTitle = '#title-list-'+this.currentChoose;
			},
			showImage(url) {
				// 预览图片
				uni.previewImage({
					urls: [url],
				});
			},
			handleSwiperHeight() {
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.swiper-item-content').boundingClientRect(data => {
						if (data) {
							this.swiperHeight = data.height + 'px'
						}
					}).exec();
				})
			},
			selected(index, selOption) {
				if (!Array.isArray(this.fileList[this.currentChoose])) {
					return
				}
				if (this.fileList[this.currentChoose].length > 0 || this.answer[this.currentChoose].length > 0) {
					return true
				} else {
					return false
				}
			},
			hasDone(i) {
				if (!Array.isArray(this.fileList[i]) || !Array.isArray(this.answer)) {
					return false
				}
				if (this.fileList[i].length > 0 || this.answer[i].length > 0) {
					return true
				} else {
					return false
				}
			},
			next() {
				//保存答题结果
				let answer = {
					answer: this.answer,
					url: this.fileList,
					timeval: this.$refs.cheader.currentTime,
				}

				const data = {
					answer: answer,
					index: this.currentIndex, //保存当前 题库的进度
				}
				//当前结果保存本地
				this.$store.commit("professionalConfig/setAnswerList", data);
				//清空答题记录
				this.whetherToNext()
			},
			deletePic(event) {
				this.fileList[this.currentChoose].splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList[this.currentChoose].length
				lists.map((item) => {
					this.fileList[this.currentChoose].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading = true;
				for (let i = 0; i < lists.length; i++) {
					try {
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[this.currentChoose][fileListLen]
						this.fileList[this.currentChoose].splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					} catch (err) {
						//移除图片
						setTimeout(() => {
							this.fileList[this.currentChoose].splice(fileListLen, 1)
						}, 1500)
					}
				}
				this.loading = false
			}

		},
		onLoad() {
			console.log("this.isShowTaskAnswer", this.isShowTaskAnswer)
		},
		computed: {
			questionTitle() {
				if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length >
					0) {
					let questions = JSON.parse(this.currentType.material_questions)
					let result = questions.find(item => item.num == this.currentChoose + 1)
					if (result !== undefined) {
						return result.question
					}
				}
				return ""
			},
			currentAnalysis() {
				try{
					const answerArr = JSON.parse(this.currentType.answer)
					return answerArr[this.currentChoose].analysis
				}catch(e){
					console.log(e)
					return ""
				}
				
				// const ret = answerArr.find((item, index) => index == this.currentChoose)
				// if (ret != undefined) {
				// 	return ret.analysis
				// } else {
				// 	return ""
				// }
			},
			score(){
				debugger
				return parseInt(this.currentType.score/this.questionList.length)
			},
			isRight(){
				return this.questionList.map(item=>-1)
			}
			// urlList(){
			// 	return this.fileList[this.currentChoose].map(item=>item.url).join(",")
			// }
		},
		watch: {
			questionTitle(newval) {
				console.log(newval)
			},
			currentChoose: {
				handler(newVal) {
					this.handleSwiperHeight()
					
				},
				immediate: true,
				deep: true
			},
			currentIndex: {
				handler(newVal) {
					this.handleSwiperHeight()
					// 恢复保存到 vuex 的答题数据

					//this.fileList[this.currentChoose] = this.fileList
					//this.answer[this.currentChoose] = this.answer
				},
				immediate: true,
				deep: true
			}
		},
		components: {
			cheader,
			star,
			"question-content": questionContent,
			cover,
			userTitle,
			enterCourse
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 164rpx;

		.center {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #2D2D2D;
			font-size: 32rpx;
		}

		.content {
			background-color: #fff;
			padding: 0 36rpx;
			padding-bottom: 80rpx;

			.top-opt {
				width: 100%;
				padding: 10rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.progress-text {
					text {
						color: #777777;
						font-size: 26rpx;

						&:first-child {
							font-size: 34rpx;
							color: #2D2D2D;
							font-weight: bold;
						}
					}
				}

				.opt {
					display: flex;
					align-items: center;
					justify-content: space-between;

					view {
						margin-left: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						text {
							font-size: 18rpx;
							color: #777;
						}
					}
				}
			}

			.q-type {
				margin-top: 12rpx;
				background-color: #CDF3E7;
				border-radius: 12rpx;
				color: $main-color;
				font-size: 24rpx;
				width: 120rpx;
				height: 46rpx;
				text-align: center;
				line-height: 46rpx;
			}

			.question {
				display: flex;
				flex-direction: column;
				align-content: space-between;
				justify-content: center;

				.title {
					width: 100%;
					color: #5A5A5A;
					font-size: 28rpx;
					font-weight: bold;
					margin-top: 16rpx;
				}

				.question-content {
					color: #5A5A5A;
					font-size: 28rpx;
					margin-top: 24rpx;
					line-height: 40rpx;
				}
			}

			.q-text {
				line-height: 50rpx;
				color: #5A5A5A;
				font-size: 24rpx;
			}

			.q-title {
				margin-top: 20rpx;
				margin-bottom: 28rpx;
			}

			.q-sel-list {

				.q-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 40rpx;

					text {
						width: 590rpx;
					}

					.q-check-normal {
						width: 70rpx;
						height: 70rpx;
						border: 1rpx solid #AFAFAF;
						color: #5A5A5A;
						background-color: #fff;
						border-radius: 16rpx;
						line-height: 70rpx;
						text-align: center;
					}

					.q-check-sel {
						background: #01997A;
						border: 0;
						color: #fff;

					}
				}
			}

		}

		.title-container {
			display: flex;
			align-items: flex-start;
			justify-content: center;
			background-color: rgb(247, 247, 247);

			.title-list {
				height: 78rpx;
				background-color: rgb(247, 247, 247);
				width: 100%;
				white-space: nowrap;
				.title {
					display: inline-block;
					width: 110rpx;
					color: #777777;
					font-size: 28rpx;
					padding: 15rpx 30rpx;
					padding-bottom: 15;
				}
			}

			.right-icon {
				width: 140rpx;
				padding-top: 15rpx;
				padding-right: 30rpx;

				::v-deep .uicon-arrow-up {
					display: inline-block;
					border-radius: 50%;
					border: 1rpx solid #777;
				}

				::v-deep .uicon-arrow-down {
					display: inline-block;
					border-radius: 50%;
					border: 1rpx solid #777;
				}
			}

			.done {
				color: $main-color !important;
			}

			.sel {
				color: #0A0A0A;
				font-weight: bold;
			}
		}

		.answer {

			padding: 0 36rpx;

			.title {
				font-size: 24rpx;
				color: #777777;
				font-weight: bold;
			}

			.q-text {
				line-height: 50rpx;
				color: #5A5A5A;
				font-size: 24rpx;
			}

			.q-title {
				margin-top: 20rpx;
				margin-bottom: 28rpx;
			}

			.a-content {
				margin-top: 12rpx;
				margin-bottom: 36rpx;
				padding: 26rpx;
				min-height: 233rpx;
				background: #FFFFFF;
				border-radius: 22rpx 22rpx 22rpx 22rpx;
				border: 1rpx solid #707070;
				font-weight: 400;
				font-size: 28rpx;
				color: #5A5A5A;
				line-height: 50rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.img-list {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: flex-start;
				.img-container{
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					justify-content: flex-start;
					align-content: center;
				}
				image {
					margin: 10rpx;
					padding: 0 16rpx;
					height: 80rpx;
				}
			}

		}
		.q-answer,.analysis,.knowledge-points, .traps, .knowledge-origin{
			margin-top: 16rpx;
			background-color: #fff;
			padding-bottom: 30rpx;
		}
		.analysis{
			.answer-info{
				color: #5A5A5A;
				font-size: 22rpx;
			}
		}
		.comment {
			margin-top: 16rpx;
			background-color: #fff;
			padding-bottom: 60rpx;

			.qanswer {
				color: #5A5A5A;
			}

			.edit-score {}
		}
		.bottom-btn {
			padding: 0 36rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 56rpx;
			padding-bottom: 100rpx;
			view {
				width: 170rpx;
				height: 70rpx;
				border-radius: 16rpx;
				border: 1rpx solid #01997A;
				line-height: 70rpx;
				text-align: center;
				font-size: 28rpx;
			}

			.prev {
				background: #FFFFFF;
				color: $main-color;
			}

			.next {
				background-color: $main-color;
				color: #fff;
			}
		}

	}
</style>