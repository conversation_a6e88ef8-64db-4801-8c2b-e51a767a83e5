<template>
	<view class="container">
		<web-view :src="host"></web-view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			host: '',
		};
	},
	onLoad(options) {
		console.log(options)
		const params = options.params ? JSON.parse(options.params) : {};
		const origin_name = this.$store.getters.originName
		if (typeof params !== "{}") {
			const { host, cateName, pqid, type, title } = params
			this.host = host + `?phone=${this.userInfo.phone}&title=${title}
					&cateName=${cateName}
					&total_time=${params.total_time}
					&type=${type}&token=${this.userInfo.token}&pqid=${pqid}
					&origin_name=${origin_name}&startPage=${this.$store.getters.currentStartPage}
					&aboutId=${params.aboutId}
					`
			console.log(this.host)
		}
	},
	computed: {
		...mapState('user', ['userInfo'])
	},
	onBackPress(options) {
		if (options.from === 'backbutton') { // 来源是标题栏返回按钮或物理返回键
			// 自定义逻辑（如弹窗确认）
			uni.showModal({
				title: '提示',
				content: '确定要返回吗？',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack(); // 用户确认后手动返回
					}
				}
			});
			return true; // 拦截默认返回行为
		}
		return false; // 其他来源不拦截
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding-bottom: 20rpx;

	.center {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}

	.content {
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;

		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;

					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}

		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
	}
}
</style>
