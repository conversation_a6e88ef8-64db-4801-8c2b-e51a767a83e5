const state = {
    userInfo: {
        token: ''
        // 其他用户信息...
    }
}

const actions = {
    async login({ commit }) {
        try {
            let loginData = null;
            
            // #ifdef MP-WEIXIN
            const loginResult = await uni.login()
            if (!loginResult.code) {
                throw new Error('获取登录码失败')
            }
            // 调用微信小程序登录接口
            loginData = await uni.request({
                url: '/api/wx/login', // 替换为您的实际登录接口
                data: {
                    code: loginResult.code
                }
            })
            // #endif

            // #ifdef APP-PLUS
            // App登录逻辑
            loginData = await uni.request({
                url: '/api/app/login', // 替换为您的实际APP登录接口
                method: 'POST',
                data: {
                    // 这里可能需要其他登录参数
                    platform: 'app'
                }
            })
            // #endif

            if (loginData && loginData.data && loginData.data.code === 200) {
                const userData = loginData.data.data
                // 保存用户信息
                commit('setUser', userData)
                // 保存token到本地存储
                uni.setStorageSync('token', userData.token)
                return userData
            } else {
                throw new Error(loginData?.data?.msg || '登录失败')
            }
        } catch (error) {
            console.error('登录失败:', error)
            throw error
        }
    }
}

const mutations = {
    setUser(state, userInfo) {
        state.userInfo = userInfo
    }
}

export default {
    namespaced: true,
    state,
    actions,
    mutations
} 