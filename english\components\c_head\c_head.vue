<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'  @leftClick="back">
		     <view class="u-nav-slot" slot="left">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
				<text>耗时{{showTime}}</text>
			</view>
		</u-navbar>
	</view>
</template>

<script>
	import {formatTime} from "@/utils/tool.js"
	export default {
		data(){
			return{
				timerId:null,
				currentTime:0,
			}
		},
		methods:{
			//开始定时器
			startTimer(){
				if(this.timerId!=null){
					clearInterval(this.timerId)
					this.timerId = null
				}else{
					this.timerId = setInterval(()=>{
						this.currentTime++
					}, 1000)
				}
			},
			back(){
				uni.navigateTo({
					url:"/subpkg/allquestion/allquestion"
				})
			},
		},
		props:{
			initTimeVal:{
				type:Number,
				default:0
			}
		},
		created() {
			this.startTimer()
		},
		computed:{
			showTime(){
				return formatTime(this.currentTime)
			},
		},
		watch:{
			initTimeVal(newval){
				this.currentTime = newval
			}
		}
	}
</script>

<style lang="scss" scoped>
</style>