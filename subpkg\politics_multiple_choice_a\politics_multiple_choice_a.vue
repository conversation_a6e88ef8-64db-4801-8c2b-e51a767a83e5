<template>
	<view class="container">
		<cheader :initTimeVal="timeval" ref="cheader"></cheader>
		<view class="content">
			<question-content></question-content>
			<view class="q-sel-list">
				<view class="q-item" v-for="(value, key) in question.options" :key="key" @click="toggleSel(key.toUpperCase())">
					<view class="q-check-normal" :class='{ "q-check-sel": answer.includes(key.toUpperCase()) }'>
						{{ key }}
					</view>
					<text class="q-text">{{ value }}</text>
				</view>
			</view>


		</view>
		<bottom-btn @prev="prev" @next="next"></bottom-btn>
	</view>
</template>

<script>
import cheader from "@/components/c_head/c_head.vue"
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
import bottomBtn from "@/components/bottomBtn/bottomBtn.vue"
import QuestionContent from "../component/content/content.vue"
export default {
	data() {
		return {
			safeTopHeight: 0,
			answer: "",
			question: "",//具体问题
			questionOption: {},//选项内容
		};
	},
	mixins: [mixins],
	mounted() {
		this.safeTopHeight = uni.$safeTopHeight
	},
	onLoad(option) {
		const {
			id
		} = option
		this.id = id

	},
	methods: {
		//下一题
		next() {
			//保存答题结果
			let answer = {
				answer: this.answer,
				url: [],
				timeval: this.$refs.cheader.currentTime,
			}

			const data = {
				answer: answer,
				index: this.currentIndex,//保存当前 题库的进度
			}
			//const res = await updateAnswer(data)
			//当前结果保存本地
			this.$store.commit("professionalConfig/setAnswerList", data);
			this.whetherToNext()
		},
		toggleSel(key) {
			if (this.answer == "") {
				this.answer = key
				return;
			}
			let answer = this.answer.toUpperCase().split(",")
			if (answer.includes(key)) {
				answer = answer.filter(item => item !== key)
			} else {
				answer.push(key)
			}
			//排序
			this.answer = answer.sort().join(",")
		}
	},
	components: {
		cheader,
		star,
		bottomBtn,
		QuestionContent
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding-bottom: 120rpx;

	.center {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
		font-weight: bold;
	}

	.content {
		padding: 0 36rpx;

		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;

					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}

		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}

		.q-text {

			line-height: 50rpx;
			color: #5A5A5A;
			font-size: 28rpx;
		}

		.q-title {
			margin-top: 36rpx;
			margin-bottom: 50rpx;
		}

		.q-sel-list {

			.q-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;

				&:last-child {
					margin-bottom: 120rpx;
				}

				text {
					width: 590rpx;
				}

				.q-check-normal {
					width: 70rpx;
					height: 70rpx;
					border: 1rpx solid #AFAFAF;
					color: #5A5A5A;
					background-color: #fff;
					border-radius: 15rpx;
					line-height: 70rpx;
					text-align: center;
				}

				.q-check-sel {
					background: #01997A;
					border: 0;
					color: #fff;

				}
			}
		}


	}
}
</style>
