<template>
	<view class="container">
		<view class="tabs">
			<u-tabs  :list="list" lineWidth="50" lineHeight="4" lineColor="#01997A" :activeStyle="{
			           
						 color: '#01997A',
						
			        }" :inactiveStyle="{
			             color: '#777777',
						
			        }" itemStyle="padding-left: 20px; padding-right: 20px; height: 40px;"
					@click="change"
					>
			</u-tabs>
		</view>
		<view class="content-container" >
			<template v-if="currentIndex==3">			
				<view class="exchange"  >
					<u--input class="input-code" border="none"  v-model="couponNum"></u--input>
					<text>兑换</text>
				</view>
			</template>
			<template v-else>
				<view class="wait-for-use"  v-if="currentIndex!=3">
					<view class="tip">
						<u-icon name="coupon-fill"  color="#009c7b"  size="38"></u-icon>
						<text>领取更多优惠券</text>
					</view>
					<view class="more" @click="getMore">
						<text>查看更多</text>
						<u-icon name="play-right-fill" color="#BCBBBB"  size="18"></u-icon>
					</view>
				</view>
				
				<view class="coupon-container">
					<view class="coupon" v-for="(item,k) in couponList" :key="k">
						<view class="left">
							<text>{{item.info.coupon_price}} <text style="font-size: 32rpx;">元</text></text>
							<text v-if="item.info.use_min_price > 0">满{{item.info.use_min_price}}可用</text>
							<text v-else>无门槛</text>
						</view>
						<view class="right">
							<view class="name">
								{{item.info.coupon_title}}
							</view>
							<view class="detail-info">
								<view class="detail-left">
									<text class="date" v-if="item.info.coupon_time > 0">领取后{{item.info.coupon_time}}天有效</text>
									<text class="date" v-else>有效期  {{item.info.end_use_time_text}}</text>
									<view class="rule"  @click="seeRule(item.info.coupon_desc)">
										<text>使用规则</text>
										<u-icon name="arrow-right"  color="#009c7b"  size="24"></u-icon>
									</view>
								</view>
								<view class="detail-right" v-if="item.status == 0">
									<view class="use-btn" @click="goBuy">
										立即使用
									</view>
								</view>
								<view class="detail-right" v-else-if="item.status == 2">
									<view class="use-btn">
										已过期
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
		
		<u-popup :show="show" mode="bottom"  @close="close" @open="open">
			<view class='u-popup-slot'>
				<text>使用规则:</text>
				<text class="coupon_desc">{{coupon_desc}}</text>
			</view>
		</u-popup>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex:0,
				couponNum:"",
				list: [{
						name: '未使用'
					},
					{
						name: '已使用',
					},
					{
						name: '已过期'
					},
					{
						name: '兑换码'
					}
				],
				couponList:[],
				page:1,
				total:0,
				show:false,
				coupon_desc:''
			};
		},
		onLoad(){
			this.getCoupon();
			this.getTypeNum();
		},
		methods:{
			goBuy(){
				uni.navigateTo({
					url:'/subpkg/choosecourse/choosecourse'
				})
			},
			seeRule(coupon_desc) {
				this.coupon_desc = coupon_desc
				this.show=true;
			},
			 open() {
			  // console.log('open');
			},
			close() {
			  this.show = false
			  // console.log('close');
			},
			getMore() {
				uni.navigateTo({
					url:'../all_coupon/all_coupon'
				})
			},
			change(item){
				console.log(item.index)
				this.currentIndex = item.index
				if(item.index != 3) {
					this.getCoupon();
					this.page = 1;
					this.couponList = []
				}
			},
			getCoupon(){
				let that = this;
				const  data  = uni.$u.http.get('/coupon/userCouonList',{params:{status:that.currentIndex}}).then(rest=>{
					let res = rest.data
					that.couponList  = res.rows
					that.total = res.total
					
				});
			},
			getTypeNum(){
				let that = this;
				const  data  = uni.$u.http.get('/coupon/getTypeNum').then(rest=>{
					let res = rest.data
					that.list[0].name  = that.list[0].name+'('+res.will+')';
					that.list[1].name  = that.list[1].name+'('+res.is_use+')';
					that.list[2].name  = that.list[2].name+'('+res.had+')';
					
				});
			},
			onPullDownRefresh(){
				this.page = 1;  
				this.couponList = []
				this.total = 0
				this.getCoupon(uni.stopPullDownRefresh)
				
			},
					
			onReachBottom(){
				if(this.orderInfo.length >=this.total){
					return uni.$u.toast("没有更多数据")
				} 
				this.page +=1;
				this.getCoupon();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		view{
			box-sizing: border-box;
		}
		.u-popup-slot {
			width: 200px;
			height: 150px;
			@include flex;
			justify-content: center;
			align-items: center;
			.coupon_desc{
				color:#111;
				font-size: 24rpx;
				
			}
		}
		.content-container{
			padding: 30rpx;
			.exchange{
				height: 84rpx;
				border-radius: 40rpx;
				background-color: #F6F7FB;
				display: flex;
				align-items: center;
				justify-content: center;
				padding-left: 240rpx;
				margin-bottom: 20rpx;
				.input-code{
					flex: 3;
				
				}
				text{
					flex: 1;
					height: 100%;
					border-radius: 0 40rpx 40rpx 0 ;
					background-color: $main-color;
					color: #FFF;
					line-height: 84rpx;
					text-align: center;
				}
				
			}
		
			.wait-for-use{
				height: 120rpx;
				border-radius: 40rpx;
				border: 1rpx solid #AFAFAF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 28rpx;
				.tip{
					display: flex;
					align-items: center;
					justify-content: space-between;
					text{
						color: #5A5A5A;
						font-size: 28rpx;
						margin-left: 16rpx;
					}
				}
				.more{
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						color: #EE7878;
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}
		 .coupon-container{
			 margin-top: 36rpx;
			 .coupon{
				 margin-bottom: 28rpx;
				 height: 170rpx;
				 background-size: contain;
				 background-repeat: no-repeat;
				 display: flex;
				 align-items: center;
				 justify-content: flex-start;
				 .left{
					 width: 192rpx;
					 height: 100%;
					 background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
					 background-repeat: no-repeat;
					 background-size: contain;
					 color: #fff;
					 display: flex;
					 flex-direction: column;
					 align-items: center;
					 justify-content: center;
					 text{
						 &:first-child{ 
							 font-weight: bold;
							font-size: 40rpx;
						 }
						 &:last-child{
							 color: #CDF3E7;
							 font-size: 26rpx;
						 }
					 }
				 }
				 .right{
					 background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
					 background-repeat: no-repeat;
					 background-size: contain;
					 width: 500rpx;
					 height: 100%;
					 padding: 24rpx 34rpx;
					 .name{
						 color: #4C5370;
						 font-size: 30rpx;
						 font-weight: bold;
					 }
					 .detail-info{
						 display: flex;
						 align-items: center;
						 justify-content: space-between;
						 .detail-left{
							 text.date{
								 color: #AFAFAF;
								 font-size: 24rpx;
							 }
							 .rule{
								 margin-top: 16rpx;
								 display: flex;
								 align-items: center;
								 justify-content: flex-start;
								 text{
									 font-size: 24rpx;
									 color: $main-color;
								 }
							 }
						 }
						 .detail-right{
							 height: 100%;
							 display: flex;
							 align-items: center;
							 justify-content: center;
							 .use-btn{
								 width: 132rpx;
								 height: 54rpx;
								 border-radius: 32rpx;
								 border: 1rpx solid #01997A;
								 color: #01997A;
								 font-weight: 500;
								 line-height: 54rpx;
								 text-align: center;
								 font-size: 26rpx;
							 }
						 }
					 }
				 }
			 }
		 }
		}
	}
</style>