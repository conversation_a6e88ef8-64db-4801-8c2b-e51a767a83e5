<template>
	<view class="container">
		<view class="top">
			<view class="left">
				<u-tabs :list="list" lineWidth="50" lineHeight="4" lineColor="#01997A" :activeStyle="{
						 color: '#01997A',	
						  fontWeight:'bold',
						  fontSize:'32rpx'
				    }" :inactiveStyle="{
						 fontSize:'32rpx',
						 fontWeight:'bold',
				         color: '#777777',
				    }" itemStyle="padding-left: 8px; padding-right: 8px; height: 40px;" @click="change">
				</u-tabs>
			</view>
			<view class="right">
				预约记录
			</view>
		</view>
		
		<view class="live-broadcast">
			<view class="broadcast-info">
				<view class="broadcast-title">
					政治精讲最新时政题型总结
				</view>
				<view class="broadcast-detail">
					<text>孙老师</text>
					<text>2025年12月12日20:00-21:00</text>
					<text  class="living">直播中</text>
				</view>
			</view>
			
		</view>
		
		<view class="live-broadcast">
			<view class="broadcast-info">
				<view class="broadcast-title">
					政治精讲最新时政题型总结
				</view>
				<view class="broadcast-detail">
					<text>孙老师</text>
					<text>2025年12月12日20:00-21:00</text>
					<text class="living">直播中</text>
				</view>
			</view>
			
		</view>
		
		<view class="live-broadcast">
			<view class="broadcast-info">
				<view class="broadcast-title">
					政治精讲最新时政题型总结
				</view>
				<view class="broadcast-detail">
					<text>孙老师</text>
					<text>2025年12月12日20:00-21:00</text>
					<text class="cancle">取消预约</text>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					{
						name: '全部'
					},
					{
						name: '政治'
					},
					{
						name: '英语',
					},
					{
						name: '数学'
					},
					{
						name: '专业课'
					}
				],
			};
		},
		methods:{
			change(item){
				console.log(item)
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	min-height: calc(100vh - 44px);
	background-color: $container-bg-color;
	padding: 0 30rpx;
	padding-top: 30rpx;
	.top{
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 50rpx;
		.right{

					width: 140rpx;
					height: 60rpx;
					border-radius: 16rpx;
					border: 3rpx solid #01997A;
					line-height: 60rpx;
					text-align: center;
					color: #01997A;
					font-size: 28rpx;
					font-weight: bold;
					margin-left: 50rpx;
				
		}
	}
	
	.live-broadcast{
		margin-bottom: 36rpx;
		box-sizing: border-box;
		background: linear-gradient( to bottom, #CDF3E7 0%, #FFFFFF 100%);
		border-radius: 20rpx;
		padding:28rpx;
			.broadcast-title{
				color: #4A4A4C;
				font-size: 30rpx;
				font-weight: bold;
			}
			.broadcast-detail{
				box-sizing: border-box;
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				text{
					color: #A2A2A2;
					font-size: 24rpx;
					&:nth-child(3){
						height: 44rpx;
						padding: 0 20rpx;
						width: 130rpx;
						border-radius: 40rpx;
						line-height: 44rpx;
						text-align: center;
						font-size: 24rpx;
						color: #fff;
					}
				}
				text.cancle{
					color: #01997A;
					border: 1rpx solid $main-color;
				}
				text.living{
					background: linear-gradient( 90deg, #10C19D 0%, #14B494 63%, #01997A 100%);
				}

			}
	}
}
</style>
