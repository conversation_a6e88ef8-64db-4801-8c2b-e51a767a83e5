<template>
	<view class="container">
	<!-- 	简介 -->
	 <view class="introduction-container">
		<view class="introduction">
			<view class="item">
				<view class="title">
					<text>{{statisticsData['allNum'] | handleData}}</text>
					<text>张</text>
				</view>
				<text>试卷数</text>
			</view>
			<view class="item">
				<view class="title">
					<text>{{statisticsData['completeNum']| handleData}}</text>
					<text>题</text>
				</view>
				<text>刷题量</text>
			</view>
			<view class="item">
				<view class="title">
					<text>{{statisticsData['days']| handleData}}</text>
					<text>天</text>
				</view>
				<text>练习天数</text>
			</view>
		</view>
	  </view>
	  <!-- 中间部分tab -->
	  <view class="mid-tabs">
	  	<view class="all-course-h1">
			<view class="left-title">
				<view class="green-block-big">
					
				</view>
				<text>真题题库</text>
			</view>
			<u-icon name="setting" @click="show=true"  size="56"></u-icon>
	  		
	  	</view>
		<view class="tabs" style="margin-left: 92rpx;">
			<u-tabs
			        :list="list"
			        lineWidth="50"
					lineHeight="8"
			        lineColor="#01997A"
			        :activeStyle="{
						  color: '#01997A',
						  fontSize:'32rpx'
			        }"
			        :inactiveStyle="{
			             color: '#777777',
						  fontSize:'32rpx'
			        }"
			        itemStyle="padding-left: 37rpx; padding-right: 37rpx; height: 34px;"
					@change="titleChange"
			    >
			    </u-tabs>
		</view>
		
	  </view>
	  <view class="content" :style="{height:bottomHeight}">
	  		<view class="left">
	  			<side-bar :list="nowSublist" :currentSel="sel" @changeItem="changeItem" :height="bottomHeight" ></side-bar>
	  		</view>
		<view class="right">			
			<scroll-view scroll-y="true"  class="practise-container" :style="{height:bottomScroolHeight}">
				<view class="exam-list">
					<view class="item" v-for="(item, i) in pratiseList" :key="item.id">
						<text class="num">{{i+1}}</text>
						<view class="title">
							<text class="title-name">{{item.category.name + item.name}}真题试卷</text>
							<!-- <text class="date">刷题量&nbsp;5/20 &nbsp;正确率100%</text> -->
							<text class="date">未开始</text>
						</view>
						<view class="exam-progress" v-if="item.doing">
							<text class="do-continue">继续做题</text>
							<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
						</view>
						<view class="exam-progress">
							<text class="do-continue">&nbsp;</text>
						</view>
					</view>
				</view>
			</scroll-view>
			
		</view>
	  </view>
	       
	
	</view>
</template>

<script>
		import sideBar from "../../components/Sidebar/Sidebar.vue"
		import practiseListCom from "../../components/practiseList/practiseList.vue"
	export default {
		data() {
			return {
				//弹出层
				  show: false,
				//底部高度 动态获取
				bottomHeight:0,
				allType:[], //题库分类
				bottomScroolHeight:0,
				list:[
					{name:'政治'},
					{name:'英语'},
					{name:'数学'},
					{name:'专业课'},
				],
				professionalTitleList:[],//专项题库标题
				nowSublist:[{id:1,name:'政治'}],
				allSublist:[
					[{id:1,name:'政治'}],
					[{id:2,name:'英语一'},{id:3,name:'英语二'}],
					[{id:4,name:'数学一'},{id:5,name:'数学二'},{id:6,name:'数学三'}],
					[{id:7,name:'专业课'}],
				],//专项题库子标题
				sel:1,
				currentTag:"all",//当前选中的题库类型
				currentCid:0,//选中的题库分类
				searchParam:{},
				pratiseList:[
					
				],
				statisticsData:{},
			};
		},
		components:{
			sideBar
		},
		methods:{
			changeItem(item){
				this.sel = item.id; 
			},
			async getBaseData (){
				let that = this;
				const  data  = await  uni.$u.http.get('/index/getBaseData',{}).then(res=>{
					that.allSublist = res.data.array;
					that.statisticsData  = res.data.count
					console.log(that.statisticsData)
					that.getAllTrueQuestion();
				});
			},	
			async getAllTrueQuestion(){
				let that = this;
				const  data  = await  uni.$u.http.get('/index/getAllTrueQuestion',{params:{id:that.sel}}).then(res=>{
					that.pratiseList = res.data.data;
					console.log(res.data.data)
				});
			},	
			tagchange(id){
				this.currentTag = id
			},
			 open() {
			        // console.log('open');
			      },
			close() {
			    this.show = false
			// console.log('close');
			},
			//大标题切换
			titleChange(item){
				console.log(item);
				this.nowSublist = this.allSublist[item.index];
				this.sel = this.nowSublist[0].id
				this.getAllTrueQuestion();
			},
			async getStatisticsData(){
			 let data = await getStatistics()
			 if(data!==false){
				 this.statisticsData = data
			 }else{
				this.statisticsData = {}
			 }
			}
		},
		async onShow() {
			//初始化专项题库
			//let data = await getProfessionalQuestion()
			
			//this.getStatisticsData()
			
		},
		async onLoad() {
			this.getBaseData();
			
			//加载类型
			//const allType =  await getAllTypes();
			// if(allType!=false){
			// 	for(let [key, value] of Object.entries(allType)){
			// 			this.allType.push({
			// 				id:key,
			// 				name:value
			// 			})
			// 	}
			// 	this.allType.unshift({
			// 		id:'all',
			// 		name:"综合"
			// 	})
			// }
		},
		mounted() {
			uni.getSystemInfo({
			  success: (res)=>{
				uni.createSelectorQuery().select('.introduction-container').boundingClientRect(data => {
				 //计算右侧课程 列表高度
				 uni.createSelectorQuery().select('.mid-tabs').boundingClientRect(midData => {
				  this.bottomHeight =  res.windowHeight-data.height-midData.height+'px'
						//课程列表高度
						uni.createSelectorQuery().select('.tag-items').boundingClientRect(tagsData => {
						 this.bottomScroolHeight =  res.windowHeight-data.height-midData.heightt+'px'
						 
						 
						}).exec();
					
				  
				 }).exec();
					
				 
				}).exec();
				
				
			  }
			});
		},
		
		filters:{
			handleData(data){
				if(typeof data !=="undefined"){
					return data
				}else{
					return 0
				}
			}
		},
		watch:{
			//监听子标题变化
			professionalSublist:{
				immediate:true,
				deep:true,
				handler(newVal){
					//默认选中第一个子标题
					
				}
			},
			//左侧选中变化 请求数据
			async sel(newval){
				//更显选择的分类
				console.log(newval)
				this.currentCid = newval
				this.currentTag = "all"
				this.searchParam = {type:this.currentTag, cid:newval}
			},
			//标签改变 更新搜索参数
			async currentTag(newval){
				
				
				this.searchParam = {...this.searchParam,type:newval}
				// let params = {
				// 	cid:this.currentCid,
				// 	type:newval
				// }
				// let data = await getQuestionList(params)
				// if(data!=false){
				// 	console.log(data)
				// }
			},
			
			//参数改变发送最终请求
			searchParam:{
				deep:true,
				async handler(val){
					//let data = await getQuestionList(val)
					// if(data!==false){
					// 	this.pratiseList = data.map(item=>{
					// 		return{
					// 			id:item.id,
					// 			title:item.title,
					// 			all:item.question_num,
					// 			rate:item.right_rate,
					// 			doing:item.doing,
					// 			complete:item.hasDoneNum
					// 		}
					// 	})
					// }
				}
				
			}
		},
	
	}
</script>

<style lang="scss" scoped>
.container{
	box-sizing: border-box;
	height: 100vh;
	overflow: hidden;
	.introduction-container{
		box-sizing: border-box;
		padding: 0 30rpx;
	}
	.introduction{
		height: 120rpx;
		margin-top: 32rpx;
		background-color: #F6F7FB;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 22rpx 22rpx 0rpx 0rpx;
		
		.item{
			width: 25%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			.title{
				text{
					&:first-child{
						font-weight: bold;
						color: #201E2E;
						font-size: 32rpx;
					}
					&:last-child{
						font-size: 22rpx;
						color: #777777;
					}
				}
			}
			text{
				font-size: 18rpx;
				color: #777777;
			}
		}
		
	}
	.mid-tabs{
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 4rpx 1rpx rgba(184,184,184,0.16);
		padding: 0 30rpx;
		.all-course-h1 {
			padding: 32rpx 0;
			padding-bottom: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left-title{
				display: flex;
				align-items: center;
				justify-content: flex-start;
				.green-block-big {
					width: 18rpx;
					height: 37rpx;
					background: #01997A;
					border-radius: 0rpx 10rpx 0rpx 10rpx;
						
				}
						
				text {
					padding-left: 10rpx;
					width: 128rpx;
					height: 45rpx;
					font-weight: bold;
					font-size: 32rpx;
					color: #1F1F27;
				}
			}

		
		}
	}
	
	.content{
		display: flex;
		align-items: flex-start;
		box-sizing: border-box;
		background-color: $container-bg-color;
		.left{
			background-color: #fff;
		}
		.right{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;
			padding: 0 20rpx;
			width:95%;
			.practise-container{
				margin-top: 22rpx;
				.exam-list{
					padding-top: 20rpx;
					padding-bottom: 50rpx;
					
					.item{
						display: flex;
						align-items: center;
						justify-content: flex-start;
						background-color: #fff;
						border-radius: 20rpx;
						padding: 18rpx 20rpx;
						margin-bottom: 20rpx;
						.num{
							width: 40rpx;
							height: 40rpx;
							background-color: $main-color;
							color: #fff;
							border-radius: 50%;
							line-height: 40rpx;
							font-size: 24rpx;
							font-weight: bold;
							text-align: center;
							margin-right: 20rpx;
							
						}
						.title{
							flex: 6	;
							margin-right: 20rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-direction: column;
							.title-name{
								font-weight: bold;
								font-size: 28rpx;
								color: #4A4A4C;
								width: 100%;
							}
							.date{
								width: 100%;
								color: #9F9F9F;
								font-size: 22rpx;
							}
						}
						.exam-progress{
							flex: 1;
							display: flex;
							align-items: center;
							justify-content: space-between;
							.do-continue{								
								color: #EE7878;
								font-size: 20rpx;
							}
						}
					}
				}
			}
		}
	}
}
</style>
