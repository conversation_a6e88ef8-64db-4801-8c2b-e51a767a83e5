<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left" @click="goBack()">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
			            <u-icon
							color="#009c7b"
			                name="clock"
			                size="38"
			            ></u-icon>
						<text>{{useTime}}</text>
			</view>
		</u-navbar>
		
		<view class="content">
			<view class="q-type">
				单词带背
			</view>
			
			<view class="question">
				<text class="title">一、请写出下列单词的词性和意思</text>
				<view class="words-list">
					<view class="word" v-for="(item, i) in answer">
						<view>{{i+1}}.{{item.word}}</view>
						<view class="word-info">	
							<u-input
								placeholder="请输入内容"
								border="bottom"
								clearable
								v-model:value="item.uanswer"
							  >
							  <u-text
									:text="i+1"
									slot="prefix"
									margin="0 3px 0 0"
									class="order-num"
									type="tips"
								></u-text>
							</u-input>
						</view>
					</view>
				</view>
			</view>
		</view>
		
	
		<view class="btn-list">
			<view class="bottom-btn tmp-btn" @click="submitTmpData">
				临时保存
			</view>
			<view class="bottom-btn" @click="submitData">
				提交
			</view>
		</view>
		<!-- <view class="bottom-btn" @click="submitData">
			提交
		</view> -->
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	export default {
		data() {
			return {
				taskId:'',
				wordData:[],
				fileList:[],
				answer:[],
				useTime:'00:00',
				secondTime:0,
			};
		},
		async onLoad(options) {
			this.taskId = options.task_id
			this.getWordDetail();
			let that = this;
			await setInterval(function(){
				that.secondTime++;
				let tmp_min = parseInt(that.secondTime/60);
				let tmp_second = that.secondTime%60;
				that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
			},1000)
		},
		methods:{
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getUnitWord',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data.word_list;
					that.secondTime = res.data.useTime
					that.wordData.map(item=>{
						if(item.uanswer != undefined) {
							that.answer.push({word:item.word,uanswer:item.uanswer})
						} else  {
							that.answer.push({word:item.word,uanswer:''})
						}
						
					})
					console.log(that.answer);
					
				})
			},
			submitTmpData() {
				let pData  = {
					answer:this.answer,
					about_id:this.taskId,
					type:'word',
					useTime:this.secondTime
				}
				let that = this;
				let  data  = uni.$u.http.post('/task/taskTempRecord',pData).then(res=>{
					console.log(res)
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						//uni.navigateBack({delta:1})	
					} else {
						uni.$u.toast(res.msg)
					}
				});
				
			},
			submitData() {
				let answer_flag = false
				this.answer.map((item)=>{
					if(item.uanswer != '') {
						answer_flag = true;
					}
				})
				if(!answer_flag) {
					uni.$u.toast('你填写单词答案');
					return  false;
				}
				let pData  = {
					answer:this.answer,
					about_id:this.taskId,
					type:'word',
					useTime:this.secondTime
				}
				let that = this;
				let  data  = uni.$u.http.post('/task/taskOtherRecord',pData).then(res=>{
					console.log(res)
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						setTimeout(function(){
							uni.navigateTo({
								url:'/english/word_answer/word_answer?task_id='+that.taskId
							})
						},1000)	
					} else {
						uni.$u.toast(res.msg)
					}
				});
				
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
		},
	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	padding-bottom: 20rpx;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			color: #5A5A5A;
			font-size: 28rpx;
			.title{
				width: 100%;
				margin-top: 16rpx;
			}
			.words-list{
				.word{
					margin-top: 24rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					.word-info {
						display: flex;
						::v-deep .u-text__value--tips{
							display: inline-block;
							height: 33rpx;
							width: 33rpx;
							border-radius: 50%;
							background-color: #01997A;
							text-align: center;
							line-height: 33rpx;
							color: #fff;
						}
					
					}
				}
			}
		}
	}
	
	.btn-list {
		display: flex;
		
		.bottom-btn{
			margin: 0 auto;
			width: 300rpx;
			height: 80rpx;
			color: #fff;
			line-height: 80rpx;
			text-align: center;
			background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
			border-radius: 40rpx;
		}
		.tmp-btn{
			background: #F6F7FB;
			color: #01997A;
			font-weight: bold;
		}
	}
	
}
</style>
