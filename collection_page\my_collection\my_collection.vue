<template>
	<view class="container">
		<view class="head u-border-bottom">
			<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="text" activeColor="#009c7b"></uni-segmented-control>
		</view>
		<!-- 信息面板 -->
		<view class="panel" v-if="current == 1">
			<view class="info u-border-right">
				<text class="num">
					{{ dataCount.total }}
					<text>节</text>
				</text>
				<text class="subject">累计收藏</text>
			</view>
			<view class="info" v-for="(item, key) in dataCount.alone_total">
				<text class="num">
					{{ item.count }}
					<text>节</text>
				</text>

				<text class="subject">{{ showText[item.type] }}</text>
			</view>
		</view>

		<view class="panel" v-if="current == 0">
			<view class="info u-border-right">
				<text class="num">
					{{ starData.allNum | handleNull }}
					<text>题</text>
				</text>
				<text class="subject">累计收藏</text>
			</view>
			<view class="info" v-for="item in starData.cateData" :key="item.id">
				<text class="num">
					{{ item.num }}
					<text>题</text>
				</text>

				<text class="subject">{{ item.name }}</text>
			</view>

		</view>
		<view class="panel" v-if="current == 2 || current == 3">
			<view class="info u-border-right">
				<text class="num">
					{{ anywhereListenAndViewStatisticsData.total | handleNull }}
					<text>题</text>
				</text>
				<text class="subject">累计收藏</text>
			</view>
			<view class="info">
				<text class="num">
					{{ anywhereListenAndViewStatisticsData.other_total | handleNull }}
					<text>题</text>
				</text>

				<text class="subject">专业课</text>
			</view>
			<view class="info">
				<text class="num">
					{{ anywhereListenAndViewStatisticsData.public_total.politics | handleNull }}
					<text>题</text>
				</text>

				<text class="subject">政治</text>
			</view>
			<view class="info">
				<text class="num">
					{{ anywhereListenAndViewStatisticsData.public_total.math | handleNull }}
					<text>题</text>
				</text>

				<text class="subject">数学</text>
			</view>
			<view class="info">
				<text class="num">
					{{ anywhereListenAndViewStatisticsData.public_total.eng | handleNull }}
					<text>题</text>
				</text>

				<text class="subject">英语</text>
			</view>

		</view>



		<!-- tab选择 -->
		<view class="user-tabs">
			<view class="user-tabs-left">

				<u-tabs v-if="current == 0" :list="subjectList" @click="questionTitleChange" lineWidth="50" lineHeight="6" lineColor="#009c7b"></u-tabs>

				<u-tabs v-if="current == 1" :list="list" @click="tabClick" lineWidth="50" lineHeight="6" lineColor="#009c7b"></u-tabs>

				<u-tabs v-if="current == 2" :list="anywhereListenAndViewListCat" @click="anyClick" lineWidth="50" lineHeight="6" lineColor="#009c7b"></u-tabs>

				<u-tabs v-if="current == 3" :list="anywhereListenAndViewListCat" @click="anyClick" lineWidth="50" lineHeight="6" lineColor="#009c7b"></u-tabs>
			</view>
			<view class="user-tabs-right">
				<mimicry-switch :current.sync="switchs"></mimicry-switch>
			</view>
		</view>

		<!-- 列表信息 -->
		<template v-if="current == 0">
			<view class="list" v-if="questionList.length > 0">
				<view class="item u-border-bottom" v-for="obj in questionList" :key="obj.id" @click="tobank(obj, pqidcontainqids[obj.id])">
					<view class="item-title">
						<text>{{ obj.title }}</text>
						<text>考题数：{{ pqidcontainqids[obj.id].length }}题</text>
					</view>
					<view class="enter">
						<text>进入练习</text>
						<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
					</view>
				</view>
			</view>
			<u-empty v-if="loading == false && questionList.length == 0" mode="list" text="没有收藏" textSize="24">
			</u-empty>
			<!-- 			<view class="more" @click="getMoreStarQuestionList" v-if="show && loading==false">
				<image src="../../static/png/down-arrow-double.png" mode="widthFix"></image>
				<text>加载更多</text>
			</view> -->
		</template>


		<template v-if="current == 1">
			<view class="list">
				<view class="item u-border-bottom" v-for="(items, i) in collectListVideo" :key="i">
					<view class="item-title">
						<text>{{ items.alias_name ? items.alias_name : items.name }}</text>
						<text>主讲老师:{{ items.teacher_text.join(',') }}</text>
					</view>
					<view class="enter" @click="toListen(items)">
						<text>继续学习</text>
						<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
					</view>
				</view>
			</view>

			<!-- 			<view class="more" @click="getMore" v-if="show && loading==false">
				<image src="../../static/png/down-arrow-double.png" mode="widthFix"></image>
				<text>加载更多</text>
			</view> -->
		</template>


		<template v-if="current == 2 || current == 3">
			<!-- 			<view class="list" v-if="anywhereListenAndViewList.length>0">
				<view class="item u-border-bottom" v-for="obj in questionList" :key="obj.id" @click="tobank(obj, pqidcontainqids[obj.id])">
					<view class="item-title">
						<text>{{obj.title}}</text>
						<text>考题数：{{pqidcontainqids[obj.id].length}}题</text>
					</view>
					<view class="enter" >
						<text>进入练习</text>
						<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
					</view>
				</view>
			</view> -->






			<view class="list-audio" v-if="anywhereListenAndViewList.length > 0">
				<view class="item u-border-bottom" v-for="item in anywhereListenAndViewList" :key="item.id" @click="() => toListenOrVedio(item)">
					<image :src="item.pic"></image>
					<view class="title-info">
						<text class="t-h1">{{ item.name }}</text>
						<text class="tip m-2">{{ item.subtitle }}</text>
						<text class="tip">已经播放{{ item.num | handleNull }}次|共{{ item.total }}集</text>
					</view>
					<view class="play-icon">
						<u-icon name="play-circle-fill" size="50" color="#009c7b"></u-icon>
					</view>
				</view>
			</view>
			<u-empty v-if="loading == false && anywhereListenAndViewList.length == 0" mode="list" text="没有收藏" textSize="24">
			</u-empty>
			<!-- <view class="more" @click="getListenAndViewMore" v-if="show && loading==false">
				<image src="../../static/png/down-arrow-double.png" mode="widthFix"></image>
				<text>加载更多</text>
			</view> -->

		</template>

	</view>
</template>

<script>
import mimicrySwitch from "@/components/mimicry-switch/mimicry-switch.vue"
import {
	getStarList,
	getAllStarQuestionData
} from "@/api/collection/index.js"
import {
	getQuestionListByPqid,
	getProgress
} from "@/api/professional/index.js"
import {
	mapState
} from "vuex"
import {
	selectTemplate
} from "@/utils/tool.js"
import {
	getTopCate,
	getStatisticsData,
	getAnyStarList
} from "@/api/exampoint/index.js"
export default {
	data() {
		return {
			starData: {}, //收藏的数据
			items: ['收藏的题目', '收藏的课程', '考点随身学', '考点随身听'],
			videoPage: 1,
			show: true,
			current: 0,
			videoTotal: 0,
			showText: ['英语', '政治', '数学', '专业课'],
			list: [{
				id: 1,
				name: "政治"
			},
			{
				id: 0,
				name: "英语"
			},
			{
				id: 2,
				name: "数学"
			},
			{
				id: 3,
				name: "专业课"
			}
			],
			switchs: false,
			dataCount: '',
			type: 1,
			dataList: '',
			collectListVideo: [],
			currentSubjectId: 0,
			page: {
				perPageNum: 15,
				page: 1
			},
			loading: false, //请求数据中
			pqidcontainqids: {}, //题库包含的题目数量
			questionList: [], //题库列表
			anywhereListenAndViewListCat: [], //随身听和随身看分类
			anywhereListenAndViewStatisticsData: {}, //随身听和随身看统计数据
			anywhereListenAndViewList: [], //随身听和随身看列表
			anywhereListenAndViewPage: 1,
			currentSelanyWhereListenAndViewCid: 0
		};
	},
	onLoad() {
		this.countCollection();
		this.getUserCollectionList();
		//随身听随身学的顶级分类
		getTopCate().then(data => this.anywhereListenAndViewListCat = data.list)
	},
	methods: {
		getMore() {
			this.videoPage++;
			this.getUserCollectionList();
		},
		getStarListBySubject() {

		},
		toListenOrVedio(item) {
			if (this.current == 2) {
				//考点随身听
				uni.navigateTo({
					url: `/choosecoursepkg/study/study_exam_point?cid=${item.id}&point_ids=${item.point_ids}`
				})
			}
			if (this.current == 3) {
				//考点随身听
				uni.navigateTo({
					url: `/subpkg/listen_audio/listen_audio?cid=${item.id}&name=${item.name}&point_ids=${item.point_ids}`
				})
			}
		},
		/**
		 * 到题库
		 * @param {Object} item 题库信息
		 * @param {Array} containsIds 题库包含的题目id数组
		 */
		async tobank(item, containsIds) {
			this.$store.commit("professionalConfig/clearAnswerList")
			//记录进入题库的页面
			this.$store.commit("professionalConfig/setCurrentStartPage")
			//题目列表
			let answeringQuestion = []
			//获取题目列表
			const params = {
				pqid: item.id,
				perPageNum: this.config.num,
			}
			let questionList = await getQuestionListByPqid(params)
			//如果题目列表为空返回
			if (questionList === false) {
				return
			}
			//获取 一级分类信息
			const subject = this.subjectList.find(item => item.id == this.currentSubjectId)
			questionList.forEach(obj => {

				if (containsIds.includes(obj.id)) {
					// 构造当前 题目的列表数据
					answeringQuestion.push({
						...obj,
						cateName: subject.name,
						type: item.type,
						pqid: item.id,
						id: obj.qid,
						subtitle: obj.qtitle,
						qtype: obj.qtype,
						professionTitle: item.title
					})
				}

			})
			this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
			//判断配置信息是否跳过已做题目
			//默认第一道题开始
			let startIndex = 0
			if (this.config.do_unread == 1) {
				//获取题库该中 已经做了多少道题
				let res = await getProgress({
					pqid: item.id
				})
				if (res !== false) {
					startIndex = res.currentIndex
				}
			}
			this.$store.commit("professionalConfig/setCurrentIndex", startIndex);
			selectTemplate(answeringQuestion[startIndex], item)
		},
		//科目更改 获取具体科目的收藏列表
		async questionTitleChange(item) {
			console.log(item)
			this.loading = true
			this.currentSubjectId = item.id
			this.page.page = 1
			this.questionList = []
			this.pqidcontainqids = {}
			await this.searchStarListBySubject()
			this.loading = false
		},
		// 获取更多
		getMoreStarQuestionList() {
			this.page.page++
			this.searchStarListBySubject(true)
		},
		//根据科目获取收藏列表
		async searchStarListBySubject(append = false) {
			this.show = true
			//最近 全部切换 1 最近 0 全部
			const recent = this.switchs ? 0 : 1
			const params = {
				id: this.currentSubjectId,
				//...this.page,
				recent
			}
			let data = await getStarList(params)
			if (data != false) {
				if (append) {
					this.questionList = [...this.questionList, ...data.questionInfo]
					this.pqidcontainqids = {
						...this.pqidcontainqids,
						...data.pqidcontainqids
					}
				} else {
					this.questionList = data.questionInfo
					this.pqidcontainqids = data.pqidcontainqids
				}

				//data.questionInfo.length == 0 && (this.show = false)
			}
			// console.log(this.questionList)
			// if (this.questionList.length == 0) {
			// 	this.show = false
			// }
		},
		getUserCollectionList() {
			let that = this;
			let collectionData = {
				page: that.videoPage,
				type: that.type,
				sort: this.switchs ? 0 : 1
			};
			uni.$u.http.get('/student/getUserCollectionList', {
				params: collectionData
			}).then(res => {

				that.collectListVideo = [...that.collectListVideo, ...res.data.data]
				that.videoTotal = res.data.total
				if (that.collectListVideo.length >= that.videoTotal) {
					that.show = false
				}
			})
		},
		countCollection() {
			let that = this;

			uni.$u.http.get('/student/countCollection').then(res => {
				that.dataCount = res.data;
			})
		},

		toListen(item) {
			uni.$u.http.get('/student/getJumpUrl', {
				params: {
					chapter_id: item.chatper_id,
					video_id: item.video_id
				}
			}).then(res => {
				uni.navigateTo({
					url: '/choosecoursepkg/study/study?course_type=0&course_id=' + item.course_id +
						'&chapter_id=' + item.chatper_id + '&video_id=' + item.video_id +
						'&conly_couse_id=' + res.data.module
				})
			})
		},
		tabClick(item) {
			this.collectListVideo = [];
			this.videoPage = 1;
			this.type = item.id;
			this.getUserCollectionList()
		},
		/**
		 * 随身听 随身学  子项目点击
		 */
		anyClick(item) {
			this.anywhereListenAndViewList = []
			this.anywhereListenAndViewPage = 1
			this.currentSelanyWhereListenAndViewCid = item.id
			this.getAnyListenAndVieList()
		},
		/**
		 * 获取随身听随身学列表
		 * @param {Object} 
		 */
		async getAnyListenAndVieList() {
			let type;
			if (this.current == 2) {
				type = 2
			}
			if (this.current == 3) {
				type = 1
			}
			const params = {
				page: this.anywhereListenAndViewPage,
				type,
				topLevelCid: this.currentSelanyWhereListenAndViewCid,
				recent: this.switchs ? 0 : 1
			}
			this.loading = true
			this.show = true
			const data = await getAnyStarList(params)
			console.log(data)
			this.anywhereListenAndViewList = [...this.anywhereListenAndViewList, ...data.list]
			if (data.list == 0) {
				this.show = false
				//uni.tip("没有更多")
			}
			this.loading = false

		},
		//随身听随身学加载更多
		getListenAndViewMore() {
			this.anywhereListenAndViewPage++
			this.getAnyListenAndVieList()
		},
		onClickItem(e) {
			if (this.current != e.currentIndex) {
				this.current = e.currentIndex;
			}
		},
		async getStarDataOnItemChange() {
			//获取统计数据
			const data = await getAllStarQuestionData()
			if (data !== false) {
				this.starData = data
				//科目选中第一个
				this.currentSubjectId = this.starData['cateData'][0].id
				this.searchStarListBySubject()
			}
		}
	},
	filters: {
		handleNull(val) {
			if (typeof val == "undefined") {
				return 0
			}
			return val
		}
	},
	watch: {
		current: {
			immediate: true,
			deep: true,
			async handler(index) {
				this.show = true
				if (index == 0) {
					// 获取收藏的题目
					this.getStarDataOnItemChange()
					return;
				} else if (index == 1) {
					//获取收藏的视频
					return;
				} else if (index == 2) {
					//随身学的统计数据
					var data = await getStatisticsData({
						type: 2
					})

				} else if (index == 3) {
					//随身听的统计数据
					var data = await getStatisticsData({
						type: 1
					})
				}
				this.anywhereListenAndViewStatisticsData = data
				this.anywhereListenAndViewList = []
				this.anywhereListenAndViewPage = 1
				this.currentSelanyWhereListenAndViewCid = this.anywhereListenAndViewListCat[0].id
				this.getAnyListenAndVieList()
			}
		},
		switchs(val) {
			if (this.current == 0) {
				this.page.page = 1
				this.questionList = []
				this.pqidcontainqids = {}
				this.searchStarListBySubject()

			}

			if (this.current == 1) {
				this.collectListVideo = [];
				this.getUserCollectionList();
			}

			if (this.current == 2 || this.current == 3) {
				this.anywhereListenAndViewList = []
				this.anywhereListenAndViewPage = 1
				this.getAnyListenAndVieList()
			}

		}
	},
	computed: {
		//一级科目列表
		subjectList() {
			if (typeof this.starData['cateData'] != 'undefined') {
				return this.starData['cateData'].map(item => {
					return {
						id: item.id,
						name: item.name
					}
				})
			} else {
				return []
			}
		},
		...mapState('professionalConfig', ['currentTypes', 'config']),
	},
	components: {
		mimicrySwitch
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 0 30rpx;

	.head {
		margin: 0 -30rpx;
	}

	.panel {
		margin-top: 30rpx;
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #F6F7FB;
		border-radius: 22rpx;

		.info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			padding: 0 20rpx;

			.num {
				color: #201E2E;
				font-size: 32rpx;
				font-weight: bold;

				text {
					font-size: 18rpx;
					color: #777;
					font-weight: normal;
				}
			}

			.subject {
				font-size: 18rpx;
				color: #777;
			}
		}
	}

	.user-tabs {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.user-tabs-left {}

		.user-tabs-right {}
	}

	.list-audio {
		padding: 0 30rpx;
		padding-bottom: 40rpx;

		.title-name {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			padding-bottom: 0;

			.left {
				color: #1F1F27;
				font-size: 32rpx;
				font-weight: bold;

				.green-block {
					display: inline-block;
					width: 14rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #01997A;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
					margin-right: 6rpx;
				}
			}

		}

		.item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 22rpx;
			padding-top: 40rpx;

			image {
				width: 100rpx;
				height: 100rpx;
				border-radius: 14rpx;
			}

			.title-info {
				width: 60%;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				text-align: left;
				margin-left: -40rpx;

				.t-h1 {
					width: 100%;
					color: #201E2E;
					font-size: 32rpx;
					font-weight: bold;
				}

				.tip {
					width: 100%;
					color: #AFAFAF;
					font-size: 20rpx;
					margin-top: 12rpx;
				}

				.m-2 {
					margin-top: 2rpx;
				}
			}

			.play-icon {
				margin-right: 20rpx;
			}
		}
	}

	.list {

		.course-list {
			padding: 0 30rpx;

			.course-info {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 0;

				.course-info-left {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.course-num {
						width: 120rpx;
						font-weight: bold;
						font-size: 36rpx;
						color: #5A5A5A;
						text-align: center;

						image {
							width: 60rpx;
						}
					}

					.course-title {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						flex-direction: column;

						.course-tip {
							width: 100%;
							color: #201E2E;
							font-size: 30rpx;
							margin-top: 12rpx;
						}

						.tip {
							margin-top: 4rpx;
							width: 100%;
							display: flex;
							align-items: center;
							justify-content: flex-start;

							text {
								color: #AFAFAF;
								font-size: 20rpx !important;
							}

						}
					}
				}

				.down {
					flex: 1;
				}

				image {
					width: 60rpx;
				}
			}
		}

		.item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;

			.item-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;

				text {
					&:first-child {
						width: 100%;
						color: #201E2E;
						font-size: 30rpx;
						font-weight: bold;
						padding: 0 4rpx;
					}

					&:last-child {
						width: 100%;
						text-align: left;
						margin-top: 4rpx;
						color: #9F9F9F;
						font-size: 22rpx;
						padding: 0 4rpx;
					}
				}
			}

			.enter {
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					color: #EE7878;
					font-size: 20rpx;
					margin-right: 10rpx;
				}
			}
		}
	}

	.more {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		image {
			width: 20rpx;
		}

		text {
			margin-left: 6rpx;
			font-size: 30rpx;
			color: #A2A2A2;
			font-weight: bold;
		}

	}
}
</style>