<template>
	<view class="content">
		
		<rich-text
			:nodes="content"></rich-text>
	</view>
</template>

<script>
	export default {
		
		data() {
			return {
				tagStyle: {
					img: 'width:100%;display:block;',
					table: 'width:100%',
					video: 'width:100%'
				},
				content: ``
			}
		},
		onLoad(options) {
			let type = options.type;
			
			uni.$u.http.post('/index/getAgreement',{type:type}).then(res=>{
				this.content  = res.data.content
			})
			// getUserAgreement(options.type).then(res => {
			// 	this.content = res.data.content
			// 	uni.setNavigationBarTitle({
			// 		title: res.data.title
			// 	});
			// }).catch(err => {
			// 	that.$util.Tips({
			// 		title: err
			// 	});
			// })
		}
		
	}
</script>

<style scoped>
	page {
		background-color: #fff;
	}

	.content {
		padding: 40rpx 30rpx;
		line-height: 2;
	}
</style>
