<template>
	<view class="container">
		<view class="head">
			<uni-segmented-control :values="tabList" @clickItem="chooseItem" activeColor="#06A177" styleType="text"></uni-segmented-control>
		</view>
		<view class="title">
			<view class="left">
				<view class="green-block">

				</view>
				<view class="h1">
					考试趋势图
				</view>
			</view>
			<view class="right-container">
				<!-- 左侧按钮 -->
				<view @click="changeTime(1)" class="left-button" :class="{ selected: currentSelTimeIndex == 1 }">本周</view>

				<!-- 中间下拉框 -->
				<picker :value="currentMonth" :range="months" @change="onMonthChange">
					<view class="dropdown" @click="changeTime(2)" :class="{ selected: currentSelTimeIndex == 2 }">

						<view class="dropdown-content">{{ months[currentMonth] }}</view>

						<view class="arrow-down">
							<u-icon name="arrow-down" :color="currentSelTimeIndex == 2 ? '#fff' : '#707070'" size="28"></u-icon>
						</view>

					</view>
				</picker>
				<!-- 右侧按钮 -->
				<view @click="changeTime(3)" class="right-button" :class="{ selected: currentSelTimeIndex == 3 }">全部</view>
			</view>
		</view>

		<view class="statics">
			<l-echart ref="chartRef" @finished="initEcharts"></l-echart>
		</view>

		<view class="color-block">
			<view class="color-rect" style="background-color: #1BB394;">

			</view>
			<text>平均分</text>
			<view class="color-rect" style="background-color:  #EE7A8F;">

			</view>
			<text>学生成绩</text>
			<view class="color-rect" style="background-color:  #FFB975;">

			</view>
			<text>院校线</text>
			<view class="color-rect" style="background-color:  #CAA0FF;">

			</view>
			<text>A区国家线</text>
			<view class="color-rect" style="background-color:  #92BAFF;">

			</view>
			<text>B区国家线</text>
		</view>

		<view class="title">
			<view class="left">
				<view class="green-block">

				</view>
				<view class="h1">
					考试列表
				</view>
			</view>
		</view>

		<view class="sel-list">
			<view class="header">
				<text class="head-item">序号</text>
				<text class="head-item">考试主题</text>
				<text class="head-item">考试成绩</text>
				<text class="head-item">平均分</text>
				<text class="head-item">考试时间</text>
				<text class="head-item">详情</text>
			</view>
			<view class="order-list u-border-bottom" v-for="item in examList" :key="item.id">
				<text class="list-item">{{ item.id }}</text>
				<text class="list-item">{{ item.mange_topic.title }}</text>
				<text class="list-item">{{ item.score }}/{{ item.total_score_text }}</text>
				<text class="list-item">{{ item.average_text }}/{{ item.total_score_text }}</text>
				<text class="list-item" style="font-size: 18rpx;">{{ item.examtime }}</text>
				<view class="btn-opt">
					<text class="btn">查看</text>
				</view>
			</view>
			<u-empty v-if="examList.length == 0 && loaded == true" :marginTop="'20rpx'" class="empty" text="暂无数据" :textSize='"24rpx"'></u-empty>
		</view>
	</view>
</template>

<script>
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import moment from "../module/moment/moment"
import { getExamAnalysisData } from "@/api/camp/index.js"
import { selectEvenlySpacedIndices } from "../utils/index.js"
let chart = null
export default {
	data() {
		return {
			loaded: false,
			currentMonth: 0,
			echarts: null,
			months: [],
			ec: {
				option: {
					title: {
						text: ''
					},
					tooltip: {
						backgroundColor: 'rgba(0,0,0,0.5)',
						textStyle: {
							color: '#fff', // 设置 tooltip 中文字的颜色为黄色
						},
						trigger: 'axis',
						//formatter: '{b}\r\n{c0}人',
						axisPointer: {
							type: 'cross',
							label: {
								backgroundColor: '#6a7985'
							}
						},
					},
					grid: {
						left: '2%',
						right: '6%',
						top: '6%',
						bottom: '1%',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						data: [],
						axisLine: {
							// y轴
							show: false
						},
						axisTick: {
							// y轴刻度线
							show: false
						},
						splitLine: {
							// 网格线
							show: false
						}
					},
					yAxis: {
						type: 'value',
						axisLine: {
							// y轴
							show: false
						},
						axisTick: {
							// y轴刻度线
							show: false
						},
						splitLine: {
							// 网格线
							show: false
						}
					},
					series: [{
						name: '成绩',
						type: 'line',
						smooth: true,
						lineStyle: {
							color: '#1BB394'
						},
						areaStyle: {
							color: 'rgba(27, 179, 148, 0.2)' // 设置填充颜色
						},
						itemStyle: {
							color: '#1BB394'
						},
						markLine: {
							data: [{
								yAxis: "", // 横线所在的纵坐标值
								lineStyle: {
									color: '#FFB975', // 横线颜色
									type: 'solid', // 横线类型，虚线
								},
								symbol: 'none'
							},

							{
								yAxis: "", // 横线所在的纵坐标值
								lineStyle: {
									color: '#CAA0FF', // 横线颜色
									type: 'solid', // 横线类型，虚线
								},
								symbol: 'none'
							},
							{
								yAxis: "", // 横线所在的纵坐标值
								lineStyle: {
									color: '#92BAFF', // 横线颜色
									type: 'solid', // 横线类型，虚线
								},
								symbol: 'none'
							}
							],
							symbol: 'none'
						},

						data: []
					},
					{
						name: '平均分',
						type: 'line',
						smooth: true,
						lineStyle: {
							color: '#EE7A8F'
						},
						areaStyle: {
							color: 'rgba(238, 122, 143, 0.2)' // 设置填充颜色
						},
						itemStyle: {
							color: '#EE7A8F'
						},

						data: []
					},

					]
				}
			},
			currentSelTimeIndex: 1,
			date_type: 1,
			searchMonth: moment().format('YYYY-MM'),
			//分析数据
			analysisData: {},
			selectedName: "",
			backUpOption: {},
			echartsInstance: null
		}

	},
	onLoad() {
		let i = 0
		this.months = Array.from({ length: 6 }, () => {
			return moment().subtract(i++, 'month').format('MM') + '月'
		})
		//备份初始化的 echarts数据
		this.backUpOption = JSON.parse(JSON.stringify(this.ec.option))
		delete this.backUpOption.series[0].markLine
		//获取分析的数据
		this.getData()
	},
	methods: {
		// 新增的 echarts 初始化方法
		async initEcharts() {
			// chart 图表实例不能存在data里
			this.echartsInstance = await this.$refs.chartRef.init(echarts);
			this.echartsInstance.setOption(this.ec.option)
		},
		//时间选择发生变化
		changeTime(val) {
			this.currentSelTimeIndex = val
			this.date_type = val;
			//本周和全部直接获取数据 不需要选择
			if (val == 1) {
				this.getData()
			}
			if (val == 3) {
				this.getData()
			}
		},
		chooseItem(val) {
			this.selectedName = this.tabList[val.currentIndex - 0]
		},
		//获取考试分析数据
		async getData() {
			const params = {
				date_type: this.date_type,
				month: this.searchMonth,
			}
			this.loaded = false;
			const data = await getExamAnalysisData(params)
			this.loaded = true;
			if (data != false) {
				this.analysisData = data.data
				if (this.selectedName == "") {
					this.selectedName = this.tabList[0]
				}
				//更新echarts 数据
				this.updateEchartsOptions()

			}
			this.updateEchartsOptions(true)

		},
		selectWeek() {
			// 处理本周按钮的逻辑
		},
		onMonthChange(event) {
			this.currentMonth = event.detail.value;
			const reg = /\d+/g
			const month = this.months[this.currentMonth].match(reg)
			this.searchMonth = moment().format("YYYY") + '-' + month[0]
			this.getData()
		},
		selectAll() {
		},
		//获取科目对应的// #id
		findIdByTabSelectedName() {
			let index = ""


			Object.keys(this.analysisData.nameArr).forEach(key => {
				if (this.analysisData.nameArr[key] == this.selectedName) {
					index = key
				}
			})
			return index
		},
		updateEchartsOptions(backup = false) {
			if (this.echartsInstance != null) {
				const option = backup ? this.backUpOption : this.ec.option
				this.echartsInstance.setOption(option)
			}
		}
	},
	onUnload() {
		this.echartsInstance = null
	},
	computed: {
		tabList() {
			if (typeof this.analysisData.count == 'undefined') {
				return []
			}
			return Object.keys(this.analysisData.count)
		},
		examList() {
			if (typeof this.analysisData.nameArr == 'undefined') {
				return []
			}

			const id = this.findIdByTabSelectedName()

			return this.analysisData.list_tmp[id].detail
		}
	},
	watch: {
		//tab 科目变化处理echarts数据
		examList: {
			handler(val) {
				if (val.length == 0) {
					return this.updateEchartsOptions(true)
				}
				//获取科目对应的 id
				const id = this.findIdByTabSelectedName()

				const indexs = selectEvenlySpacedIndices(this.analysisData.detail[id].data_x, 7)
				//处理x轴
				this.ec.option.xAxis.data = this.analysisData.detail[id].data_x.map((item, index) => {
					if (indexs.includes(index)) {
						return moment(item).format("MM/DD")
					}
				})
				//院校线
				this.ec.option.series[0].markLine.data[0].yAxis = this.analysisData?.detail[id]?.data_y?.arrC[0]
				//A去线
				this.ec.option.series[0].markLine.data[1].yAxis = this.analysisData?.detail[id]?.data_y?.arrA[0]
				//B区线
				this.ec.option.series[0].markLine.data[2].yAxis = this.analysisData?.detail[id]?.data_y?.arrB[0]


				//平均分
				this.ec.option.series[1].data = this.analysisData?.detail[id]?.data_y?.average.filter((item, index) => indexs.includes(index))
				//我的分数
				this.ec.option.series[0].data = this.analysisData?.detail[id]?.data_y?.my_score.filter((item, index) => indexs.includes(index))

				this.updateEchartsOptions()

			},
			immediate: true,
			deep: true
		}
	}

}
</script>

<style lang="scss" scoped>
.container {
	padding: 0 30rpx;
}

.head {
	width: 360rpx;
}

::v-deep .segmented-control__text {
	font-size: 28rpx !important;
}

.title {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.left {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	margin-top: 30rpx;
}

.green-block {
	width: 18rpx;
	height: 37rpx;
	background: #01997A;
	border-radius: 0rpx 10rpx 0rpx 10rpx;
}

.h1 {
	font-weight: bold;
	font-size: 32rpx;
	color: #1F1F27;
	margin-left: 10rpx;
}

.right-container {
	flex: 1;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	border-radius: 8px;
	font-size: 24rpx;
	height: 54rpx;
	color: #01997A;
}

.selected {
	background-color: #01997A !important;
	color: #fff;
}

.left-button {
	width: 68rpx;
	height: 54rpx;
	background: #fff;
	border-radius: 12rpx 0rpx 0rpx 12rpx;
	border: 1rpx solid #01997A;
	line-height: 54rpx;
	text-align: center;
}

.dropdown {
	height: 54rpx;
	line-height: 54rpx;
	padding-left: 24rpx;
	padding-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 1rpx solid #01997A;
	border-left: none;
	border-right: none;
}

.arrow-down {
	margin-left: 10rpx;
}

.right-button {
	width: 68rpx;
	height: 54rpx;
	background: #fff;
	border-radius: 0rpx 12rpx 12rpx 0rpx;
	border: 1rpx solid #01997A;
	line-height: 54rpx;
	text-align: center;
	border: 1rpx solid #01997A;
}

.statics {
	width: 100%;
	height: 400rpx;
	border-radius: 20rpx 20rpx 20rpx 20rpx;
	border: 1rpx solid #1BB394;
	margin-top: 16rpx;
}

.uni-ec-canvas {
	width: 100%;
	height: 100%;
	display: block;
}

.color-block {
	margin-top: 22rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #5A5A5A;
	font-size: 22rpx;

	.color-rect {
		width: 28rpx;
		height: 28rpx;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
	}
}

.sel-list {
	margin-top: 32rpx;

	.header,
	.order-list {
		height: 74rpx;
		background: #01997A;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.head-item {
			width: 15%;
			text-align: center;
			color: #fff;
			font-size: 24rpx;
		}
	}

	.order-list {
		height: 108rpx;
		border-radius: 0;
		background-color: #fff;

		.list-item,
		.btn-opt {
			width: 15%;
			text-align: center;
			color: #2D2D2D;
			font-size: 20rpx;
		}

		.btn-opt {
			display: flex;
			align-items: center;
			justify-content: center;

			.btn {
				color: #01997A;
				height: 50rpx;
				line-height: 50rpx;
				font-size: 22rpx;
			}
		}
	}
}
</style>