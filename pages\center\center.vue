<template>
	<view class="container">
		<!-- 头部 -->
		<view class="head">

			<view class="setting">
				<image src="/static/png/setting.png" mode="widthFix" @click="toSetting"></image>
			</view>
			<view class="title">
				<view class="left">
					<view class="avatar">
						<image :src="userDetail.avatar">
						</image>
					</view>
					<view class="info">
						<view class="top-text">
							<text>{{ (userDetail.nickname || userDetail.username) | formatUsername }}</text>
							<image src="https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/coin.png" @click="toTadpole()">
							</image>
							<text class="coin" @click="toTadpole()">{{ userDetail.point != null ? userDetail.point : 0 }}</text>

							<image v-if="userDetail.is_camp == 1" src="https://website-1300870289.cos.ap-nanjing.myqcloud.com/study_points2x.png" @click="toPoint()">
							</image>
							<text v-if="userDetail.is_camp == 1" class="coin" @click="toPoint()">{{ userDetail.camp_point }}</text>


						</view>
						<view class="bottom-text">您已经加入研趣{{ userDetail.join_day }}天</view>
					</view>

				</view>
				<view class="right" v-if="userDetail.exam_day > 0">
					<text class="cls_text">{{ userDetail.cls_stu.substring(2) }}倒计时</text>
					<text>{{ userDetail.exam_day }}天</text>
				</view>
			</view>
		</view>
		<!-- 快捷导航 -->
		<view class="grid-container" :style="{ paddingBottom: userDetail.is_camp == 1 ? '140rpx' : '0' }">
			<view class="grid">
				<view class="grid-item" v-for="item in gridList" :key="item.id" @click="jumpPage(item)" v-if="(item.isCampAction == false || userDetail.is_camp == 1) && ![5, 7].includes(item.id)">
					<image mode="widthFix" :src="item.icon"></image>
					<text>{{ item.text }}</text>
				</view>
			</view>
		</view>


		<!-- tab 栏目 -->
		<u-tabs :list="list" lineWidth="0" @change="changeTime" :activeStyle="{
			color: '#201E2E',
			fontWeight: 'bold',
			fontSize: '32rpx'
		}" :inactiveStyle="{
			color: '#A2A2A2',
			fontWeight: 'bold',
			fontSize: '32rpx'
		}" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;">
		</u-tabs>

		<view class='time-sel'>
			<view class="date-quick">
				<date-list @custom-event="changeType"></date-list>
			</view>
		</view>

		<!-- 数据图表 -->
		<view class="char-container">
			<view class="data-report">
				<view class="data-report-title">
					<text>学习总时长(小时)</text>
				</view>
				<view class="show-all-time" v-if="showAllTime != ''">
					{{ showAllTime }}
				</view>
				<view class="data-report-grid">
					<!-- <view class="uni-ec-canvas" id="echarts"></view> -->
					<view class="uni-ec-canvas"><l-echart ref="chartRef" @finished="initEcharts"></l-echart></view>
				</view>
				<view class="progress-container" v-if="courseList.length > 0">
					<template v-for="(item, index) in courseList">
						<template v-for="(citem, cindex) in courseData">
							<course-progress v-if="item.name == citem.subject_name" :title="item.name" :percentage="Number(parseFloat(citem.pre).toFixed(2))" :date="citem.second_text ? citem.second_text : '0分'" :key="index"></course-progress>

						</template>
					</template>
					<!-- <course-progress :title="'英语'" :percentage="50" :date="'0小时12分钟'"></course-progress>
					<course-progress :title="'数学'" :percentage="50" :date="'0小时12分钟'"></course-progress>
					<course-progress :title="'专业课'" :percentage="50" :date="'0小时12分钟'"></course-progress> -->
				</view>
			</view>
		</view>
		<view class="bottom-container">
			<view class="h1-title">
				<text>模考测评</text>
			</view>
			<view class="result">
				<u-tabs :list="catTabs" @change="tabChange" lineWidth="40" lineHeight="6" lineColor="#009c7b" :activeStyle="{
					color: '#009c7b',
					fontWeight: 'bold',
				}" :inactiveStyle="{
					fontWeight: 'bold',
					color: '#606266',

				}" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 34px;"></u-tabs>

				<view class="sel-month">
					<picker mode="date" fields="month" @change="bindDateChange">
						<text>({{ examListStartMonth }})月份</text>
						<image class="bottom-arrow" src="../../static/png/down-arrow-double.png" mode=""></image>
					</picker>
				</view>

				<view class="exam-list">
					<template v-if="correnctionListLoading == false && correnctionList.length > 0">
						<view class="item" v-for="(item, i) in correnctionList" :key="item.id" @click="toAnswer(item)">
							<view class="exam-left">
								<text class="num">{{ i + 1 }}</text>
								<view class="title">
									<text class="title-name">{{ item.title }}</text>
									<text class="date">{{ item.submit_time | formatTime }}</text>
								</view>
							</view>
							<view class="exam-progress">
								<text class="complete">{{ item.allScore }}</text>
								<text>/{{ item.question_score ? item.question_score : 0 }}</text>
							</view>
						</view>
						<view class="more" @click="getMore">
							<image mode="widthFix" src="../../static/png/down-arrow-double.png"></image>
						</view>
					</template>
					<u-empty v-if="correnctionListLoading == false && correnctionList.length == 0" mode="list" text="没有批改结果" textSize="24">
					</u-empty>
				</view>



			</view>
		</view>
		<u-popup :show="showGetPhone" @close="showGetPhone = false" mode="center">
			<view class="btn-opt">
				<button class="btn" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">点击授权</button>
			</view>
		</u-popup>
	</view>
</template>

<script>
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import dateList from "@/components/dateList/dateList.vue"
import courseProgress from "@/components/courseProgress/courseProgress.vue"
import {
	mapState
} from "vuex"
import {
	getCorrectionResult,
	getQuestionListByPqid
} from "@/api/professional/index.js"
import {
	formatFullDate,
	selectTemplateAnswer
} from "@/utils/tool.js"
export default {
	data() {
		return {
			defaultAvatar: '/static/png/logo.png',
			showGetPhone: false,
			//是否在请求批改结果中
			correnctionListLoading: false,
			examListStartMonth: "",
			startTime: "",
			pageInfo: {
				page: 1,
				perPageNum: 10,
				allNum: 0,
			},
			currentSubject: "全部",
			userDetail: '',
			selType: 1,
			timeshow: '',
			showAllTime: '',
			sourceType: 0,
			ec: {
				option: {
					xAxis: [{
						type: 'category',
						data: ['10/01', '10/02', '10/03', '10/04', '10/05', '10/06', '10/07'],
						axisPointer: {
							type: 'shadow'
						},
						axisLabel: {
							interval: 0, // 强制显示所有标签

						},
						axisLine: {
							show: true, // 是否显示轴线
							lineStyle: {
								color: '#BCBCBC', // 轴线颜色
								width: 2, // 轴线宽度
								type: 'solid' // 轴线类型，还可选：'dashed' | 'dotted'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						min: 0,
						max: 0,
						axisLabel: {
							formatter: '{value}'
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#BCBCBC',
								width: 2,
								type: 'solid'
							}
						}
					}],
					series: [{
						name: 'Bar',
						type: 'bar',
						barWidth: '40rpx',
						data: ['01', '02', '03', '04', '05', '06', '07'],
						itemStyle: {
							// 定义柱状图的样式
							barBorderRadius: [40, 40, 0, 0],

							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [{
									offset: 0,
									color: '#27CBA9' // 0% 处的颜色
								}, {
									offset: 1,
									color: '#2EECC6' // 100% 处的颜色
								}],
								global: false // 缺省为 false
							}
						}
					},
					{
						name: 'Line',
						type: 'line',
						color: ['#CDF3E7'],
						yAxisIndex: 0,
						itemStyle: {
							normal: {
								color: 'rgba(22,172,142,0.5)',
							}
						},
						symbol: 'circle',
						symbolSize: 12,
						data: [0, 0, 0, 0, 0, 0, 0],
					}
					]
				},
			},
			gridList: [{
				id: 1,
				text: "我的订单",
				url: "/choosecoursepkg/my_orders/my_orders",
				icon: "/static/png/order.png",
				isCampAction: false
			},
			{
				id: 2,
				text: "优惠券",
				url: "/couponpkg/coupon_list/coupon_list",
				icon: "/static/png/coupon.png",
				isCampAction: false
			},
			{
				id: 3,
				text: "教材资料",
				url: '/materialpkg/textbook_materials/textbook_materials',
				icon: "/static/png/material.png",
				isCampAction: false
			},
			{
				id: 4,
				text: "蝌蚪币商城",
				icon: "/static/png/shop.png",
				isCampAction: false
			},
			{
				id: 5,
				text: "1v1授课",
				icon: "/static/png/teach.png",
				isCampAction: false
			},
			{
				id: 6,
				text: "我的收藏",
				url: "/collection_page/my_collection/my_collection",
				icon: "/static/png/my_star.png",
				isCampAction: false
			},
			{
				id: 7,
				text: "我的预约",
				icon: "/static/png/my_appointment.png",
				isCampAction: false
			},
			{
				id: 8,
				text: "答疑专区",
				url: "/subpkg/Online_Q_A/Online_Q_A",
				icon: "https://website-1300870289.cos.ap-nanjing.myqcloud.com/487ec23c69158db90112dc6c3135d170634.png",
				isCampAction: false
			},
			{
				id: 9,
				text: "VIP教室预约",
				url: "/subpkg/vip_appointment/vip_appointment",
				icon: "https://website-1300870289.cos.ap-nanjing.myqcloud.com/376cfe4df04073ce0f3ec31fed8f3d2af29.png",
				isCampAction: true
			},
			{
				id: 10,
				text: "集训营请假",
				url: "/subpkg/for_leave/for_leave",
				icon: "https://website-1300870289.cos.ap-nanjing.myqcloud.com/96877851b6f2f99b055c5a5ef497798852e.png",
				isCampAction: true
			},
			{
				id: 11,
				text: "我的课表",
				url: "/subpkg/schedule/schedule",
				icon: "https://website-1300870289.cos.ap-nanjing.myqcloud.com/773d839aa629d61011f74eb48e992217500.png",
				isCampAction: true
			},

			{
				id: 12,
				text: "考试分析",
				url: "/subpkg/exam_analysis/exam_analysis",
				icon: "https://website-1300870289.cos.ap-nanjing.myqcloud.com/8455e335395d3bc47d5b30024a9513fbef2.png",
				isCampAction: true,
			}
			],
			list: [{
				name: '学习总数据'
			},
			{
				name: '听课数据'
			},
			{
				name: '做题数据'
			},
			],
			catTabs: [{
				name: '全部'
			},
			{
				name: '政治'
			},
			{
				name: '英语'
			},
			{
				name: '数学'
			},
			{
				name: '专业课'
			},
			],
			correnctionList: [],
			courseList: [],
			courseData: [],
			echartsInstance: null
		};
	},
	components: {
		dateList,
		courseProgress
	},
	watch: {
		userInfo: {
			handler: function (newV, oldV) {
				if (newV) {
					this.getStudyData();
					this.getCourseType();
					this.getCourseStudyData();
					this.getUserInfo();
				}
			},
			deep: true
		}
	},
	computed: {
		...mapState('user', ['userInfo']),
	},
	onShow() {

		if (typeof this.userInfo.phone == "undefined" || this.userInfo.phone == "") {

		} else {
			this.getStudyData();
			this.getCourseType();
			this.getCourseStudyData();
			this.getUserInfo();
		}

	},
	created() {
		//初始化查询时间 默认本年本月
		const date = new Date();
		let month = date.getMonth() + 1;
		this.examListStartMonth = (month < 10 ? '0' + month : month)
		this.startTime = date.getFullYear() + '-' + this.examListStartMonth;
		this.getCorrenctionList()
	},
	filters: {
		formatTime(val) {
			if (typeof val === "number") {
				const start = val - 2 * 60 * 60;
				let date = new Date(val * 1000)
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				date = new Date(start * 1000)
				const starthours = String(date.getHours()).padStart(2, '0');
				const startminutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day}  ${hours}:${minutes}-${hours}:${minutes}`;
			}
		},
		formatUsername(val) {
			if (undefined != val && val.length > 7) {
				return val.substring(0, 6) + '...'
			} else {
				return val
			}
		}
	},
	methods: {
		async initEcharts() {
			// chart 图表实例不能存在data里
			this.echartsInstance = await this.$refs.chartRef.init(echarts);
			this.echartsInstance.setOption(this.ec.option)
		},
		toSetting() {
			uni.navigateTo({
				url: '/subpkg/setting/setting'
			})
		},
		toScanLogin() {
			uni.scanCode({
				// scanType: ['QR_CODE'], //条形码
				success: function (res) {
					console.log('res', res)
					// console.log('条码类型：' + res.scanType);
					// console.log('条码内容：' + res.result);
					// 微信小程序
					if (res.errMsg == "scanCode:ok") {
						// 扫描到的信息
						console.log(res.path);
						uni.navigateTo({
							url: '/' + res.path
						})
					} else {
						console.log("未识别到二维码，请重新尝试！")
					}
				}
			});
		},
		async decryptPhoneNumber(e) {
			//await getUserProfile()
			console.log(e);

			if (e.detail.errMsg.indexOf('ok') != -1) {
				const {
					code,
					data
				} = await uni.$u.http.post("/mini_user/getMobile", {
					code: e.detail.code
				})
				if (code == 1) {
					this.$store.commit('user/setUser', {
						phone: data.phone
					})
				}
			}
		},
		async toAnswer(item) {
			this.$store.commit("professionalConfig/setCurrentStartPage")
			//题目列表
			let answeringQuestion = []
			//获取题目列表
			const params = {
				pqid: item.id,
				perPageNum: 10,
				mockExam: 1
			}
			let questionList = await getQuestionListByPqid(params)
			//如果题目列表为空返回
			if (questionList === false) {
				return
			}
			questionList.forEach(obj => {
				// 构造当前 题目的列表数据
				answeringQuestion.push({
					...obj,
					cateName: item.pname,
					type: item.type,
					pqid: item.id,
					total_time: item.total_time,
					id: obj.qid,
					subtitle: obj.qtitle,
					qtype: obj.qtype,
					professionTitle: item.title
				})
			})
			this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
			//判断配置信息是否跳过已做题目
			//默认第一道题开始
			this.$store.commit("professionalConfig/setCurrentIndex", 0);
			selectTemplateAnswer(answeringQuestion[0], false)
			//selectTemplate(answeringQuestion[startIndex], item)
		},
		jumpPage(item) {
			// if ([5, 7, 8].includes(item.id)) {
			// 	//判断是否开启了聊天功能
			// 	if (this.$store.state.professionalConfig.config?.is_open_chat != '1') {
			// 		return uni.tip('功能暂未开放')
			// 	}
			// }
			if (item.id == 4) {
				this.toOtherMiniProgram()
			} else {
				//营地学员的菜单需要授权获取 手机号码
				if (item.isCampAction) {
					//正则验证手机号码
					const reg = /^[1-9]\d{10}$/
					//手机号验证失败
					if (!reg.test(this.userInfo.phone)) {
						this.showGetPhone = true
						return;
					}

				}

				uni.navigateTo({
					url: item.url
				})

			}
		},

		toOtherMiniProgram() {
			// 根据平台判断跳转方式
			// #ifdef MP-WEIXIN
			uni.navigateToMiniProgram({
				appId: 'wx0a6370cd56a42990',
				success(res) {
					console.log('跳转成功');
				},
				fail(err) {
					console.error('跳转失败', err);
				}
			});
			// #endif

			// #ifdef APP-PLUS
			plus.share.getServices(function (res) {
				let sweixin = null;
				for (let i = 0; i < res.length; i++) {
					if (res[i].id == 'weixin') {
						sweixin = res[i];
					}
				}
				if (sweixin) {
					sweixin.launchMiniProgram({
						id: 'gh_acb7cf64b43c', // 微信小程序原始ID
						type: 0, // 正式版:0，测试版:1，体验版:2
						path: '', // 打开页面的路径，如果为空则打开首页
						success: function () {
							console.log('App端打开微信小程序成功');
						},
						fail: function (err) {
							console.error('App端打开微信小程序失败', err);
							uni.showToast({
								title: '打开微信小程序失败，请确保已安装微信',
								icon: 'none'
							});
						}
					});
				} else {
					uni.showToast({
						title: '请先安装微信',
						icon: 'none'
					});
				}
			}, function (err) {
				console.error('获取分享服务失败', err);
				uni.showToast({
					title: '获取分享服务失败',
					icon: 'none'
				});
			});
			// #endif
		},
		getUserInfo() {
			let that = this;
			uni.$u.http.get('/mini_user/getUserInfo').then(rest => {
				this.userDetail = rest.data;
				this.avatarUrl = this.userDetail.avatar ? this.userDetail.avatar : this.defaultAvatar;
			});
		},
		bindDateChange(date) {
			if (typeof date.detail.value === "string") {
				this.examListStartMonth = date.detail.value.split("-")[1]
				this.startTime = date.detail.value
				this.pageInfo.page = 1
				this.correnctionList = []
				this.getCorrenctionList()
			}

		},
		tabChange(item) {
			this.currentSubject = item.name
			this.correnctionList = []
			this.pageInfo.page = 1
			this.getCorrenctionList()
		},
		getMore() {
			if (this.correnctionList.length >= this.pageInfo.allNum) {
				return uni.tip("没有更多")
			}
			this.pageInfo.page++
			this.getCorrenctionList()
		},

		//获取模考结果
		async getCorrenctionList() {
			let params = {
				perPageNum: this.pageInfo.perPageNum,
				page: this.pageInfo.page,
				mounth: this.examListStartMonth,
				subject: this.currentSubject,
				startTime: this.startTime
			}
			this.correnctionListLoading = true;
			const ret = await getCorrectionResult(params);
			this.correnctionListLoading = false
			if (ret !== false) {
				this.pageInfo.allNum = ret.allNum
				this.correnctionList = [...this.correnctionList, ...ret.list]
			}

		},
		changeTime(e) {
			console.log('changeTime', e)
			this.sourceType = e.index;
			this.getStudyData();
			this.getCourseStudyData()
		},
		getCourseType() {
			let that = this;
			that.courseList = [];
			const data = uni.$u.http.get('/task/getUserCourse', {
				params: {
					from: 'center'
				}
			}).then(rest => {
				let res = rest.data.course_text;

				res.map(item => {
					return that.courseList.push({
						name: item.name
					})
				})
			});
		},

		changeType(type) {
			this.selType = type;
			console.log('changeType', type)
			this.getStudyData()
			this.getCourseStudyData()
		},
		getStudyData() {
			let that = this;
			uni.$u.http.get('/mini_user/getStudyData', {
				params: {
					type: that.selType,
					sourse_type: that.sourceType
				}
			}).then(res => {

				that.ec.option.xAxis[0].data = res.data.data_x
				that.ec.option.series[0].data = res.data.data_y[1]
				that.ec.option.series[1].data = res.data.data_y[1]
				that.ec.option.yAxis[0].max = (res.data.data_y[2] * 1.2).toFixed(2);
				this.echartsInstance.setOption(this.ec.option)
				this.showAllTime = res.data.data_y[3] > 0 ? res.data.data_y[4] : ''

			})
		},
		toTadpole() {
			uni.navigateTo({
				url: '/subpkg/tadpole_record/tadpole_record'
			})
		},
		toPoint() {
			uni.navigateTo({
				url: '/subpkg/points_record/points_record'
			})

		},
		getCourseStudyData() {
			let that = this;
			uni.$u.http.get('/mini_user/getCourseStudyData', {
				params: {
					type: that.selType,
					sourse_type: that.sourceType
				}
			}).then(res => {

				if (res.data.length > 0) {
					console.log('res.data', res.data)
					that.courseData = res.data;
				} else {
					that.courseData = [];
				}

			})
		},

	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: $container-bg-color;
	position: relative;
	overflow: hidden;

	.uni-ec-canvas {
		width: 100%;
		height: 100%;
		display: block;
	}

	.data {
		position: relative;
	}

	.head {
		position: relative;

		.setting {
			position: absolute;
			top: 110rpx;
			right: 30rpx;
			width: 40rpx;
			height: 40rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		background-image: url("https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/%E7%BB%84%20224%402x.png");
		background-size: cover;
		height: 490rpx;
		padding-top: 136rpx;
		position: relative;

		.title {
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.btn-auth {
					font-size: 18rpx;
					border: none;
					background-color: #f8f8f8;
					opacity: 0.3;
					outline: none;
				}

				.avatar {
					width: 100rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 94rpx;
						height: 90rpx;
						border-radius: 40rpx;


					}


				}

				.info {
					margin-left: 4rpx;

					.top-text {
						color: #201E2E;
						font-size: 32rpx;
						font-weight: bold;
						display: flex;
						align-items: center;
						justify-content: flex-start;

						image {
							width: 40rpx;
							height: 40rpx;
						}

						.coin {
							color: #323232;
							font-size: 22rpx;
						}
					}

					.bottom-text {
						margin-top: 12rpx;
						color: #6D6D6D;
						font-size: 22rpx;
					}
				}
			}

			.right {
				width: 205rpx;
				height: 107rpx;
				background: #E0F9F1;
				opacity: 0.42;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				.cls_text {
					font-size: 28rpx;
				}

				text {
					color: #4D504F;

					&:last-child {
						font-size: 30rpx;
						padding: 4rpx 0;
					}
				}
			}

			.scan {
				width: 40rpx;
				display: flex;
			}
		}

	}

	.grid-container {
		margin-top: 124rpx;
		box-sizing: border-box;
		padding: 0 30rpx;

		.grid {
			box-sizing: border-box;
			position: absolute;
			top: 272rpx;
			padding-bottom: 20rpx;
			border-radius: 16rpx;
			// padding: 36rpx 52rpx;
			background-color: #FFFFFF;
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			align-items: center;
			font-size: 24rpx;
			margin-bottom: 38rpx;
			color: #323232;
			width: 690rpx;

			.grid-item {
				box-sizing: border-box;
				width: 25%;
				margin-top: 16rpx;

				image {
					width: 100rpx;
				}

				border-radius: 16rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
			}
		}
	}

	.time-sel {
		position: relative;
		z-index: 999;

		.date-quick {
			position: absolute;
			top: 52rpx;
			right: 45rpx;
		}

	}

	.char-container {
		padding: 0 30rpx;
		box-sizing: border-box;
		position: relative;

		.data-report {
			box-sizing: border-box;
			margin-top: 26rpx;
			margin-bottom: 20rpx;
			border-radius: 20rpx;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: center;

			box-shadow: 2rpx 4rpx 2rpx 2rpx rgb(224, 224, 224);

			.data-report-title {
				width: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				position: absolute;
				top: 32rpx;
				left: 28rpx;


				text {
					color: #201E2E;
					font-size: 28rpx;
					margin-left: 12rpx;
					font-weight: bold;
				}
			}


			.show-all-time {
				position: absolute;
				top: 10%;
				left: 50%;
				transform: translateX(-50%);
				width: 137rpx;
				height: 34rpx;
				background: #CDF3E7;
				border-radius: 16rpx;
				color: #01997A;
				font-size: 20rpx;
				text-align: center;
				line-height: 34rpx;
			}

			.data-report-grid {

				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;

				.uni-ec-canvas {
					width: 100%;
					height: 800rpx;
				}
			}

			.progress-container {
				width: 100%;
				position: relative;
				bottom: 58rpx;
			}

		}
	}


	.bottom-container {
		box-sizing: border-box;
		padding: 0 30rpx;
		padding-bottom: 50rpx;

		.h1-title {
			color: #201E2E;
			font-size: 32rpx;
			font-weight: bold;
		}

		.result {
			box-shadow: 2rpx 4rpx 2rpx 2rpx rgb(224, 224, 224);
			border-radius: 20rpx;
			margin-top: 18rpx;
			background-color: #fff;
			position: relative;
			padding: 0 20rpx;

			.sel-month {
				position: absolute;
				top: 16rpx;
				right: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				text {
					font-size: 22rpx;
					color: #A2A2A2;
				}

				.bottom-arrow {
					margin-left: 8rpx;
					width: 15rpx;
					height: 15rpx;
				}
			}

			.exam-list {
				padding-top: 20rpx 15rpx;

				.item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border: 1px solid #01997A;
					border-radius: 20rpx;
					padding: 18rpx 20rpx;
					margin-bottom: 20rpx;

					.exam-left {
						display: flex;
						align-items: center;
						justify-content: flex-start;
					}

					.num {
						width: 60rpx;
						height: 60rpx;
						background-color: $main-color;
						color: #fff;
						border-radius: 50%;
						line-height: 60rpx;
						font-size: 30rpx;
						font-weight: bold;
						text-align: center;

					}

					.title {
						display: flex;
						align-items: center;
						justify-content: flex-start;
						flex-direction: column;
						margin-left: 20rpx;

						.title-name {
							width: 100%;

							font-weight: bold;
							font-size: 30rpx;
							color: #4A4A4C;
						}

						.date {
							color: #9F9F9F;
							font-size: 22rpx;
						}
					}

					.exam-progress {
						.complete {
							color: #01997A;
						}
					}
				}

				.more {
					text-align: center;
					margin-top: 28rpx;

					image {
						display: inline-block;
						width: 20rpx;
					}
				}
			}
		}
	}
}

.btn-opt {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 600rpx;
	height: 260rpx;

	z-index: 100;

	.btn {
		z-index: 100;
		width: 80%;
		background-color: #01997A;
		border-radius: 34rpx;
		color: #fff;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 24rpx;
	}
}
</style>