<template>
	<view class="bottom-btn">
		<view class="btn prev" @click="customPrev">
			上一题
		</view>
		<view class="btn next" @click="customNext">
			下一题
		</view>
	</view>
</template>

<script>
export default {
	name: "bottomBtn",
	data() {
		return {

		};
	},
	methods: {
		customPrev() {
			this.$emit('prev')
		},
		customNext() {
			this.$emit('next')
		}
	}
}
</script>

<style lang="scss">
.bottom-btn {

	padding: 20rpx 36rpx;
	position: fixed;
	bottom: 0;
	width: 100%;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100rpx;

	.btn {
		width: 170rpx;
		height: 70rpx;
		border-radius: 16rpx;
		border: 1rpx solid #01997A;
		line-height: 70rpx;
		text-align: center;
		font-size: 28rpx;
	}

	.prev {
		background: #FFFFFF;
		color: $main-color;
	}

	.next {
		background-color: $main-color;
		color: #fff;
	}
}
</style>