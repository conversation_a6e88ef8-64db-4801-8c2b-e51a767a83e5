<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true' @leftClick="back">
			<view class="u-nav-slot" slot="left">
				<u-icon name="arrow-left" size="38">
				</u-icon>
			</view>
			<view class="center" slot="center">
				<text>耗时{{ showTime }}</text>
			</view>
		</u-navbar>
	</view>
</template>

<script>
import { formatTime } from "@/utils/tool.js"
import { mapState } from "vuex"
export default {
	data() {
		return {
			timerId: null,
			currentTime: 0,
		}
	},
	methods: {
		//开始定时器
		startTimer() {
			if (this.timerId != null) {
				clearInterval(this.timerId)
				this.timerId = null
			} else {
				this.timerId = setInterval(() => {

					const currentQuestionInfo = this.currentTypes[this.currentIndex]

					//模拟考试超时直接提交生成答题卡
					if (currentQuestionInfo.type == 2) {
						//用时
						const totalTime = this.currentAnswerList.reduce((total, item) => {
							return total + item.answer.timeval
						}, 0)
						if (totalTime >= currentQuestionInfo.total_time * 60) {
							uni.showModal({
								showCancel: false,
								content: '本次考试时间已用完，进入答题卡提交！',
								success: function (res) {
									if (res.confirm) {
										uni.redirectTo({
											url: "/subpkg/answer_sheet/answer_sheet?forbidToQuestion=1"
										})
									}
								}
							})
						}
					}
					this.currentTime++
				}, 1000)
			}
		},
		back() {
			//uni.navigateBack()
			// uni.redirectTo({
			// 	url:"/subpkg/allquestion/allquestion"
			// })
			uni.showModal({
				title: '提示',
				content: '退出刷题，将会丢失当前答题进度',
				showCancel: true,
				success: (res) => {
					if (res.confirm) {
						this.$store.commit('professionalConfig/enterCurrentStartPage')
					}
				}
			});
		},
	},
	props: {
		initTimeVal: {
			type: Number,
			default: 0
		}
	},
	created() {
		this.startTimer()
	},
	computed: {
		showTime() {
			return formatTime(this.currentTime)
		},
		...mapState("professionalConfig", ['currentTypes', "currentIndex", "currentAnswerList"]),
	},
	watch: {

		initTimeVal: {
			handler(newval) {
				console.log('newval', newval)
				this.currentTime = newval
			},
			immediate: true,
			deep: true
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	height: 88rpx;
	overflow: hidden;
}
</style>