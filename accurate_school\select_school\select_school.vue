<template>
	<view class="container">
		<!-- 头部 -->
		<view class="head"
			:style="{backgroundImage:'url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/select_bg.png)','paddingTop': `${statusBarHeight}px`}">
			<view class="title">
				<u-icon size="40" name="arrow-left" color="#fff" @click="back"></u-icon>
				<text>《院校专业选择分析报告》</text>
			</view>
			<view class="get">免费领取</view>
			<view class="btn">考的好更要报的好</view>
			<image src="/static/img/down.png" mode=""></image>
		</view>
		<!-- 个人专属计划 -->
		<view class="content">
			<view class="play">个人专属计划</view>
			<view class="top">请认真填写以下信息，</view>
			<view class="top">获取更精准的<text> 《院校专业选择分析报告》</text></view>
			<!-- 计划表单 -->
			<view class="formInput">
				<u-form :model="form" ref="uForm">
					<view class="title"><text>*</text> 1. 请您选择您的年级</view>
					<u-form-item prop="uname">
						<u-radio-group v-model="form.uname" size="30rpx" active-color="#01997A">
							<u-radio name='1'>大一</u-radio>
							<u-radio name='2'>大二</u-radio>
							<u-radio name='3'>大三</u-radio>
							<u-radio name='4'>大四</u-radio>
							<u-radio name='5'>其他</u-radio>
						</u-radio-group>
					</u-form-item>
					<view class="title"><text>*</text>2. 请选择您的学习经历</view>
					<u-form-item prop="education">
						<u-radio-group v-model="form.education" size="30rpx" active-color="#01997A">

							<u-radio name='1'>同等学历</u-radio>
							<u-radio name='2'>自考本科</u-radio>
							<u-radio name='3'>专升本</u-radio>
							<u-radio name='4'>大学本科</u-radio>

						</u-radio-group>
					</u-form-item>
					<view class="title"><text>*</text> 3. 请输入您学习的本科院校和专业</view>
					<view class="uForm">
						<u-form-item class="topForm" prop="academy">
							<u-input border="false" placeholder="输入学校" v-model="form.academy" type="select" />
						</u-form-item>
						<u-form-item class="topForm" prop="specialty">
							<u-input border="false" placeholder="输入专业" v-model="form.specialty" type="select" />
						</u-form-item>
					</view>
					<view class="title"> 4. 请输入您报考的意向学校和专业</view>
					<view class="uForm">
						<u-form-item prop="academys">
							<u-input border="false" placeholder="输入学校" v-model="form.academys" type="select" />
						</u-form-item>
						<u-form-item prop="specialtys">
							<u-input border="false" placeholder="输入专业" v-model="form.specialtys" type="select" />
						</u-form-item>
					</view>
					<view class="title"> 5. 请选择您的英语水平</view>
					<u-radio-group v-model="form.englist" size="30rpx" active-color="#01997A" prop="englist">
						<u-radio name='1'>英语四级</u-radio>
						<u-radio name='2'>英语六级</u-radio>
						<u-radio name='3'>专业四级</u-radio>
						<u-radio name='4'>专业八级</u-radio>


					</u-radio-group>

					<view class="title last"><text>*</text> 6. 请输入您的微信号，以便于及时将报告发给您？</view>
					<u-form-item class="last" prop="weixin">
						<u-input border="false" placeholder="输入微信号" v-model="form.weixin" type="select" />
					</u-form-item>
					<button :loading="loading" class="login" @click="submit">提交</button>
				</u-form>
			</view>
		</view>

	</view>

</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 20, // 默认值
				loading: false,
				form: {
					uname: -1,
					education: "",
					academy: "",
					weixin: "",
					englist: "",
					specialty: "",
					specialtys: "",
					academys: "",
				},
				rules: {
					uname: [{
						required: true,
						message: '请选择您的年级',
						// 可以单个或者同时写两个触发验证方式 
						trigger: 'change',

					}],
					education: [{
						required: true,
						message: '请选择您的学习经历',
						// 可以单个或者同时写两个触发验证方式 
						trigger: 'change',
					}],
					academy: [{
						required: true,
						message: '请输入您的本科院校',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],
					specialty: [{
						required: true,
						message: '请输入您的本科专业',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],
					weixin: [{
						required: true,
						message: '请输入您的微信号',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],

				},
				// 年级
				studentList: [{
						id: 1,
						text: '大一',

					},
					{
						id: 2,
						text: '大二',

					},
					{
						id: 3,
						text: '大三',

					},
					{
						id: 4,
						text: '大四',

					},
					{
						id: 5,
						text: '其他',

					}
				],
				// 学历
				educationList: [{
						id: 1,
						text: '同等学历'
					},
					{
						id: 2,
						text: '自考本科'
					},
					{
						id: 3,
						text: '专升本'
					},
					{
						id: 4,
						text: '大学本科'
					}
				],
				// 英语等级
				EnglishList: [{
						id: 1,
						text: '英语四级'
					},
					{
						id: 2,
						text: '英语六级'
					},
					{
						id: 3,
						text: '专业四级'
					},
					{
						id: 4,
						text: '专业八级'
					}
				]
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		mounted() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			// 设置状态栏高度
			this.statusBarHeight = systemInfo.statusBarHeight || 20;
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			submit() {
				if(this.loading) {
					return
				}
				if(this.form.uname == -1) {
					return uni.tip('请输入年级')
				}
				if(this.form.education == "") {
					return uni.tip('请选择您的学习经历')
				}
				if(this.form.academy == "") {
					return uni.tip('请输入本科院校')
				}
				if(this.form.specialty == "") {
					return uni.tip('请输入本科专业')
				}
				if(this.form.academys == "") {
					return uni.tip('请输入意向学校')
				}
				if(this.form.specialtys == "") {
					return uni.tip('请输入意向专业')
				}
				if(this.form.englist == "") {
					return uni.tip('请选择英语水平业')
				}
				if(this.form.weixin == "") {
					return uni.tip('请输入微信号')
				}

				this.loading = true
				uni.$u.http.post('/mini_user/submitUserQuestion', this.form).then(res => {
					this.loading = false
					if(res.code == 1) {
						uni.tip('提交成功')
						this.form = {
							uname: -1,
							education: "",
							academy: "",
							weixin: "",
							englist: "",
							specialty: "",
							specialtys: "",
							academys: "",
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.head {

			width: 100%;
			height: 490rpx;
			position: relative;
			// padding-top: 86rpx;
			background-size: contain;
			background-repeat: no-repeat;

			.title {
				width: 100%;
				height: 50rpx;
				display: flex;
				align-items: center;
				padding: 0 30rpx;

				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 38rpx;
					color: #FAE7B9;
					letter-spacing: 2rpx;
					margin-left: 20rpx;
				}
			}

			.get {
				margin-top: 32rpx;
				margin-left: 52rpx;
				font-size: 34rpx;
				color: #fff;
				font-weight: 600;
				letter-spacing: 2rpx;

			}

			.btn {
				width: 300rpx;
				height: 70rpx;
				color: #01997A;
				line-height: 70rpx;
				text-align: center;
				background-color: #fff;
				margin-top: 80rpx;
				margin-left: 50rpx;
				border-radius: 10rpx;
			}

			image {
				width: 351rpx;
				height: 380rpx;
				position: absolute;
				z-index: 999;
				bottom: 40rpx;
				right: 20rpx
			}
		}

		.content {
			width: 100%;
			height: 500rpx;
			background-color: #fff;
			position: relative;
			margin-top: -100rpx;
			padding: 42rpx 30rpx 20rpx 30rpx;
			box-sizing: border-box;
			border-radius: 40rpx 40rpx 0rpx 0rpx;
		
			.play {
				font-size: 30rpx;
				color: #4C5370;
				font-weight: bold;
				text-align: center;
				margin-bottom: 42rpx;
			}

			.top {

				font-weight: bold;
				font-size: 24rpx;
				color: #5A5A5A;

				text {
					color: #EE7878;
				}
			}

			.title {
				font-weight: bold;
				font-size: 26rpx;
				color: #201E2E;
				margin-top: 60rpx;
				margin-bottom: 20rpx;

				text {
					color: #01997A;
				}
			}

			.last {
				margin-top: 30rpx;
			}

		}

		::v-deep .u-radio-group {
			display: block !important;
			min-height: 66rpx;
			line-height: 66rpx;
			margin-top: 10rpx;
		}

		::v-deep .u-radio {
			background: #EEFAF6;
			padding-left: 28rpx;
			margin: 10rpx 0;
			border-radius: 18rpx;
			display: block;
			width: 100%;
		}

		/* 添加样式控制u-radio内部的文本元素 */
		::v-deep .u-radio__label {
			display: inline-block !important;  // 使文本为内联块元素
			width: auto !important;  // 防止文本占据整行
			text-align: left;  // 文本左对齐
		}

		/* 如果需要，控制u-radio内部的图标和文本的布局 */
		::v-deep .u-radio__icon-wrap {
			display: inline-flex !important;
			vertical-align: middle;
		}

		::v-deep .uForm .u-input--radius,
		.u-input--square {
			border-radius: 16rpx !important;
		}

		::v-deep .u-input {
			background-color: #EEFAF6;
		}

		::v-deep .uForm {
			display: flex;
			justify-content: space-between;

			.u-form-item {
				width: 45% !important;
				height: 88rpx;

			}
		}

		.formInput {
			font-weight: bold;
			font-size: 26rpx;
			color: #4C5370;
			padding-bottom: 120rpx;
			.login {
				box-sizing: border-box;
				width: 520rpx;
				height: 80rpx;
				border-radius: 40rpx;
				background-color: $main-color;
				font-size: 24rpx;
				color: #fff;
				text-align: center;
				line-height: 80rpx;
			}
		}

		::v-deep .u-input__content__field-wrapper__field {
			font-weight: bold !important;
			font-size: 26rpx !important;
			color: #4C5370 !important;
		}

		::v-deep .u-button {
			height: 81rpx;
			width: 400rpx;
			font-size: 30rpx;
			color: #FFFFFF;
			border-radius: 50rpx;
			border: none;
			background-color: #01997A;
			margin-top: 74rpx;
			margin-bottom: 20rpx;
		}
	}
</style>