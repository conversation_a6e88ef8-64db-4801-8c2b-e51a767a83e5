<template>
	<view class="wrong-list-container u-border-top">
		<!-- 信息面板 -->
		<view class="panel">
			<view class="info u-border-right">
				<text class="num">
					{{dayTotal}}
					<text>天</text>
				</text>
				<text class="subject">累计天数</text>
			</view>
			<view class="info">
				<text class="num">
					{{errorTotal}}
					<text>个</text>
				</text>
				<text class="subject">累计单词</text>
			</view>
		</view>
		<view class="list" v-for="(item, index) in list" :key="index">
			<u-collapse @change="change" @close="close" @open="open" :border='false'>

				<u-collapse-item :title="item.word + ' '+item.prop" >
					<view class="word-meaning">
						<view class="line">
							<text>正确答案:</text>
							<text>{{item.rightAnswer}}</text>
						</view>
						<view class="line">
							<text>来自:</text>
							<text>{{item.from}}</text>
						</view>
						<view class="line eg">
							<text>eg:</text>
							<text>{{item.eg}}</text>
						</view>
					</view>
				</u-collapse-item>
			</u-collapse>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				page:1,
				errorTotal:0,
				dayTotal:0,
				list: [				]
			};
		},
		onLoad(){
			this.getList()
		},
		onReachBottom() {
		
			if (this.list.length >= this.errorTotal) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			} else  {
				this.page += 1
				this.getList()
				
			}
		},
		methods: {
			getList() {
				let that = this;
				uni.$u.http.get('/task/getWordErrorList',{params:{page:this.page}}).then(res=>{
					let  tmpList = res.data.rows;
					this.errorTotal = res.data.total;
					this.dayTotal = res.data.day_total;
					tmpList.map(item=>{
						let tmpData  =  {
							word : item.words.word,
							prop : item.words.pos_text,
							rightAnswer:item.words.simple_meaning,
							from:(item.words.day_unit.book_id == 1?'必考词':'基础性')+item.words.day_unit.name,
							eg:item.words.example,
							userMark:'wrong'
						};
						
						that.list.push(tmpData);
					})
				
				
				})
			},
			open(e) {
				// console.log('open', e)
			},
			close(e) {
				// console.log('close', e)
			},
			change(e) {
				// console.log('change', e)
			}
		}
	}
</script>

<style lang="scss">
	.wrong-list-container {
		padding: 0 20rpx;
	}

	.panel {
		margin-top: 30rpx;
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #F6F7FB;
		border-radius: 22rpx;

		.info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			padding: 20rpx 120rpx;

			.num {
				color: #201E2E;
				font-size: 32rpx;
				font-weight: bold;

				text {
					font-size: 18rpx;
					color: #777;
					font-weight: normal;
				}
			}

			.subject {
				font-size: 18rpx;
				color: #777;
			}
		}
	}
	

	.list {
		margin-top: 30rpx;

		::v-deep .u-collapse-item {
			border: 1rpx solid #009c7b;

			// .u-collapse-item__content {
			// 	border-top: 1rpx solid #009c7b;
			// }
		}
		.word-meaning{
			border-top: 1rpx solid #009c7b;
			margin: 0 -15px;
			padding: 0 15px;
		}
		.line {
			font-size: 28rpx;
			color: #5A5A5A;
			margin-top: 10rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			text {
				&:last-child {
					margin-left: 10rpx;
				}
			}
		}

		.eg {
			margin-bottom: 10rpx;
			align-items: flex-start;
		}
	}
</style>