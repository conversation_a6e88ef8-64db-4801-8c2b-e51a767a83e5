<script>
import { createNamespacedHelpers } from "vuex"
import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'
import { getAppConfig } from '@/api/common/common.js'
let { mapMutations, mapState, mapActions } = createNamespacedHelpers('user')
export default {
	data() {
		return {
			userStore: ','
		};
	},

	onLaunch: function () {
		this.init()
	},
	computed: {
		...mapState([
			'userInfo'
		])
	},
	onShow: function () {
		this.isUpdate()


	},
	onHide: function () {
		console.log('App Hide')
	},
	methods: {
		async init(){
			await this.$store.dispatch('sysConfig/getRequestConfig')
			await this.isLogin();
			this.$store.dispatch('professionalConfig/getStuConfig')
			//#ifdef APP-PLUS
			this.upgradeApp()
			//#endif
		},
		//判断用户是否登陆
		async isLogin() {
			try {
				// #ifdef MP-WEIXIN
				await uni.checkSession()
				// #endif

				// #ifdef APP-PLUS
				const token = uni.getStorageSync('token')
				if (!token) {
					uni.navigateTo({
						url: '/subpkg/login/login'
					})
					return
				}
				this.setUser({ token })
				// #endif

				if (typeof this.userInfo.token !== "string" || this.userInfo.token.length <= 0) {
					throw new Error('未登录')
				}
			} catch (e) {
				console.log('unlogin', e)
				try {
					// #ifdef MP-WEIXIN
					let res = await this.login()
					console.log('login result:', res)
					// #endif

					// #ifdef APP-PLUS
					uni.navigateTo({
						url: '/subpkg/login/login'
					})
					// #endif
				} catch (loginError) {
					console.error('登录失败:', loginError)

					// #ifdef MP-WEIXIN
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
					// #endif

					// #ifdef APP-PLUS
					uni.navigateTo({
						url: '/subpkg/login/login'
					})
					// #endif
				}
			}
		},
		...mapMutations(['setUser']),
		...mapActions(['login']),
		isUpdate() {
			if (uni.canIUse('getUpdateManager')) {
				const updateManager = uni.getUpdateManager()
				updateManager.onCheckForUpdate(function (res) {
					// 请求完新版本信息的回调
					if (res.hasUpdate) {
						updateManager.onUpdateReady(function () {
							uni.showModal({
								title: '更新提示',
								content: '新版本已经准备好，是否重启小程序？',
								success: function (res) {
									// res: {errMsg: "showModal: ok", cancel: fal    se, confirm: true}
									if (res.confirm) {
										// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
										updateManager.applyUpdate()
									}
								}
							})
						})
					}
				})
			}
		},
		upgradeApp() {

			app_upgrade(async (versionCode) => {
				const appConfig = await getAppConfig(versionCode)
				console.log(appConfig)
				if (appConfig !== false) {
					return {
						changelog: appConfig.changelog,
						status: 1, // 0 无新版本 | 1 有新版本
						path: appConfig.app_download_path // 新apk地址
					}
				} else {
					return {
						status: 0,
						path: '',
						changelog: ''
					}
				}
			})
		}
	}
}
</script>

<style lang="scss">
@import "uview-ui/index.scss";

/*每个页面公共css */
::-webkit-scrollbar {
	display: none;
}

.add-box-shadow {
	padding: 10rpx 20rpx;
	margin: 20rpx 20rpx;
	background-color: #fff;
	border-radius: 14rpx;
	box-shadow: 2rpx 4rpx 2rpx 2rpx rgb(224, 224, 224);
}

view {
	box-sizing: border-box;
}
</style>
