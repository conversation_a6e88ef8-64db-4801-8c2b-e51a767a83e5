import {
	mapState,
	mapMutations
} from "vuex"
import {
	getQuestionDetail,
	addAnswerRecords,
	addAnswerAndReportData,
	taskOtherRecord
} from "@/api/professional/index.js"
import {
	selectTemplate,
	selectTemplateAnswer
} from "@/utils/tool.js"
import {
	subjectTypeArr
} from "@/utils/setting.js"
export default {
	data() {
		return {
			timeval: 0,
			question: {},
			answer: "",
			fileList: [],
			selfScore: 0,
			timerId: null,
			//完形填空 阅读理解 新题型 单独处理
			currentSelArr: [],
			currentChoose: 0,
		};
	},
	methods: {
		...mapMutations("professionalConfig", ['setCurrentIndex', 'changeCurrentAnswerStatus']),
		//模考类型统一按答题模式处理
		toPrevAnswerAtMockType() {
			if (this.currentIndex == 0) {
				return uni.tip("已经是第一题了")
			}
			//题型相同
			if (this.currentType.qtype == this.currentTypes[this.currentIndex - 1].qtype) {
				//更新答题索引
				this.setCurrentIndex(this.currentIndex - 1)
			} else {
				//this.setCurrentIndex(this.currentIndex-1)
				uni.doCallBack = () => {
					this.setCurrentIndex(this.currentIndex - 1)
				}
				selectTemplateAnswer(this.currentTypes[this.currentIndex - 1])
			}
		},
		answerPrev() {
			//真题， 模考类型和任务题库单独处理 不区分答题和背题模式
			if (this.currentType.type == 1 || this.currentType.type == 2 || this.currentType?.aboutId > 0) {
				return this.toPrevAnswerAtMockType()
			}
			//答题模式 或者是 我的答题记录进入
			if (this.config.type == 0 || this.isFromRecordsList) {
				if (this.currentIndex == 0) {
					return uni.tip("已经是第一题了")
				}
				if (this.currentType.qtype == this.currentTypes[this.currentIndex - 1].qtype) {
					//更新答题索引
					this.setCurrentIndex(this.currentIndex - 1)
				} else {
					//this.setCurrentIndex(this.currentIndex-1)
					uni.doCallBack = () => {
						this.setCurrentIndex(this.currentIndex - 1)
					}
					selectTemplateAnswer(this.currentTypes[this.currentIndex - 1])
				}
			} else {
				selectTemplate(this.currentTypes[this.currentIndex])
			}
		},
		// //模考点击下一题
		// toNextAnswerAtMockType(){
		// 	//判断当前是否是最后一题
		// 	if(this.currentIndex>=this.currentTypes.length-1){
		// 		let tip = ""
		// 		//是否批改已经结束
		// 		if(this.currentType?.correction_completed){
		// 			return uni.showModal({
		// 				title:"提示",
		// 				content:"已经是最后一题返回个人中心？",
		// 				success: async (res) => {
		// 					if (res.confirm) {
		// 						uni.switchTab({
		// 							url:"/pages/center/center"
		// 						})
		// 					}
		// 				}
		// 			})
		// 		}else{
		// 			//批改没有结束创建答题报告数据
		// 			 return uni.showModal({
		// 				title:"提示",
		// 				content:'提交后批改结果在个人中心查看',
		// 				success: async (res) => {
		// 					if (res.confirm) {
		// 						//提交答题报告
		// 						const ret = await this.addRecordsData()
		// 						//跳转到答题报告
		// 						uni.redirectTo({
		// 							url:`/subpkg/examination_report/examination_report?report_id=${ret.report_id}`
		// 						})
		// 					}
		// 				}
		// 			})
		// 		}
		// 	}
		// 	//不是最后一题

		// }
		//答案解析下一题处理
		toNextAnswerTmp() {
			if (this.currentType.qtype == this.currentTypes[this.currentIndex + 1].qtype) {
				//更新答题索引
				this.setCurrentIndex(this.currentIndex + 1)
			} else {
				//this.setCurrentIndex(this.currentIndex+1)
				uni.doCallBack = () => {
					this.setCurrentIndex(this.currentIndex + 1)
				}
				//更新模板
				selectTemplateAnswer(this.currentTypes[this.currentIndex + 1])
			}
		},
		answerNext() {
			//任务题库 把对错加入结果中
			if (this.$store.state.professionalConfig.isShowTaskAnswer == true) {

				if (this.isRight === -1) {
					return uni.tip("请先判断对错");
				}
				if(Array.isArray(this.isRight) && this.isRight.includes(-1)){
					return uni.tip("存在未判断对错的题目");
				}
				//获取用户答案
				const data = this.currentAnswerList.find(
					(item) => item.index == this.currentIndex
				);
				data.answer.isRight = this.isRight;
				// 保存答案
				this.$store.commit("professionalConfig/setAnswerList", data);
			}

			//没有批改自测阶段 添加自测数据
			if (this.currentType.type == 2 && this.correctionCompleted != 1) {
				//获取 用户答案
				const data = this.currentAnswerList.find(item => item.index == this.currentIndex)
				data.answer.selfScore = this.selfScore
				// 保存答案
				this.$store.commit("professionalConfig/setAnswerList", data);
			}

			//默认提示
			let contentTip = "已经最后一题是否返回上一级"
			//处理最后一题的逻辑
			if (this.currentIndex >= this.currentTypes.length - 1) {
				//提示信息 模考类型
				if (this.currentType.type == 2) {
					contentTip = "提交后批改结果在个人中心查看"
					//模考类型 并且已经批改
					if (this.correctionCompleted == 1) {
						contentTip = "已经是最后一题是否跳转到个人中心"
					}
				}

				//任务题库单独处理
				if (this.currentType.type == 3) {
					uni.showModal({
						title: "提示",
						content: "提交本次任务结果",
						success: async (res) => {
							if (res.confirm) {
								//提交之前过滤之前答题记录
								this.$store.commit("professionalConfig/filterPreviousRecords");
								await this.addRecordsData();
								uni.switchTab({
									url: "/pages/task/task",
								});
							}
						}
					})
				} else {
					uni.showModal({
						title: "提示",
						content: contentTip,
						success: async (res) => {
							if (res.confirm) {
								//模考类型 分别处理
								if (this.currentType.type == 2) {
									//批改结束跳转到个人中心
									if (this.correctionCompleted == 1) {
										uni.switchTab({
											url: "/pages/center/center"
										})
									} else {
										const { reportId } = this.$store.state.professionalConfig

										if (!reportId) {
											uni.showModal({
												title: '提示',
												content: '未找到答题报告，是否返回做题页面？',
												success: (res) => {
													if (res.confirm) {
														// 返回做题页面
														uni.switchTab({
															url: '/pages/task/task'
														})
													}
												}
											})
											return
										}
										// 先生成报告
										const ret = await this.addRecordsData(reportId)
										//跳转到答题报告
										uni.redirectTo({
											url: `/subpkg/examination_report/examination_report?report_id=${ret.report_id}`
										})
									}
								} else {
									// uni.redirectTo({
									// 	url: "/subpkg/allquestion/allquestion?type=" + this.currentType.type
									// })
									this.$store.commit('professionalConfig/enterCurrentStartPage')
								}
							}
						}
					})
				}
				return
			}
			//当前类型是模拟试卷 把 自测评分添加到答案里面去
			if (this.currentType.type == 2 || this.currentType?.aboutId > 0) {
				return this.toNextAnswerTmp()
			}

			//专项题库 且是 背题背题模式 下一题是 重新做题 否则一律直接展示答案
			if (this.isFromRecordsList || this.currentType.type != 0 || (this.config.type == 0 && this.currentType
					.type == 0)) {
				return this.toNextAnswerTmp()
			} else {
				//this.setCurrentIndex(this.currentIndex+1)
				// if (this.currentIndex + 1 >= this.currentTypes.length) {
				// 	uni.showModal({
				// 		title: "提示",
				// 		content: contentTip,
				// 		success: async (res) => {
				// 			if (res.confirm) {
				// 				//提交答题报告
				// 				if (this.currentType.type == 2) {
				// 					const ret = await this.addRecordsData()
				// 					//跳转到答题报告
				// 					uni.redirectTo({
				// 						url: `/subpkg/examination_report/examination_report?report_id=${ret.report_id}`
				// 					})
				// 				}else{
				// 					uni.redirectTo({
				// 						url: "/subpkg/allquestion/allquestion?type=" + this.currentType.type
				// 					})
				// 				}
				// 			}
				// 		}
				// 	})
				// 	return
				// }
				uni.doCallBack = () => {
					this.setCurrentIndex(this.currentIndex + 1)
				}
				selectTemplate(this.currentTypes[this.currentIndex + 1])
			}
		},


		//上一题
		async prev(save = true) {
			
			if (this.currentIndex == 0) {
				return uni.tip("已经是第一题了")
			}

			if (typeof this.$store.state.professionalConfig.currentAnswerList[this.currentIndex - 1]?.submitTime != "undefined") {
					return uni.alert('之前做题记录在解析界面查看')
			}

			save && this.saveCurrentAnswer()
			if (this.currentType.qtype == this.currentTypes[this.currentIndex - 1].qtype) {
				//更新答题索引
				this.setCurrentIndex(this.currentIndex - 1)
			} else {
				//this.setCurrentIndex(this.currentIndex-1)
				//设置回调等待页面跳转完成
				uni.doCallBack = () => {
					this.setCurrentIndex(this.currentIndex - 1)
				}
				selectTemplate(this.currentTypes[this.currentIndex - 1])
			}

		},
		/**
		 * 保存当前答题结果
		 */
		saveCurrentAnswer() {
			if (this.correctionCompleted == 1 || this.isFromRecordsList) {
				return
			}
			//不存在答题时间直接返回 从 答案解析进入
			if (typeof this?.$refs?.cheader?.currentTime == 'undefined') {
				return
			}
			//保存答题结果
			let answer = {
				answer: this.answer,
				url: this?.urlList?this.urlList:[],
				timeval: this.$refs.cheader.currentTime,
			}

			const data = {
				answer: answer,
				index: this.currentIndex, //保存当前 题库的进度
			}
			//const res = await updateAnswer(data)
			//当前结果保存本地
			this.$store.commit("professionalConfig/setAnswerList", data);
		},
		//记录答题和生成的报告数据
		async addRecordsData(report_id=0) {
			//写入答题记录
			let postAnswer = []
			//构建答题数据
			// this.currentAnswerList.forEach(item=>{
			// 	const postItem = {
			// 	    pqid: this.currentTypes[item.index].pqid,
			// 		qid: this.currentTypes[item.index].id,
			// 		answer:item.answer,
			// 	}
			// 	//是否客观题
			// 	if(['SINGLE', 'MULTI'].includes(this.currentTypes[item.index].qtype)){
			// 		isRight = this.currentTypes[item.index].answer == item.answer.answer
			// 	}
			// 	postAnswer.push(postItem)
			// })
			//保存答题记录到服务器
			//await addAnswerRecords(postAnswer);
			//更新答题状态
			this.changeCurrentAnswerStatus({
				isSubmit: true,
				submitTime: new Date().getTime()
			})
			let reportData = {}
			//总分
			let totalScore = 0
			//用户得分
			let currentScore = 0
			//生成答题卡数据
			let answerCard = []
			//回答正确的数目 冗余字段 方便后台统计
			let rightNum = 0
			let totalNum = 0
			//遍历答题数据
			this.currentAnswerList.forEach((item, index) => {
				//答题卡数据			
				const answerCardItem = {
					//大题库id
					qid: this.currentTypes[item.index].id,
					//答题顺序
					index: item.index
				}
				let isRight = 0 //0不正确，1正确
				//判断答案是不是数组是数组说明 英语完型填空 阅读理解和新题型
				if (Array.isArray(item.answer.answer) && this.currentTypes[item.index].is_new != 3) {
					const sysAnswer = JSON.parse(this.currentTypes[item.index].answer)
					// 完形填空的数据单独存储
					answerCardItem[this.currentTypes[item.index].qtype] = {
						keyName: this.currentTypes[item.index].qtitle,
						answerList: []
					}
					sysAnswer.forEach((sysAnswerItem) => {
						//总分
						totalScore += this.currentTypes[item.index].score
						//总数量
						totalNum++
						//判断是否正确 获取用户答题数据
						const userAnswer = item.answer.answer.find(userAnswerItem => userAnswerItem
							.index == sysAnswerItem.num - 1)
						//判断是否正确
						if (typeof userAnswer !== 'undefined' && userAnswer.selOption ==
							sysAnswerItem.answer.trim().toUpperCase()) {
							rightNum++
							currentScore += this.currentTypes[item.index].score
							isRight = 1
						} else {
							isRight = 0
						}
						answerCardItem[this.currentTypes[item.index].qtype].answerList.push({
							index: sysAnswerItem.num - 1,
							isRight: isRight
						})
					})

				} else if (this.currentTypes[item.index].is_new == 3) {
					// debugger
					// const sysAnswer = JSON.parse(this.currentTypes[item.index].answer)
					// // 完形填空的数据单独存储
					// answerCardItem[this.currentTypes[item.index].qtype] = {
					// 	keyName: this.currentTypes[item.index].qtitle,
					// 	answerList: []  
					// }
					// let tmpSum = 0
					// sysAnswer.forEach((sysAnswerItem, sysAnswerItemIndex) => {
					// 	answerCardItem[this.currentTypes[item.index].qtype].answerList.push({
					// 		index: sysAnswerItemIndex,
					// 		isRight: 1
					// 	})
					// })



					answerCardItem[this.currentTypes[item.index].qtype] = {
						keyName: this.currentTypes[item.index].qtitle,
						answerList: []
					}
					//总分
					totalScore += this.currentTypes[item.index].score - 0
					let tmpSum = 0
					//翻译模考自测评分
					item.answer.answer.forEach((userAnswerItem, userAnswerItemIndex) => {
						let selfScore = 0;
						if (typeof item.answer.selfScore !== 'undefined' && typeof item.answer
							.selfScore[userAnswerItemIndex] != 'undefined') {
							selfScore = item.answer.selfScore[userAnswerItemIndex]
						}
						if (selfScore > 0) {
							currentScore += selfScore - 0
							isRight = 1
						} else {
							isRight = 0
						}
						answerCardItem[this.currentTypes[item.index].qtype].answerList.push({
							index: userAnswerItemIndex,
							isRight: isRight
						})
					})
				} else {
					//总分
					totalScore += this.currentTypes[item.index].score - 0
					//判断是否正确
					if (item.answer.answer.trim().toUpperCase() == this.currentTypes[item.index].answer
						.trim().toUpperCase()) {
						rightNum++
						//当前得分
						currentScore += this.currentTypes[item.index].score - 0
						isRight = 1
					} else if (typeof item.answer.selfScore != 'undefined' && item.answer.selfScore > 0) {
						//加上自测评分
						currentScore += item.answer.selfScore - 0
						isRight = 1
					} else {
						isRight = 0
					}
					answerCardItem['isRight'] = isRight
				}
				answerCardItem['is_new'] = this.currentTypes[item.index].is_new
				answerCard.push(answerCardItem)
				//答题记录数据 提交到后台
				const postItem = {
					pqid: this.currentTypes[item.index].pqid,
					qid: this.currentTypes[item.index].id,
					answer: item.answer,
					qtype: this.currentTypes[item.index].qtype,//所属题型
					type: this.currentTypes[item.index].type,//所属题库类型
				}
				//主观题  一律是 不正确
				if (!subjectTypeArr.includes(this.currentTypes[item.index].qtype)) {
					isRight = 0
				}
				//任务题库存 正确与否展示 写入自评结果
				if (typeof item.answer.isRight != "undefined") {
					isRight = item.answer.isRight == 1 ? 1 : 0;
				}
				postItem['isRight'] = isRight
				postAnswer.push(postItem)
			})
			//用时
			const totalTime = this.currentAnswerList.reduce((total, item) => {
				return total + item.answer.timeval
			}, 0)
			//是否包含主观题
			const hasContainSubjective = this.currentTypes.some(item => {
				if (!subjectTypeArr.includes(item.qtype)) {
					return true
				} else {
					return false
				}
			})

			//生成记录数据 提交后端
			reportData = {
				//专项题库id
				pqid: this.currentTypes[0].pqid,
				//主题
				title: this.currentTypes[0].professionTitle,
				//答题卡数据
				answerCard,
				//总分
				totalScore,
				//用户得分
				currentScore,
				//回答正确的数目
				rightNum,
				//题目总数量
				totalNum: totalNum > 0 ? totalNum : this.currentTypes.length,
				//答题用时
				totalTime,
				hasContainSubjective,
				type: this.currentType.type,
				about_id:this.currentType?.aboutId ? this.currentType.aboutId:0
			}
			//任务题库
			if (this.currentType?.aboutId > 0) {
				const taskData = {
					about_id: this.currentType?.aboutId,
					type: 'professional',
					useTime: reportData.totalTime,
					total_ret: "100%",
					professional_id: reportData.pqid
				}
				await taskOtherRecord(taskData)
			}

			if(postAnswer.length==0){
				return uni.alert("当前没有答题")
			}

			const ret = await addAnswerAndReportData({
				postAnswer,
				reportData,
				report_id
			})
			return ret

		},
		//是否跳转到下一题
		async whetherToNext() {
			//任务题库点击随时提交
			if (this.$store.state.professionalConfig.isShowTaskAnswer == true) {
				return;
			}
			//练习模式 或者是 模拟考试 以及真题试卷  先做题生成报告再显示答案 
			if (this.config.type == 0 || this.currentType.type == 1 || this.currentType.type == 2 || this
				.currentType?.aboutId > 0) {
											
				if (this.currentIndex + 1 >= this.currentTypes.length) {
					//最后一题 跳转到答题卡
					
					if(this.currentType.type == 3){
						return uni.alert("任务题库请点击顶部按钮提交")
					}
					
					
					uni.redirectTo({
						url: "/subpkg/answer_sheet/answer_sheet"
					})
					// const ret = await this.addRecordsData()
					// if(ret!=false){
					// 	uni.redirectTo({
					// 		url:`/subpkg/examination_report/examination_report?report_id=${ret.report_id}`
					// 	})
					// }
				} else {
					//判断下一题模板与当前模板是否一致来决定是否做页面跳转
					if (this.currentType.qtype == this.currentTypes[this.currentIndex + 1].qtype) {
						//更新下一题索引
						this.setCurrentIndex(this.currentIndex + 1)
					} else {
						//更新下一题索引
						//this.setCurrentIndex(this.currentIndex+1)
						uni.doCallBack = () => {
							this.setCurrentIndex(this.currentIndex + 1)
						}
						//进入下一题
						selectTemplate(this.currentTypes[this.currentIndex + 1])
					}
				}

			} else {
				//背题模式 做一题 看一题答案
				//进入当前的答案模板
				selectTemplateAnswer(this.currentTypes[this.currentIndex])
			}
		}
	},

	onLoad() {
		typeof uni.doCallBack == "function" && uni.doCallBack()
		delete uni.doCallBack
	},
	computed: {
		...mapState("professionalConfig", ['currentTypes', "currentIndex", 'config', 'currentAnswerList',
			'currentAnswerStatus', "isShowTaskAnswer",
		]),
		//总题库所属id
		qid() {
			return this.currentTypes[this.currentIndex].id //大题库 试题id
		},
		//专项题库id
		pqid() {
			return this.currentTypes[this.currentIndex].pqid // 专项题库/试卷id
		},
		//类型
		ptype() {
			return this.currentTypes[this.currentIndex].type //类型是否试卷
		},
		currentType() {
			return this.currentTypes[this.currentIndex] //当前 答题的 详细题库信息,问题信息
		},
		statisticsInfo() {
			return {
				usingTime: this.timeval,
				rightRate: this.currentType.rightRateOfAllQuestion,
				easyWrongOption: this.currentType.most_error_item,
			}
		},
		//完形填空构造 问题列表
		questionList() {
			if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length >
				0) {
				let questions = JSON.parse(this.currentType.material_questions)
				return questions.map((item, index) => {
					let returnOpt = {}
					Object.keys(item).forEach(key => {
						if (key.indexOf('q') != -1 && key.length == 2) {
							returnOpt[key.slice(1).toUpperCase()] = item[key]
						}
					})
					return returnOpt
				})
			} else {
				return []
			}
		},
		//是否已经批改
		correctionCompleted() {
			return this.currentType?.correction_completed
		},
		currentStartPage() {
			return this.$store.state.professionalConfig.currentStartPage
		},
		isFromRecordsList() {
			return this.currentStartPage == "collection_page/practice_record/practice_record"
		}
	},
	watch: {
		//当前 题目索引变化更改答案
		currentIndex: {
			handler: function(val, oldval) {
				console.log('currentIndex', val)
				this.question = {
					title: this.currentTypes[val].title ? this.currentTypes[val].title : '',
					is_star: this.currentTypes[val].is_star,
				}
				//选择题处理选项
				if (typeof this.currentTypes[val].options_json === "string" && this.currentTypes[val].options_json
					.length > 0) {
					this.question['options'] = JSON.parse(this.currentTypes[val].options_json)
				}
				let answerObj = {}
				//模考试卷通知后组织答案数据
				if (typeof this.currentType.answer_records !== "undefined") {
					const answertmp = JSON.parse(this.currentType.answer_records.answer)
					answerObj = {
						answer: answertmp,
						timeval: answertmp.timeval,
					}
				} else {
					//获取答案
					answerObj = this.currentAnswerList.find(item => item.index == val)
					console.log('answerObj', answerObj)
				}
				if (typeof answerObj !== "undefined") {
					this.answer = answerObj.answer.answer //文本
					this.currentSelArr = answerObj.answer.answer //数组
					this.timeval = answerObj.answer.timeval ? answerObj.answer.timeval : 0 //时间
					//模拟试卷加上 自测评分
					if (this.currentType.type == 2) {
						this.selfScore = answerObj.answer.selfScore ? answerObj.answer.selfScore : 0
						//翻译题单独处理
						if (this.currentType.is_new == 3) {
							this.selfScore = answerObj.answer.selfScore ? answerObj.answer.selfScore : {}
						}
					}
					if (this.currentType.is_new == 3) {

						if (answerObj.answer.url.length == 0) {
							this.fileList = [
								[],
								[],
								[],
								[],
								[]
							]
							return
						}
						answerObj.answer.url.forEach((item, index) => {
							const urls = item.split(",").filter(val => val?.length > 0);
							console.log('urls', urls)
							if (urls.length == 0) {
								this.fileList[index] = []
							} else {
								this.fileList[index] = urls.map(obj => {
									return {
										url: obj
									}
								})


							}

						})

					} else {
						if (answerObj.answer.url?.length > 0) {
							//是否有图片
							this.fileList = answerObj.answer.url.split(",").filter(item => item != "").map(item => {
								return {
									url: item
								}
							})
						} else {
							this.fileList = [];
						}
						//处理对错判断
						if (typeof this.isRight != "undefined") {
							if (typeof answerObj.answer.isRight != "undefined") {
								this.isRight = answerObj.answer.isRight - 0;
							} else {
								this.isRight = -1;
							}
						}
					}
				} else {
					//翻译题 图片和文本
					if (this.currentType.is_new == 3) {
						//清空答案和时间
						this.answer = [
							"", "", "", "", ""
						]
						this.fileList = [
							[],
							[],
							[],
							[],
							[]
						]
					} else {
						//清空答案和时间
						this.answer = ""
						this.fileList = []
					}

					//完形填空 阅读理解 新题型 单独处理
					this.currentSelArr = []
					this.currentChoose = 0;
					this.timeval = 0
					//防止计时器不更新
					this.$nextTick(() => {
					if (this.$refs.cheader) {
							this.$refs.cheader.currentTime = 0
						}
					})
					this.selfScore = {}
				}
			},
			immediate: true,
			deep: true
		}
	}
}