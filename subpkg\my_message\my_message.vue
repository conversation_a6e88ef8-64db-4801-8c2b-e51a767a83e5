<template>
	<view class="container">
		<view class="tabs">
			<view :class="['tabs-item', index == 1 ? 'bg' : '']" @click="switchover(1)">点赞</view>
			<view :class="['tabs-item', index == 2 ? 'bg' : '']" @click="switchover(2)">评论</view>
		</view>
		<template v-if="index == 1">
			<view class="list" v-for="item in lists" :key="item.id">
				<view class="left">
					<image class="avatar" :src="item.user.avatar" mode=""></image>
					<view class="user">
						<view class="name">
							<text>{{ item.user.nickname }}</text>
							<text class="time">{{ item.create_text }}</text>
						</view>
						<text class="tex">赞了你的动态</text>
					</view>
				</view>
				<view class="right">
					<image class="img" :src="item.article.image_arr_text[0]" mode=""></image>
				</view>

			</view>
		</template>

		<template v-if="index == 2">
			<view class="list" v-for="(item, index) in list" :key="index">
				<view class="left">
					<image class="avatar" :src="item.user.avatar" mode=""></image>
					<view class="user">
						<view class="name">
							<text>{{ item.user.nickname }}</text>
							<text class="time">{{ item.create_text }}</text>
						</view>
						<view class="tex-content">
							<view :class="[item.content.length < 15 ? 'text-content' : '']">
								<text>评论了你：</text>
								<text class="content">{{ item.content }}</text>
								<u-icon v-if="!item.flag && item.content.length > 15" name="arrow-up" size="28" @click="all(item)"></u-icon>
							</view>
							<u-icon v-if="item.flag && item.content.length > 15" name="arrow-right" size="28" @click="all(item)"></u-icon>
						</view>


					</view>
				</view>
				<view class="right">
					<image class="img" :src="item.article.image_arr_text[0]" mode=""></image>
				</view>
			</view>
		</template>

	</view>
</template>

<script>
export default {
	data() {
		return {
			likeTotal: 0,
			commentTotal: 0,
			likePage: 1,
			commentPage: 1,
			index: 1,
			lists: [],
			list: [
			],
		}
	},
	onLoad() {

		this.getAllZan();
		this.getAllComment();
		// this.list.forEach(item => item.flag = true)
	},
	methods: {
		async getAllZan() {
			let that = this;
			let ret = await uni.$u.http.get('/article/getAllZan', { params: { page: this.likePage } }).then(rest => {
				console.log(rest.data.data)
				that.lists = [...that.lists, ...rest.data.data];
				that.commentTotal = rest.data.total


			})
		},
		async getAllComment() {
			let that = this;
			let ret = await uni.$u.http.get('/article/getAllComment', { params: { page: this.commentPage } }).then(rest => {

				that.list = [...that.list, ...rest.data.data];

				that.likeTotal = rest.data.total

			})
		},
		switchover(val) {
			this.index = val

		},
		all(item) {
			var index = this.list.findIndex(val => val.id == item.id)
			item.flag = !item.flag
			this.list.splice(index, 1, item)

		}

	},
	onReachBottom() {
		if (this.index == 1) {
			if (this.lists.length >= this.likeTotal) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			}
			this.likePage += 1
			this.getAllZan()

		} else {
			if (this.lists.length >= this.commentTotal) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			}
			this.likePage += 1
			this.getAllZan()
		}
	},
}
</script>

<style lang="scss" scoped>
.container {
	.tabs {
		width: 690rpx;
		height: 80rpx;
		display: flex;
		margin: 30rpx auto 15rpx;

		.tabs-item {
			flex: 1;
			text-align: center;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: #01997A;
			background-color: #F6F7FB;

			&:first-child {
				border-radius: 26rpx 0rpx 0rpx 26rpx;
			}

			&:last-child {
				border-radius: 0rpx 26rpx 26rpx 0rpx;
			}
		}

		.bg {
			color: #fff;
			background: #01997A;

		}
	}

	.list {
		width: 690rpx;
		// height: 160rpx;
		padding: 35rpx 0;
		box-sizing: border-box;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		// align-items: center;
		border-bottom: 1px solid #f4f4f4;

		&:last-child {
			margin-bottom: 130rpx;
		}

		.left {
			flex: 3;
			display: flex;
			// align-items: center;

			.avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;

			}

			.user {
				margin-left: 10rpx;
				width: 450rpx;

				.name {
					font-size: 26rpx;
					color: #060606;
					// margin-bottom: 10rpx;

					.time {
						font-weight: 500;
						font-size: 24rpx;
						color: #818181;
						margin-left: 30rpx;
					}
				}

				.tex {
					font-size: 24rpx;
					color: #060606;


				}

				.tex-content {
					display: flex;
					font-size: 24rpx;
					color: #060606;
					margin-top: 10rpx;

					.text-content {
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					::v-deep .u-icon {
						display: inline-block;
					}
				}
			}
		}

		.right {
			flex: 1;
			text-align: right;

			.img {
				width: 80rpx;
				height: 80rpx;
			}
		}

	}
}
</style>