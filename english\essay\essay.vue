<template>
	<view class="container">
		<cheader :initTimeVal="timeval" ref="cheader"></cheader>
		<view class="content">
			<question-content></question-content>
		</view>
		<!-- 填写答案 -->
		<view class="answer">
			<view class="title">
				我的答案
			</view>
			<view class="a-content" v-if="!Array.isArray(answer)">
				<u--textarea height="290rpx" border="none" v-model="answer" placeholder="请输入答案"></u--textarea>
			</view>

			<view class="q-upload">
				<u-upload height="200rpx" width="200rpx" uploadText="上传照片" :fileList="fileList" @afterRead="afterRead"
					@delete="deletePic" name="1" multiple :maxCount="10"></u-upload>
			</view>

		</view>
		<view class="bottom-btn">
			<view class="prev" @click="prev">
				上一题
			</view>
			<view class="next" @click="next">
				下一题
			</view>
		</view>
	</view>
</template>

<script>
	import cheader from "@/components/c_head/c_head.vue"
	import star from "@/components/star/star.vue"
	import {uploadFile} from "@/api/common/common.js"
	import {updateAnswer, getQuestionAnswer,addAnswerRecords} from "@/api/professional/index.js"
	import mixins from "@/mixins/index.js"
	import {selectTemplate, selectTemplateAnswer} from "@/utils/tool.js"
	import questionContent from "../components/content/content.vue"
	export default {
		mixins:[mixins],
		data() {
			return {
				id: 0,
				currentTime: 0,
				loading:false,
			};
		},
		methods: {
			async next (){
				//上传图片时候禁止提交
				if(this.loading){
					return uni.tip("图片上传中...")
				}
				// if(this.answer!="" || this.fileList.length>0){
					let answer = {
						answer:this.answer,
						url:this.urlList,
						timeval : this.$refs.cheader.currentTime,
					}
					
					const data = {
						answer:answer,
						index:this.currentIndex,//保存当前 题库的进度
					}
					//const res = await updateAnswer(data)
					//当前结果保存本地
					this.$store.commit("professionalConfig/setAnswerList", data);
				//}
				//提交题库的后续逻辑
				//获取答题的配置信息
				this.whetherToNext()
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)
					}
				}
				this.loading = false
			},
		},
		onLoad(option) {
			const {
				id
			} = option
			this.id = id

		},
		// onShow() {
		// 	this.getDetail(this.id)
		// 	const answerList = this.$store.state.professionalConfig.currentAnswerList
		// 	//获取答案
		// 	const answerObj =  answerList.find(item=>item.index==this.currentIndex)
		// 	if(answerObj !== undefined){
		// 		const answer = answerObj.answer
		// 		this.answer = answer.answer
		// 		this.timeval = answer.timeval
		// 		if(answer.url.length>0){
		// 			this.fileList = answer.url.split(",").map(item=>{
		// 				return {
		// 					url:item
		// 				}
		// 			})
		// 		}
		// 	}
		// 	//this.getAnswer()
			
		// },
		computed: {
			urlList(){					
				return this.fileList.map(item=>item.url).join(",")
			},
		},
		components: {
			cheader,
			star,
			"question-content": questionContent
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 20rpx;

		.content {
			background-color: #fff;
			padding-bottom: 80rpx;

			.top-opt {
				width: 100%;
				padding: 10rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.progress-text {
					text {
						color: #777777;
						font-size: 26rpx;

						&:first-child {
							font-size: 34rpx;
							color: #2D2D2D;
							font-weight: bold;
						}
					}
				}

			}

			.q-type {
				margin-top: 12rpx;
				background-color: #CDF3E7;
				border-radius: 12rpx;
				color: $main-color;
				font-size: 24rpx;
				width: 86rpx;
				height: 46rpx;
				text-align: center;
				line-height: 46rpx;
			}
		}

		.question {
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;

			.title {
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}

			.question-content {
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
			}
		}

		.answer {
			margin-top: 32rpx;
			padding: 0 36rpx;
			margin-bottom: 36rpx;

			.title {
				font-size: 24rpx;
				color: #777777;
				font-weight: bold;
			}

			.a-content {
				margin-top: 12rpx;
				margin-bottom: 36rpx;
				border: 1rpx solid #707070;
				margin-top: 12rpx;
				border-radius: 22rpx;
				padding: 10rpx;
			}

		}

		.bottom-btn {
			padding: 0 36rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 56rpx;

			view {
				width: 170rpx;
				height: 70rpx;
				border-radius: 16rpx;
				border: 1rpx solid #01997A;
				line-height: 70rpx;
				text-align: center;
				font-size: 28rpx;
			}

			.prev {
				background: #FFFFFF;
				color: $main-color;
			}

			.next {
				background-color: $main-color;
				color: #fff;
			}
		}

	}
</style>