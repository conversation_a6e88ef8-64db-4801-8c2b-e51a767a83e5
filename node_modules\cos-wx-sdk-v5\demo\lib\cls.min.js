!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).ClsClient=t()}(this,(function(){"use strict";function _callSuper(e,t,r){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],_getPrototypeOf(e).constructor):t.apply(e,r))}function _construct(e,t,r){if(_isNativeReflectConstruct())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var n=new(e.bind.apply(e,i));return r&&_setPrototypeOf(n,r.prototype),n}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){_defineProperty(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _regeneratorRuntime(){_regeneratorRuntime=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function l(e,t,r,i){var o=t&&t.prototype instanceof m?t:m,s=Object.create(o.prototype),a=new C(i||[]);return n(s,"_invoke",{value:E(e,r,a)}),s}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var h="suspendedStart",p="suspendedYield",d="executing",g="completed",y={};function m(){}function v(){}function b(){}var w={};c(w,s,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(B([])));x&&x!==r&&i.call(x,s)&&(w=x);var S=b.prototype=m.prototype=Object.create(w);function $(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function R(e,t){function r(n,o,s,a){var u=f(e[n],e,o);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"==typeof l&&i.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,s,a)}),(function(e){r("throw",e,s,a)})):t.resolve(l).then((function(e){c.value=e,s(c)}),(function(e){return r("throw",e,s,a)}))}a(u.arg)}var o;n(this,"_invoke",{value:function(e,i){function n(){return new t((function(t,n){r(e,i,t,n)}))}return o=o?o.then(n,n):n()}})}function E(t,r,i){var n=h;return function(o,s){if(n===d)throw new Error("Generator is already running");if(n===g){if("throw"===o)throw s;return{value:e,done:!0}}for(i.method=o,i.arg=s;;){var a=i.delegate;if(a){var u=O(a,i);if(u){if(u===y)continue;return u}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===h)throw n=g,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=d;var c=f(t,r,i);if("normal"===c.type){if(n=i.done?g:p,c.arg===y)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n=g,i.method="throw",i.arg=c.arg)}}}function O(t,r){var i=r.method,n=t.iterator[i];if(n===e)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=e,O(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),y;var o=f(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var s=o.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function B(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}return v.prototype=b,n(S,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:v,configurable:!0}),v.displayName=c(b,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,u,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},$(R.prototype),c(R.prototype,a,(function(){return this})),t.AsyncIterator=R,t.async=function(e,r,i,n,o){void 0===o&&(o=Promise);var s=new R(l(e,r,i,n),o);return t.isGeneratorFunction(r)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},$(S),c(S,u,"Generator"),c(S,s,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=B,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(k),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(i,n){return a.type="throw",a.arg=t,r.next=i,n&&(r.method="next",r.arg=e),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return n("end");if(s.tryLoc<=this.prev){var u=i.call(s,"catchLoc"),c=i.call(s,"finallyLoc");if(u&&c){if(this.prev<s.catchLoc)return n(s.catchLoc,!0);if(this.prev<s.finallyLoc)return n(s.finallyLoc)}else if(u){if(this.prev<s.catchLoc)return n(s.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return n(s.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),k(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var n=i.arg;k(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,i){return this.delegate={iterator:B(t),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=e),y}},t}function _toPrimitive(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==typeof t?t:String(t)}function asyncGeneratorStep(e,t,r,i,n,o,s){try{var a=e[o](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(i,n)}function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(i,n){var o=e.apply(t,r);function s(e){asyncGeneratorStep(o,i,n,s,a,"next",e)}function a(e){asyncGeneratorStep(o,i,n,s,a,"throw",e)}s(void 0)}))}}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,_toPropertyKey(i.key),i)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _isNativeFunction(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}function _wrapNativeSuper(e){var t="function"==typeof Map?new Map:void 0;return _wrapNativeSuper=function(e){if(null===e||!_isNativeFunction(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return _construct(e,arguments,_getPrototypeOf(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(r,e)},_wrapNativeSuper(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _possibleConstructorReturn(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}var ClsSDKError=function(e){function t(e){var r,i;(_classCallCheck(this,t),"string"==typeof e)?r=_callSuper(this,t,[e]):((r=_callSuper(this,t,[e.message])).clsRequestId=(null===(i=e.headers)||void 0===i?void 0:i["x-cls-requestid"])||"",r.httpStatus=e.status,r.httpCode=e.code);return _possibleConstructorReturn(r)}return _inherits(t,e),_createClass(t,[{key:"requestId",get:function(){return this.clsRequestId}},{key:"status",get:function(){return this.httpStatus}},{key:"code",get:function(){return this.httpCode}},{key:"getMessage",value:function(){return this.message}},{key:"toString",value:function(){return"[ClsSDKError]message:"+this.getMessage()+"  requestId:"+this.requestId}},{key:"toLocaleString",value:function(){return"[ClsSDKError]message:"+this.getMessage()+"  requestId:"+this.requestId}}]),t}(_wrapNativeSuper(Error));function isString$2(e){return"string"==typeof e}function wait(e){return new Promise((function(t){setTimeout(t,e)}))}function isNotEmpty(e){return null!=e&&""!==e}var ClientConfig=function(){function e(t){if(_classCallCheck(this,e),_defineProperty(this,"clsTopicId",""),_defineProperty(this,"region",void 0),_defineProperty(this,"endpoint",void 0),_defineProperty(this,"api",void 0),_defineProperty(this,"clsCredential",void 0),_defineProperty(this,"getAuthorization",void 0),_defineProperty(this,"maxRetainDuration",20),_defineProperty(this,"maxRetainSize",30),_defineProperty(this,"logExpiredDays",7),_defineProperty(this,"logPath",""),_defineProperty(this,"httpAdapter",void 0),_defineProperty(this,"autoFillSourceIp",void 0),_defineProperty(this,"sourceIp",void 0),_defineProperty(this,"proxy",void 0),_defineProperty(this,"getAgent",void 0),!t.topicId||"string"!=typeof t.topicId)throw new ClsSDKError("topicId is required and must be a string");if(t.region&&isNotEmpty(t.region))this.region=t.region;else{if(!t.endpoint||!isNotEmpty(t.endpoint))throw new ClsSDKError("region or endpoint is required");this.endpoint=t.endpoint}isNotEmpty(t.api)&&(this.api=t.api),isNotEmpty(t.sourceIp)&&(this.sourceIp=t.sourceIp),isNotEmpty(t.autoFillSourceIp)&&(this.autoFillSourceIp=t.autoFillSourceIp),isNotEmpty(t.credential)&&(this.clsCredential=t.credential),isNotEmpty(t.getAuthorization)&&(this.getAuthorization=t.getAuthorization),isNotEmpty(t.proxy)&&(this.proxy=t.proxy),isNotEmpty(t.getAgent)&&(this.getAgent=t.getAgent),isNotEmpty(t.httpAdapter)&&(this.httpAdapter=t.httpAdapter),t.maxRetainDuration&&(this.maxRetainDuration=t.maxRetainDuration),t.maxRetainSize&&(this.maxRetainSize=t.maxRetainSize),t.logExpiredDays&&(this.logExpiredDays=t.logExpiredDays),t.logPath&&(this.logPath=t.logPath),t.onError&&(this.onError=t.onError),this.clsTopicId=t.topicId}return _createClass(e,[{key:"topicId",get:function(){return this.clsTopicId}},{key:"credential",get:function(){return this.clsCredential}},{key:"onError",value:function(e){}}]),e}();function bind$2(e,t){return function(){return e.apply(t,arguments)}}const{toString:toString$1}=Object.prototype,{getPrototypeOf:getPrototypeOf}=Object,kindOf=(cache=Object.create(null),e=>{const t=toString$1.call(e);return cache[t]||(cache[t]=t.slice(8,-1).toLowerCase())});var cache;const kindOfTest=e=>(e=e.toLowerCase(),t=>kindOf(t)===e),typeOfTest=e=>t=>typeof t===e,{isArray:isArray$1}=Array,isUndefined$1=typeOfTest("undefined");function isBuffer$1(e){return null!==e&&!isUndefined$1(e)&&null!==e.constructor&&!isUndefined$1(e.constructor)&&isFunction$1(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const isArrayBuffer$1=kindOfTest("ArrayBuffer");function isArrayBufferView$1(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&isArrayBuffer$1(e.buffer),t}const isString$1=typeOfTest("string"),isFunction$1=typeOfTest("function"),isNumber$1=typeOfTest("number"),isObject$1=e=>null!==e&&"object"==typeof e,isBoolean=e=>!0===e||!1===e,isPlainObject=e=>{if("object"!==kindOf(e))return!1;const t=getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},isDate$1=kindOfTest("Date"),isFile$1=kindOfTest("File"),isBlob$1=kindOfTest("Blob"),isFileList=kindOfTest("FileList"),isStream$1=e=>isObject$1(e)&&isFunction$1(e.pipe),isFormData$1=e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||isFunction$1(e.append)&&("formdata"===(t=kindOf(e))||"object"===t&&isFunction$1(e.toString)&&"[object FormData]"===e.toString()))},isURLSearchParams$1=kindOfTest("URLSearchParams"),trim$1=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function forEach$1(e,t,{allOwnKeys:r=!1}={}){if(null==e)return;let i,n;if("object"!=typeof e&&(e=[e]),isArray$1(e))for(i=0,n=e.length;i<n;i++)t.call(null,e[i],i,e);else{const n=r?Object.getOwnPropertyNames(e):Object.keys(e),o=n.length;let s;for(i=0;i<o;i++)s=n[i],t.call(null,e[s],s,e)}}function findKey(e,t){t=t.toLowerCase();const r=Object.keys(e);let i,n=r.length;for(;n-- >0;)if(i=r[n],t===i.toLowerCase())return i;return null}const _global="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,isContextDefined=e=>!isUndefined$1(e)&&e!==_global;function merge$1(){const{caseless:e}=isContextDefined(this)&&this||{},t={},r=(r,i)=>{const n=e&&findKey(t,i)||i;isPlainObject(t[n])&&isPlainObject(r)?t[n]=merge$1(t[n],r):isPlainObject(r)?t[n]=merge$1({},r):isArray$1(r)?t[n]=r.slice():t[n]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&forEach$1(arguments[e],r);return t}const extend$1=(e,t,r,{allOwnKeys:i}={})=>(forEach$1(t,((t,i)=>{r&&isFunction$1(t)?e[i]=bind$2(t,r):e[i]=t}),{allOwnKeys:i}),e),stripBOM=e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits=(e,t,r,i)=>{e.prototype=Object.create(t.prototype,i),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject=(e,t,r,i)=>{let n,o,s;const a={};if(t=t||{},null==e)return t;do{for(n=Object.getOwnPropertyNames(e),o=n.length;o-- >0;)s=n[o],i&&!i(s,e,t)||a[s]||(t[s]=e[s],a[s]=!0);e=!1!==r&&getPrototypeOf(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},endsWith=(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;const i=e.indexOf(t,r);return-1!==i&&i===r},toArray=e=>{if(!e)return null;if(isArray$1(e))return e;let t=e.length;if(!isNumber$1(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},isTypedArray=(TypedArray="undefined"!=typeof Uint8Array&&getPrototypeOf(Uint8Array),e=>TypedArray&&e instanceof TypedArray);var TypedArray;const forEachEntry=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let i;for(;(i=r.next())&&!i.done;){const r=i.value;t.call(e,r[0],r[1])}},matchAll=(e,t)=>{let r;const i=[];for(;null!==(r=e.exec(t));)i.push(r);return i},isHTMLForm=kindOfTest("HTMLFormElement"),toCamelCase=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,r){return t.toUpperCase()+r})),hasOwnProperty=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),isRegExp=kindOfTest("RegExp"),reduceDescriptors=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),i={};forEach$1(r,((r,n)=>{let o;!1!==(o=t(r,n,e))&&(i[n]=o||r)})),Object.defineProperties(e,i)},freezeMethods=e=>{reduceDescriptors(e,((t,r)=>{if(isFunction$1(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;const i=e[r];isFunction$1(i)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")}))}))},toObjectSet=(e,t)=>{const r={},i=e=>{e.forEach((e=>{r[e]=!0}))};return isArray$1(e)?i(e):i(String(e).split(t)),r},noop$1=()=>{},toFiniteNumber=(e,t)=>(e=+e,Number.isFinite(e)?e:t),ALPHA="abcdefghijklmnopqrstuvwxyz",DIGIT="0123456789",ALPHABET={DIGIT:DIGIT,ALPHA:ALPHA,ALPHA_DIGIT:ALPHA+ALPHA.toUpperCase()+DIGIT},generateString=(e=16,t=ALPHABET.ALPHA_DIGIT)=>{let r="";const{length:i}=t;for(;e--;)r+=t[Math.random()*i|0];return r};function isSpecCompliantForm(e){return!!(e&&isFunction$1(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])}const toJSONObject=e=>{const t=new Array(10),r=(e,i)=>{if(isObject$1(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[i]=e;const n=isArray$1(e)?[]:{};return forEach$1(e,((e,t)=>{const o=r(e,i+1);!isUndefined$1(o)&&(n[t]=o)})),t[i]=void 0,n}}return e};return r(e,0)},isAsyncFn=kindOfTest("AsyncFunction"),isThenable=e=>e&&(isObject$1(e)||isFunction$1(e))&&isFunction$1(e.then)&&isFunction$1(e.catch);var utils$4={isArray:isArray$1,isArrayBuffer:isArrayBuffer$1,isBuffer:isBuffer$1,isFormData:isFormData$1,isArrayBufferView:isArrayBufferView$1,isString:isString$1,isNumber:isNumber$1,isBoolean:isBoolean,isObject:isObject$1,isPlainObject:isPlainObject,isUndefined:isUndefined$1,isDate:isDate$1,isFile:isFile$1,isBlob:isBlob$1,isRegExp:isRegExp,isFunction:isFunction$1,isStream:isStream$1,isURLSearchParams:isURLSearchParams$1,isTypedArray:isTypedArray,isFileList:isFileList,forEach:forEach$1,merge:merge$1,extend:extend$1,trim:trim$1,stripBOM:stripBOM,inherits:inherits,toFlatObject:toFlatObject,kindOf:kindOf,kindOfTest:kindOfTest,endsWith:endsWith,toArray:toArray,forEachEntry:forEachEntry,matchAll:matchAll,isHTMLForm:isHTMLForm,hasOwnProperty:hasOwnProperty,hasOwnProp:hasOwnProperty,reduceDescriptors:reduceDescriptors,freezeMethods:freezeMethods,toObjectSet:toObjectSet,toCamelCase:toCamelCase,noop:noop$1,toFiniteNumber:toFiniteNumber,findKey:findKey,global:_global,isContextDefined:isContextDefined,ALPHABET:ALPHABET,generateString:generateString,isSpecCompliantForm:isSpecCompliantForm,toJSONObject:toJSONObject,isAsyncFn:isAsyncFn,isThenable:isThenable};function AxiosError(e,t,r,i,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),i&&(this.request=i),n&&(this.response=n)}utils$4.inherits(AxiosError,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:utils$4.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const prototype$1=AxiosError.prototype,descriptors={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{descriptors[e]={value:e}})),Object.defineProperties(AxiosError,descriptors),Object.defineProperty(prototype$1,"isAxiosError",{value:!0}),AxiosError.from=(e,t,r,i,n,o)=>{const s=Object.create(prototype$1);return utils$4.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),AxiosError.call(s,e.message,t,r,i,n),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var httpAdapter=null;function isVisitable(e){return utils$4.isPlainObject(e)||utils$4.isArray(e)}function removeBrackets(e){return utils$4.endsWith(e,"[]")?e.slice(0,-2):e}function renderKey(e,t,r){return e?e.concat(t).map((function(e,t){return e=removeBrackets(e),!r&&t?"["+e+"]":e})).join(r?".":""):t}function isFlatArray(e){return utils$4.isArray(e)&&!e.some(isVisitable)}const predicates=utils$4.toFlatObject(utils$4,{},null,(function(e){return/^is[A-Z]/.test(e)}));function toFormData(e,t,r){if(!utils$4.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const i=(r=utils$4.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!utils$4.isUndefined(t[e])}))).metaTokens,n=r.visitor||c,o=r.dots,s=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&utils$4.isSpecCompliantForm(t);if(!utils$4.isFunction(n))throw new TypeError("visitor must be a function");function u(e){if(null===e)return"";if(utils$4.isDate(e))return e.toISOString();if(!a&&utils$4.isBlob(e))throw new AxiosError("Blob is not supported. Use a Buffer instead.");return utils$4.isArrayBuffer(e)||utils$4.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,r,n){let a=e;if(e&&!n&&"object"==typeof e)if(utils$4.endsWith(r,"{}"))r=i?r:r.slice(0,-2),e=JSON.stringify(e);else if(utils$4.isArray(e)&&isFlatArray(e)||(utils$4.isFileList(e)||utils$4.endsWith(r,"[]"))&&(a=utils$4.toArray(e)))return r=removeBrackets(r),a.forEach((function(e,i){!utils$4.isUndefined(e)&&null!==e&&t.append(!0===s?renderKey([r],i,o):null===s?r:r+"[]",u(e))})),!1;return!!isVisitable(e)||(t.append(renderKey(n,r,o),u(e)),!1)}const l=[],f=Object.assign(predicates,{defaultVisitor:c,convertValue:u,isVisitable:isVisitable});if(!utils$4.isObject(e))throw new TypeError("data must be an object");return function e(r,i){if(!utils$4.isUndefined(r)){if(-1!==l.indexOf(r))throw Error("Circular reference detected in "+i.join("."));l.push(r),utils$4.forEach(r,(function(r,o){!0===(!(utils$4.isUndefined(r)||null===r)&&n.call(t,r,utils$4.isString(o)?o.trim():o,i,f))&&e(r,i?i.concat(o):[o])})),l.pop()}}(e),t}function encode$2(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function AxiosURLSearchParams(e,t){this._pairs=[],e&&toFormData(e,this,t)}const prototype=AxiosURLSearchParams.prototype;function encode$1(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function buildURL$2(e,t,r){if(!t)return e;const i=r&&r.encode||encode$1,n=r&&r.serialize;let o;if(o=n?n(t,r):utils$4.isURLSearchParams(t)?t.toString():new AxiosURLSearchParams(t,r).toString(i),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}prototype.append=function(e,t){this._pairs.push([e,t])},prototype.toString=function(e){const t=e?function(t){return e.call(this,t,encode$2)}:encode$2;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class InterceptorManager{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){utils$4.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}var transitionalDefaults={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},URLSearchParams$1="undefined"!=typeof URLSearchParams?URLSearchParams:AxiosURLSearchParams,FormData$1="undefined"!=typeof FormData?FormData:null,Blob$1="undefined"!=typeof Blob?Blob:null,platform$1={isBrowser:!0,classes:{URLSearchParams:URLSearchParams$1,FormData:FormData$1,Blob:Blob$1},protocols:["http","https","file","blob","url","data"]};const hasBrowserEnv="undefined"!=typeof window&&"undefined"!=typeof document,hasStandardBrowserEnv=(product="undefined"!=typeof navigator&&navigator.product,hasBrowserEnv&&["ReactNative","NativeScript","NS"].indexOf(product)<0);var product;const hasStandardBrowserWebWorkerEnv="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts;var utils$3=Object.freeze({__proto__:null,hasBrowserEnv:hasBrowserEnv,hasStandardBrowserEnv:hasStandardBrowserEnv,hasStandardBrowserWebWorkerEnv:hasStandardBrowserWebWorkerEnv}),platform={...utils$3,...platform$1};function toURLEncodedForm(e,t){return toFormData(e,new platform.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,i){return platform.isNode&&utils$4.isBuffer(e)?(this.append(t,e.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function parsePropPath(e){return utils$4.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}function arrayToObject(e){const t={},r=Object.keys(e);let i;const n=r.length;let o;for(i=0;i<n;i++)o=r[i],t[o]=e[o];return t}function formDataToJSON(e){function t(e,r,i,n){let o=e[n++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),a=n>=e.length;if(o=!o&&utils$4.isArray(i)?i.length:o,a)return utils$4.hasOwnProp(i,o)?i[o]=[i[o],r]:i[o]=r,!s;i[o]&&utils$4.isObject(i[o])||(i[o]=[]);return t(e,r,i[o],n)&&utils$4.isArray(i[o])&&(i[o]=arrayToObject(i[o])),!s}if(utils$4.isFormData(e)&&utils$4.isFunction(e.entries)){const r={};return utils$4.forEachEntry(e,((e,i)=>{t(parsePropPath(e),i,r,0)})),r}return null}function stringifySafely(e,t,r){if(utils$4.isString(e))try{return(t||JSON.parse)(e),utils$4.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(r||JSON.stringify)(e)}const defaults={transitional:transitionalDefaults,adapter:["xhr","http"],transformRequest:[function(e,t){const r=t.getContentType()||"",i=r.indexOf("application/json")>-1,n=utils$4.isObject(e);n&&utils$4.isHTMLForm(e)&&(e=new FormData(e));if(utils$4.isFormData(e))return i?JSON.stringify(formDataToJSON(e)):e;if(utils$4.isArrayBuffer(e)||utils$4.isBuffer(e)||utils$4.isStream(e)||utils$4.isFile(e)||utils$4.isBlob(e))return e;if(utils$4.isArrayBufferView(e))return e.buffer;if(utils$4.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(n){if(r.indexOf("application/x-www-form-urlencoded")>-1)return toURLEncodedForm(e,this.formSerializer).toString();if((o=utils$4.isFileList(e))||r.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return toFormData(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return n||i?(t.setContentType("application/json",!1),stringifySafely(e)):e}],transformResponse:[function(e){const t=this.transitional||defaults.transitional,r=t&&t.forcedJSONParsing,i="json"===this.responseType;if(e&&utils$4.isString(e)&&(r&&!this.responseType||i)){const r=!(t&&t.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(e){if(r){if("SyntaxError"===e.name)throw AxiosError.from(e,AxiosError.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:platform.classes.FormData,Blob:platform.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};utils$4.forEach(["delete","get","head","post","put","patch"],(e=>{defaults.headers[e]={}}));var defaults$1=defaults;const ignoreDuplicateOf=utils$4.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var parseHeaders=e=>{const t={};let r,i,n;return e&&e.split("\n").forEach((function(e){n=e.indexOf(":"),r=e.substring(0,n).trim().toLowerCase(),i=e.substring(n+1).trim(),!r||t[r]&&ignoreDuplicateOf[r]||("set-cookie"===r?t[r]?t[r].push(i):t[r]=[i]:t[r]=t[r]?t[r]+", "+i:i)})),t};const $internals=Symbol("internals");function normalizeHeader(e){return e&&String(e).trim().toLowerCase()}function normalizeValue(e){return!1===e||null==e?e:utils$4.isArray(e)?e.map(normalizeValue):String(e)}function parseTokens(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let i;for(;i=r.exec(e);)t[i[1]]=i[2];return t}const isValidHeaderName=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function matchHeaderValue(e,t,r,i,n){return utils$4.isFunction(i)?i.call(this,t,r):(n&&(t=r),utils$4.isString(t)?utils$4.isString(i)?-1!==t.indexOf(i):utils$4.isRegExp(i)?i.test(t):void 0:void 0)}function formatHeader(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,r)=>t.toUpperCase()+r))}function buildAccessors(e,t){const r=utils$4.toCamelCase(" "+t);["get","set","has"].forEach((i=>{Object.defineProperty(e,i+r,{value:function(e,r,n){return this[i].call(this,t,e,r,n)},configurable:!0})}))}class AxiosHeaders{constructor(e){e&&this.set(e)}set(e,t,r){const i=this;function n(e,t,r){const n=normalizeHeader(t);if(!n)throw new Error("header name must be a non-empty string");const o=utils$4.findKey(i,n);(!o||void 0===i[o]||!0===r||void 0===r&&!1!==i[o])&&(i[o||t]=normalizeValue(e))}const o=(e,t)=>utils$4.forEach(e,((e,r)=>n(e,r,t)));return utils$4.isPlainObject(e)||e instanceof this.constructor?o(e,t):utils$4.isString(e)&&(e=e.trim())&&!isValidHeaderName(e)?o(parseHeaders(e),t):null!=e&&n(t,e,r),this}get(e,t){if(e=normalizeHeader(e)){const r=utils$4.findKey(this,e);if(r){const e=this[r];if(!t)return e;if(!0===t)return parseTokens(e);if(utils$4.isFunction(t))return t.call(this,e,r);if(utils$4.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=normalizeHeader(e)){const r=utils$4.findKey(this,e);return!(!r||void 0===this[r]||t&&!matchHeaderValue(this,this[r],r,t))}return!1}delete(e,t){const r=this;let i=!1;function n(e){if(e=normalizeHeader(e)){const n=utils$4.findKey(r,e);!n||t&&!matchHeaderValue(r,r[n],n,t)||(delete r[n],i=!0)}}return utils$4.isArray(e)?e.forEach(n):n(e),i}clear(e){const t=Object.keys(this);let r=t.length,i=!1;for(;r--;){const n=t[r];e&&!matchHeaderValue(this,this[n],n,e,!0)||(delete this[n],i=!0)}return i}normalize(e){const t=this,r={};return utils$4.forEach(this,((i,n)=>{const o=utils$4.findKey(r,n);if(o)return t[o]=normalizeValue(i),void delete t[n];const s=e?formatHeader(n):String(n).trim();s!==n&&delete t[n],t[s]=normalizeValue(i),r[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return utils$4.forEach(this,((r,i)=>{null!=r&&!1!==r&&(t[i]=e&&utils$4.isArray(r)?r.join(", "):r)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const r=new this(e);return t.forEach((e=>r.set(e))),r}static accessor(e){const t=(this[$internals]=this[$internals]={accessors:{}}).accessors,r=this.prototype;function i(e){const i=normalizeHeader(e);t[i]||(buildAccessors(r,e),t[i]=!0)}return utils$4.isArray(e)?e.forEach(i):i(e),this}}AxiosHeaders.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),utils$4.reduceDescriptors(AxiosHeaders.prototype,(({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}})),utils$4.freezeMethods(AxiosHeaders);var AxiosHeaders$1=AxiosHeaders;function transformData(e,t){const r=this||defaults$1,i=t||r,n=AxiosHeaders$1.from(i.headers);let o=i.data;return utils$4.forEach(e,(function(e){o=e.call(r,o,n.normalize(),t?t.status:void 0)})),n.normalize(),o}function isCancel(e){return!(!e||!e.__CANCEL__)}function CanceledError(e,t,r){AxiosError.call(this,null==e?"canceled":e,AxiosError.ERR_CANCELED,t,r),this.name="CanceledError"}function settle$2(e,t,r){const i=r.config.validateStatus;r.status&&i&&!i(r.status)?t(new AxiosError("Request failed with status code "+r.status,[AxiosError.ERR_BAD_REQUEST,AxiosError.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):e(r)}utils$4.inherits(CanceledError,AxiosError,{__CANCEL__:!0});var cookies=platform.hasStandardBrowserEnv?{write(e,t,r,i,n,o){const s=[e+"="+encodeURIComponent(t)];utils$4.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),utils$4.isString(i)&&s.push("path="+i),utils$4.isString(n)&&s.push("domain="+n),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function isAbsoluteURL$2(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function combineURLs$2(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function buildFullPath$2(e,t){return e&&!isAbsoluteURL$2(t)?combineURLs$2(e,t):t}var isURLSameOrigin=platform.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let r;function i(r){let i=r;return e&&(t.setAttribute("href",i),i=t.href),t.setAttribute("href",i),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return r=i(window.location.href),function(e){const t=utils$4.isString(e)?i(e):e;return t.protocol===r.protocol&&t.host===r.host}}():function(){return!0};function parseProtocol(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function speedometer(e,t){e=e||10;const r=new Array(e),i=new Array(e);let n,o=0,s=0;return t=void 0!==t?t:1e3,function(a){const u=Date.now(),c=i[s];n||(n=u),r[o]=a,i[o]=u;let l=s,f=0;for(;l!==o;)f+=r[l++],l%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-n<t)return;const h=c&&u-c;return h?Math.round(1e3*f/h):void 0}}function progressEventReducer(e,t){let r=0;const i=speedometer(50,250);return n=>{const o=n.loaded,s=n.lengthComputable?n.total:void 0,a=o-r,u=i(a);r=o;const c={loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:u||void 0,estimated:u&&s&&o<=s?(s-o)/u:void 0,event:n};c[t?"download":"upload"]=!0,e(c)}}const isXHRAdapterSupported="undefined"!=typeof XMLHttpRequest;var xhrAdapter=isXHRAdapterSupported&&function(e){return new Promise((function(t,r){let i=e.data;const n=AxiosHeaders$1.from(e.headers).normalize();let o,s,{responseType:a,withXSRFToken:u}=e;function c(){e.cancelToken&&e.cancelToken.unsubscribe(o),e.signal&&e.signal.removeEventListener("abort",o)}if(utils$4.isFormData(i))if(platform.hasStandardBrowserEnv||platform.hasStandardBrowserWebWorkerEnv)n.setContentType(!1);else if(!1!==(s=n.getContentType())){const[e,...t]=s?s.split(";").map((e=>e.trim())).filter(Boolean):[];n.setContentType([e||"multipart/form-data",...t].join("; "))}let l=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",r=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";n.set("Authorization","Basic "+btoa(t+":"+r))}const f=buildFullPath$2(e.baseURL,e.url);function h(){if(!l)return;const i=AxiosHeaders$1.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders());settle$2((function(e){t(e),c()}),(function(e){r(e),c()}),{data:a&&"text"!==a&&"json"!==a?l.response:l.responseText,status:l.status,statusText:l.statusText,headers:i,config:e,request:l}),l=null}if(l.open(e.method.toUpperCase(),buildURL$2(f,e.params,e.paramsSerializer),!0),l.timeout=e.timeout,"onloadend"in l?l.onloadend=h:l.onreadystatechange=function(){l&&4===l.readyState&&(0!==l.status||l.responseURL&&0===l.responseURL.indexOf("file:"))&&setTimeout(h)},l.onabort=function(){l&&(r(new AxiosError("Request aborted",AxiosError.ECONNABORTED,e,l)),l=null)},l.onerror=function(){r(new AxiosError("Network Error",AxiosError.ERR_NETWORK,e,l)),l=null},l.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const i=e.transitional||transitionalDefaults;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(new AxiosError(t,i.clarifyTimeoutError?AxiosError.ETIMEDOUT:AxiosError.ECONNABORTED,e,l)),l=null},platform.hasStandardBrowserEnv&&(u&&utils$4.isFunction(u)&&(u=u(e)),u||!1!==u&&isURLSameOrigin(f))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&cookies.read(e.xsrfCookieName);t&&n.set(e.xsrfHeaderName,t)}void 0===i&&n.setContentType(null),"setRequestHeader"in l&&utils$4.forEach(n.toJSON(),(function(e,t){l.setRequestHeader(t,e)})),utils$4.isUndefined(e.withCredentials)||(l.withCredentials=!!e.withCredentials),a&&"json"!==a&&(l.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&l.addEventListener("progress",progressEventReducer(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&l.upload&&l.upload.addEventListener("progress",progressEventReducer(e.onUploadProgress)),(e.cancelToken||e.signal)&&(o=t=>{l&&(r(!t||t.type?new CanceledError(null,e,l):t),l.abort(),l=null)},e.cancelToken&&e.cancelToken.subscribe(o),e.signal&&(e.signal.aborted?o():e.signal.addEventListener("abort",o)));const p=parseProtocol(f);p&&-1===platform.protocols.indexOf(p)?r(new AxiosError("Unsupported protocol "+p+":",AxiosError.ERR_BAD_REQUEST,e)):l.send(i||null)}))};const knownAdapters={http:httpAdapter,xhr:xhrAdapter};utils$4.forEach(knownAdapters,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const renderReason=e=>`- ${e}`,isResolvedHandle=e=>utils$4.isFunction(e)||null===e||!1===e;var adapters={getAdapter:e=>{e=utils$4.isArray(e)?e:[e];const{length:t}=e;let r,i;const n={};for(let o=0;o<t;o++){let t;if(r=e[o],i=r,!isResolvedHandle(r)&&(i=knownAdapters[(t=String(r)).toLowerCase()],void 0===i))throw new AxiosError(`Unknown adapter '${t}'`);if(i)break;n[t||"#"+o]=i}if(!i){const e=Object.entries(n).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new AxiosError("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(renderReason).join("\n"):" "+renderReason(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i},adapters:knownAdapters};function throwIfCancellationRequested(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new CanceledError(null,e)}function dispatchRequest(e){throwIfCancellationRequested(e),e.headers=AxiosHeaders$1.from(e.headers),e.data=transformData.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return adapters.getAdapter(e.adapter||defaults$1.adapter)(e).then((function(t){return throwIfCancellationRequested(e),t.data=transformData.call(e,e.transformResponse,t),t.headers=AxiosHeaders$1.from(t.headers),t}),(function(t){return isCancel(t)||(throwIfCancellationRequested(e),t&&t.response&&(t.response.data=transformData.call(e,e.transformResponse,t.response),t.response.headers=AxiosHeaders$1.from(t.response.headers))),Promise.reject(t)}))}const headersToObject=e=>e instanceof AxiosHeaders$1?{...e}:e;function mergeConfig(e,t){t=t||{};const r={};function i(e,t,r){return utils$4.isPlainObject(e)&&utils$4.isPlainObject(t)?utils$4.merge.call({caseless:r},e,t):utils$4.isPlainObject(t)?utils$4.merge({},t):utils$4.isArray(t)?t.slice():t}function n(e,t,r){return utils$4.isUndefined(t)?utils$4.isUndefined(e)?void 0:i(void 0,e,r):i(e,t,r)}function o(e,t){if(!utils$4.isUndefined(t))return i(void 0,t)}function s(e,t){return utils$4.isUndefined(t)?utils$4.isUndefined(e)?void 0:i(void 0,e):i(void 0,t)}function a(r,n,o){return o in t?i(r,n):o in e?i(void 0,r):void 0}const u={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t)=>n(headersToObject(e),headersToObject(t),!0)};return utils$4.forEach(Object.keys(Object.assign({},e,t)),(function(i){const o=u[i]||n,s=o(e[i],t[i],i);utils$4.isUndefined(s)&&o!==a||(r[i]=s)})),r}const VERSION="1.6.8",validators$1={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{validators$1[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));const deprecatedWarnings={};function assertOptions(e,t,r){if("object"!=typeof e)throw new AxiosError("options must be an object",AxiosError.ERR_BAD_OPTION_VALUE);const i=Object.keys(e);let n=i.length;for(;n-- >0;){const o=i[n],s=t[o];if(s){const t=e[o],r=void 0===t||s(t,o,e);if(!0!==r)throw new AxiosError("option "+o+" must be "+r,AxiosError.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new AxiosError("Unknown option "+o,AxiosError.ERR_BAD_OPTION)}}validators$1.transitional=function(e,t,r){function i(e,t){return"[Axios v"+VERSION+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,n,o)=>{if(!1===e)throw new AxiosError(i(n," has been removed"+(t?" in "+t:"")),AxiosError.ERR_DEPRECATED);return t&&!deprecatedWarnings[n]&&(deprecatedWarnings[n]=!0,console.warn(i(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,o)}};var validator={assertOptions:assertOptions,validators:validators$1};const validators=validator.validators;class Axios{constructor(e){this.defaults=e,this.interceptors={request:new InterceptorManager,response:new InterceptorManager}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t;Error.captureStackTrace?Error.captureStackTrace(t={}):t=new Error;const r=t.stack?t.stack.replace(/^.+\n/,""):"";e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=mergeConfig(this.defaults,t);const{transitional:r,paramsSerializer:i,headers:n}=t;void 0!==r&&validator.assertOptions(r,{silentJSONParsing:validators.transitional(validators.boolean),forcedJSONParsing:validators.transitional(validators.boolean),clarifyTimeoutError:validators.transitional(validators.boolean)},!1),null!=i&&(utils$4.isFunction(i)?t.paramsSerializer={serialize:i}:validator.assertOptions(i,{encode:validators.function,serialize:validators.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=n&&utils$4.merge(n.common,n[t.method]);n&&utils$4.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete n[e]})),t.headers=AxiosHeaders$1.concat(o,n);const s=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const u=[];let c;this.interceptors.response.forEach((function(e){u.push(e.fulfilled,e.rejected)}));let l,f=0;if(!a){const e=[dispatchRequest.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,u),l=e.length,c=Promise.resolve(t);f<l;)c=c.then(e[f++],e[f++]);return c}l=s.length;let h=t;for(f=0;f<l;){const e=s[f++],t=s[f++];try{h=e(h)}catch(e){t.call(this,e);break}}try{c=dispatchRequest.call(this,h)}catch(e){return Promise.reject(e)}for(f=0,l=u.length;f<l;)c=c.then(u[f++],u[f++]);return c}getUri(e){return buildURL$2(buildFullPath$2((e=mergeConfig(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}utils$4.forEach(["delete","get","head","options"],(function(e){Axios.prototype[e]=function(t,r){return this.request(mergeConfig(r||{},{method:e,url:t,data:(r||{}).data}))}})),utils$4.forEach(["post","put","patch"],(function(e){function t(t){return function(r,i,n){return this.request(mergeConfig(n||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:i}))}}Axios.prototype[e]=t(),Axios.prototype[e+"Form"]=t(!0)}));var Axios$1=Axios;class CancelToken{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const r=this;this.promise.then((e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null})),this.promise.then=e=>{let t;const i=new Promise((e=>{r.subscribe(e),t=e})).then(e);return i.cancel=function(){r.unsubscribe(t)},i},e((function(e,i,n){r.reason||(r.reason=new CanceledError(e,i,n),t(r.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new CancelToken((function(t){e=t})),cancel:e}}}var CancelToken$1=CancelToken;function spread(e){return function(t){return e.apply(null,t)}}function isAxiosError(e){return utils$4.isObject(e)&&!0===e.isAxiosError}const HttpStatusCode={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(HttpStatusCode).forEach((([e,t])=>{HttpStatusCode[t]=e}));var HttpStatusCode$1=HttpStatusCode;function createInstance(e){const t=new Axios$1(e),r=bind$2(Axios$1.prototype.request,t);return utils$4.extend(r,Axios$1.prototype,t,{allOwnKeys:!0}),utils$4.extend(r,t,null,{allOwnKeys:!0}),r.create=function(t){return createInstance(mergeConfig(e,t))},r}const axios=createInstance(defaults$1);axios.Axios=Axios$1,axios.CanceledError=CanceledError,axios.CancelToken=CancelToken$1,axios.isCancel=isCancel,axios.VERSION=VERSION,axios.toFormData=toFormData,axios.AxiosError=AxiosError,axios.Cancel=axios.CanceledError,axios.all=function(e){return Promise.all(e)},axios.spread=spread,axios.isAxiosError=isAxiosError,axios.mergeConfig=mergeConfig,axios.AxiosHeaders=AxiosHeaders$1,axios.formToJSON=e=>formDataToJSON(utils$4.isHTMLForm(e)?new FormData(e):e),axios.getAdapter=adapters.getAdapter,axios.HttpStatusCode=HttpStatusCode$1,axios.default=axios;var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function getAugmentedNamespace(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var i=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,i.get?i:{enumerable:!0,get:function(){return e[t]}})})),r}var bind$1=function(e,t){return function(){for(var r=new Array(arguments.length),i=0;i<r.length;i++)r[i]=arguments[i];return e.apply(t,r)}},bind=bind$1,toString=Object.prototype.toString;function isArray(e){return"[object Array]"===toString.call(e)}function isUndefined(e){return void 0===e}function isBuffer(e){return null!==e&&!isUndefined(e)&&null!==e.constructor&&!isUndefined(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function isArrayBuffer(e){return"[object ArrayBuffer]"===toString.call(e)}function isFormData(e){return"undefined"!=typeof FormData&&e instanceof FormData}function isArrayBufferView(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer}function isString(e){return"string"==typeof e}function isNumber(e){return"number"==typeof e}function isObject(e){return null!==e&&"object"==typeof e}function isDate(e){return"[object Date]"===toString.call(e)}function isFile(e){return"[object File]"===toString.call(e)}function isBlob(e){return"[object Blob]"===toString.call(e)}function isFunction(e){return"[object Function]"===toString.call(e)}function isStream(e){return isObject(e)&&isFunction(e.pipe)}function isURLSearchParams(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams}function trim(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}function isStandardBrowserEnv(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)}function forEach(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),isArray(e))for(var r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function merge(){var e={};function t(t,r){"object"==typeof e[r]&&"object"==typeof t?e[r]=merge(e[r],t):e[r]=t}for(var r=0,i=arguments.length;r<i;r++)forEach(arguments[r],t);return e}function deepMerge(){var e={};function t(t,r){"object"==typeof e[r]&&"object"==typeof t?e[r]=deepMerge(e[r],t):e[r]="object"==typeof t?deepMerge({},t):t}for(var r=0,i=arguments.length;r<i;r++)forEach(arguments[r],t);return e}function extend(e,t,r){return forEach(t,(function(t,i){e[i]=r&&"function"==typeof t?bind(t,r):t})),e}var utils$2={isArray:isArray,isArrayBuffer:isArrayBuffer,isBuffer:isBuffer,isFormData:isFormData,isArrayBufferView:isArrayBufferView,isString:isString,isNumber:isNumber,isObject:isObject,isUndefined:isUndefined,isDate:isDate,isFile:isFile,isBlob:isBlob,isFunction:isFunction,isStream:isStream,isURLSearchParams:isURLSearchParams,isStandardBrowserEnv:isStandardBrowserEnv,forEach:forEach,merge:merge,deepMerge:deepMerge,extend:extend,trim:trim},enhanceError$1=function(e,t,r,i,n){return e.config=t,r&&(e.code=r),e.request=i,e.response=n,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e},enhanceError=enhanceError$1,createError$2=function(e,t,r,i,n){var o=new Error(e);return enhanceError(o,t,r,i,n)},createError$1=createError$2,settle$1=function(e,t,r){var i=r.config.validateStatus;!i||i(r.status)?e(r):t(createError$1("Request failed with status code "+r.status,r.config,null,r.request,r))},utils$1=utils$2;function encode(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var buildURL$1=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(utils$1.isURLSearchParams(t))i=t.toString();else{var n=[];utils$1.forEach(t,(function(e,t){null!=e&&(utils$1.isArray(e)?t+="[]":e=[e],utils$1.forEach(e,(function(e){utils$1.isDate(e)?e=e.toISOString():utils$1.isObject(e)&&(e=JSON.stringify(e)),n.push(encode(t)+"="+encode(e))})))})),i=n.join("&")}if(i){var o=e.indexOf("#");-1!==o&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e},isAbsoluteURL$1=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)},combineURLs$1=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e},isAbsoluteURL=isAbsoluteURL$1,combineURLs=combineURLs$1,buildFullPath$1=function(e,t){return e&&!isAbsoluteURL(t)?combineURLs(e,t):t};
/*!
   * axios-miniprogram-adapter 0.3.5 (https://github.com/bigMeow/axios-miniprogram-adapter)
   * API https://github.com/bigMeow/axios-miniprogram-adapter/blob/master/doc/api.md
   * Copyright 2018-2022 bigMeow. All Rights Reserved
   * Licensed under MIT (https://github.com/bigMeow/axios-miniprogram-adapter/blob/master/LICENSE)
   */
function _interopDefault$1(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var utils=_interopDefault$1(utils$2),settle=_interopDefault$1(settle$1),buildURL=_interopDefault$1(buildURL$1),buildFullPath=_interopDefault$1(buildFullPath$1),createError=_interopDefault$1(createError$2),chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function encoder(e){for(var t,r,i=String(e),n=0,o=chars,s="";i.charAt(0|n)||(o="=",n%1);s+=o.charAt(63&t>>8-n%1*8)){if((r=i.charCodeAt(n+=3/4))>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");t=t<<8|r}return s}var platFormName="wechat";function getRequest(){switch(!0){case"object"==typeof wx:return platFormName="wechat",wx.request.bind(wx);case"object"==typeof swan:return platFormName="baidu",swan.request.bind(swan);case"object"==typeof dd:return platFormName="dd",dd.httpRequest.bind(dd);case"object"==typeof my:return platFormName="alipay",(my.request||my.httpRequest).bind(my);default:return wx.request.bind(wx)}}function transformResponse(e,t,r){var i=e.header||e.headers,n=e.statusCode||e.status,o="";return 200===n?o="OK":400===n&&(o="Bad Request"),{data:e.data,status:n,statusText:o,headers:i,config:t,request:r}}function transformError(e,t,r){switch(platFormName){case"wechat":-1!==e.errMsg.indexOf("request:fail abort")?t(createError("Request aborted",r,"ECONNABORTED","")):-1!==e.errMsg.indexOf("timeout")?t(createError("timeout of "+r.timeout+"ms exceeded",r,"ECONNABORTED","")):t(createError("Network Error",r,null,""));break;case"dd":case"alipay":[14,19].includes(e.error)?t(createError("Request aborted",r,"ECONNABORTED","",e)):[13].includes(e.error)?t(createError("timeout of "+r.timeout+"ms exceeded",r,"ECONNABORTED","",e)):t(createError("Network Error",r,null,"",e));break;case"baidu":t(createError("Network Error",r,null,""))}}function transformConfig(e){var t;return["alipay","dd"].includes(platFormName)&&(e.headers=e.header,delete e.header,"dd"===platFormName&&"GET"!==e.method&&"application/json"===(null===(t=e.headers)||void 0===t?void 0:t["Content-Type"])&&"[object Object]"===Object.prototype.toString.call(e.data)&&(e.data=JSON.stringify(e.data))),e}var isJSONstr=function(e){try{return"string"==typeof e&&e.length&&(e=JSON.parse(e))&&"[object Object]"===Object.prototype.toString.call(e)}catch(e){return!1}};function mpAdapter(e,t){var r=(void 0===t?{}:t).transformRequestOption,i=void 0===r?function(e){return e}:r,n=getRequest();return new Promise((function(t,r){var o,s=e.data,a=e.headers,u={method:e.method&&e.method.toUpperCase()||"GET",url:buildURL(buildFullPath(e.baseURL,e.url),e.params,e.paramsSerializer),timeout:e.timeout,success:function(i){var n=transformResponse(i,e,u);settle(t,r,n)},fail:function(t){transformError(t,r,e)},complete:function(){o=void 0}};if(e.auth){var c=[e.auth.username||"",e.auth.password||""],l=c[0],f=c[1];a.Authorization="Basic "+encoder(l+":"+f)}utils.forEach(a,(function(e,t){var r=t.toLowerCase();(void 0===s&&"content-type"===r||"referer"===r)&&delete a[t]})),u.header=a,e.responseType&&(u.responseType=e.responseType),e.cancelToken&&e.cancelToken.promise.then((function(e){o&&(o.abort(),r(e),o=void 0)})),isJSONstr(s)&&(s=JSON.parse(s)),void 0!==s&&(u.data=s),o=n(i(transformConfig(u)))}))}var dist=mpAdapter,mpAdapter$1=getDefaultExportFromCjs(dist),SystemClock=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"offset",0)}return _createClass(e,[{key:"systemClockOffset",get:function(){return this.offset}},{key:"now",value:function(){return Date.now()+(this.offset||0)}},{key:"handleOffset",value:function(e){var t=Date.parse(e);Math.abs(this.now()-t)>=3e4&&(this.offset=t-Date.now())}}]),e}(),systemClock=new SystemClock,cryptoJs={exports:{}};function commonjsRequire(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var core={exports:{}},_nodeResolve_empty={},_nodeResolve_empty$1=Object.freeze({__proto__:null,default:_nodeResolve_empty}),require$$0=getAugmentedNamespace(_nodeResolve_empty$1),hasRequiredCore;function requireCore(){return hasRequiredCore||(hasRequiredCore=1,function(e,t){var r;e.exports=(r=r||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==commonjsGlobal&&commonjsGlobal.crypto&&(r=commonjsGlobal.crypto),!r&&"function"==typeof commonjsRequire)try{r=require$$0}catch(e){}var i=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},n=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),o={},s=o.lib={},a=s.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=s.WordArray=a.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(var o=0;o<n;o++){var s=r[o>>>2]>>>24-o%4*8&255;t[i+o>>>2]|=s<<24-(i+o)%4*8}else for(var a=0;a<n;a+=4)t[i+a>>>2]=r[a>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new u.init(t,e)}}),c=o.enc={},l=c.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var o=t[n>>>2]>>>24-n%4*8&255;i.push((o>>>4).toString(16)),i.push((15&o).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new u.init(r,t/2)}},f=c.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var o=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new u.init(r,t)}},h=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=s.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,i=this._data,n=i.words,o=i.sigBytes,s=this.blockSize,a=o/(4*s),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,l=e.min(4*c,o);if(c){for(var f=0;f<c;f+=s)this._doProcessBlock(n,f);r=n.splice(0,c),i.sigBytes-=l}return new u.init(r,l)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=p.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new d.HMAC.init(e,r).finalize(t)}}});var d=o.algo={};return o}(Math),r)}(core)),core.exports}var x64Core={exports:{}},hasRequiredX64Core;function requireX64Core(){return hasRequiredX64Core||(hasRequiredX64Core=1,function(e,t){var r,i,n,o,s,a,u;e.exports=(u=requireCore(),n=(i=u).lib,o=n.Base,s=n.WordArray,(a=i.x64={}).Word=o.extend({init:function(e,t){this.high=e,this.low=t}}),a.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=t!=r?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],i=0;i<t;i++){var n=e[i];r.push(n.high),r.push(n.low)}return s.create(r,this.sigBytes)},clone:function(){for(var e=o.clone.call(this),t=e.words=this.words.slice(0),r=t.length,i=0;i<r;i++)t[i]=t[i].clone();return e}}),u)}(x64Core)),x64Core.exports}var libTypedarrays={exports:{}},hasRequiredLibTypedarrays;function requireLibTypedarrays(){return hasRequiredLibTypedarrays||(hasRequiredLibTypedarrays=1,function(e,t){var r;e.exports=(r=requireCore(),function(){if("function"==typeof ArrayBuffer){var e=r.lib.WordArray,t=e.init,i=e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var r=e.byteLength,i=[],n=0;n<r;n++)i[n>>>2]|=e[n]<<24-n%4*8;t.call(this,i,r)}else t.apply(this,arguments)};i.prototype=e}}(),r.lib.WordArray)}(libTypedarrays)),libTypedarrays.exports}var encUtf16={exports:{}},hasRequiredEncUtf16;function requireEncUtf16(){return hasRequiredEncUtf16||(hasRequiredEncUtf16=1,function(e,t){var r;e.exports=(r=requireCore(),function(){var e=r,t=e.lib.WordArray,i=e.enc;function n(e){return e<<8&4278255360|e>>>8&16711935}i.Utf16=i.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n+=2){var o=t[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var r=e.length,i=[],n=0;n<r;n++)i[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return t.create(i,2*r)}},i.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],o=0;o<r;o+=2){var s=n(t[o>>>2]>>>16-o%4*8&65535);i.push(String.fromCharCode(s))}return i.join("")},parse:function(e){for(var r=e.length,i=[],o=0;o<r;o++)i[o>>>1]|=n(e.charCodeAt(o)<<16-o%2*16);return t.create(i,2*r)}}}(),r.enc.Utf16)}(encUtf16)),encUtf16.exports}var encBase64={exports:{}},hasRequiredEncBase64;function requireEncBase64(){return hasRequiredEncBase64||(hasRequiredEncBase64=1,function(e,t){var r;e.exports=(r=requireCore(),function(){var e=r,t=e.lib.WordArray;function i(e,r,i){for(var n=[],o=0,s=0;s<r;s++)if(s%4){var a=i[e.charCodeAt(s-1)]<<s%4*2|i[e.charCodeAt(s)]>>>6-s%4*2;n[o>>>2]|=a<<24-o%4*8,o++}return t.create(n,o)}e.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,i=this._map;e.clamp();for(var n=[],o=0;o<r;o+=3)for(var s=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<r;a++)n.push(i.charAt(s>>>6*(3-a)&63));var u=i.charAt(64);if(u)for(;n.length%4;)n.push(u);return n.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return i(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),r.enc.Base64)}(encBase64)),encBase64.exports}var encBase64url={exports:{}},hasRequiredEncBase64url;function requireEncBase64url(){return hasRequiredEncBase64url||(hasRequiredEncBase64url=1,function(e,t){var r;e.exports=(r=requireCore(),function(){var e=r,t=e.lib.WordArray;function i(e,r,i){for(var n=[],o=0,s=0;s<r;s++)if(s%4){var a=i[e.charCodeAt(s-1)]<<s%4*2|i[e.charCodeAt(s)]>>>6-s%4*2;n[o>>>2]|=a<<24-o%4*8,o++}return t.create(n,o)}e.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,i=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var o=[],s=0;s<i;s+=3)for(var a=(r[s>>>2]>>>24-s%4*8&255)<<16|(r[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|r[s+2>>>2]>>>24-(s+2)%4*8&255,u=0;u<4&&s+.75*u<i;u++)o.push(n.charAt(a>>>6*(3-u)&63));var c=n.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,n=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var s=0;s<n.length;s++)o[n.charCodeAt(s)]=s}var a=n.charAt(64);if(a){var u=e.indexOf(a);-1!==u&&(r=u)}return i(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),r.enc.Base64url)}(encBase64url)),encBase64url.exports}var md5={exports:{}},hasRequiredMd5;function requireMd5(){return hasRequiredMd5||(hasRequiredMd5=1,function(e,t){var r;e.exports=(r=requireCore(),function(e){var t=r,i=t.lib,n=i.WordArray,o=i.Hasher,s=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=s.MD5=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var o=this._hash.words,s=e[t+0],u=e[t+1],p=e[t+2],d=e[t+3],g=e[t+4],y=e[t+5],m=e[t+6],v=e[t+7],b=e[t+8],w=e[t+9],_=e[t+10],x=e[t+11],S=e[t+12],$=e[t+13],R=e[t+14],E=e[t+15],O=o[0],A=o[1],k=o[2],C=o[3];O=c(O,A,k,C,s,7,a[0]),C=c(C,O,A,k,u,12,a[1]),k=c(k,C,O,A,p,17,a[2]),A=c(A,k,C,O,d,22,a[3]),O=c(O,A,k,C,g,7,a[4]),C=c(C,O,A,k,y,12,a[5]),k=c(k,C,O,A,m,17,a[6]),A=c(A,k,C,O,v,22,a[7]),O=c(O,A,k,C,b,7,a[8]),C=c(C,O,A,k,w,12,a[9]),k=c(k,C,O,A,_,17,a[10]),A=c(A,k,C,O,x,22,a[11]),O=c(O,A,k,C,S,7,a[12]),C=c(C,O,A,k,$,12,a[13]),k=c(k,C,O,A,R,17,a[14]),O=l(O,A=c(A,k,C,O,E,22,a[15]),k,C,u,5,a[16]),C=l(C,O,A,k,m,9,a[17]),k=l(k,C,O,A,x,14,a[18]),A=l(A,k,C,O,s,20,a[19]),O=l(O,A,k,C,y,5,a[20]),C=l(C,O,A,k,_,9,a[21]),k=l(k,C,O,A,E,14,a[22]),A=l(A,k,C,O,g,20,a[23]),O=l(O,A,k,C,w,5,a[24]),C=l(C,O,A,k,R,9,a[25]),k=l(k,C,O,A,d,14,a[26]),A=l(A,k,C,O,b,20,a[27]),O=l(O,A,k,C,$,5,a[28]),C=l(C,O,A,k,p,9,a[29]),k=l(k,C,O,A,v,14,a[30]),O=f(O,A=l(A,k,C,O,S,20,a[31]),k,C,y,4,a[32]),C=f(C,O,A,k,b,11,a[33]),k=f(k,C,O,A,x,16,a[34]),A=f(A,k,C,O,R,23,a[35]),O=f(O,A,k,C,u,4,a[36]),C=f(C,O,A,k,g,11,a[37]),k=f(k,C,O,A,v,16,a[38]),A=f(A,k,C,O,_,23,a[39]),O=f(O,A,k,C,$,4,a[40]),C=f(C,O,A,k,s,11,a[41]),k=f(k,C,O,A,d,16,a[42]),A=f(A,k,C,O,m,23,a[43]),O=f(O,A,k,C,w,4,a[44]),C=f(C,O,A,k,S,11,a[45]),k=f(k,C,O,A,E,16,a[46]),O=h(O,A=f(A,k,C,O,p,23,a[47]),k,C,s,6,a[48]),C=h(C,O,A,k,v,10,a[49]),k=h(k,C,O,A,R,15,a[50]),A=h(A,k,C,O,y,21,a[51]),O=h(O,A,k,C,S,6,a[52]),C=h(C,O,A,k,d,10,a[53]),k=h(k,C,O,A,_,15,a[54]),A=h(A,k,C,O,u,21,a[55]),O=h(O,A,k,C,b,6,a[56]),C=h(C,O,A,k,E,10,a[57]),k=h(k,C,O,A,m,15,a[58]),A=h(A,k,C,O,$,21,a[59]),O=h(O,A,k,C,g,6,a[60]),C=h(C,O,A,k,x,10,a[61]),k=h(k,C,O,A,p,15,a[62]),A=h(A,k,C,O,w,21,a[63]),o[0]=o[0]+O|0,o[1]=o[1]+A|0,o[2]=o[2]+k|0,o[3]=o[3]+C|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var o=e.floor(i/4294967296),s=i;r[15+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,u=a.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,r,i,n,o,s){var a=e+(t&r|~t&i)+n+s;return(a<<o|a>>>32-o)+t}function l(e,t,r,i,n,o,s){var a=e+(t&i|r&~i)+n+s;return(a<<o|a>>>32-o)+t}function f(e,t,r,i,n,o,s){var a=e+(t^r^i)+n+s;return(a<<o|a>>>32-o)+t}function h(e,t,r,i,n,o,s){var a=e+(r^(t|~i))+n+s;return(a<<o|a>>>32-o)+t}t.MD5=o._createHelper(u),t.HmacMD5=o._createHmacHelper(u)}(Math),r.MD5)}(md5)),md5.exports}var sha1={exports:{}},hasRequiredSha1;function requireSha1(){return hasRequiredSha1||(hasRequiredSha1=1,function(e,t){var r,i,n,o,s,a,u,c;e.exports=(c=requireCore(),i=(r=c).lib,n=i.WordArray,o=i.Hasher,s=r.algo,a=[],u=s.SHA1=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],u=r[4],c=0;c<80;c++){if(c<16)a[c]=0|e[t+c];else{var l=a[c-3]^a[c-8]^a[c-14]^a[c-16];a[c]=l<<1|l>>>31}var f=(i<<5|i>>>27)+u+a[c];f+=c<20?1518500249+(n&o|~n&s):c<40?1859775393+(n^o^s):c<60?(n&o|n&s|o&s)-1894007588:(n^o^s)-899497514,u=s,s=o,o=n<<30|n>>>2,n=i,i=f}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+u|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(i+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=o._createHelper(u),r.HmacSHA1=o._createHmacHelper(u),c.SHA1)}(sha1)),sha1.exports}var sha256={exports:{}},hasRequiredSha256;function requireSha256(){return hasRequiredSha256||(hasRequiredSha256=1,function(e,t){var r;e.exports=(r=requireCore(),function(e){var t=r,i=t.lib,n=i.WordArray,o=i.Hasher,s=t.algo,a=[],u=[];!function(){function t(t){for(var r=e.sqrt(t),i=2;i<=r;i++)if(!(t%i))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var i=2,n=0;n<64;)t(i)&&(n<8&&(a[n]=r(e.pow(i,.5))),u[n]=r(e.pow(i,1/3)),n++),i++}();var c=[],l=s.SHA256=o.extend({_doReset:function(){this._hash=new n.init(a.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],a=r[4],l=r[5],f=r[6],h=r[7],p=0;p<64;p++){if(p<16)c[p]=0|e[t+p];else{var d=c[p-15],g=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,y=c[p-2],m=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;c[p]=g+c[p-7]+m+c[p-16]}var v=i&n^i&o^n&o,b=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),w=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&f)+u[p]+c[p];h=f,f=l,l=a,a=s+w|0,s=o,o=n,n=i,i=w+(b+v)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0,r[5]=r[5]+l|0,r[6]=r[6]+f|0,r[7]=r[7]+h|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=e.floor(i/4294967296),r[15+(n+64>>>9<<4)]=i,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(l),t.HmacSHA256=o._createHmacHelper(l)}(Math),r.SHA256)}(sha256)),sha256.exports}var sha224={exports:{}},hasRequiredSha224;function requireSha224(){return hasRequiredSha224||(hasRequiredSha224=1,function(e,t){var r,i,n,o,s,a;e.exports=(a=requireCore(),requireSha256(),i=(r=a).lib.WordArray,n=r.algo,o=n.SHA256,s=n.SHA224=o.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=4,e}}),r.SHA224=o._createHelper(s),r.HmacSHA224=o._createHmacHelper(s),a.SHA224)}(sha224)),sha224.exports}var sha512={exports:{}},hasRequiredSha512;function requireSha512(){return hasRequiredSha512||(hasRequiredSha512=1,function(e,t){var r;e.exports=(r=requireCore(),requireX64Core(),function(){var e=r,t=e.lib.Hasher,i=e.x64,n=i.Word,o=i.WordArray,s=e.algo;function a(){return n.create.apply(n,arguments)}var u=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=a()}();var l=s.SHA512=t.extend({_doReset:function(){this._hash=new o.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],a=r[4],l=r[5],f=r[6],h=r[7],p=i.high,d=i.low,g=n.high,y=n.low,m=o.high,v=o.low,b=s.high,w=s.low,_=a.high,x=a.low,S=l.high,$=l.low,R=f.high,E=f.low,O=h.high,A=h.low,k=p,C=d,B=g,q=y,L=m,T=v,P=b,j=w,F=_,D=x,N=S,H=$,U=R,z=E,M=O,I=A,W=0;W<80;W++){var G,K,V=c[W];if(W<16)K=V.high=0|e[t+2*W],G=V.low=0|e[t+2*W+1];else{var J=c[W-15],X=J.high,Z=J.low,Q=(X>>>1|Z<<31)^(X>>>8|Z<<24)^X>>>7,Y=(Z>>>1|X<<31)^(Z>>>8|X<<24)^(Z>>>7|X<<25),ee=c[W-2],te=ee.high,re=ee.low,ie=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ne=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=c[W-7],se=oe.high,ae=oe.low,ue=c[W-16],ce=ue.high,le=ue.low;K=(K=(K=Q+se+((G=Y+ae)>>>0<Y>>>0?1:0))+ie+((G+=ne)>>>0<ne>>>0?1:0))+ce+((G+=le)>>>0<le>>>0?1:0),V.high=K,V.low=G}var fe,he=F&N^~F&U,pe=D&H^~D&z,de=k&B^k&L^B&L,ge=C&q^C&T^q&T,ye=(k>>>28|C<<4)^(k<<30|C>>>2)^(k<<25|C>>>7),me=(C>>>28|k<<4)^(C<<30|k>>>2)^(C<<25|k>>>7),ve=(F>>>14|D<<18)^(F>>>18|D<<14)^(F<<23|D>>>9),be=(D>>>14|F<<18)^(D>>>18|F<<14)^(D<<23|F>>>9),we=u[W],_e=we.high,xe=we.low,Se=M+ve+((fe=I+be)>>>0<I>>>0?1:0),$e=me+ge;M=U,I=z,U=N,z=H,N=F,H=D,F=P+(Se=(Se=(Se=Se+he+((fe+=pe)>>>0<pe>>>0?1:0))+_e+((fe+=xe)>>>0<xe>>>0?1:0))+K+((fe+=G)>>>0<G>>>0?1:0))+((D=j+fe|0)>>>0<j>>>0?1:0)|0,P=L,j=T,L=B,T=q,B=k,q=C,k=Se+(ye+de+($e>>>0<me>>>0?1:0))+((C=fe+$e|0)>>>0<fe>>>0?1:0)|0}d=i.low=d+C,i.high=p+k+(d>>>0<C>>>0?1:0),y=n.low=y+q,n.high=g+B+(y>>>0<q>>>0?1:0),v=o.low=v+T,o.high=m+L+(v>>>0<T>>>0?1:0),w=s.low=w+j,s.high=b+P+(w>>>0<j>>>0?1:0),x=a.low=x+D,a.high=_+F+(x>>>0<D>>>0?1:0),$=l.low=$+H,l.high=S+N+($>>>0<H>>>0?1:0),E=f.low=E+z,f.high=R+U+(E>>>0<z>>>0?1:0),A=h.low=A+I,h.high=O+M+(A>>>0<I>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[30+(i+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(i+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(l),e.HmacSHA512=t._createHmacHelper(l)}(),r.SHA512)}(sha512)),sha512.exports}var sha384={exports:{}},hasRequiredSha384;function requireSha384(){return hasRequiredSha384||(hasRequiredSha384=1,function(e,t){var r,i,n,o,s,a,u,c;e.exports=(c=requireCore(),requireX64Core(),requireSha512(),i=(r=c).x64,n=i.Word,o=i.WordArray,s=r.algo,a=s.SHA512,u=s.SHA384=a.extend({_doReset:function(){this._hash=new o.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=16,e}}),r.SHA384=a._createHelper(u),r.HmacSHA384=a._createHmacHelper(u),c.SHA384)}(sha384)),sha384.exports}var sha3={exports:{}},hasRequiredSha3;function requireSha3(){return hasRequiredSha3||(hasRequiredSha3=1,function(e,t){var r;e.exports=(r=requireCore(),requireX64Core(),function(e){var t=r,i=t.lib,n=i.WordArray,o=i.Hasher,s=t.x64.Word,a=t.algo,u=[],c=[],l=[];!function(){for(var e=1,t=0,r=0;r<24;r++){u[e+5*t]=(r+1)*(r+2)/2%64;var i=(2*e+3*t)%5;e=t%5,t=i}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var n=1,o=0;o<24;o++){for(var a=0,f=0,h=0;h<7;h++){if(1&n){var p=(1<<h)-1;p<32?f^=1<<p:a^=1<<p-32}128&n?n=n<<1^113:n<<=1}l[o]=s.create(a,f)}}();var f=[];!function(){for(var e=0;e<25;e++)f[e]=s.create()}();var h=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var o=e[t+2*n],s=e[t+2*n+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(A=r[n]).high^=s,A.low^=o}for(var a=0;a<24;a++){for(var h=0;h<5;h++){for(var p=0,d=0,g=0;g<5;g++)p^=(A=r[h+5*g]).high,d^=A.low;var y=f[h];y.high=p,y.low=d}for(h=0;h<5;h++){var m=f[(h+4)%5],v=f[(h+1)%5],b=v.high,w=v.low;for(p=m.high^(b<<1|w>>>31),d=m.low^(w<<1|b>>>31),g=0;g<5;g++)(A=r[h+5*g]).high^=p,A.low^=d}for(var _=1;_<25;_++){var x=(A=r[_]).high,S=A.low,$=u[_];$<32?(p=x<<$|S>>>32-$,d=S<<$|x>>>32-$):(p=S<<$-32|x>>>64-$,d=x<<$-32|S>>>64-$);var R=f[c[_]];R.high=p,R.low=d}var E=f[0],O=r[0];for(E.high=O.high,E.low=O.low,h=0;h<5;h++)for(g=0;g<5;g++){var A=r[_=h+5*g],k=f[_],C=f[(h+1)%5+5*g],B=f[(h+2)%5+5*g];A.high=k.high^~C.high&B.high,A.low=k.low^~C.low&B.low}A=r[0];var q=l[a];A.high^=q.high,A.low^=q.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var i=8*t.sigBytes,o=32*this.blockSize;r[i>>>5]|=1<<24-i%32,r[(e.ceil((i+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,u=a/8,c=[],l=0;l<u;l++){var f=s[l],h=f.high,p=f.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),c.push(p),c.push(h)}return new n.init(c,a)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=o._createHelper(h),t.HmacSHA3=o._createHmacHelper(h)}(Math),r.SHA3)}(sha3)),sha3.exports}var ripemd160={exports:{}},hasRequiredRipemd160;function requireRipemd160(){return hasRequiredRipemd160||(hasRequiredRipemd160=1,function(e,t){var r;e.exports=(r=requireCore(),
/** @preserve
  			(c) 2012 by Cédric Mesnil. All rights reserved.

  			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

  			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
  			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

  			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  			*/
function(e){var t=r,i=t.lib,n=i.WordArray,o=i.Hasher,s=t.algo,a=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),u=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),f=n.create([0,1518500249,1859775393,2400959708,2840853838]),h=n.create([1352829926,1548603684,1836072691,2053994217,0]),p=s.RIPEMD160=o.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var o,s,p,w,_,x,S,$,R,E,O,A=this._hash.words,k=f.words,C=h.words,B=a.words,q=u.words,L=c.words,T=l.words;for(x=o=A[0],S=s=A[1],$=p=A[2],R=w=A[3],E=_=A[4],r=0;r<80;r+=1)O=o+e[t+B[r]]|0,O+=r<16?d(s,p,w)+k[0]:r<32?g(s,p,w)+k[1]:r<48?y(s,p,w)+k[2]:r<64?m(s,p,w)+k[3]:v(s,p,w)+k[4],O=(O=b(O|=0,L[r]))+_|0,o=_,_=w,w=b(p,10),p=s,s=O,O=x+e[t+q[r]]|0,O+=r<16?v(S,$,R)+C[0]:r<32?m(S,$,R)+C[1]:r<48?y(S,$,R)+C[2]:r<64?g(S,$,R)+C[3]:d(S,$,R)+C[4],O=(O=b(O|=0,T[r]))+E|0,x=E,E=R,R=b($,10),$=S,S=O;O=A[1]+p+R|0,A[1]=A[2]+w+E|0,A[2]=A[3]+_+x|0,A[3]=A[4]+o+S|0,A[4]=A[0]+s+$|0,A[0]=O},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,o=n.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return n},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function d(e,t,r){return e^t^r}function g(e,t,r){return e&t|~e&r}function y(e,t,r){return(e|~t)^r}function m(e,t,r){return e&r|t&~r}function v(e,t,r){return e^(t|~r)}function b(e,t){return e<<t|e>>>32-t}t.RIPEMD160=o._createHelper(p),t.HmacRIPEMD160=o._createHmacHelper(p)}(),r.RIPEMD160)}(ripemd160)),ripemd160.exports}var hmac={exports:{}},hasRequiredHmac;function requireHmac(){return hasRequiredHmac||(hasRequiredHmac=1,function(e,t){var r,i,n,o;e.exports=(r=requireCore(),n=(i=r).lib.Base,o=i.enc.Utf8,void(i.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var r=e.blockSize,i=4*r;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),s=this._iKey=t.clone(),a=n.words,u=s.words,c=0;c<r;c++)a[c]^=1549556828,u[c]^=909522486;n.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})))}(hmac)),hmac.exports}var pbkdf2={exports:{}},hasRequiredPbkdf2;function requirePbkdf2(){return hasRequiredPbkdf2||(hasRequiredPbkdf2=1,function(e,t){var r,i,n,o,s,a,u,c,l;e.exports=(l=requireCore(),requireSha256(),requireHmac(),i=(r=l).lib,n=i.Base,o=i.WordArray,s=r.algo,a=s.SHA256,u=s.HMAC,c=s.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:a,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=u.create(r.hasher,e),n=o.create(),s=o.create([1]),a=n.words,c=s.words,l=r.keySize,f=r.iterations;a.length<l;){var h=i.update(t).finalize(s);i.reset();for(var p=h.words,d=p.length,g=h,y=1;y<f;y++){g=i.finalize(g),i.reset();for(var m=g.words,v=0;v<d;v++)p[v]^=m[v]}n.concat(h),c[0]++}return n.sigBytes=4*l,n}}),r.PBKDF2=function(e,t,r){return c.create(r).compute(e,t)},l.PBKDF2)}(pbkdf2)),pbkdf2.exports}var evpkdf={exports:{}},hasRequiredEvpkdf;function requireEvpkdf(){return hasRequiredEvpkdf||(hasRequiredEvpkdf=1,function(e,t){var r,i,n,o,s,a,u,c;e.exports=(c=requireCore(),requireSha1(),requireHmac(),i=(r=c).lib,n=i.Base,o=i.WordArray,s=r.algo,a=s.MD5,u=s.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,i=this.cfg,n=i.hasher.create(),s=o.create(),a=s.words,u=i.keySize,c=i.iterations;a.length<u;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(var l=1;l<c;l++)r=n.finalize(r),n.reset();s.concat(r)}return s.sigBytes=4*u,s}}),r.EvpKDF=function(e,t,r){return u.create(r).compute(e,t)},c.EvpKDF)}(evpkdf)),evpkdf.exports}var cipherCore={exports:{}},hasRequiredCipherCore;function requireCipherCore(){return hasRequiredCipherCore||(hasRequiredCipherCore=1,function(e,t){var r;e.exports=(r=requireCore(),requireEvpkdf(),void(r.lib.Cipher||function(e){var t=r,i=t.lib,n=i.Base,o=i.WordArray,s=i.BufferedBlockAlgorithm,a=t.enc;a.Utf8;var u=a.Base64,c=t.algo.EvpKDF,l=i.Cipher=s.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:m}return function(t){return{encrypt:function(r,i,n){return e(i).encrypt(t,r,i,n)},decrypt:function(r,i,n){return e(i).decrypt(t,r,i,n)}}}}()});i.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var f=t.mode={},h=i.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=f.CBC=function(){var t=h.extend();function r(t,r,i){var n,o=this._iv;o?(n=o,this._iv=e):n=this._prevBlock;for(var s=0;s<i;s++)t[r+s]^=n[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var i=this._cipher,n=i.blockSize;r.call(this,e,t,n),i.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}),t.Decryptor=t.extend({processBlock:function(e,t){var i=this._cipher,n=i.blockSize,o=e.slice(t,t+n);i.decryptBlock(e,t),r.call(this,e,t,n),this._prevBlock=o}}),t}(),d=(t.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,i=r-e.sigBytes%r,n=i<<24|i<<16|i<<8|i,s=[],a=0;a<i;a+=4)s.push(n);var u=o.create(s,i);e.concat(u)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};i.BlockCipher=l.extend({cfg:l.cfg.extend({mode:p,padding:d}),reset:function(){var e;l.reset.call(this);var t=this.cfg,r=t.iv,i=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(i,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var g=i.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),y=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?o.create([1398893684,1701076831]).concat(r).concat(t):t).toString(u)},parse:function(e){var t,r=u.parse(e),i=r.words;return 1398893684==i[0]&&1701076831==i[1]&&(t=o.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:t})}},m=i.SerializableCipher=n.extend({cfg:n.extend({format:y}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=e.createEncryptor(r,i),o=n.finalize(t),s=n.cfg;return g.create({ciphertext:o,key:r,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,r,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(r,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),v=(t.kdf={}).OpenSSL={execute:function(e,t,r,i,n){if(i||(i=o.random(8)),n)s=c.create({keySize:t+r,hasher:n}).compute(e,i);else var s=c.create({keySize:t+r}).compute(e,i);var a=o.create(s.words.slice(t),4*r);return s.sigBytes=4*t,g.create({key:s,iv:a,salt:i})}},b=i.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:v}),encrypt:function(e,t,r,i){var n=(i=this.cfg.extend(i)).kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;var o=m.encrypt.call(this,e,t,n.key,i);return o.mixIn(n),o},decrypt:function(e,t,r,i){i=this.cfg.extend(i),t=this._parse(t,i.format);var n=i.kdf.execute(r,e.keySize,e.ivSize,t.salt,i.hasher);return i.iv=n.iv,m.decrypt.call(this,e,t,n.key,i)}})}()))}(cipherCore)),cipherCore.exports}var modeCfb={exports:{}},hasRequiredModeCfb;function requireModeCfb(){return hasRequiredModeCfb||(hasRequiredModeCfb=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,r,i){var n,o=this._iv;o?(n=o.slice(0),this._iv=void 0):n=this._prevBlock,i.encryptBlock(n,0);for(var s=0;s<r;s++)e[t+s]^=n[s]}return e.Encryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize;t.call(this,e,r,n,i),this._prevBlock=e.slice(r,r+n)}}),e.Decryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize,o=e.slice(r,r+n);t.call(this,e,r,n,i),this._prevBlock=o}}),e}(),r.mode.CFB)}(modeCfb)),modeCfb.exports}var modeCtr={exports:{}},hasRequiredModeCtr;function requireModeCtr(){return hasRequiredModeCtr||(hasRequiredModeCtr=1,function(e,t){var r,i,n;e.exports=(n=requireCore(),requireCipherCore(),n.mode.CTR=(r=n.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._counter;n&&(o=this._counter=n.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[i-1]=o[i-1]+1|0;for(var a=0;a<i;a++)e[t+a]^=s[a]}}),r.Decryptor=i,r),n.mode.CTR)}(modeCtr)),modeCtr.exports}var modeCtrGladman={exports:{}},hasRequiredModeCtrGladman;function requireModeCtrGladman(){return hasRequiredModeCtrGladman||(hasRequiredModeCtrGladman=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),
/** @preserve
  			 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
  			 * derived from CryptoJS.mode.CTR
  			 * <NAME_EMAIL>
  			 */
r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if(255&~(e>>24))e+=1<<24;else{var t=e>>16&255,r=e>>8&255,i=255&e;255===t?(t=0,255===r?(r=0,255===i?i=0:++i):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=i}return e}function i(e){return 0===(e[0]=t(e[0]))&&(e[1]=t(e[1])),e}var n=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),i(s);var a=s.slice(0);r.encryptBlock(a,0);for(var u=0;u<n;u++)e[t+u]^=a[u]}});return e.Decryptor=n,e}(),r.mode.CTRGladman)}(modeCtrGladman)),modeCtrGladman.exports}var modeOfb={exports:{}},hasRequiredModeOfb;function requireModeOfb(){return hasRequiredModeOfb||(hasRequiredModeOfb=1,function(e,t){var r,i,n;e.exports=(n=requireCore(),requireCipherCore(),n.mode.OFB=(r=n.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._keystream;n&&(o=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<i;s++)e[t+s]^=o[s]}}),r.Decryptor=i,r),n.mode.OFB)}(modeOfb)),modeOfb.exports}var modeEcb={exports:{}},hasRequiredModeEcb;function requireModeEcb(){return hasRequiredModeEcb||(hasRequiredModeEcb=1,function(e,t){var r,i;e.exports=(i=requireCore(),requireCipherCore(),i.mode.ECB=((r=i.lib.BlockCipherMode.extend()).Encryptor=r.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),r.Decryptor=r.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),r),i.mode.ECB)}(modeEcb)),modeEcb.exports}var padAnsix923={exports:{}},hasRequiredPadAnsix923;function requirePadAnsix923(){return hasRequiredPadAnsix923||(hasRequiredPadAnsix923=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,i=4*t,n=i-r%i,o=r+n-1;e.clamp(),e.words[o>>>2]|=n<<24-o%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Ansix923)}(padAnsix923)),padAnsix923.exports}var padIso10126={exports:{}},hasRequiredPadIso10126;function requirePadIso10126(){return hasRequiredPadIso10126||(hasRequiredPadIso10126=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.pad.Iso10126={pad:function(e,t){var i=4*t,n=i-e.sigBytes%i;e.concat(r.lib.WordArray.random(n-1)).concat(r.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Iso10126)}(padIso10126)),padIso10126.exports}var padIso97971={exports:{}},hasRequiredPadIso97971;function requirePadIso97971(){return hasRequiredPadIso97971||(hasRequiredPadIso97971=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971)}(padIso97971)),padIso97971.exports}var padZeropadding={exports:{}},hasRequiredPadZeropadding;function requirePadZeropadding(){return hasRequiredPadZeropadding||(hasRequiredPadZeropadding=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},r.pad.ZeroPadding)}(padZeropadding)),padZeropadding.exports}var padNopadding={exports:{}},hasRequiredPadNopadding;function requirePadNopadding(){return hasRequiredPadNopadding||(hasRequiredPadNopadding=1,function(e,t){var r;e.exports=(r=requireCore(),requireCipherCore(),r.pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding)}(padNopadding)),padNopadding.exports}var formatHex={exports:{}},hasRequiredFormatHex;function requireFormatHex(){return hasRequiredFormatHex||(hasRequiredFormatHex=1,function(e,t){var r,i,n,o;e.exports=(o=requireCore(),requireCipherCore(),i=(r=o).lib.CipherParams,n=r.enc.Hex,r.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var t=n.parse(e);return i.create({ciphertext:t})}},o.format.Hex)}(formatHex)),formatHex.exports}var aes={exports:{}},hasRequiredAes;function requireAes(){return hasRequiredAes||(hasRequiredAes=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib.BlockCipher,i=e.algo,n=[],o=[],s=[],a=[],u=[],c=[],l=[],f=[],h=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,i=0;for(t=0;t<256;t++){var d=i^i<<1^i<<2^i<<3^i<<4;d=d>>>8^255&d^99,n[r]=d,o[d]=r;var g=e[r],y=e[g],m=e[y],v=257*e[d]^16843008*d;s[r]=v<<24|v>>>8,a[r]=v<<16|v>>>16,u[r]=v<<8|v>>>24,c[r]=v,v=16843009*m^65537*y^257*g^16843008*r,l[d]=v<<24|v>>>8,f[d]=v<<16|v>>>16,h[d]=v<<8|v>>>24,p[d]=v,r?(r=g^e[e[e[m^g]]],i^=e[e[i]]):r=i=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],g=i.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,i=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],s=0;s<i;s++)s<r?o[s]=t[s]:(c=o[s-1],s%r?r>6&&s%r==4&&(c=n[c>>>24]<<24|n[c>>>16&255]<<16|n[c>>>8&255]<<8|n[255&c]):(c=n[(c=c<<8|c>>>24)>>>24]<<24|n[c>>>16&255]<<16|n[c>>>8&255]<<8|n[255&c],c^=d[s/r|0]<<24),o[s]=o[s-r]^c);for(var a=this._invKeySchedule=[],u=0;u<i;u++){if(s=i-u,u%4)var c=o[s];else c=o[s-4];a[u]=u<4||s<=4?c:l[n[c>>>24]]^f[n[c>>>16&255]]^h[n[c>>>8&255]]^p[n[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,u,c,n)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,f,h,p,o),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,i,n,o,s,a){for(var u=this._nRounds,c=e[t]^r[0],l=e[t+1]^r[1],f=e[t+2]^r[2],h=e[t+3]^r[3],p=4,d=1;d<u;d++){var g=i[c>>>24]^n[l>>>16&255]^o[f>>>8&255]^s[255&h]^r[p++],y=i[l>>>24]^n[f>>>16&255]^o[h>>>8&255]^s[255&c]^r[p++],m=i[f>>>24]^n[h>>>16&255]^o[c>>>8&255]^s[255&l]^r[p++],v=i[h>>>24]^n[c>>>16&255]^o[l>>>8&255]^s[255&f]^r[p++];c=g,l=y,f=m,h=v}g=(a[c>>>24]<<24|a[l>>>16&255]<<16|a[f>>>8&255]<<8|a[255&h])^r[p++],y=(a[l>>>24]<<24|a[f>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^r[p++],m=(a[f>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&l])^r[p++],v=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[l>>>8&255]<<8|a[255&f])^r[p++],e[t]=g,e[t+1]=y,e[t+2]=m,e[t+3]=v},keySize:8});e.AES=t._createHelper(g)}(),r.AES)}(aes)),aes.exports}var tripledes={exports:{}},hasRequiredTripledes;function requireTripledes(){return hasRequiredTripledes||(hasRequiredTripledes=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib,i=t.WordArray,n=t.BlockCipher,o=e.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],u=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=o.DES=n.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var i=s[r]-1;t[r]=e[i>>>5]>>>31-i%32&1}for(var n=this._subKeys=[],o=0;o<16;o++){var c=n[o]=[],l=u[o];for(r=0;r<24;r++)c[r/6|0]|=t[(a[r]-1+l)%28]<<31-r%6,c[4+(r/6|0)]|=t[28+(a[r+24]-1+l)%28]<<31-r%6;for(c[0]=c[0]<<1|c[0]>>>31,r=1;r<7;r++)c[r]=c[r]>>>4*(r-1)+3;c[7]=c[7]<<5|c[7]>>>27}var f=this._invSubKeys=[];for(r=0;r<16;r++)f[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),h.call(this,1,1431655765);for(var i=0;i<16;i++){for(var n=r[i],o=this._lBlock,s=this._rBlock,a=0,u=0;u<8;u++)a|=c[u][((s^n[u])&l[u])>>>0];this._lBlock=s,this._rBlock=o^a}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,h.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function p(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}e.DES=n._createHelper(f);var d=o.TripleDES=n.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=f.createEncryptor(i.create(t)),this._des2=f.createEncryptor(i.create(r)),this._des3=f.createEncryptor(i.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=n._createHelper(d)}(),r.TripleDES)}(tripledes)),tripledes.exports}var rc4={exports:{}},hasRequiredRc4;function requireRc4(){return hasRequiredRc4||(hasRequiredRc4=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=i.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;n=0;for(var o=0;n<256;n++){var s=n%r,a=t[s>>>2]>>>24-s%4*8&255;o=(o+i[n]+a)%256;var u=i[n];i[n]=i[o],i[o]=u}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,r=this._j,i=0,n=0;n<4;n++){r=(r+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[r],e[r]=o,i|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,i}e.RC4=t._createHelper(n);var s=i.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});e.RC4Drop=t._createHelper(s)}(),r.RC4)}(rc4)),rc4.exports}var rabbit={exports:{}},hasRequiredRabbit;function requireRabbit(){return hasRequiredRabbit||(hasRequiredRabbit=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=[],o=[],s=[],a=i.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)u.call(this);for(r=0;r<8;r++)n[r]^=i[r+4&7];if(t){var o=t.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=c>>>16|4294901760&l,h=l<<16|65535&c;for(n[0]^=c,n[1]^=f,n[2]^=l,n[3]^=h,n[4]^=c,n[5]^=f,n[6]^=l,n[7]^=h,r=0;r<4;r++)u.call(this)}},_doProcessBlock:function(e,t){var r=this._X;u.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2});function u(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,a=i>>>16,u=((n*n>>>17)+n*a>>>15)+a*a,c=((4294901760&i)*i|0)+((65535&i)*i|0);s[r]=u^c}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}e.Rabbit=t._createHelper(a)}(),r.Rabbit)}(rabbit)),rabbit.exports}var rabbitLegacy={exports:{}},hasRequiredRabbitLegacy;function requireRabbitLegacy(){return hasRequiredRabbitLegacy||(hasRequiredRabbitLegacy=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=[],o=[],s=[],a=i.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)u.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(t){var o=t.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=c>>>16|4294901760&l,h=l<<16|65535&c;for(i[0]^=c,i[1]^=f,i[2]^=l,i[3]^=h,i[4]^=c,i[5]^=f,i[6]^=l,i[7]^=h,n=0;n<4;n++)u.call(this)}},_doProcessBlock:function(e,t){var r=this._X;u.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2});function u(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,a=i>>>16,u=((n*n>>>17)+n*a>>>15)+a*a,c=((4294901760&i)*i|0)+((65535&i)*i|0);s[r]=u^c}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}e.RabbitLegacy=t._createHelper(a)}(),r.RabbitLegacy)}(rabbitLegacy)),rabbitLegacy.exports}var blowfish={exports:{}},hasRequiredBlowfish;function requireBlowfish(){return hasRequiredBlowfish||(hasRequiredBlowfish=1,function(e,t){var r;e.exports=(r=requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore(),function(){var e=r,t=e.lib.BlockCipher,i=e.algo;const n=16,o=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],s=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function u(e,t){let r=t>>24&255,i=t>>16&255,n=t>>8&255,o=255&t,s=e.sbox[0][r]+e.sbox[1][i];return s^=e.sbox[2][n],s+=e.sbox[3][o],s}function c(e,t,r){let i,o=t,s=r;for(let t=0;t<n;++t)o^=e.pbox[t],s=u(e,o)^s,i=o,o=s,s=i;return i=o,o=s,s=i,s^=e.pbox[n],o^=e.pbox[n+1],{left:o,right:s}}function l(e,t,r){let i,o=t,s=r;for(let t=n+1;t>1;--t)o^=e.pbox[t],s=u(e,o)^s,i=o,o=s,s=i;return i=o,o=s,s=i,s^=e.pbox[1],o^=e.pbox[0],{left:o,right:s}}function f(e,t,r){for(let t=0;t<4;t++){e.sbox[t]=[];for(let r=0;r<256;r++)e.sbox[t][r]=s[t][r]}let i=0;for(let s=0;s<n+2;s++)e.pbox[s]=o[s]^t[i],i++,i>=r&&(i=0);let a=0,u=0,l=0;for(let t=0;t<n+2;t+=2)l=c(e,a,u),a=l.left,u=l.right,e.pbox[t]=a,e.pbox[t+1]=u;for(let t=0;t<4;t++)for(let r=0;r<256;r+=2)l=c(e,a,u),a=l.left,u=l.right,e.sbox[t][r]=a,e.sbox[t][r+1]=u;return!0}var h=i.Blowfish=t.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4;f(a,t,r)}},encryptBlock:function(e,t){var r=c(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=l(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=t._createHelper(h)}(),r.Blowfish)}(blowfish)),blowfish.exports}!function(e,t){var r;e.exports=(r=requireCore(),requireX64Core(),requireLibTypedarrays(),requireEncUtf16(),requireEncBase64(),requireEncBase64url(),requireMd5(),requireSha1(),requireSha256(),requireSha224(),requireSha512(),requireSha384(),requireSha3(),requireRipemd160(),requireHmac(),requirePbkdf2(),requireEvpkdf(),requireCipherCore(),requireModeCfb(),requireModeCtr(),requireModeCtrGladman(),requireModeOfb(),requireModeEcb(),requirePadAnsix923(),requirePadIso10126(),requirePadIso97971(),requirePadZeropadding(),requirePadNopadding(),requireFormatHex(),requireAes(),requireTripledes(),requireRc4(),requireRabbit(),requireRabbitLegacy(),requireBlowfish(),r)}(cryptoJs);var cryptoJsExports=cryptoJs.exports,crypto=getDefaultExportFromCjs(cryptoJsExports),util$5={sha1:function(e){return crypto.SHA1(e).toString()},sha1_hmac:function(e,t){return crypto.HmacSHA1(e,t).toString()},getParamKeylist:function(e){return Object.keys(e).sort((function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1}))},getHeaderKeylist:function(e){for(var t=[],r=0,i=Object.keys(e);r<i.length;r++){var n=i[r],o=n.toLowerCase();!e[n]||"content-type"!==o&&"content-md5"!==o&&"host"!==o&&"x"!==o[0]||t.push(n)}return t.sort((function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1}))},camSafeUrlEncode:function(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")},obj2str:function(e,t){var r,i,n,o=[],s=t(e);for(r=0;r<s.length;r++)n=void 0===e[i=s[r]]?"":""+e[i],i=i.toLowerCase(),i=util$5.camSafeUrlEncode(i),n=util$5.camSafeUrlEncode(n)||"",o.push(i+"="+n);return o.join("&")}};function sign(e){var t,r,i=e=e||{},n=i.SecretId,o=i.SecretKey,s=(e.method||"get").toLowerCase(),a=Object.assign({},e.query||{}),u=Object.assign({},e.headers||{});if(!n)throw new ClsSDKError("missing param SecretId");if(!o)throw new ClsSDKError("missing param SecretKey");var c=Math.floor(systemClock.now()/1e3)-1,l=c,f=e.expires;l+=void 0===f?900:1*f||0;var h="".concat(c,";").concat(l),p="".concat(c,";").concat(l),d=null===(t=util$5.getHeaderKeylist(u))||void 0===t?void 0:t.join(";").toLowerCase(),g=null===(r=util$5.getParamKeylist(a))||void 0===r?void 0:r.join(";").toLowerCase(),y=util$5.obj2str(a,util$5.getParamKeylist),m=util$5.obj2str(u,util$5.getHeaderKeylist),v=[s.toLowerCase(),"/".concat(e.api||""),y,m,""].join("\n"),b=["sha1",p,util$5.sha1(v),""].join("\n"),w=util$5.sha1_hmac(p,o),_=util$5.sha1_hmac(b,w);return["q-sign-algorithm=".concat("sha1"),"q-ak=".concat(n),"q-sign-time=".concat(h),"q-key-time=".concat(p),"q-header-list="+d,"q-url-param-list="+g,"q-signature=".concat(_)].join("&")}var indexMinimal={},minimal$1={},aspromise,hasRequiredAspromise;function requireAspromise(){if(hasRequiredAspromise)return aspromise;return hasRequiredAspromise=1,aspromise=function(e,t){var r=new Array(arguments.length-1),i=0,n=2,o=!0;for(;n<arguments.length;)r[i++]=arguments[n++];return new Promise((function(n,s){r[i]=function(e){if(o)if(o=!1,e)s(e);else{for(var t=new Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];n.apply(null,t)}};try{e.apply(t||null,r)}catch(e){o&&(o=!1,s(e))}}))},aspromise}var base64$1={},hasRequiredBase64,eventemitter,hasRequiredEventemitter,float,hasRequiredFloat,inquire_1,hasRequiredInquire;function requireBase64(){return hasRequiredBase64||(hasRequiredBase64=1,function(e){var t=e;t.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};for(var r=new Array(64),i=new Array(123),n=0;n<64;)i[r[n]=n<26?n+65:n<52?n+71:n<62?n-4:n-59|43]=n++;t.encode=function(e,t,i){for(var n,o=null,s=[],a=0,u=0;t<i;){var c=e[t++];switch(u){case 0:s[a++]=r[c>>2],n=(3&c)<<4,u=1;break;case 1:s[a++]=r[n|c>>4],n=(15&c)<<2,u=2;break;case 2:s[a++]=r[n|c>>6],s[a++]=r[63&c],u=0}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=r[n],s[a++]=61,1===u&&(s[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,s.slice(0,a))),o.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var o="invalid encoding";t.decode=function(e,t,r){for(var n,s=r,a=0,u=0;u<e.length;){var c=e.charCodeAt(u++);if(61===c&&a>1)break;if(void 0===(c=i[c]))throw Error(o);switch(a){case 0:n=c,a=1;break;case 1:t[r++]=n<<2|(48&c)>>4,n=c,a=2;break;case 2:t[r++]=(15&n)<<4|(60&c)>>2,n=c,a=3;break;case 3:t[r++]=(3&n)<<6|c,a=0}}if(1===a)throw Error(o);return r-s},t.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}}(base64$1)),base64$1}function requireEventemitter(){if(hasRequiredEventemitter)return eventemitter;function e(){this._listeners={}}return hasRequiredEventemitter=1,eventemitter=e,e.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},e.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],i=0;i<r.length;)r[i].fn===t?r.splice(i,1):++i;return this},e.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],i=1;i<arguments.length;)r.push(arguments[i++]);for(i=0;i<t.length;)t[i].fn.apply(t[i++].ctx,r)}return this},eventemitter}function requireFloat(){if(hasRequiredFloat)return float;function e(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),i=128===r[3];function n(e,i,n){t[0]=e,i[n]=r[0],i[n+1]=r[1],i[n+2]=r[2],i[n+3]=r[3]}function o(e,i,n){t[0]=e,i[n]=r[3],i[n+1]=r[2],i[n+2]=r[1],i[n+3]=r[0]}function s(e,i){return r[0]=e[i],r[1]=e[i+1],r[2]=e[i+2],r[3]=e[i+3],t[0]}function a(e,i){return r[3]=e[i],r[2]=e[i+1],r[1]=e[i+2],r[0]=e[i+3],t[0]}e.writeFloatLE=i?n:o,e.writeFloatBE=i?o:n,e.readFloatLE=i?s:a,e.readFloatBE=i?a:s}():function(){function o(e,t,r,i){var n=t<0?1:0;if(n&&(t=-t),0===t)e(1/t>0?0:2147483648,r,i);else if(isNaN(t))e(2143289344,r,i);else if(t>34028234663852886e22)e((n<<31|2139095040)>>>0,r,i);else if(t<11754943508222875e-54)e((n<<31|Math.round(t/1401298464324817e-60))>>>0,r,i);else{var o=Math.floor(Math.log(t)/Math.LN2);e((n<<31|o+127<<23|8388607&Math.round(t*Math.pow(2,-o)*8388608))>>>0,r,i)}}function s(e,t,r){var i=e(t,r),n=2*(i>>31)+1,o=i>>>23&255,s=8388607&i;return 255===o?s?NaN:n*(1/0):0===o?1401298464324817e-60*n*s:n*Math.pow(2,o-150)*(s+8388608)}e.writeFloatLE=o.bind(null,t),e.writeFloatBE=o.bind(null,r),e.readFloatLE=s.bind(null,i),e.readFloatBE=s.bind(null,n)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),i=128===r[7];function n(e,i,n){t[0]=e,i[n]=r[0],i[n+1]=r[1],i[n+2]=r[2],i[n+3]=r[3],i[n+4]=r[4],i[n+5]=r[5],i[n+6]=r[6],i[n+7]=r[7]}function o(e,i,n){t[0]=e,i[n]=r[7],i[n+1]=r[6],i[n+2]=r[5],i[n+3]=r[4],i[n+4]=r[3],i[n+5]=r[2],i[n+6]=r[1],i[n+7]=r[0]}function s(e,i){return r[0]=e[i],r[1]=e[i+1],r[2]=e[i+2],r[3]=e[i+3],r[4]=e[i+4],r[5]=e[i+5],r[6]=e[i+6],r[7]=e[i+7],t[0]}function a(e,i){return r[7]=e[i],r[6]=e[i+1],r[5]=e[i+2],r[4]=e[i+3],r[3]=e[i+4],r[2]=e[i+5],r[1]=e[i+6],r[0]=e[i+7],t[0]}e.writeDoubleLE=i?n:o,e.writeDoubleBE=i?o:n,e.readDoubleLE=i?s:a,e.readDoubleBE=i?a:s}():function(){function o(e,t,r,i,n,o){var s=i<0?1:0;if(s&&(i=-i),0===i)e(0,n,o+t),e(1/i>0?0:2147483648,n,o+r);else if(isNaN(i))e(0,n,o+t),e(2146959360,n,o+r);else if(i>17976931348623157e292)e(0,n,o+t),e((s<<31|2146435072)>>>0,n,o+r);else{var a;if(i<22250738585072014e-324)e((a=i/5e-324)>>>0,n,o+t),e((s<<31|a/4294967296)>>>0,n,o+r);else{var u=Math.floor(Math.log(i)/Math.LN2);1024===u&&(u=1023),e(4503599627370496*(a=i*Math.pow(2,-u))>>>0,n,o+t),e((s<<31|u+1023<<20|1048576*a&1048575)>>>0,n,o+r)}}}function s(e,t,r,i,n){var o=e(i,n+t),s=e(i,n+r),a=2*(s>>31)+1,u=s>>>20&2047,c=4294967296*(1048575&s)+o;return 2047===u?c?NaN:a*(1/0):0===u?5e-324*a*c:a*Math.pow(2,u-1075)*(c+4503599627370496)}e.writeDoubleLE=o.bind(null,t,0,4),e.writeDoubleBE=o.bind(null,r,4,0),e.readDoubleLE=s.bind(null,i,0,4),e.readDoubleBE=s.bind(null,n,4,0)}(),e}function t(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function r(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function i(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function n(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}return hasRequiredFloat=1,float=e(e)}function requireInquire(){if(hasRequiredInquire)return inquire_1;function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}return hasRequiredInquire=1,inquire_1=inquire,inquire_1}var utf8$2={},hasRequiredUtf8,pool_1,hasRequiredPool,longbits,hasRequiredLongbits,hasRequiredMinimal;function requireUtf8(){return hasRequiredUtf8||(hasRequiredUtf8=1,function(e){var t=e;t.length=function(e){for(var t=0,r=0,i=0;i<e.length;++i)(r=e.charCodeAt(i))<128?t+=1:r<2048?t+=2:55296==(64512&r)&&56320==(64512&e.charCodeAt(i+1))?(++i,t+=4):t+=3;return t},t.read=function(e,t,r){if(r-t<1)return"";for(var i,n=null,o=[],s=0;t<r;)(i=e[t++])<128?o[s++]=i:i>191&&i<224?o[s++]=(31&i)<<6|63&e[t++]:i>239&&i<365?(i=((7&i)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,o[s++]=55296+(i>>10),o[s++]=56320+(1023&i)):o[s++]=(15&i)<<12|(63&e[t++])<<6|63&e[t++],s>8191&&((n||(n=[])).push(String.fromCharCode.apply(String,o)),s=0);return n?(s&&n.push(String.fromCharCode.apply(String,o.slice(0,s))),n.join("")):String.fromCharCode.apply(String,o.slice(0,s))},t.write=function(e,t,r){for(var i,n,o=r,s=0;s<e.length;++s)(i=e.charCodeAt(s))<128?t[r++]=i:i<2048?(t[r++]=i>>6|192,t[r++]=63&i|128):55296==(64512&i)&&56320==(64512&(n=e.charCodeAt(s+1)))?(i=65536+((1023&i)<<10)+(1023&n),++s,t[r++]=i>>18|240,t[r++]=i>>12&63|128,t[r++]=i>>6&63|128,t[r++]=63&i|128):(t[r++]=i>>12|224,t[r++]=i>>6&63|128,t[r++]=63&i|128);return r-o}}(utf8$2)),utf8$2}function requirePool(){if(hasRequiredPool)return pool_1;return hasRequiredPool=1,pool_1=function(e,t,r){var i=r||8192,n=i>>>1,o=null,s=i;return function(r){if(r<1||r>n)return e(r);s+r>i&&(o=e(i),s=0);var a=t.call(o,s,s+=r);return 7&s&&(s=1+(7|s)),a}}}function requireLongbits(){if(hasRequiredLongbits)return longbits;hasRequiredLongbits=1,longbits=t;var e=requireMinimal();function t(e,t){this.lo=e>>>0,this.hi=t>>>0}var r=t.zero=new t(0,0);r.toNumber=function(){return 0},r.zzEncode=r.zzDecode=function(){return this},r.length=function(){return 1};var i=t.zeroHash="\0\0\0\0\0\0\0\0";t.fromNumber=function(e){if(0===e)return r;var i=e<0;i&&(e=-e);var n=e>>>0,o=(e-n)/4294967296>>>0;return i&&(o=~o>>>0,n=~n>>>0,++n>4294967295&&(n=0,++o>4294967295&&(o=0))),new t(n,o)},t.from=function(i){if("number"==typeof i)return t.fromNumber(i);if(e.isString(i)){if(!e.Long)return t.fromNumber(parseInt(i,10));i=e.Long.fromString(i)}return i.low||i.high?new t(i.low>>>0,i.high>>>0):r},t.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+4294967296*r)}return this.lo+4294967296*this.hi},t.prototype.toLong=function(t){return e.Long?new e.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var n=String.prototype.charCodeAt;return t.fromHash=function(e){return e===i?r:new t((n.call(e,0)|n.call(e,1)<<8|n.call(e,2)<<16|n.call(e,3)<<24)>>>0,(n.call(e,4)|n.call(e,5)<<8|n.call(e,6)<<16|n.call(e,7)<<24)>>>0)},t.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},t.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},t.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},t.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10},longbits}function requireMinimal(){return hasRequiredMinimal||(hasRequiredMinimal=1,function(e){var t=e;function r(e,t,r){for(var i=Object.keys(t),n=0;n<i.length;++n)void 0!==e[i[n]]&&r||(e[i[n]]=t[i[n]]);return e}function i(e){function t(e,i){if(!(this instanceof t))return new t(e,i);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),i&&r(this,i)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t}t.asPromise=requireAspromise(),t.base64=requireBase64(),t.EventEmitter=requireEventemitter(),t.float=requireFloat(),t.inquire=requireInquire(),t.utf8=requireUtf8(),t.pool=requirePool(),t.LongBits=requireLongbits(),t.isNode=Boolean(void 0!==commonjsGlobal&&commonjsGlobal&&commonjsGlobal.process&&commonjsGlobal.process.versions&&commonjsGlobal.process.versions.node),t.global=t.isNode&&commonjsGlobal||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||commonjsGlobal,t.emptyArray=Object.freeze?Object.freeze([]):[],t.emptyObject=Object.freeze?Object.freeze({}):{},t.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},t.isString=function(e){return"string"==typeof e||e instanceof String},t.isObject=function(e){return e&&"object"==typeof e},t.isset=t.isSet=function(e,t){var r=e[t];return!(null==r||!e.hasOwnProperty(t))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},t.Buffer=function(){try{var e=t.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),t._Buffer_from=null,t._Buffer_allocUnsafe=null,t.newBuffer=function(e){return"number"==typeof e?t.Buffer?t._Buffer_allocUnsafe(e):new t.Array(e):t.Buffer?t._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},t.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,t.Long=t.global.dcodeIO&&t.global.dcodeIO.Long||t.global.Long||t.inquire("long"),t.key2Re=/^true|false|0|1$/,t.key32Re=/^-?(?:0|[1-9][0-9]*)$/,t.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,t.longToHash=function(e){return e?t.LongBits.from(e).toHash():t.LongBits.zeroHash},t.longFromHash=function(e,r){var i=t.LongBits.fromHash(e);return t.Long?t.Long.fromBits(i.lo,i.hi,r):i.toNumber(Boolean(r))},t.merge=r,t.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},t.newError=i,t.ProtocolError=i("ProtocolError"),t.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},t.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},t.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},t._configure=function(){var e=t.Buffer;e?(t._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},t._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):t._Buffer_from=t._Buffer_allocUnsafe=null}}(minimal$1)),minimal$1}var writer=Writer$1,util$4=requireMinimal(),BufferWriter$1,LongBits$1=util$4.LongBits,base64=util$4.base64,utf8$1=util$4.utf8;function Op(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function noop(){}function State(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function Writer$1(){this.len=0,this.head=new Op(noop,0,0),this.tail=this.head,this.states=null}var create$1=function(){return util$4.Buffer?function(){return(Writer$1.create=function(){return new BufferWriter$1})()}:function(){return new Writer$1}};function writeByte(e,t,r){t[r]=255&e}function writeVarint32(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e}function VarintOp(e,t){this.len=e,this.next=void 0,this.val=t}function writeVarint64(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function writeFixed32(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}Writer$1.create=create$1(),Writer$1.alloc=function(e){return new util$4.Array(e)},util$4.Array!==Array&&(Writer$1.alloc=util$4.pool(Writer$1.alloc,util$4.Array.prototype.subarray)),Writer$1.prototype._push=function(e,t,r){return this.tail=this.tail.next=new Op(e,t,r),this.len+=t,this},VarintOp.prototype=Object.create(Op.prototype),VarintOp.prototype.fn=writeVarint32,Writer$1.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new VarintOp((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},Writer$1.prototype.int32=function(e){return e<0?this._push(writeVarint64,10,LongBits$1.fromNumber(e)):this.uint32(e)},Writer$1.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},Writer$1.prototype.uint64=function(e){var t=LongBits$1.from(e);return this._push(writeVarint64,t.length(),t)},Writer$1.prototype.int64=Writer$1.prototype.uint64,Writer$1.prototype.sint64=function(e){var t=LongBits$1.from(e).zzEncode();return this._push(writeVarint64,t.length(),t)},Writer$1.prototype.bool=function(e){return this._push(writeByte,1,e?1:0)},Writer$1.prototype.fixed32=function(e){return this._push(writeFixed32,4,e>>>0)},Writer$1.prototype.sfixed32=Writer$1.prototype.fixed32,Writer$1.prototype.fixed64=function(e){var t=LongBits$1.from(e);return this._push(writeFixed32,4,t.lo)._push(writeFixed32,4,t.hi)},Writer$1.prototype.sfixed64=Writer$1.prototype.fixed64,Writer$1.prototype.float=function(e){return this._push(util$4.float.writeFloatLE,4,e)},Writer$1.prototype.double=function(e){return this._push(util$4.float.writeDoubleLE,8,e)};var writeBytes=util$4.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var i=0;i<e.length;++i)t[r+i]=e[i]};Writer$1.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(writeByte,1,0);if(util$4.isString(e)){var r=Writer$1.alloc(t=base64.length(e));base64.decode(e,r,0),e=r}return this.uint32(t)._push(writeBytes,t,e)},Writer$1.prototype.string=function(e){var t=utf8$1.length(e);return t?this.uint32(t)._push(utf8$1.write,t,e):this._push(writeByte,1,0)},Writer$1.prototype.fork=function(){return this.states=new State(this),this.head=this.tail=new Op(noop,0,0),this.len=0,this},Writer$1.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new Op(noop,0,0),this.len=0),this},Writer$1.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},Writer$1.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},Writer$1._configure=function(e){BufferWriter$1=e,Writer$1.create=create$1(),BufferWriter$1._configure()};var writer_buffer=BufferWriter,Writer=writer;(BufferWriter.prototype=Object.create(Writer.prototype)).constructor=BufferWriter;var util$3=requireMinimal();function BufferWriter(){Writer.call(this)}function writeStringBuffer(e,t,r){e.length<40?util$3.utf8.write(e,t,r):t.utf8Write?t.utf8Write(e,r):t.write(e,r)}BufferWriter._configure=function(){BufferWriter.alloc=util$3._Buffer_allocUnsafe,BufferWriter.writeBytesBuffer=util$3.Buffer&&util$3.Buffer.prototype instanceof Uint8Array&&"set"===util$3.Buffer.prototype.set.name?function(e,t,r){t.set(e,r)}:function(e,t,r){if(e.copy)e.copy(t,r,0,e.length);else for(var i=0;i<e.length;)t[r++]=e[i++]}},BufferWriter.prototype.bytes=function(e){util$3.isString(e)&&(e=util$3._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(BufferWriter.writeBytesBuffer,t,e),this},BufferWriter.prototype.string=function(e){var t=util$3.Buffer.byteLength(e);return this.uint32(t),t&&this._push(writeStringBuffer,t,e),this},BufferWriter._configure();var reader=Reader$1,util$2=requireMinimal(),BufferReader$1,LongBits=util$2.LongBits,utf8=util$2.utf8;function indexOutOfRange(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function Reader$1(e){this.buf=e,this.pos=0,this.len=e.length}var create_array="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new Reader$1(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new Reader$1(e);throw Error("illegal buffer")},create=function(){return util$2.Buffer?function(e){return(Reader$1.create=function(e){return util$2.Buffer.isBuffer(e)?new BufferReader$1(e):create_array(e)})(e)}:create_array},value;function readLongVarint(){var e=new LongBits(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw indexOutOfRange(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw indexOutOfRange(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function readFixed32_end(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function readFixed64(){if(this.pos+8>this.len)throw indexOutOfRange(this,8);return new LongBits(readFixed32_end(this.buf,this.pos+=4),readFixed32_end(this.buf,this.pos+=4))}Reader$1.create=create(),Reader$1.prototype._slice=util$2.Array.prototype.subarray||util$2.Array.prototype.slice,Reader$1.prototype.uint32=(value=4294967295,function(){if(value=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return value;if((this.pos+=5)>this.len)throw this.pos=this.len,indexOutOfRange(this,10);return value}),Reader$1.prototype.int32=function(){return 0|this.uint32()},Reader$1.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)},Reader$1.prototype.bool=function(){return 0!==this.uint32()},Reader$1.prototype.fixed32=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);return readFixed32_end(this.buf,this.pos+=4)},Reader$1.prototype.sfixed32=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);return 0|readFixed32_end(this.buf,this.pos+=4)},Reader$1.prototype.float=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);var e=util$2.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},Reader$1.prototype.double=function(){if(this.pos+8>this.len)throw indexOutOfRange(this,4);var e=util$2.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},Reader$1.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw indexOutOfRange(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,r):t===r?new this.buf.constructor(0):this._slice.call(this.buf,t,r)},Reader$1.prototype.string=function(){var e=this.bytes();return utf8.read(e,0,e.length)},Reader$1.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw indexOutOfRange(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw indexOutOfRange(this)}while(128&this.buf[this.pos++]);return this},Reader$1.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},Reader$1._configure=function(e){BufferReader$1=e,Reader$1.create=create(),BufferReader$1._configure();var t=util$2.Long?"toLong":"toNumber";util$2.merge(Reader$1.prototype,{int64:function(){return readLongVarint.call(this)[t](!1)},uint64:function(){return readLongVarint.call(this)[t](!0)},sint64:function(){return readLongVarint.call(this).zzDecode()[t](!1)},fixed64:function(){return readFixed64.call(this)[t](!0)},sfixed64:function(){return readFixed64.call(this)[t](!1)}})};var reader_buffer=BufferReader,Reader=reader;(BufferReader.prototype=Object.create(Reader.prototype)).constructor=BufferReader;var util$1=requireMinimal();function BufferReader(e){Reader.call(this,e)}BufferReader._configure=function(){util$1.Buffer&&(BufferReader.prototype._slice=util$1.Buffer.prototype.slice)},BufferReader.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},BufferReader._configure();var rpc={},service=Service,util=requireMinimal();function Service(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");util.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(Service.prototype=Object.create(util.EventEmitter.prototype)).constructor=Service,Service.prototype.rpcCall=function e(t,r,i,n,o){if(!n)throw TypeError("request must be specified");var s=this;if(!o)return util.asPromise(e,s,t,r,i,n);if(s.rpcImpl)try{return s.rpcImpl(t,r[s.requestDelimited?"encodeDelimited":"encode"](n).finish(),(function(e,r){if(e)return s.emit("error",e,t),o(e);if(null!==r){if(!(r instanceof i))try{r=i[s.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return s.emit("error",e,t),o(e)}return s.emit("data",r,t),o(null,r)}s.end(!0)}))}catch(e){return s.emit("error",e,t),void setTimeout((function(){o(e)}),0)}else setTimeout((function(){o(Error("already ended"))}),0)},Service.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this},function(e){e.Service=service}(rpc);var roots={};!function(e){var t=e;function r(){t.util._configure(),t.Writer._configure(t.BufferWriter),t.Reader._configure(t.BufferReader)}t.build="minimal",t.Writer=writer,t.BufferWriter=writer_buffer,t.Reader=reader,t.BufferReader=reader_buffer,t.util=requireMinimal(),t.rpc=rpc,t.roots=roots,t.configure=r,r()}(indexMinimal);var minimal=indexMinimal,$protobuf=minimal,$Reader=$protobuf.Reader,$Writer=$protobuf.Writer,$util=$protobuf.util,$root=$protobuf.roots.default||($protobuf.roots.default={});$root.cls=function(){var e={};return e.Log=function(){function e(e){if(this.contents=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.time=$util.Long?$util.Long.fromBits(0,0,!1):0,e.prototype.contents=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),t.uint32(8).int64(e.time),null!=e.contents&&e.contents.length)for(var r=0;r<e.contents.length;++r)$root.cls.Log.Content.encode(e.contents[r],t.uint32(18).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,i=new $root.cls.Log;e.pos<r;){var n=e.uint32();switch(n>>>3){case 1:i.time=e.int64();break;case 2:i.contents&&i.contents.length||(i.contents=[]),i.contents.push($root.cls.Log.Content.decode(e,e.uint32()));break;default:e.skipType(7&n)}}if(!i.hasOwnProperty("time"))throw $util.ProtocolError("missing required 'time'",{instance:i});return i},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(!($util.isInteger(e.time)||e.time&&$util.isInteger(e.time.low)&&$util.isInteger(e.time.high)))return"time: integer|Long expected";if(null!=e.contents&&e.hasOwnProperty("contents")){if(!Array.isArray(e.contents))return"contents: array expected";for(var t=0;t<e.contents.length;++t){var r=$root.cls.Log.Content.verify(e.contents[t]);if(r)return"contents."+r}}return null},e.fromObject=function(e){if(e instanceof $root.cls.Log)return e;var t=new $root.cls.Log;if(null!=e.time&&($util.Long?(t.time=$util.Long.fromValue(e.time)).unsigned=!1:"string"==typeof e.time?t.time=parseInt(e.time,10):"number"==typeof e.time?t.time=e.time:"object"==typeof e.time&&(t.time=new $util.LongBits(e.time.low>>>0,e.time.high>>>0).toNumber())),e.contents){if(!Array.isArray(e.contents))throw TypeError(".cls.Log.contents: array expected");t.contents=[];for(var r=0;r<e.contents.length;++r){if("object"!=typeof e.contents[r])throw TypeError(".cls.Log.contents: object expected");t.contents[r]=$root.cls.Log.Content.fromObject(e.contents[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.contents=[]),t.defaults)if($util.Long){var i=new $util.Long(0,0,!1);r.time=t.longs===String?i.toString():t.longs===Number?i.toNumber():i}else r.time=t.longs===String?"0":0;if(null!=e.time&&e.hasOwnProperty("time")&&("number"==typeof e.time?r.time=t.longs===String?String(e.time):e.time:r.time=t.longs===String?$util.Long.prototype.toString.call(e.time):t.longs===Number?new $util.LongBits(e.time.low>>>0,e.time.high>>>0).toNumber():e.time),e.contents&&e.contents.length){r.contents=[];for(var n=0;n<e.contents.length;++n)r.contents[n]=$root.cls.Log.Content.toObject(e.contents[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/cls.Log"},e.Content=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.key="",e.prototype.value="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),t.uint32(10).string(e.key),t.uint32(18).string(e.value),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,i=new $root.cls.Log.Content;e.pos<r;){var n=e.uint32();switch(n>>>3){case 1:i.key=e.string();break;case 2:i.value=e.string();break;default:e.skipType(7&n)}}if(!i.hasOwnProperty("key"))throw $util.ProtocolError("missing required 'key'",{instance:i});if(!i.hasOwnProperty("value"))throw $util.ProtocolError("missing required 'value'",{instance:i});return i},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":$util.isString(e.key)?$util.isString(e.value)?null:"value: string expected":"key: string expected"},e.fromObject=function(e){if(e instanceof $root.cls.Log.Content)return e;var t=new $root.cls.Log.Content;return null!=e.key&&(t.key=String(e.key)),null!=e.value&&(t.value=String(e.value)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.key="",r.value=""),null!=e.key&&e.hasOwnProperty("key")&&(r.key=e.key),null!=e.value&&e.hasOwnProperty("value")&&(r.value=e.value),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/cls.Log.Content"},e}(),e}(),e.LogTag=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.key="",e.prototype.value="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),t.uint32(10).string(e.key),t.uint32(18).string(e.value),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,i=new $root.cls.LogTag;e.pos<r;){var n=e.uint32();switch(n>>>3){case 1:i.key=e.string();break;case 2:i.value=e.string();break;default:e.skipType(7&n)}}if(!i.hasOwnProperty("key"))throw $util.ProtocolError("missing required 'key'",{instance:i});if(!i.hasOwnProperty("value"))throw $util.ProtocolError("missing required 'value'",{instance:i});return i},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":$util.isString(e.key)?$util.isString(e.value)?null:"value: string expected":"key: string expected"},e.fromObject=function(e){if(e instanceof $root.cls.LogTag)return e;var t=new $root.cls.LogTag;return null!=e.key&&(t.key=String(e.key)),null!=e.value&&(t.value=String(e.value)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.key="",r.value=""),null!=e.key&&e.hasOwnProperty("key")&&(r.key=e.key),null!=e.value&&e.hasOwnProperty("value")&&(r.value=e.value),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/cls.LogTag"},e}(),e.LogGroup=function(){function e(e){if(this.logs=[],this.logTags=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.logs=$util.emptyArray,e.prototype.contextFlow="",e.prototype.filename="",e.prototype.source="",e.prototype.logTags=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.logs&&e.logs.length)for(var r=0;r<e.logs.length;++r)$root.cls.Log.encode(e.logs[r],t.uint32(10).fork()).ldelim();if(null!=e.contextFlow&&Object.hasOwnProperty.call(e,"contextFlow")&&t.uint32(18).string(e.contextFlow),null!=e.filename&&Object.hasOwnProperty.call(e,"filename")&&t.uint32(26).string(e.filename),null!=e.source&&Object.hasOwnProperty.call(e,"source")&&t.uint32(34).string(e.source),null!=e.logTags&&e.logTags.length)for(r=0;r<e.logTags.length;++r)$root.cls.LogTag.encode(e.logTags[r],t.uint32(42).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,i=new $root.cls.LogGroup;e.pos<r;){var n=e.uint32();switch(n>>>3){case 1:i.logs&&i.logs.length||(i.logs=[]),i.logs.push($root.cls.Log.decode(e,e.uint32()));break;case 2:i.contextFlow=e.string();break;case 3:i.filename=e.string();break;case 4:i.source=e.string();break;case 5:i.logTags&&i.logTags.length||(i.logTags=[]),i.logTags.push($root.cls.LogTag.decode(e,e.uint32()));break;default:e.skipType(7&n)}}return i},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.logs&&e.hasOwnProperty("logs")){if(!Array.isArray(e.logs))return"logs: array expected";for(var t=0;t<e.logs.length;++t){if(r=$root.cls.Log.verify(e.logs[t]))return"logs."+r}}if(null!=e.contextFlow&&e.hasOwnProperty("contextFlow")&&!$util.isString(e.contextFlow))return"contextFlow: string expected";if(null!=e.filename&&e.hasOwnProperty("filename")&&!$util.isString(e.filename))return"filename: string expected";if(null!=e.source&&e.hasOwnProperty("source")&&!$util.isString(e.source))return"source: string expected";if(null!=e.logTags&&e.hasOwnProperty("logTags")){if(!Array.isArray(e.logTags))return"logTags: array expected";for(t=0;t<e.logTags.length;++t){var r;if(r=$root.cls.LogTag.verify(e.logTags[t]))return"logTags."+r}}return null},e.fromObject=function(e){if(e instanceof $root.cls.LogGroup)return e;var t=new $root.cls.LogGroup;if(e.logs){if(!Array.isArray(e.logs))throw TypeError(".cls.LogGroup.logs: array expected");t.logs=[];for(var r=0;r<e.logs.length;++r){if("object"!=typeof e.logs[r])throw TypeError(".cls.LogGroup.logs: object expected");t.logs[r]=$root.cls.Log.fromObject(e.logs[r])}}if(null!=e.contextFlow&&(t.contextFlow=String(e.contextFlow)),null!=e.filename&&(t.filename=String(e.filename)),null!=e.source&&(t.source=String(e.source)),e.logTags){if(!Array.isArray(e.logTags))throw TypeError(".cls.LogGroup.logTags: array expected");t.logTags=[];for(r=0;r<e.logTags.length;++r){if("object"!=typeof e.logTags[r])throw TypeError(".cls.LogGroup.logTags: object expected");t.logTags[r]=$root.cls.LogTag.fromObject(e.logTags[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.logs=[],r.logTags=[]),t.defaults&&(r.contextFlow="",r.filename="",r.source=""),e.logs&&e.logs.length){r.logs=[];for(var i=0;i<e.logs.length;++i)r.logs[i]=$root.cls.Log.toObject(e.logs[i],t)}if(null!=e.contextFlow&&e.hasOwnProperty("contextFlow")&&(r.contextFlow=e.contextFlow),null!=e.filename&&e.hasOwnProperty("filename")&&(r.filename=e.filename),null!=e.source&&e.hasOwnProperty("source")&&(r.source=e.source),e.logTags&&e.logTags.length){r.logTags=[];for(i=0;i<e.logTags.length;++i)r.logTags[i]=$root.cls.LogTag.toObject(e.logTags[i],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/cls.LogGroup"},e}(),e.LogGroupList=function(){function e(e){if(this.logGroupList=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.logGroupList=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.logGroupList&&e.logGroupList.length)for(var r=0;r<e.logGroupList.length;++r)$root.cls.LogGroup.encode(e.logGroupList[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,i=new $root.cls.LogGroupList;e.pos<r;){var n=e.uint32();if(n>>>3==1)i.logGroupList&&i.logGroupList.length||(i.logGroupList=[]),i.logGroupList.push($root.cls.LogGroup.decode(e,e.uint32()));else e.skipType(7&n)}return i},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.logGroupList&&e.hasOwnProperty("logGroupList")){if(!Array.isArray(e.logGroupList))return"logGroupList: array expected";for(var t=0;t<e.logGroupList.length;++t){var r=$root.cls.LogGroup.verify(e.logGroupList[t]);if(r)return"logGroupList."+r}}return null},e.fromObject=function(e){if(e instanceof $root.cls.LogGroupList)return e;var t=new $root.cls.LogGroupList;if(e.logGroupList){if(!Array.isArray(e.logGroupList))throw TypeError(".cls.LogGroupList.logGroupList: array expected");t.logGroupList=[];for(var r=0;r<e.logGroupList.length;++r){if("object"!=typeof e.logGroupList[r])throw TypeError(".cls.LogGroupList.logGroupList: object expected");t.logGroupList[r]=$root.cls.LogGroup.fromObject(e.logGroupList[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.logGroupList=[]),e.logGroupList&&e.logGroupList.length){r.logGroupList=[];for(var i=0;i<e.logGroupList.length;++i)r.logGroupList[i]=$root.cls.LogGroup.toObject(e.logGroupList[i],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/cls.LogGroupList"},e}(),e}();var cls=$root,handleLogs={formatLogGroup:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new cls.cls.LogGroup;t.filename&&(r.filename=t.filename),t.source&&(r.source=t.source),e.forEach((function(e){var t=new cls.cls.Log;t.time=e.time,Object.keys(e.contents).forEach((function(r){var i=e.contents[r],n=isString$2(i)?i:JSON.stringify(i);t.contents.push(new cls.cls.Log.Content({key:r,value:n}))})),r.logs.push(t)}));var i=cls.cls.LogGroup.verify(r);if(i)throw new ClsSDKError("log format is incorrect: ".concat(i));return r},log2Buffer:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new cls.cls.LogGroupList,i=handleLogs.formatLogGroup(e,t);return r.logGroupList.push(i),cls.cls.LogGroupList.encode(r).finish()},log2JSON:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.source,i=t.filename,n=_objectSpread2(_objectSpread2(_objectSpread2({},i&&{filename:i}),r&&{source:r}),{},{logs:e.map((function(e){var t=e.contents;return Object.keys(e.contents).forEach((function(e){try{void 0!==t[e]?t[e]=t[e].toString():t[e]=""}catch(e){throw new ClsSDKError("log format is incorrect: ".concat(e.message))}})),{contents:e.contents,time:e.time}}))});return JSON.stringify(n)},log2ZhiyanJSON:function(e,t){var r={topic:t,data:e.map((function(e){var t=e.contents;return Object.keys(e.contents).forEach((function(e){try{void 0!==t[e]?t[e]=t[e].toString():t[e]=""}catch(e){throw new ClsSDKError("log format is incorrect: ".concat(e.message))}})),{fields:e.contents,timestamp:e.time}}))};return JSON.stringify(r)}},HttpConnection=function(){function e(t){var r,i;_classCallCheck(this,e),_defineProperty(this,"stsCache",[]),_defineProperty(this,"CLS_HOST","cls.tencentcs.com"),_defineProperty(this,"cancelRequestSource",axios.CancelToken.source()),_defineProperty(this,"retryTimes",3),_defineProperty(this,"topicId",""),_defineProperty(this,"autoFillSourceIp",!1),_defineProperty(this,"api",{anony:"/tracklog",auth:"/structuredlog"}),_defineProperty(this,"credential",void 0),_defineProperty(this,"getAuthorization",void 0),_defineProperty(this,"ins",void 0),this.topicId=t.topicId,this.credential=t.credential,this.getAuthorization=t.getAuthorization,this.autoFillSourceIp=null===(r=t.autoFillSourceIp)||void 0===r||r,this.api=null!==(i=t.api)&&void 0!==i?i:{anony:"/tracklog",auth:"/structuredlog"},this.ins=this.getIns(t)}var t;return _createClass(e,[{key:"needAuth",get:function(){return isNotEmpty(this.credential)||isNotEmpty(this.getAuthorization)}},{key:"getIns",value:function(e){var t,r;this.retryTimes=null!==(t=e.retry)&&void 0!==t?t:3;var i=null!==(r=e.protocol)&&void 0!==r?r:"https";if(!isNotEmpty(e.region)&&!isNotEmpty(e.endpoint))throw new ClsSDKError("region or endpoint are required");var n="".concat(e.region,".").concat(this.CLS_HOST);e.region?n="".concat(e.region,".").concat(this.CLS_HOST):e.endpoint&&(n=e.endpoint);var o=this.getCommonHeaders(n),s={baseURL:"".concat(i,"://").concat(n),headers:o,timeout:5e3,cancelToken:this.cancelRequestSource.token,params:{topic_id:this.topicId}};if(e.agent){var a=e.agent,u=a.httpAgent,c=a.httpsAgent,l=a.proxy;i=e.agent.protocol||i,u&&(s.httpAgent=u),c&&(s.httpsAgent=c),l&&(s.proxy=l),e.proxy&&(s.proxy=l)}var f=axios.create(s);return this.generateCancelToken(f),this.initMethods(f),this.setReqInterceptors(f),this.setResInterceptors(f),f.defaults.adapter=mpAdapter$1,f}},{key:"getCommonHeaders",value:function(e){var t=_objectSpread2({},this.autoFillSourceIp&&{"x-cls-add-source":"1"});return this.needAuth?(t["Content-Type"]="application/x-protobuf",t.Host=e):t["Content-Type"]="application/json",t}},{key:"initMethods",value:function(e){var t=this;["PUT","POST","GET","DELETE","HEAD"].forEach((function(r){t[r.toLowerCase()]=function(t){if(!t.url)throw new Error("url 不能为空");return e(_objectSpread2({method:r},t))}}))}},{key:"changeAdapter",value:function(e){if(!this.ins)throw new ClsSDKError("HttpConnection is not initialized");this.ins.defaults.adapter=e}},{key:"cancelRequest",value:function(){if(!this.ins)throw new ClsSDKError("HttpConnection is not initialized");this.cancelRequestSource.cancel("cancel"),this.generateCancelToken(this.ins)}},{key:"setReqInterceptors",value:function(e){var t=this;e.interceptors.request.use(function(){var e=_asyncToGenerator(_regeneratorRuntime().mark((function e(r){var i,n,o,s,a;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=r.headers,n=r.params,o=r.url,s=r.method,e.next=3,t._getAuthorization({method:s,headers:i,query:n,api:(null==o?void 0:o.replace(/^\//g,""))||""});case 3:return(a=e.sent)&&(r.headers.Authorization=a.Authorization,a.SecurityToken&&(r.headers["x-cls-token"]=a.SecurityToken)),e.abrupt("return",r);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),(function(e){return Promise.resolve(e)}))}},{key:"setResInterceptors",value:function(e){var t=this;e.interceptors.response.use((function(e){return systemClock.handleOffset(e.headers.date),e}),function(){var r=_asyncToGenerator(_regeneratorRuntime().mark((function r(i){var n,o,s,a,u;return _regeneratorRuntime().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!axios.isCancel(i)){r.next=3;break}throw new ClsSDKError({code:"canceled",message:"Operation canceled by the user."});case 3:if(i.config.retryTimes||(i.config.retryTimes=0),!i.response){r.next=15;break}if(systemClock.handleOffset(i.response.headers.date),n=i.response,o=n.status,s=n.headers,a=n.config,u=n.data,!(413!==o&&a.retryTimes<t.retryTimes)){r.next=14;break}return a.retryTimes++,r.next=11,wait(1e3);case 11:return r.abrupt("return",e(a));case 14:throw new ClsSDKError({status:o,headers:s,code:u.errorcode,message:u.errormessage});case 15:throw new ClsSDKError({code:i.code||i.message,message:i.message});case 16:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}())}},{key:"putLogs",value:function(e){if(!this.ins)throw new ClsSDKError("HttpConnection is not initialized");var t="string"==typeof this.api?this.api:this.api.auth,r="string"==typeof this.api?this.api:this.api.anony;if(this.needAuth){var i=handleLogs.log2Buffer(e);return this.post({url:t,data:i})}var n=handleLogs.log2JSON(e);return this.post({url:r,data:n})}},{key:"putZhiyanLogs",value:function(e){if(!this.ins)throw new ClsSDKError("HttpConnection is not initialized");var t=handleLogs.log2ZhiyanJSON(e,this.topicId),r="string"==typeof this.api?this.api:this.api.anony;return this.post({url:r,data:t})}},{key:"generateCancelToken",value:function(e){this.cancelRequestSource=axios.CancelToken.source(),e.defaults.cancelToken=this.cancelRequestSource.token}},{key:"_getAuthorization",value:(t=_asyncToGenerator(_regeneratorRuntime().mark((function t(r){var i,n,o,s,a=this;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.formatHeader(r.headers),!this.credential){t.next=3;break}return t.abrupt("return",e.calcAuth(_objectSpread2(_objectSpread2(_objectSpread2({},this.credential),r),{},{headers:n})));case 3:if(o=void 0,function(){var e,t;for(e=a.stsCache.length-1;e>=0;e--){t=a.stsCache[e];var r=Math.round(systemClock.now()/1e3)+30;if(!(t.StartTime&&r<t.StartTime||r>=t.ExpiredTime)){o=t;break}a.stsCache.splice(e,1)}}(),!(null!==(i=o)&&void 0!==i&&i.ExpiredTime&&o.ExpiredTime-systemClock.now()/1e3>60)){t.next=9;break}return t.abrupt("return",e.calcAuth(_objectSpread2(_objectSpread2(_objectSpread2({},r),{},{headers:n},o),{},{SecretId:o.TmpSecretId,SecretKey:o.TmpSecretKey})));case 9:if(!this.getAuthorization){t.next=24;break}return t.next=12,this.getAuthorization({method:r.method,query:r.query,headers:n});case 12:if("string"!=typeof(s=t.sent)){t.next=17;break}return t.abrupt("return",{Authorization:s});case 17:if(!(s.StartTime&&s.TmpSecretId&&s.TmpSecretKey&&s.Token&&s.ExpiredTime)){t.next=23;break}return o=s,this.stsCache.push(o),t.abrupt("return",e.calcAuth(_objectSpread2(_objectSpread2(_objectSpread2({},r),{},{headers:n},o),{},{SecretId:o.TmpSecretId,SecretKey:o.TmpSecretKey})));case 23:throw new ClsSDKError("getAuthorization return value is not standardized.");case 24:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})}],[{key:"formatHeader",value:function(e){var t={},r=new Set(["Content-Type","Host","Content-Length"]);return Object.keys(e).forEach((function(i){r.has(i)&&(t[i]=e[i])})),t}},{key:"calcAuth",value:function(e){return{Authorization:sign(e),SecurityToken:e.SecurityToken||""}}}]),e}(),UploadState;!function(e){e[e.start=0]="start",e[e.waiting=1]="waiting",e[e.running=2]="running",e[e.stop=3]="stop"}(UploadState||(UploadState={}));var Uploader=function(){function e(t){var r,i,n,o=t.config;_classCallCheck(this,e),_defineProperty(this,"queue",[]),_defineProperty(this,"state",UploadState.stop),this.config=o,this.http=new HttpConnection({region:this.config.region,api:this.config.api,endpoint:this.config.endpoint,topicId:this.config.topicId,agent:null===(r=(i=this.config).getAgent)||void 0===r?void 0:r.call(i),proxy:this.config.proxy,credential:this.config.credential,getAuthorization:this.config.getAuthorization,autoFillSourceIp:null!==(n=this.config.autoFillSourceIp)&&void 0!==n?n:!isNotEmpty(this.config.sourceIp)})}var t;return _createClass(e,[{key:"start",value:function(){this.state=UploadState.start,this.startNextBatch()}},{key:"add",value:function(e){var t={index:this.queue.length,logs:e};this.queue.push(t),this.startNextBatch()}},{key:"startNextBatch",value:(t=_asyncToGenerator(_regeneratorRuntime().mark((function e(){var t;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.state!==UploadState.running){e.next=2;break}return e.abrupt("return");case 2:this.state=UploadState.running;case 3:if(!(this.queue.length>0&&this.isStart())){e.next=21;break}if(!(t=this.queue.shift())){e.next=19;break}if(e.prev=6,!this.config.endpoint||!this.config.endpoint.includes("zhiyan")){e.next=12;break}return e.next=10,this.http.putZhiyanLogs(t.logs);case 10:e.next=14;break;case 12:return e.next=14,this.http.putLogs(t.logs);case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(6),this.config.onError(e.t0);case 19:e.next=3;break;case 21:this.state=UploadState.waiting;case 22:case"end":return e.stop()}}),e,this,[[6,16]])}))),function(){return t.apply(this,arguments)})},{key:"isStart",value:function(){return this.state!==UploadState.stop}},{key:"stop",value:function(){this.http.cancelRequest(),this.state=UploadState.stop}}]),e}(),ClsClient=function(){function e(t){_classCallCheck(this,e),_defineProperty(this,"logTimer",void 0),_defineProperty(this,"checkCacheLocked",!1),_defineProperty(this,"logList",[]),_defineProperty(this,"initTag",!1),t&&this.init(t)}var t;return _createClass(e,[{key:"init",value:function(e){this.config=new ClientConfig(e),this.uploader=new Uploader({config:this.config}),this.initTag=!0}},{key:"checkLogCaches",value:(t=_asyncToGenerator(_regeneratorRuntime().mark((function e(){var t,r,i,n,o,s;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.checkCacheLocked){e.next=2;break}return e.abrupt("return");case 2:if(0!==(t=this.logList.length)){e.next=5;break}return e.abrupt("return");case 5:if(clearTimeout(this.logTimer),this.checkCacheLocked=!0,this.logList.some((function(e){return e.immediate}))||!(t<this.config.maxRetainSize)){e.next=18;break}if(r=this.logList[0],i=r.time,n=systemClock.now(),!((o=(n-i)/1e3)<this.config.maxRetainDuration)){e.next=18;break}return s=this.config.maxRetainDuration-o+1,this.checkCacheLocked=!1,this.logTimer=setTimeout(this.checkLogCaches.bind(this),1e3*s),e.abrupt("return");case 18:this.checkCacheLocked=!1,this.uploader.add(this.logList.splice(0,t)),this.checkLogCaches();case 21:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"log",value:function(e,t){if(!this.initTag)throw new ClsSDKError("ClsClient initialization method not called!");var r=null!=t&&t,i={contents:e,time:Date.now(),immediate:r};this.logList.push(i),this.checkLogCaches()}},{key:"destory",value:function(){clearTimeout(this.logTimer),this.uploader.stop(),this.initTag=!1}}]),e}();return ClsClient}));
