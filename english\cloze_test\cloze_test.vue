<template>
	<view class="container">
		<c-head></c-head>
		<content :qType="'完型填空'" :qText="qText"></content>
			<cover>
		      <scroll-view class="drawer-container" scroll-y="true" style="height: 100%; background-color:#fff">
				  <view class="title-container">
				  	<view class="title-list" :style="{height:isShowAll? Math.ceil(16/4)*80+'rpx' :'80rpx'}">
				  		<text @click="currentChoose=i" :class="[{done:i<4},{sel:currentChoose==i}]"  class="title" v-for="i in 16"> 第{{i+1}}题</text>
				  	</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up"  @click="isShowAll=true" size="44"></u-icon>
					</view>
				  </view>
				  <view class="q-container">
				  	<view class="q-sel-list">
				  		<view class="q-item" v-for="item in qData.seloption" :key="item.optionName">
				  			<view class="q-check-normal" :class='{"q-check-sel": currentSel==item.optionName}'  @click="currentSel=item.optionName">
				  				{{item.optionName}}
				  			</view>
				  			<text class="q-text">{{item.text}}</text>
				  		</view>
				  	</view>
					
					
		
					
				  </view>
	
				  
				  

				  
	
				  
	
				  
			
				  	
				  			<view class="bottom-btn">
				  				<view class="prev">
				  					上一题
				  				</view>
				  				<view class="next">
				  					下一题
				  				</view>
				  			</view>
			  </scroll-view>
		</cover>
	</view>
</template>

<script>
	import cHead from "@/components/c_head/c_head.vue"
	import content from "../components/content/content.vue"
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	export default {
		data() {
			return {
				isShowAll:false,
				currentSel:"",
				currentChoose:0,
				trapList:[{
						id:1,
						title:'考点精讲1-英语语法知识'
					},
					{
						id:2,
						title:'考点精讲1-英语语法知识'
					}],
				qData:{
					title:'21.What should the government do with regard to artificial glass?',
					seloption:[
						{
							optionName:"A",
							text:"pauses"
						},
						{
							optionName:"B",
							text:"pauses"
						},
						{
							optionName:"C",
							text:"pauses"
						},
						{
							optionName:"D",
							text:"pauses"
						}
					]
				},
				qText:`Here's a common scenario that any number of entrepreneurs face today: you're the CEO of a small business, 
						and though you're making a nice__,  need to find a way to take it to the next level. 
						What you need  is __ growth by establishing a growth team. 
						A growth team is made up of members from different departments within your __ company, 
						and harnesses the power of collaboration to focus __ on finding ways to grow.`
				
			};
		},
		components:{
			cHead,
			content,
			cover
		},
	}
</script>

<style lang="scss" scoped>
.container{
	box-sizing: b;
	background-color: $container-bg-color;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
			}
		}
	
	}
	.drawer-container{
		height: 100%;
		background-color:rgb(247, 247, 247);
	}
	.title-container{
		display: flex;
		align-items: flex-start;
		justify-content: center;
		background-color: rgb(247, 247, 247);
		.title-list{
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			transition: height 0.3s ease;
			overflow: hidden;
			background-color: rgb(247, 247, 247);
			.title{
				width: 100rpx;
				color: #777777;
				font-size: 28rpx;
				padding: 15rpx 30rpx;
			}
		}
		.right-icon{
			width: 140rpx;
			padding-top: 15rpx;
			padding-right: 30rpx;
			::v-deep .uicon-arrow-up{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
			::v-deep .uicon-arrow-down{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
		}
		.done{
			color: $main-color !important;
		}
		.sel{
			color: #0A0A0A;
			font-weight: bold;
		}
	}

	.q-container{
		padding: 0 36rpx;
		background-color: #fff;
		margin-top: 60rpx;
		
		
	}
	.q-text{
		
		line-height: 50rpx;
		color: #5A5A5A;
		font-size: 28rpx;
	}
	.q-title{
		margin-top: 36rpx;
		margin-bottom: 50rpx;
	}
	.q-sel-list{
		padding-bottom: 12rpx;
		.q-item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;
			text{
				width: 590rpx;
			}
			.q-check-normal{
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}
			.q-check-sel{			
				background: #01997A;
				border: 0;
				color: #fff;
				
			}
		}
	}
		
	.q-answer,.analysis,.knowledge-points, .traps, .knowledge-origin{
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 30rpx;
	}
	.q-answer-info{
		margin-bottom: 30rpx;
		text{
			font-size: 28rpx;
			color: #5A5A5A;
			font-weight: bold;
			letter-spacing: 4rpx;
			&:nth-child(2){
				margin-left: 10rpx;
				color: $main-color;
			}
			&:last-child{
				margin-left: 10rpx;
				color: rgb(206, 97, 117);
			}
		}
	}
	
	.analysis{
		.answer-info{
			color: #5A5A5A;
			font-size: 28rpx;
		}
	}
	.knowledge-points{
		.point{
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			.point-tag{
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				 font-weight: bold;
			}
		}
	}
	.knowledge-origin{
		.knowledge-origin-title{
			color: #5A5A5A;
			font-size: 28rpx;
		}
		}
	.bottom-btn{
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		margin-bottom: 100rpx;
		view{
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}
		.prev{
			background: #FFFFFF;
			color: $main-color;
		}
		.next{
			background-color: $main-color;
			color: #fff;
		}
	}
	
}
</style>