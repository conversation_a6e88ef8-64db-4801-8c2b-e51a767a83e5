<template>
	<view class="container u-border-top">
		<view class="cate-list">
			<text v-for="item in cateList" :key="item.id" @click="turnTo(item)">{{item.name}}</text>
		</view>
		<view class="img-container">
			<swiper class="swiper" :style="{ height: swiperHeight + 'px' }" circular :autoplay="autoplay" :interval="interval"
				:duration="duration">
				<swiper-item v-for="item in bannerList" :key="item.id">
					<image :src="item.image"  @load="onImageLoad" mode="widthFix" style="width: 100%;"></image>
				</swiper-item>
			</swiper>
		</view>
		<view class="line">

		</view>
		<div class="list">
			<view class="title-name">
				<view class="left">
					<text class="green-block"></text>
					<text>推荐</text>
				</view>
				<view class="content">

				</view>
			</view>
			<view class="item u-border-bottom" v-for="i in recommendList" :key="i"   @click="toStudy(i.id)">
				<view>
					<image  class="image" :src="i.pic" mode="widthFix"> 
					</imgae>					
				</view>				
				<view class="title-info">
					<text class="t-h1">{{i.name}}</text>
					<text class="tip">{{i.subtitle}}</text>
					<view class="tip"><u-icon name="play-right"  size="30"></u-icon>已播放{{i.total_text.see_num}}次|共{{i.total_text.total}}集</view>
				</view>
				<view class="play-icon">
					<u-icon name="play-circle-fill" size="50" color="#009c7b"></u-icon>
				</view>
			</view>
		</div>

	</view>
</template>

<script>
	import {getBannerList,getStudyCateList} from "@/api/exampoint/index.js"
	export default {
		data() {
			return {
				bannerList: [],
				indicatorDots: true,
				autoplay: true,
				interval: 3000,
				duration: 800,
				swiperHeight:0,
				cateList: [
				],
				recommendList:[],
			};
		},
		methods: {
			turnTo(item) {
				console.log(item)
				uni.navigateTo({
					url: `/subpkg/exam_point_study/exam_point_study_second?id=${item.id}`
				})
			},
			toStudy(id) {
				uni.navigateTo({
					url:'/choosecoursepkg/study/study_exam_point?cid='+id
				})
			},
			 onImageLoad(event) {
			      const { height, width } = event.detail;
				  console.log(height)
			      const screenWidth = wx.getSystemInfoSync().windowWidth;
			      const imageHeight = (height / width) * screenWidth;
			      if (imageHeight > this.swiperHeight) {
			        this.swiperHeight = imageHeight;
			      }
			    }

		},
		async onLoad() {
			
		},
		created(){
			// getBannerList(0).then(res => {
			// 	if (res !== false) {
			// 		this.bannerList = res
			// 	}
			// })
			getStudyCateList().then(res=>{
				if(res !=false){
					this.cateList = res.data.map(item=>{
						return {
							id:item.id,
							name:item.name
						}
					})
					this.recommendList = res.recommendList
				}
				})
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.cate-list {
			padding: 18rpx 0;
			padding-left: 30rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			flex-wrap: wrap;

			text {
				font-weight: 500;
				font-size: 28rpx;
				color: #201E2E;
				line-height: 70rpx;
				text-align: center;
				width: 150rpx;
				height: 70rpx;
				background: #F6F7FB;
				border-radius: 12rpx;
				margin-bottom: 18rpx;
				margin-right: 30rpx;
			}
		}
		
		.img-container {
			padding: 0 30rpx;
		}

		.line {
			width: 100%;
			height: 10rpx;
			background: #F6F7FB;
		}

		.list {
			padding: 0 30rpx;
			padding-bottom: 40rpx;

			.title-name {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 0;
				padding-bottom: 0;

				.left {
					color: #1F1F27;
					font-size: 32rpx;
					font-weight: bold;

					.green-block {
						display: inline-block;
						width: 14rpx;
						height: 28rpx;
						line-height: 28rpx;
						background: #01997A;
						border-radius: 0rpx 8rpx 0rpx 8rpx;
						margin-right: 6rpx;
					}
				}

			}

			.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-bottom: 22rpx;
				padding-top: 40rpx;

				// image{
				// 	width: 100rpx;
				// 	height: 100rpx;
				// 	border-radius: 14rpx;
				// }
				.image{
					width: 100rpx;
					margin-left: 30rpx;
				}
				.title-info {
					width: 60%;
					display: flex;
					align-items: center;
					justify-content: center;
					flex-direction: column;
					text-align: left;

					.t-h1 {
						width: 100%;
						color: #201E2E;
						font-size: 32rpx;
						font-weight: bold;
					}

					.tip {
						width: 100%;
						color: #AFAFAF;
						font-size: 26rpx;
						margin-top: 16rpx;
						display: flex;
					}
				}

				.play-icon {
					margin-right: 20rpx;
				}
			}
		}
	}
</style>