<template>
	<view class="video-container" ref="videoContainer">
		<view id="video-wrapper-container">
			<view id="video-wrapper" class="video-wrapper" :prop="renderjsAction" :change:prop="videoRenderer.renderjsActionChange">
				<!-- 用空白view作为视频容器，renderjs会在这里创建视频元素 -->
			</view>

			<view id="controls-container" class="controls-bar-container">
				<view class="progress-container" @tap.stop="moveProcess($event)">
					<my-progress res="my_progress" :isFull='isFull' @posChange="posChange" :percentage="percentage"></my-progress>
				</view>

				<view class="controls-bar" @tap.stop="() => { }">
					<view class="video-left-icon">
						<u-icon @tap="play" :name="playIcon" color="#fff" size="38"></u-icon>
						<u-icon :name="!mutedFlag ? volumeIcon : volumeOffIcon" color="#fff" @tap="mutedChange" size="38"></u-icon>
						<text class="controls-bar-time">{{ currentTime }}/{{ allTime != 0 ? allTime : "0:00" }}</text>
					</view>
					<view class="video-right-icon">
						<image :src="backPng" mode="widthFix" @tap="move(1)"></image>
						<image :src="forwardPng" mode="widthFix" @tap="move(2)"></image>
						<view @tap="showSetRateList = !showSetRateList" class="set-rate">
							<text>x{{ currentRate }}</text>
							<view class="choose-rate" v-if="showSetRateList">
								<text @tap="setRate(0.75)">x0.75</text>
								<text @tap="setRate(1)">x1</text>
								<text @tap="setRate(1.25)">x1.25</text>
								<text @tap="setRate(1.5)">x1.5</text>
								<text @tap="setRate(2)">x2</text>
							</view>
						</view>
						<image @tap="full" src="@/static/img/full_screen.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import backPng from "@/static/img/back_30s.png"
import forwardPng from "@/static/img/forward_30s.png"
import myProgress from '@/components/my-progress/my-progress.vue';

export default {
	data() {
		return {
			showSetRateList: false,
			firstStart: false,
			backPng,
			forwardPng,
			playIcon: "play-right-fill",
			volumeIcon: "volume",
			volumeOffIcon: "volume-off",
			isPlay: false,
			currentRate: 1,
			isFull: false,
			currentTime: "0:00",
			currentSecond: 0,
			allTime: "0",
			percentage: 0,
			allSecond: 0,
			isFullClick: false,
			mutedFlag: false,
			userIsMove: false,
			hadFlag: false,
			hadFirstPlay: false,
			setIntervalTime: '',
			recordIntervalTime: '',
			second: 0,
			recordSecond: 0,
			controlsBarShowStatus: true,
			recordData: {},
			// 用于与renderjs通信的属性
			renderjsAction: {
				type: '',     // 操作类型：play, pause, seek, setRate, toggleMute, fullscreen, exitFullscreen, loadVideo
				value: null,  // 操作值
				timestamp: 0  // 时间戳，用于确保每次操作都是新的
			},
			lastUpdateTime: null
		};
	},
	mounted() {
		// 监听屏幕方向变化
		if (typeof window != 'undefined') {
			window.addEventListener('orientationchange', () => {
				if (window.orientation === 90 || window.orientation === -90) {
					// 横屏
					this.sendToRenderjs('fullscreen');
				} else {
					// 竖屏
					this.sendToRenderjs('exitFullscreen');
				}
			});
		}
		// 监听返回按钮
		this.initBackButtonListener();
	},
	onUnload() {
		plus.screen.unlockOrientation();
	},
	beforeDestroy() {
		// 清理所有计时器
		clearInterval(this.setIntervalTime);
		this.setIntervalTime = null;

		clearInterval(this.recordIntervalTime);
		this.recordIntervalTime = null;

		// 重置所有状态
		this.isPlay = false;
		this.hadFirstPlay = false;
		this.hadFlag = false;
		this.userIsMove = false;
		this.lastUpdateTime = null;

		// 发送离开事件
		let tmpParams = { statTime: this.recordSecond };
		this.recordData.event = 'leavel';
		this.recordData.param = tmpParams;
		this.recordVideoEvent();

		// 通知renderjs销毁视频
		this.sendToRenderjs('destroy');


		// 移除屏幕方向监听
		window && window.removeEventListener('orientationchange', () => { });

		// 移除返回按钮监听
		plus.key.removeEventListener('backbutton');
	},
	props: ["src", "play_time", "course_type", "video_id"],
	watch: {
		src: {
			handler(newval, oldval) {
				// 重置视频状态
				this.isPlay = false;
				this.playIcon = "play-right-fill";
				this.percentage = 0;
				this.currentRate = 1;

				// 加载新视频
				this.sendToRenderjs('loadVideo', {
					src: newval,
					initialTime: 0
				});
			},
			deep: true
		}
	},
	methods: {
		// 向renderjs发送操作
		sendToRenderjs(type, value = null) {
			this.renderjsAction = {
				type: type,
				value: value,
				timestamp: Date.now()
			};
		},

		// 视频进度条操作
		moveProcess(e) {
			this.$children[0].getPoint(e);
		},

		// 学习相关方法
		beginStudy() {
			let that = this;
			if (that.course_type != '') {
				if (!this.setIntervalTime) {
					this.setIntervalTime = setInterval(function () {
						that.second += 3;
						if (that.second % 5 == 0) {
							that.getStudyData(15);
							that.second = 0;
						}
					}, 3000);
				}
			}
		},

		endStudy() {
			this.getStudyData(this.second);
			this.second = 0;
			clearInterval(this.setIntervalTime);
			this.setIntervalTime = null;
		},

		// 记录学习数据
		getStudyData(time) {
			let that = this;
			uni.$u.http.post('/mini_user/recordUserStudyData',
				{
					video_id: that.video_id,
					type: that.course_type,
					source_type: 2,
					time: (time * that.currentRate),
				},
				{
					custom: { loading: false },
				});
		},

		recordVideoEvent() {
			let that = this;
			uni.$u.http.post('/task/recordVideoEvent',
				that.recordData,
				{
					custom: { loading: false },
				});
		},

		// 视频结束事件
		handleVideoEnd() {
			this.play();
		},

		// 记录用户停留页面时间
		recordStatTime() {
			let that = this;
			this.recordIntervalTime = setInterval(function () {
				that.recordSecond += 3;
			}, 3000);
		},

		// 处理视频暂停事件（由renderjs调用）
		handleVideoPause() {
			console.log('视频暂停');
			// 更新界面状态
			this.isPlay = false;
			this.playIcon = "play-right-fill";
		},

		// 处理视频播放事件（由renderjs调用）
		handleVideoPlay() {
			console.log('视频播放');
			// 更新界面状态
			this.isPlay = true;
			this.playIcon = "pause";

			// 如果是首次播放，记录事件并开始学习记录
			if (!this.hadFirstPlay) {
				this.hadFirstPlay = true;

				// 记录播放事件
				let tmpParams = { playTime: this.currentSecond };
				this.recordData.event = 'play';
				this.recordData.param = tmpParams;
				this.recordVideoEvent();

				// 开始学习记录
				this.beginStudy();
				this.recordStatTime();
			}
		},

		// 处理视频缓冲事件（由renderjs调用）
		handleVideoBuffering() {
			console.log('视频缓冲中');
			uni.showLoading({
				title: '视频缓冲中',
				mask: false
			});
		},

		// 处理视频可以播放事件（由renderjs调用）
		handleVideoCanPlay() {
			console.log('视频可以播放');
			uni.hideLoading();
		},

		// 处理视频元数据加载完成事件（由renderjs调用）
		ParentHandleMetadataLoaded(data) {
			console.log('视频元数据加载完成:', data);

			// 视频元数据加载完成，关闭加载提示
			uni.hideLoading();

			// 更新总时长
			this.allSecond = data.duration || 0;

			// 如果有指定播放时间
			if (this.play_time > 0) {
				// 设置播放位置显示
				let tmp_time = Math.floor(this.play_time);
				let tmp_min = Math.floor(tmp_time / 60);
				let tmp_second = tmp_time % 60;
				this.currentTime = `${tmp_min}:${tmp_second < 10 ? '0' + tmp_second : tmp_second}`;

				// 更新进度百分比
				this.percentage = (this.play_time / this.allSecond) * 100;
			}

			// 更新总时长显示
			let tmp_min = Math.floor(this.allSecond / 60);
			let tmp_second = Math.floor(this.allSecond % 60);
			this.allTime = `${tmp_min}:${tmp_second < 10 ? '0' + tmp_second : tmp_second}`;

			console.log('allTime', this.allTime);
			this.$nextTick(() => {
				uni.hideLoading();
			});
		},

		// 处理视频时间更新事件（由renderjs调用）
		logicHandleTimeUpdate(data) {
			console.log('handleTimeUpdate', data);
			// 如果上次更新时间距离现在小于100ms，则跳过更新
			if (this.lastUpdateTime && Date.now() - this.lastUpdateTime < 100) {
				return;
			}
			this.lastUpdateTime = Date.now();

			// 更新当前时间显示
			let tmp_currentTime = data.currentTime || 0;
			let tmp_time = Math.floor(tmp_currentTime);
			let tmp_min = Math.floor(tmp_time / 60);
			let tmp_second = tmp_time % 60;
			this.currentTime = `${tmp_min}:${tmp_second < 10 ? '0' + tmp_second : tmp_second}`;
			console.log('currentTime', this.currentTime);
			// 更新内部时间记录
			this.currentSecond = tmp_currentTime;

			// 计算是否需要更新进度条
			let is_int = (tmp_time) % 10;
			if (this.allSecond < 60) {
				is_int = (tmp_time) % 2;
			} else if (this.allSecond < 180) {
				is_int = (tmp_time) % 5;
			}

			if (this.userIsMove || (is_int == 0 && tmp_time > 0)) {
				if (!this.hadFlag) {
					this.hadFlag = true;
					this.calcPer(tmp_currentTime);
				}
				if (this.userIsMove) {
					this.userIsMove = false;
				}
			}
		},

		// 处理视频错误事件（由renderjs调用）
		handleVideoError(error) {
			uni.hideLoading();
			console.error('视频播放错误:', error);

			uni.showToast({
				title: '视频播放出错',
				icon: 'none'
			});
		},

		// 处理全屏状态变化事件（由renderjs调用）
		handleFullscreenChange(data) {
			console.log('全屏状态变化:', data);
			this.isFull = data.isFullscreen;
		},

		// 进度条位置变化
		posChange(pos) {
			// 获取跳转的位置
			let tmpSecond = this.currentSecond;
			this.currentSecond = pos * this.allSecond;

			// 计算并更新UI
			this.calcPer(this.currentSecond);

			// 通知renderjs跳转到指定位置
			this.sendToRenderjs('seek', this.currentSecond);

			// 记录跳转事件
			let tmpParams = { playTime: tmpSecond, toPlayTime: this.currentSecond };
			this.recordData.param = tmpParams;
			this.recordData.event = 'tovideo';
			this.recordVideoEvent();
		},

		// 静音切换
		mutedChange() {
			this.mutedFlag = !this.mutedFlag;

			// 通知renderjs更新静音状态
			this.sendToRenderjs('toggleMute', this.mutedFlag);
		},

		// 计算并更新进度百分比
		calcPer(currentTime) {
			let that = this;
			setTimeout(function () {
				that.hadFlag = false;
			}, 800);

			// 发送当前时间到父组件
			this.sendToParent(currentTime);

			// 计算进度百分比
			this.percentage = (currentTime / this.allSecond) * 100;
		},

		// 快进/快退30秒
		move(type) {
			let tmpParams = {};
			let newTime = 0;

			if (type == 2) { // 快进
				let tmp = this.currentSecond + 30;
				newTime = tmp > this.allSecond ? this.allSecond : tmp;
				tmpParams = { playTime: this.currentSecond, toPlayTime: newTime };
				this.recordData.event = 'fast';
			} else { // 快退
				let tmp = this.currentSecond - 30;
				newTime = tmp < 0 ? 0 : tmp;
				tmpParams = { playTime: this.currentSecond, toPlayTime: newTime };
				this.recordData.event = 'reback';
			}

			this.currentSecond = newTime;

			// 更新UI和通知renderjs
			this.calcPer(this.currentSecond);
			this.sendToRenderjs('seek', this.currentSecond);

			// 记录事件
			this.recordData.param = tmpParams;
			this.recordVideoEvent();
		},

		// 播放/暂停控制
		play() {
			if (this.isPlay) {
				// 当前正在播放，执行暂停
				this.isPlay = false;
				this.playIcon = "play-right-fill";
				this.endStudy();

				// 记录暂停事件
				let tmpParams = { playTime: this.currentSecond };
				this.recordData.event = 'pause';
				this.recordData.param = tmpParams;
				this.recordVideoEvent();

				// 通知renderjs暂停
				this.sendToRenderjs('pause');

			} else {
				// 当前已暂停，执行播放
				this.isPlay = true;
				this.playIcon = "pause";

				// 如果已经播放过，则恢复学习记录
				if (this.hadFirstPlay) {
					this.beginStudy();
				}

				// 记录播放事件
				let tmpParams = { playTime: this.currentSecond };
				this.recordData.event = 'play';
				this.recordData.param = tmpParams;
				this.recordVideoEvent();

				// 通知renderjs播放
				this.sendToRenderjs('play');
			}
		},

		// 设置播放速率
		setRate(rate) {
			this.currentRate = rate;
			this.showSetRateList = false;

			// 记录速率变更事件
			let tmpParams = { playTime: this.currentSecond, rate: this.currentRate };
			this.recordData.event = 'rate';
			this.recordData.param = tmpParams;
			this.recordVideoEvent();

			// 通知renderjs更新播放速率
			this.sendToRenderjs('setRate', this.currentRate);
		},

		// 全屏切换
		full() {
			if (!this.isFull) {
				// 进入全屏
				this.recordData.event = 'full';
				plus.screen.lockOrientation('landscape');
				setTimeout(() => {
					this.sendToRenderjs('fullscreen');
					this.isFull = true;
				}, 100);
			} else {
				// 退出全屏
				this.exitFullScreenAndRotate();
			}

			// 记录全屏事件
			let tmpParams = { playTime: this.currentSecond };
			this.recordData.param = tmpParams;
			this.recordVideoEvent();
		},

		// 向父组件发送时间数据
		sendToParent(time) {
			let time_data = {
				now_time: time,
				total_time: this.allSecond,
				rate: this.currentRate
			};
			this.$emit('custom-event', time_data);
		},

		// 修改 initBackButtonListener 方法
		initBackButtonListener() {
			try {
				if (window.plus && window.plus.key) {
					console.log('开始设置返回按钮监听器');
					window.plus.key.addEventListener('backbutton', async (e) => {
						if (this.isFull) {
							e.preventDefault(); // 阻止默认返回行为
							await this.exitFullScreenAndRotate(); // 调用新的退出全屏方法
							return false;
						}
					}, false);
				} else {
					console.warn('plus 对象未初始化，无法设置返回按钮监听器');
				}
			} catch (error) {
				console.error('设置返回按钮监听器失败:', error);
			}
		},

		// 新增退出全屏和旋转屏幕的方法
		exitFullScreenAndRotate() {
			return new Promise((resolve) => {
				// 先退出全屏
				this.sendToRenderjs('exitFullscreen');

				// 记录事件
				this.recordData.event = 'exitfull';
				let tmpParams = { playTime: this.currentSecond };
				this.recordData.param = tmpParams;
				this.recordVideoEvent();

				// 等待一小段时间确保退出全屏完成
				setTimeout(() => {
					// 解锁屏幕方向
					plus.screen.unlockOrientation();
					// 强制旋转到竖屏
					plus.screen.lockOrientation('portrait-primary');

					// 更新状态
					this.isFull = false;

					// 等待屏幕旋转完成后恢复界面
					setTimeout(() => {
						// 确保视频容器和控件都恢复到正确的竖屏布局
						const container = document.getElementById('video-wrapper-container');
						const videoWrapper = document.getElementById('video-wrapper');
						const videoElement = document.querySelector('.video-element');

						if (container) {
							container.style.maxHeight = '520rpx';
							container.style.height = '100%';
						}

						if (videoWrapper) {
							videoWrapper.style.maxHeight = '520rpx';
							videoWrapper.style.height = '100%';
						}

						if (videoElement) {
							videoElement.style.maxHeight = '520rpx';
							videoElement.style.height = '100%';
						}

						resolve();
					}, 300); // 等待屏幕旋转完成
				}, 100);
			});
		}
	},
	components: {
		myProgress
	}
}
</script>

<!-- renderjs脚本，完全控制视频元素的创建和操作 -->
<script module="videoRenderer" lang="renderjs">
	// 视频元素引用
	let videoElement = null;
	// 控制栏显示状态
	let controlsVisible = true;
	// 控制栏自动隐藏定时器
	let controlsTimeout = null;
	// 上次操作的时间戳
	let lastActionTimestamp = 0;
	let loaded = false;
	let playTimerId = null;
	let lastTimeUpdateTime = 0;
	
	// 保存原始样式
	let originalStyles = {
		container: {},
		videoWrapper: {},
		video: {},
		controls: {}
	};
	
	export default {	
		mounted() {
			// 在DOM挂载后创建视频元素
			this.$nextTick(() => {
				try {
					this.createVideoElement();
					// 添加事件监听
					const wrapper = document.getElementById('video-wrapper');
					if (wrapper) {
						wrapper.addEventListener('click', this.handleClick);
						wrapper.addEventListener('dblclick', this.handleDoubleClick);
					}
					
					// 添加全屏变化监听
					document.addEventListener('fullscreenchange', this.handleFullscreenChange);
					document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
					document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
					document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
				} catch (error) {
					console.error('Renderjs: 视频初始化错误', error);
				}
			});
		},
		
		beforeDestroy() {
			console.log('Renderjs: 视频渲染器销毁中');
			
			// 移除事件监听
			if (videoElement) {
				videoElement.removeEventListener('timeupdate', this.handleTimeUpdate);
				videoElement.removeEventListener('loadedmetadata', this.handleMetadataLoaded);
				videoElement.removeEventListener('ended', this.handleEnded);
				videoElement.removeEventListener('error', this.handleError);
				videoElement.removeEventListener('play', this.handlePlay);
				videoElement.removeEventListener('playing', this.handlePlaying);
				videoElement.removeEventListener('pause', this.handlePause);
				videoElement.removeEventListener('waiting', this.handleWaiting);
				videoElement.removeEventListener('canplay', this.handleCanPlay);
			}
			
			const wrapper = document.getElementById('video-wrapper');
			if (wrapper) {
				wrapper.removeEventListener('click', this.handleClick);
				wrapper.removeEventListener('dblclick', this.handleDoubleClick);
			}
			
			// 移除全屏变化监听
			document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
			document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
			document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
			document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
			
			// 清除控制栏自动隐藏定时器
			if (controlsTimeout) {
				clearTimeout(controlsTimeout);
			}
		},
		
		methods: {
			renderjsActionChange(newAction, oldAction) {
					console.log('Renderjs: 收到操作', newAction.type, newAction.value);
					if (!videoElement) return;
					if (!newAction || !newAction.type) return;
					
					// 避免重复处理相同的操作
					if (newAction.timestamp === lastActionTimestamp) return;
					
					console.log('Renderjs: 收到操作', newAction.type, newAction.value);
					lastActionTimestamp = newAction.timestamp;
					
					// 根据操作类型执行不同的处理
					switch (newAction.type) {
						case 'play':
							console.log('Renderjs: 播放视频');
							this.playVideo();
							break;
							
						case 'pause':
							console.log('Renderjs: 暂停视频');
							this.pauseVideo();
							break;
							
						case 'seek':
							this.seekVideo(newAction.value);
							break;
							
						case 'setRate':
							this.setVideoRate(newAction.value);
							break;
							
						case 'toggleMute':
							this.toggleVideoMute(newAction.value);
							break;
							
						case 'fullscreen':
							this.enterFullscreen();
							break;
							
						case 'exitFullscreen':
							this.exitFullscreen();
							break;
							
						case 'loadVideo':
							this.loadVideo(newAction.value);
							break;
							
						case 'destroy':
							this.destroyVideo();
							break;
							
						default:
							console.warn('Renderjs: 未知操作类型', newAction.type);
					}
				},
			// 创建视频元素
			createVideoElement() {
				// 获取容器
				const container = document.getElementById('video-wrapper');
				if (!container) {
					console.error('Renderjs: 未找到视频容器');
					return;
				}
				
				// 创建视频元素
				videoElement = document.createElement('video');
				videoElement.id = 'renderjs-video';
				videoElement.className = 'video-element';
				videoElement.style.width = '100%';
				videoElement.style.height = '100%';
				videoElement.style.objectFit = 'contain';
				
				// 设置视频属性
				videoElement.controls = false; // 使用自定义控制栏
				videoElement.playsInline = true;
				videoElement.webkitPlaysinline = true;
				
				console.log('Renderjs: 开始绑定事件监听器');
				
				// 添加事件监听
				const self = this;
				
				// 使用 addEventListener 方式绑定事件
				videoElement.addEventListener('timeupdate', function(event) {
					console.log('Renderjs: timeupdate 事件触发');
					self.handleTimeUpdate(event);
				});
				
				videoElement.addEventListener('loadedmetadata', function(event) {
					self.handleMetadataLoaded(event);
				});
				
				videoElement.addEventListener('ended', function(event) {
					self.handleEnded(event);
				});
				
				videoElement.addEventListener('error', function(event) {
					self.handleError(event);
				});
				
				videoElement.addEventListener('playing', function(event) {
					self.handlePlaying(event);
					self.$ownerInstance.callMethod('handleVideoPlay');
				});
				
				videoElement.addEventListener('pause', function(event) {
					self.handlePause(event);
				});
				
				videoElement.addEventListener('waiting', function(event) {
					self.handleWaiting(event);
				});
				
				videoElement.addEventListener('canplay', function(event) {
					self.handleCanPlay(event);
				});
				
				console.log('Renderjs: 事件监听器绑定完成');
				
				// 添加到容器
				container.appendChild(videoElement);
				console.log('Renderjs: 视频元素已创建并添加到容器');
			},
			
			// 加载视频
			loadVideo(options) {
				if (!videoElement) return;
				
				videoElement.src = options.src;
				
				if (options.initialTime > 0) {
					videoElement.currentTime = options.initialTime;
				}
				
				if (options.muted) {
					videoElement.muted = true;
				}
				
				videoElement.load();
			},
			
			// 播放视频
			playVideo() {
				if (!videoElement) return;
				
				videoElement.play().catch(err => {
					console.error('Renderjs: 播放错误', err);
					this.$ownerInstance.callMethod('handleVideoError', {
						message: err.message
					});
				});
			},
			
			// 暂停视频
			pauseVideo() {
				if (!videoElement) return;
				videoElement.pause();
			},
			
			// 跳转到指定时间
			seekVideo(time) {
				if (!videoElement) return;
				videoElement.currentTime = time;
			},
			
			// 设置播放速率
			setVideoRate(rate) {
				if (!videoElement) return;
				videoElement.playbackRate = rate;
			},
			
			// 切换静音状态
			toggleVideoMute(muted) {
				if (!videoElement) return;
				videoElement.muted = muted;
			},
			
			// 进入全屏
			enterFullscreen() {
				if (!videoElement) return;
				
				try {
					const container = document.getElementById('video-wrapper-container');
					const videoWrapper = document.getElementById('video-wrapper');
					const controlsContainer = document.getElementById('controls-container');
					
					if (!container) return;
					
					// 保存原始样式
					originalStyles.container = {
						position: container.style.position,
						top: container.style.top,
						left: container.style.left,
						width: container.style.width,
						height: container.style.height,
						zIndex: container.style.zIndex,
						backgroundColor: container.style.backgroundColor
					};
					
					if (videoWrapper) {
						originalStyles.videoWrapper = {
							width: videoWrapper.style.width,
							height: videoWrapper.style.height,
							position: videoWrapper.style.position
						};
					}
					
					originalStyles.video = {
						width: videoElement.style.width,
						height: videoElement.style.height,
						position: videoElement.style.position,
						objectFit: videoElement.style.objectFit
					};
					
					if (controlsContainer) {
						originalStyles.controls = {
							position: controlsContainer.style.position,
							bottom: controlsContainer.style.bottom,
							left: controlsContainer.style.left,
							width: controlsContainer.style.width,
							zIndex: controlsContainer.style.zIndex,
							transform: controlsContainer.style.transform,
							pointerEvents: controlsContainer.style.pointerEvents
						};
					}
					
					// 设置全屏样式
					container.style.position = 'fixed';
					container.style.top = '0';
					container.style.left = '0';
					container.style.width = '100vw';
					container.style.height = '100vh';
					container.style.backgroundColor = '#000';
					container.style.zIndex = '9999';
					
					if (videoWrapper) {
						videoWrapper.style.width = '100%';
						videoWrapper.style.height = '100%';
						videoWrapper.style.position = 'relative';
					}
					
					videoElement.style.width = '100%';
					videoElement.style.height = '100%';
					videoElement.style.objectFit = 'contain';
					
					if (controlsContainer) {
						controlsContainer.style.position = 'absolute';
						controlsContainer.style.bottom = '0';
						controlsContainer.style.left = '0';
						controlsContainer.style.width = '100%';
						controlsContainer.style.zIndex = '10000';
						controlsContainer.style.transform = 'translateY(0)';
						controlsContainer.style.pointerEvents = 'auto';
						controlsVisible = true;
					}
					
					// 请求全屏
					if (container.requestFullscreen) {
						container.requestFullscreen();
					} else if (container.webkitRequestFullscreen) {
						container.webkitRequestFullscreen();
					} else if (container.mozRequestFullScreen) {
						container.mozRequestFullScreen();
					} else if (container.msRequestFullscreen) {
						container.msRequestFullscreen();
					} else if( videoElement.webkitEnterFullscreen){
						 videoElement.webkitEnterFullscreen()
					}
				} catch (err) {
					console.error('Renderjs: 全屏错误', err);
					this.$ownerInstance.callMethod('handleVideoError', {
						message: '全屏模式失败: ' + err.message
					});
				}
			},
			
			// 退出全屏
			exitFullscreen() {
				try {
					const container = document.getElementById('video-wrapper-container');
					const videoWrapper = document.getElementById('video-wrapper');
					const controlsContainer = document.getElementById('controls-container');
					
					// 恢复容器原始样式
					if (container) {
						Object.assign(container.style, originalStyles.container);
						container.style.maxHeight = '520rpx';
					}
					
					// 恢复视频容器原始样式
					if (videoWrapper) {
						Object.assign(videoWrapper.style, originalStyles.videoWrapper);
						videoWrapper.style.maxHeight = '520rpx';
					}
					
					// 恢复视频元素原始样式
					if (videoElement) {
						Object.assign(videoElement.style, originalStyles.video);
						videoElement.style.maxHeight = '520rpx';
						videoElement.style.width = '100%';
						videoElement.style.height = '100%';
					}
					
					// 恢复控制栏原始样式
					if (controlsContainer) {
						Object.assign(controlsContainer.style, originalStyles.controls);
						controlsContainer.style.transform = 'translateY(0)';
						controlsVisible = true;
						
						// 重新启动自动隐藏
						this.autoHideControls();
					}
					
					// 退出全屏
					if (document.exitFullscreen) {
						document.exitFullscreen();
					} else if (document.webkitExitFullscreen) {
						document.webkitExitFullscreen();
					} else if (document.mozCancelFullScreen) {
						document.mozCancelFullScreen();
					} else if (document.msExitFullscreen) {
						document.msExitFullscreen();
					}
					
					// 确保在退出全屏后重置所有尺寸
					setTimeout(() => {
						if (container) {
							container.style.maxHeight = '520rpx';
							container.style.height = '100%';
							container.style.width = '100%';
						}
						if (videoWrapper) {
							videoWrapper.style.maxHeight = '520rpx';
							videoWrapper.style.height = '100%';
							videoWrapper.style.width = '100%';
						}
						if (videoElement) {
							videoElement.style.maxHeight = '520rpx';
							videoElement.style.height = '100%';
							videoElement.style.width = '100%';
						}
						
						// 重新初始化控制条状态
						if (controlsContainer) {
							controlsVisible = true;
							this.autoHideControls();
						}
					}, 100);
					
				} catch (err) {
					console.error('Renderjs: 退出全屏错误', err);
				}
			},
			
			// 销毁视频
			destroyVideo() {
				if (!videoElement) return;
				
				videoElement.pause();
				videoElement.src = '';
				videoElement.load();
			},
			
			// 视频事件处理函数
			handleTimeUpdate(event) {
				// 添加节流逻辑
				const now = Date.now();
				if (now - lastTimeUpdateTime < 100) {
					return;
				}
				lastTimeUpdateTime = now;
				
				try {
					if (!videoElement) {
						console.error('Renderjs: handleTimeUpdate - videoElement 不存在');
						return;
					}
					
					console.log('Renderjs: 时间更新', {
						currentTime: videoElement.currentTime,
						duration: videoElement.duration,
						readyState: videoElement.readyState,
						paused: videoElement.paused
					});
					
					// 向Vue层报告当前时间
					this.$ownerInstance.callMethod('logicHandleTimeUpdate', {
						currentTime: videoElement.currentTime,
						duration: videoElement.duration
					});
				} catch (error) {
					console.error('Renderjs: 时间更新错误', error);
				}
			},
			
			handleMetadataLoaded(event) {
				
				if(loaded){
					return;
				}
				console.log('Renderjs: 视频元数据已加载', JSON.stringify(event));
				const data = {
					duration: videoElement.duration,
					videoWidth: videoElement.videoWidth,
					videoHeight: videoElement.videoHeight
				}
				console.log("data", data)
				// 向Vue层报告视频元数据
				this.$ownerInstance.callMethod('ParentHandleMetadataLoaded', data);
				loaded = true
			},
			
			handleEnded(event) {
				// 向Vue层报告视频播放结束
				this.$ownerInstance.callMethod('handleVideoEnd');
			},
			
			handleError(event) {
				// 向Vue层报告视频错误
				this.$ownerInstance.callMethod('handleVideoError', {
					code: videoElement.error ? videoElement.error.code : -1,
					message: videoElement.error ? videoElement.error.message : '未知错误'
				});
			},
			
			handlePlay(event) {
				console.log('Renderjs: 视频play事件');
				// 视频开始播放时，启动自动隐藏控制条
				this.autoHideControls();
			},
			
			handlePlaying(event) {
				console.log('Renderjs: 视频playing事件');
				//this.$ownerInstance.callMethod('handleVideoPlay');
			},
			
			handlePause(event) {
				console.log('Renderjs: 视频pause事件');
				// 视频暂停时显示控制条且不自动隐藏
				const controlsContainer = document.getElementById('controls-container');
				if (controlsContainer) {
					controlsContainer.style.transform = 'translateY(0)';
					controlsVisible = true;
				}
				// 清除自动隐藏定时器
				if (controlsTimeout) {
					clearTimeout(controlsTimeout);
				}
				// 向Vue层报告视频暂停
				this.$ownerInstance.callMethod('handleVideoPause');
			},
			
			handleWaiting(event) {
				// 向Vue层报告视频缓冲中
				this.$ownerInstance.callMethod('handleVideoBuffering');
			},
			
			handleCanPlay(event) {
				// 向Vue层报告视频可以播放
				this.$ownerInstance.callMethod('handleVideoCanPlay');
			},
			
			// 处理单击事件
			handleClick(event) {
				// 防止双击事件触发时也触发单击
				if (this.clickTimer !== null) {
					clearTimeout(this.clickTimer);
					this.clickTimer = null;
					return;
				}
				
				this.clickTimer = setTimeout(() => {
					// 单击时的逻辑：显示/隐藏控制条
					const controlsContainer = document.getElementById('controls-container');
					if (!controlsContainer) return;
					
					if (controlsVisible) {
						controlsContainer.style.transform = 'translateY(100%)';
						controlsVisible = false;
					} else {
						controlsContainer.style.transform = 'translateY(0)';
						controlsVisible = true;
						// 如果视频在播放，则设置自动隐藏
						if (videoElement && !videoElement.paused) {
							this.autoHideControls();
						}
					}
					
					this.clickTimer = null;
				}, 300); // 300ms延迟，等待可能的双击
				
				event.stopPropagation();
			},
			
			// 处理双击事件
			handleDoubleClick(event) {
				if (this.clickTimer) {
					clearTimeout(this.clickTimer);
					this.clickTimer = null;
				}
				
				// 双击时的逻辑：切换播放/暂停状态
				if (videoElement) {
					if (videoElement.paused) {
						// 如果视频已暂停，则播放
						videoElement.play().catch(err => {
							console.error('Renderjs: 播放错误', err);
							this.$ownerInstance.callMethod('handleVideoError', {
								message: err.message
							});
						});
						// 通知Vue层视频播放状态变化
						this.$ownerInstance.callMethod('handleVideoPlay');
					} else {
						// 如果视频在播放，则暂停
						videoElement.pause();
						// 显示控制条且不自动隐藏
						const controlsContainer = document.getElementById('controls-container');
						if (controlsContainer) {
							controlsContainer.style.transform = 'translateY(0)';
							controlsVisible = true;
						}
						// 通知Vue层视频暂停状态变化
						this.$ownerInstance.callMethod('handleVideoPause');
					}
				}
				
				event.stopPropagation();
			},
			
			// 自动隐藏控制条
			autoHideControls() {
				if (controlsTimeout) {
					clearTimeout(controlsTimeout);
				}
				
				// 只有在视频播放时才自动隐藏控制栏
				if (videoElement && !videoElement.paused) {
					controlsTimeout = setTimeout(() => {
						const controlsContainer = document.getElementById('controls-container');
						if (controlsContainer && controlsVisible) {
							controlsContainer.style.transform = 'translateY(100%)';
							controlsVisible = false;
						}
					}, 15000); // 修改为15秒后自动隐藏
				}
			},
			
			// 在 renderjs 部分添加 handleFullscreenChange 方法
			handleFullscreenChange() {
				const isFullscreen = !!(document.fullscreenElement || 
					document.webkitFullscreenElement || 
					document.mozFullScreenElement || 
					document.msFullscreenElement);
				
				// 如果退出了全屏，恢复所有样式
				if (!isFullscreen) {
					const container = document.getElementById('video-wrapper-container');
					const videoWrapper = document.getElementById('video-wrapper');
					const controlsContainer = document.getElementById('controls-container');
					
					// 恢复容器原始样式
					if (container) {
						Object.assign(container.style, originalStyles.container);
						container.style.maxHeight = '520rpx';
					}
					
					// 恢复视频容器原始样式
					if (videoWrapper) {
						Object.assign(videoWrapper.style, originalStyles.videoWrapper);
						videoWrapper.style.maxHeight = '520rpx';
					}
					
					// 恢复视频元素原始样式
					if (videoElement) {
						Object.assign(videoElement.style, originalStyles.video);
						videoElement.style.maxHeight = '520rpx';
						videoElement.style.width = '100%';
						videoElement.style.height = '100%';
					}
					
					// 恢复控制栏原始样式
					if (controlsContainer) {
						Object.assign(controlsContainer.style, originalStyles.controls);
						controlsContainer.style.transform = 'translateY(0)';
						controlsVisible = true;
						this.autoHideControls();
					}
				}
				
				// 通知Vue层全屏状态变化
				this.$ownerInstance.callMethod('handleFullscreenChange', {
					isFullscreen: isFullscreen
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.video-container {
	position: relative;
	width: 100%;
	height: 520rpx;
	background-color: #000;
	overflow: hidden;

	#video-wrapper-container {
		position: relative;
		width: 100%;
		height: 100%;
		max-height: 520rpx;

		.video-wrapper {
			width: 100%;
			height: 100%;
			z-index: 1;
			position: relative;
		}

		.controls-bar-container {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			transition: transform 0.3s ease;
			z-index: 999;
			background-image: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
			pointer-events: auto;

			.progress-container {
				height: 30rpx;
				display: flex;
				flex-direction: column;
				justify-content: flex-end;
			}

			.controls-bar {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 10rpx 30rpx 20rpx;

				.controls-bar-time {
					color: #fff;
					font-size: 22rpx;
					display: inline-block;
					width: 120rpx;
					text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
				}

				.video-left-icon {
					width: 40%;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					gap: 20rpx;

					text {
						color: #fff;
						font-size: 22rpx;
					}
				}

				.video-right-icon {
					width: 50%;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					padding-left: 80rpx;
					gap: 30rpx;

					image {
						width: 30rpx;
						height: 30rpx;
						opacity: 0.9;
						transition: opacity 0.2s;
						flex-shrink: 0;

						&:active {
							opacity: 0.6;
						}
					}

					.set-rate {
						position: relative;
						color: #fff;
						font-size: 24rpx;
						cursor: pointer;
						padding: 0 10rpx;
						width: 60rpx;
						text-align: center;
						flex-shrink: 0;

						.choose-rate {
							position: absolute;
							width: 100rpx;
							background-color: rgba(0, 0, 0, 0.8);
							border-radius: 8rpx;
							bottom: 50rpx;
							left: 50%;
							transform: translateX(-50%);
							padding: 10rpx 0;
							z-index: 1000;
							box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.4);

							text {
								display: block;
								width: 100%;
								text-align: center;
								padding: 15rpx 0;
								font-size: 24rpx;
								color: #fff;
								transition: all 0.2s ease;

								&:hover {
									background-color: rgba(41, 121, 255, 0.6);
								}

								&:active {
									background-color: #2979ff;
									color: #fff;
								}
							}
						}
					}
				}
			}
		}
	}
}

/* 视频元素样式 */
:deep(.video-element) {
	width: 100%;
	height: 100%;
	object-fit: contain;
	background-color: #000;
	max-height: 520rpx;
}

/* 全屏状态下的样式 */
:deep([data-fullscreen="true"]) {
	#video-wrapper-container {
		max-height: none !important;

		.video-element {
			max-height: none !important;
		}

		.controls-bar-container {
			padding-bottom: env(safe-area-inset-bottom);

			.controls-bar {
				padding: 15rpx 40rpx calc(20rpx + env(safe-area-inset-bottom));

				.video-left-icon {
					gap: 30rpx;
				}

				.video-right-icon {
					gap: 40rpx;

					image {
						width: 35rpx;
						height: 35rpx;
					}
				}
			}
		}
	}
}
</style>