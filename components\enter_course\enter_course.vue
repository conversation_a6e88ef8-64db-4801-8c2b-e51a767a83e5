<template>
	<view class="container">
		<view class="list" v-for="(item, i) in list" :key="i" v-if="item.fileName">
			<view class="course-item" :style='{marginBottom:i==list.length-1?"0":"30rpx"}'>
				<view class="left">
					<view class="ball">
					</view>
					<view class="title">
							{{item.fileName}}
					</view>
				</view>
				<view class="btn" @click="toVideo(item)">
					进入课堂
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"enter_course",
		data() {
			return {
				
			};
		},
		methods:{
			toVideo(item){
				uni.navigateTo({
					url:`/subpkg/play_knowledge/play_knowledge?url=${item.remote_url}`
				})
			}
		},
		props:{
			list:{
				type:Array,
				default:[
					{
						fileName:'实时时钟、CMOSRAM和'
					},
				]
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	.list{
		.course-item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left{
				width: 70%;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				.ball{
					width: 20rpx !important;
					height: 20rpx !important;
					border-radius: 50%;
					background: #CDF3E7;
				}
				.title{
					width: 400rpx;
					font-size: 28rpx;
					color: #4A4A4C;
					font-weight: bold;
					margin-left: 10rpx;
					text-align: center;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
			.btn{
				width: 130rpx;
				height: 44rpx;
				background: linear-gradient( 90deg, #10C19D 0%, #16AC8E 100%);
				border-radius: 363rpx 363rpx 363rpx 363rpx;
				font-size: 24rpx;
				color: #fff;
				text-align: center;
				line-height: 44rpx;
			}
		}
	}
}
</style>