<template>
	<view class="container">
		<view class="video-container">
			<my-video ref="myVideo" :src="src" :play_time="playTime" :video_id="videoId" :course_type="courseType" @custom-event="changeSecond"></my-video>
		</view>
		<view class="title">
			<text>{{ nowCourse[0].alias_name }}</text>
			<view class="left-opt">
				<u-icon :name="star" color="#777" size="54" @click="collection"></u-icon>
				<view class="text-icon" @click="showComment = true" v-if="showAll">
					<image src="@/static/png/comment.png" mode="widthFix"></image>
					<text>评价</text>
				</view>
			</view>

		</view>
		<view class="content" v-if="showAll">
			<uni-segmented-control :current="current" :values="items" :style-type="styleType" :active-color="activeColor" @clickItem="onClickItem" />


			<view class="course-container" v-show="current == 0" v-if="courseList.length > 0" v-for="(item, index) in courseList">
				<view class="title-name">
					<view class="left">
						<text class="green-block"></text>
						<text>{{ item.name }}</text>
					</view>
					<view class="expand" :animation="animationData" @click="rotate">
						<u-icon name="arrow-down" color="#FFF" size="18"></u-icon>
					</view>
				</view>
				<view class="course-list" :style="{ height }">
					<view class="list-item u-border-top" v-for="(sitem, sindex) in item.selData">
						<view class="title-text">
							<text>{{ sitem.alias_name }}</text>
						</view>
						<view class="enter" v-if="sitem.id != videoId" @click="toListen(sitem.id)">
							进入课堂
						</view>
						<view class="title-text" v-if="sitem.id == videoId">
							<image src="@/static/png/sound-wave.png" mode="widthFix"></image>
							<text>听课中</text>
						</view>
					</view>


					<!-- <view class="list-item u-border-top">
						<view class="title-text">
							<text>第1节 基本知识</text>
							<template v-if="false">
							<image src="@/static/png/sound-wave.png" mode="widthFix"></image>
							<text>听课中</text>
							</template>
</view>
<view class="enter">
	进入课堂
</view>
</view> -->
				</view>
			</view>

			<view class="practise-container" v-if="showAll" v-show="current == 1">
				<view class="lesson-list">
					<view class="lesson-info">
						<view class="lesson-title">
							<text class="samll-cricle"></text>
							<text>25考研基础阶段时政考点测试卷</text>
						</view>
						<view class="btn">
							进入考试
						</view>
					</view>
					<view class="lesson-info">
						<view class="lesson-title">
							<text class="samll-cricle"></text>
							<text>25考研基础阶段时政考点测试卷</text>
						</view>
						<view class="btn">
							<text>继续学习</text>
							<text class="tip">已学3%</text>
						</view>
					</view>
					<view class="lesson-info">
						<view class="lesson-title">
							<text class="samll-cricle"></text>
							<text>25考研基础阶段时政考点测试 卷</text>
						</view>
						<view class="btn done">
							已完成
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-popup :show="showComment" @close="close" @open="open">
			<view class="comment-container">
				<view class="head">
					<text class="h1-title">课程评价</text>
					<u-icon class="close" @click="close" name="close" size="38"></u-icon>
				</view>
				<view class="rate">
					<text>课程评价</text>
					<view class="rate-icon">
						<image v-for="i in 6" ref="rateItem" :src="rateImgs[i - 1]" :key="i" @click="rate(i)"></image>
						<text class="tip">课程如何？点点评价一下！</text>
					</view>

				</view>

				<view class="rate">
					<text>评价维度</text>
					<view class="rate-tags">
						<view :class="[definition ? 'tag' : 'not_tag']" @click="commontShow(1)">
							画质清晰度
						</view>
						<view :class="[know ? 'tag' : 'not_tag']" @click="commontShow(2)">
							重难点知识
						</view>
						<view :class="[atmosphere ? 'tag' : 'not_tag']" @click="commontShow(3)">
							课堂氛围
						</view>
					</view>

				</view>

				<view class="bottom-text">
					<view class="comment-item" v-if="definition">
						<text>画质清晰度:</text>
						<u--input border="none" v-model="definitionText" placeholder="展开说说吧"></u--input>
					</view>
					<view class="comment-item" v-if="know">
						<text>重难点知识:</text>
						<u--input border="none" v-model="knowText" placeholder="展开说说吧"></u--input>
					</view>
					<view class="comment-item" v-if="atmosphere">
						<text>课堂氛围:</text>
						<u--input border="none" v-model="atmosphereText" placeholder="展开说说吧"></u--input>
					</view>
				</view>
				<view class="btn-container">
					<view class="btn" @click="submit">
						提交
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import rateImgSel from "@/static/img/head.png"
import rateImg from "@/static/img/praise.png"
import myVideo from '@/components/my-video/my-video.vue';
export default {
	data() {
		return {
			definitionText: '',
			cateType: 1,
			knowText: '',
			atmosphereText: '',
			definition: true,
			know: true,
			atmosphere: true,
			src: '',
			rateImg,
			rateImgs: [rateImg, rateImg, rateImg, rateImg, rateImg],
			rateImgSel,
			showComment: false,
			starColor: '#ccc',
			star: 'star',
			items: ['课程目录'],
			// items: ['课程目录', '做题练习'],
			styles: [{
				value: 'button',
				text: '按钮',
				checked: true
			},
			{
				value: 'text',
				text: '文字'
			}
			],
			colors: ['#007aff', '#4cd964', '#dd524d'],
			current: 0,
			colorIndex: 0,
			activeColor: '#009c7b',
			styleType: 'button',
			turnOver: false,
			animation: {},
			animationData: {},
			listen: true,
			height: 0,
			screenWidth: 375,
			courseId: 0,
			chapterId: 0,
			courseList: [],
			nowCourse: {
				0: {
					alias_name: '',
					video_url: ''
				}
			},
			videoId: '',
			taskId: '',
			playTime: 0,
			conlyCouseId: 0,
			showAll: true,
			courseType: '',
			evaluate: 0
		}
	},
	onShareAppMessage(res) {
		return {
			title: '[研趣考研]' + this.nowCourse[0].alias_name,
			path: '/choosecoursepkg/study/study?course_id=' + this.courseId + '&chapter_id=' + this.chapterId + '&video_id=' + this.videoId + '&conly_couse_id=-1',
			imageUrl: 'https://website-1300870289.cos.ap-nanjing.myqcloud.com/376c7dd638eed4a5c7db1eeb37202a45b5e.png',
			desc: '好老师,好服务',
		}

	},

	onLoad(options) {
		let that = this;
		that.courseId = options.course_id;
		that.chapterId = options.chapter_id;
		that.videoId = options.video_id;
		console.log(options)
		that.taskId = options.taskId ? options.taskId : "";
		that.conlyCouseId = options.conly_couse_id ? options.conly_couse_id : "";
		that.courseType = options.course_type ? options.course_type : "";


		var animation = uni.createAnimation({
			duration: 500,
			timingFunction: 'ease',
		})
		this.animation = animation

		that.getAllCourse();
		that.getUserCollection(this.chapterId);
		uni.getSystemInfo({
			success: function (info) {
				console.log('屏幕宽度：' + info.windowWidth);
				// 你可以将info.windowWidth值保存到一个变量中供后续使用
				that.screenWidth = info.windowWidth;
				console.log(that.screenWidth)
			}
		});

		this.animation.rotate(0).step()
		this.height = "auto";
		this.animationData = this.animation.export()
		// #ifdef MP-WEIXIN
		if (this.conlyCouseId == -1) {
			uni.showShareMenu()
		} else {
			uni.hideShareMenu();
		}
		// #endif


	},
	mounted() {
		this.videoContent = uni.createVideoContext('local-vedio')
	},
	methods: {
		getUserCollection(chapterId) {
			let that = this;
			let collectionData = {
				chatper_id: chapterId,
				video_id: that.videoId

			};
			uni.$u.http.get('/student/getUserCollection', { params: collectionData }).then(res => {
				console.log(res)
				if (res.data.is_collection == 0) {
					that.starColor = ''
					that.star = 'star'
				} else {
					that.starColor = '#009c7b'
					that.star = 'star-fill'
				}
			})
		},

		collection() {
			let status = 1;
			if (this.star != 'star-fill') {
				this.starColor = '#009c7b'
				this.star = 'star-fill'
			} else {
				this.starColor = ''
				this.star = 'star'
				status = 0
			}
			console.log(this.cateType);
			let collectionData = {
				chatper_id: this.chapterId,
				status: status,
				type: this.cateType,
				video_id: this.videoId
			};
			let data = uni.$u.http.post('/student/collectionCourse', collectionData).then(res => {
				if (res.code == 1) {
					if (status == 1) {
						uni.tip('收藏成功');
					} else {
						uni.tip('取消成功');
					}
				}
			})

		},
		submit() {
			let formData = {
				evaluate: this.evaluate,
				type: 3,
				about_id: this.videoId,
			}
			// definition:true,
			// know:true,
			// atmosphere:true,
			if (this.definition) {
				formData.definitionText = this.definitionText
			}
			if (this.know) {
				formData.knowText = this.knowText
			}
			if (this.atmosphere) {
				formData.atmosphereText = this.atmosphereText
			}

			let data = uni.$u.http.post('/student/recordVideo', formData).then(res => {
				console.log(res)
				if (res.code == 1) {
					uni.tip('评价成功')
					this.showComment = false
				} else {

				}
			});

		},
		commontShow(type) {
			if (type == 1) {
				this.definition = !this.definition
			} else if (type == 2) {
				this.know = !this.know
			} else {
				this.atmosphere = !this.atmosphere
			}
		},
		changeSecond(time_data) {
			let that = this;
			if (that.conlyCouseId > -1) {
				if (that.taskId > 0) {
					let that = this;
					let data = uni.$u.http.post('/task/dealVideo',
						{
							task_id: that.taskId,
							chapter_id: that.chapterId,
							type: 1,
							total_time: time_data.total_time,
							video_id: that.videoId,
							time: time_data.now_time,
							rate: time_data.rate
						},
						{
							custom: { loading: false },
						});
				} else {
					let that = this;
					let data = uni.$u.http.post('/task/dealVideo',
						{
							course_id: that.conlyCouseId,
							chapter_id: that.chapterId,
							type: 2,
							time: time_data.now_time,
							total_time: time_data.total_time,
							video_id: that.videoId,
							rate: time_data.rate
						},
						{
							custom: { loading: false },
						});
				}
			}



		},
		toListen(videoId) {
			let that = this;
			this.videoId = videoId;
			that.nowCourse = that.courseList[0].selData.filter(item => item.id == that.videoId)

			if (that.nowCourse) {
				that.src = that.nowCourse[0].video_url;
				that.chapterId = that.nowCourse[0].chapter_id;
				if (that.nowCourse[0].play_msg) {
					that.playTime = that.nowCourse[0].play_msg.time
				} else {
					that.playTime = 0
				}
				this.getUserCollection(this.chapterId, this.videoId)
			}
		},
		async getAllCourse() {
			let that = this;
			if (that.taskId > 0) { // 获取任务视频
				let ret = await uni.$u.http.get('/task/getVideoTask', { params: { task_id: that.taskId } }).then(rest => {
					if (rest.code == 1) {
						that.courseList = rest.data.allCourse;
						that.cateType = that.courseList[0].type
						that.nowCourse = that.courseList[0].selData.filter(item => item.id == that.videoId)
						if (that.nowCourse) {
							console.log(that.nowCourse[0].video_url)
							that.src = that.nowCourse[0].video_url;
							if (that.nowCourse[0].play_msg) {
								that.playTime = that.nowCourse[0].play_msg.time
							}

						}
					} else {
						uni.$u.toast(rest.msg)

					}
				})
			} else {
				if (this.conlyCouseId < 0) {
					let ret = await uni.$u.http.get('/course/getAuditCourseDetail', { params: { id: that.chapterId } }).then(rest => {
						console.log(rest)
						if (rest.code == 1) {
							console.log('alias_name', rest.data.alias_name)
							that.src = rest.data.video.address_url;
							that.courseList = [];
							that.showAll = false;
							that.nowCourse = { 0: { alias_name: rest.data.alias_name ? rest.data.alias_name : rest.data.name } };
						} else {
							uni.$u.toast(rest.msg)
						}
					})
				} else {
					let ret = await uni.$u.http.get('/course/getCourseDetail', { params: { c_course_mod: this.conlyCouseId, course_id: this.courseId, chapter_id: that.chapterId } }).then(rest => {
						if (rest.code == 1) {
							that.courseList = rest.data.allCourse;
							that.cateType = that.courseList[0].type
							that.nowCourse = that.courseList[0].selData.filter(item => item.id == that.videoId)
							if (that.nowCourse) {

								that.src = that.nowCourse[0].video_url;
								if (that.nowCourse[0].play_msg) {
									that.playTime = that.nowCourse[0].play_msg.time
								}
								console.log(that.cateType)
							}
						} else {
							uni.$u.toast(rest.msg)
						}
					})
				}
			}

		},
		close() {
			this.showComment = false
		},
		onClickItem(e) {
			if (this.current !== e.currentIndex) {
				this.current = e.currentIndex
			}
		},
		rate(index) {
			this.rateImgs = this.rateImgs.map((item, i) => {
				if (i < index) {
					return this.rateImgSel
				} else {
					return this.rateImg
				}
			})
			console.log(index)
			this.evaluate = index;
		},
		open() {

		},
		close() {
			this.showComment = false
		},
		rotate() {
			if (!this.turnOver) {
				this.animation.rotate(-180).step()
				this.height = 0;
			} else {
				this.animation.rotate(0).step()
				this.height = "auto";
			}
			this.animationData = this.animation.export()
			this.turnOver = !this.turnOver
		}
	},
	components: {
		myVideo
	},
}
</script>

<style lang="scss" scoped>
.container {

	min-height: 100vh;
	background-color: $container-bg-color;

	::v-deep .u-popup__content {
		border-radius: 16rpx 16rpx 0rpx 0rpx !important;
	}

	.comment-container {
		min-height: 200rpx;
		padding: 0 30rpx;
		padding-bottom: 120rpx;

		.head {
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			margin-top: 52rpx;

			.h1-title {
				color: #0A0A0A;
				font-size: 32rpx;
				font-weight: bold;
			}

			.close {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				right: 62rpx;
			}

		}

		.rate {
			margin-top: 40rpx;
			margin-bottom: 60rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			min-height: 50rpx;

			.rate-tags {
				display: flex;
				flex-wrap: wrap;
				align-content: space-between;
				justify-content: space-between;

				.tag {
					color: #01997A;
					font-size: 26rpx;
					padding: 10rpx;
					border: 1rpx solid $main-color;
					border-radius: 8rpx;
					margin-left: 20rpx;
				}

				.not_tag {
					color: #ccc;
					font-size: 26rpx;
					padding: 10rpx;
					border: 1rpx solid #eee;
					border-radius: 8rpx;
					margin-left: 20rpx;
				}
			}

			text {
				color: #0A0A0A;
				font-size: 28rpx;
			}

			.rate-icon {
				position: relative;
				margin-left: 36rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				image {
					margin-right: 26rpx;
					width: 60rpx;
					height: 60rpx;
				}

				.tip {
					height: 22rpx;
					line-height: 22rpx;
					position: absolute;
					font-size: 22rpx;
					color: #AFAFAF;
					bottom: -22rpx;
					left: 0;
				}
			}

		}

		.bottom-text {
			background-color: #F6F7FB;
			border-radius: 16rpx;
			padding: 28rpx;

			.comment-item {
				margin-top: 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				text {
					color: #0A0A0A;
					font-size: 28rpx;
					min-width: 120rpx;
					margin-right: 20rpx;
				}
			}
		}

		.btn-container {
			margin-top: 90rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.btn {
			width: 530rpx;
			height: 90rpx;
			background: #01997A;
			border-radius: 61rpx;
			margin-top: 90rpx auto;
			color: #fff;
			text-align: center;
			line-height: 90rpx;
			font-size: 32rpx;
		}
	}

	.v-top {
		height: 420rpx;
		width: 100%;

		.backBtn {
			position: absolute;
			left: 16rpx;
			top: 45rpx;
			width: 45rpx;
			height: 45rpx;
		}

		.speed {
			position: absolute;
			right: 20rpx;
			top: 16rpx;

			.doubleSpeed {
				color: #fff;
				font-size: 14rpx;
				background-color: rgba(0, 0, 0, 0.6);
				padding: 4rpx 6rpx;
			}
		}

		// 倍速的蒙版
		.speedModal {
			background-color: rgba(0, 0, 0, 0.7);
		}

		.speedNumBox {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			align-items: center;
			background-color: #2c2c2c;
			width: 120rpx;
			position: absolute;
			right: 0rpx;
			top: 0;

			.number {
				width: 120rpx;
				font-weight: 700;
				font-size: 14rpx;
				padding: 18rpx 0;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
			}

			.active {
				color: red;
			}

			.noActive {
				color: #fff;
			}
		}
	}

	.title {
		margin-top: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		text {
			font-size: 32rpx;
			font-weight: bold;
			color: #060606;
		}

		.left-opt {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.text-icon {
				display: flex;
				align-items: center;
				justify-content: space-between;

				image {
					width: 50rpx;
				}

				text {
					color: #777777;
					font-size: 20rpx;
				}
			}
		}
	}

	.content {
		padding: 0 30rpx;
		margin-top: 90rpx;

		.course-container {
			margin-top: 46rpx;
			background-color: #fff;
			border-radius: 20rpx;

			.title-name {

				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 28rpx;

				.left {
					color: #4A4A4C;
					font-size: 28rpx;
					font-weight: bold;

					.green-block {
						display: inline-block;
						width: 14rpx;
						height: 28rpx;
						line-height: 28rpx;
						background: #01997A;
						border-radius: 0rpx 8rpx 0rpx 8rpx;
						margin-right: 6rpx;
					}
				}

				.expand {
					width: 30rpx;
					height: 30rpx;
					background: #01997A;
					border-radius: 50%;
					display: flex;
					align-items: center;
					transition: transform 0.5s ease;
					justify-content: center;

					.rotate {
						transform: rotate(180deg);
					}
				}
			}
		}

		.course-list {
			height: auto;
			overflow: hidden;
			transition: height 0.8s ease;

			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 28rpx;

				.title-text {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					font-size: 26rpx;
					color: $main-color;

					image {
						margin: 0 10rpx;
						width: 16rpx;
					}

					text {
						&:last-child {
							font-size: 22rpx;
						}
					}
				}

				.enter {
					width: 130rpx;
					height: 44rpx;
					background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
					border-radius: 60rpx;
					color: #fff;
					text-align: center;
					line-height: 44rpx;
					font-weight: bold;
					font-size: 26rpx;
				}
			}
		}

		.practise-container {
			margin-top: 46rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding-top: 30rpx;
			padding-bottom: 1rpx;

			.lesson-list {
				.lesson-info {
					padding: 0 30rpx;
					margin-bottom: 40rpx;
					color: #4A4A4C;
					font-size: 28rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lesson-title {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.samll-cricle {
							height: 20rpx;
							width: 20rpx;
							border-radius: 50%;
							background-color: #CDF3E7;
							margin-right: 10rpx;
						}

						font-size: 28rpx;
						font-weight: bold;
						color: #4A4A4C;
					}

					.btn {
						width: 130rpx;
						height: 44rpx;
						line-height: 44rpx;
						font-size: 24rpx;
						color: #fff;
						background-color: #01997A;
						text-align: center;
						border-radius: 365rpx;
						position: relative;

						.tip {
							position: absolute;
							bottom: -40rpx;
							left: 50%;
							transform: translateX(-50%);
							font-size: 20rpx;
							color: #A4A4A4;
							width: 72rpx;
						}
					}

					.done {
						background-color: #F39898;

					}
				}
			}
		}
	}

}
</style>