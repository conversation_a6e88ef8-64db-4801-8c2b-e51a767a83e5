<template>
	<view class="container">
		<div class="head" :style="{paddingTop: safeTopHeight + 'px'}">
			<u-icon  size="40" name="arrow-left" @click="back"></u-icon>
			<u-tabs
					:current="firstIndex"
			        :list="list"
			        lineWidth="50"
					lineHeight="8"
					@click="clickTabs"
			        lineColor="#01997A"
			        :activeStyle="{
						  color: '#01997A',
						  fontWeight: 'bold',
						  fontSize:'32rpx'
			        }"
			        :inactiveStyle="{
			             color: '#777777',
						  fontWeight: 'bold',
						  fontSize:'32rpx'
			        }"
			        itemStyle="padding-left: 12rpx; padding-right: 12rpx; height: 24px; padding-bottom: 12rpx;"
			    >
			    </u-tabs>
		</div>
		<view class="content">
			<view class="left":style="{height: sideBarHeight + 'px'}">
				<side-bar :list="sideBarList" :currentSel="sel" @changeItem="changeItem" :height="sideBarHeight"></side-bar>
			</view>
			<view class="right" :style="{height: sideBarHeight + 'px'}">
				<view class="quick-search-tags">
					<view class="tag"  v-for="tag in tagList" :key="tag.id" :class="{'active-tag':currentTagId==tag.id}" @click="chooseTag(tag.id)">
						{{tag.name}}
					</view> 
				</view>
				<scroll-view scroll-y="true" class="course-container" :style="{height: rightCourseListHeight + 'px'}">
					<u-loading-icon :show="show" size="36"></u-loading-icon>
					<!-- 授课信息 -->
					<!-- <view  class="course_height" :style="{height:rightCourseListHeightView>goodsHeight?rightCourseListHeightView+'px':goodsHeight+'px'}">	 -->				
						<view  class="course_height">
						<view v-if="goodsList.length > 0" class="course-list" v-for="item in goodsList">
							
							<view class="top">
								<view class="info">
									<text class="course_title">{{item.title.length>30?item.title.substring(0,29)+'...':item.title}}</text>
									
								</view>
								<view class="btn-test" >
									<button class="btn" v-if="item.is_audition">试听</button>
								</view>
							</view>
							<view @click="toDetail(item)">
								<view class="mid">
									<view class="tag">
										{{item.cate_text}}
									</view>
									<text class="date" v-if="item.time_type == 1">{{item.start_time | happenTimeFun}}-{{item.end_time | happenTimeFun}} | 共{{item.time_hour}}课时</text>
									<text class="date" v-else>有效期 <text class="time_day">{{item.time_day}}</text>年 | 共{{item.time_hour}}课时</text>
									
								</view>
								<view class="bottom">
									<view class="teacher-list">
										<view class="teacher-info" v-for="te_item in item.teacher_list">
											<image class="avatar" :src="te_item.image_text"/>
											<text>{{te_item.name}}</text>
										</view>
									
									</view>
									<view class="money">
										￥{{item.price}}
									</view>
								</view>
							</view>							
							
						</view>
						
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import sideBar from "../../components/Sidebar/Sidebar.vue"
	export default {
		data() {
			return {
				safeTopHeight:0,
				rightCourseListHeight:0,
				rightCourseListHeightView:0,
				goodsHeight:0,
				sideBarHeight:0,
				currentTagId:0,
				sel:0,
				list:[
					
				],
				sideBarList:[
					
				],
				tagList:[
					
				],
				show:true,
				allCategry:[],
				firstIndex:0,
				secondIndex:0,
				thirdIndex:0,
				goodsList:[],
				total:0,
				changeData:false,
				page:1,
				maxIndex:0,
				headHeight:0,
				windowHeight:0,	
			};
		},
		components:{
			sideBar
		},
		onLoad() {
	
			this.caculateHeight();
			this.getAllCategory();
		},
		filters : {
			happenTimeFun(num){//时间戳数据处理
				let date = new Date(num*1000);
				 //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;//月补0
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;//天补0
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;//小时补0
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;//分钟补0
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;//秒补0
				return y + '-' + MM + '-' + d ;
			},
		},
		methods:{
			onScroll(e){
				
			},
			toDetail(item) {
				uni.navigateTo({
					url:'/choosecoursepkg/courseDetail/courseDetail?goods_id='+item.id
				})
			},
			clickTabs(e){
				this.currentIndex = e.index;
				this.page = 1;
				this.getSecondCate(e.index)
				this.firstIndex = e.index;
			},
			loadMore(e){
				console.log(e)
				if((this.secondIndex+1) >= this.maxIndex ) {
					return false;
				}
				let that  = this;
				if(this.goodsList.length == this.total) {
					let  tmpCate = this.allCategry[this.firstIndex].childlist;
					let tmp_index =  this.secondIndex;
					this.sel  =  tmpCate[tmp_index].id;
					console.log(this.sel );
					this.getThirdCate(0)	
					this.secondIndex +=1;
				} else  {
					this.page+=1;
					if(this.currentTagId > 0) {
						console.log(1233)
						this.getGoodsList({third_id:this.currentTagId});
					} else {
						console.log(99999)
						this.getGoodsList({second_cate:this.sel})
					}
				}
				
				
			},
			changeItem(e){
				this.sel = e.id
				this.getThirdCate(e.index)
				//计算右侧课程 列表高度
				// uni.createSelectorQuery().select('.quick-search-tags').boundingClientRect(rightData => {
				// 	this.rightCourseListHeight =  this.windowHeight- parseInt(this.headHeight)-rightData.height +'px'
				// }).exec();
			},
			back(){
				uni.navigateBack()
			},
			refresh(){
				console.log('refresh')
			},
			chooseTag(id) {
				this.currentTagId = id;
				this.page = 1;
				if(id > 0) {
					this.getGoodsList({third_id:this.currentTagId});
				} else {
					this.getGoodsList({second_cate:this.sel})
				}
				
			},
			async getAllCategory(){
				this.show = true;
				try {
					const res = await uni.$u.http.get('/index/getAllCategory');
					if (res.data && Array.isArray(res.data)) {
						this.allCategry = res.data;
						this.list = this.allCategry.map(item => ({
							name: item.name
						}));
						
						// 确保有数据后再调用
						if (this.allCategry.length > 0) {
							this.getSecondCate(0);
						}
					}
				} catch (error) {
					console.error('获取分类数据失败:', error);
					uni.showToast({
						title: '获取分类数据失败',
						icon: 'none'
					});
				} finally {
					this.show = false;
				}
			},
			getSecondCate(e){
				if (!this.allCategry || !this.allCategry[e]) {
					console.error('分类数据不存在');
					return;
				}
				
				this.secondIndex = e;
				const tmpCate = this.allCategry[e].childlist || [];
				this.maxIndex = tmpCate.length;
				
				this.sideBarList = tmpCate.map(item => ({
					id: item.id,
					name: item.name
				}));
				
				if (tmpCate.length > 0) {
					this.sel = tmpCate[0].id;
					this.getThirdCate(0);
				}
			},
			getThirdCate(e){
				if (!this.allCategry || !this.allCategry[this.firstIndex] || 
					!this.allCategry[this.firstIndex].childlist || 
					!this.allCategry[this.firstIndex].childlist[e]) {
					console.error('三级分类数据不存在');
					return;
				}
				
				const tmpSecondCate = this.allCategry[this.firstIndex].childlist[e].childlist || [];
				this.tagList = [{id: 0, name: "全部"}].concat(
					tmpSecondCate.map(item => ({
						id: item.id,
						name: item.name
					}))
				);
				
				this.currentTagId = 0;
				this.thirdIndex = e;
				this.getGoodsList({second_cate: this.sel});
			},
			async getGoodsList(params) {
				this.show = true;
				try {
					params.page_size = 20;
					params.page = this.page;
					
					const res = await uni.$u.http.get('/index/getGoodsList', {
						params: params
					});
					
					if (res.data) {
						this.goodsList = res.data.data || [];
						this.total = res.data.total.total || 0;
						
						if (this.goodsList.length > 0) {
							this.goodsHeight = 143 * this.goodsList.length;
						}
					}
				} catch (error) {
					console.error('获取商品列表失败:', error);
					uni.showToast({
						title: '获取商品列表失败',
						icon: 'none'
					});
				} finally {
					this.show = false;
					
					// 重新计算列表高度
					uni.createSelectorQuery()
						.in(this)
						.select('.quick-search-tags')
						.boundingClientRect(rightData => {
							if (rightData) {
								this.rightCourseListHeight = this.windowHeight - this.headHeight - rightData.height;
							}
						})
						.exec();
				}
			},
			caculateHeight() {
				const that = this;
				uni.getSystemInfo({
					success: (res) => {
						that.windowHeight = res.windowHeight;
						// 根据平台区分处理
						// #ifdef MP-WEIXIN
						const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
						that.headHeight = menuButtonInfo.bottom + 10;
						that.safeTopHeight = menuButtonInfo.top;
						// #endif
						
						// #ifdef APP-PLUS || H5
						// APP端使用状态栏高度+固定高度
						that.headHeight = res.statusBarHeight + 10; // 减小固定高度从44到35
						that.safeTopHeight = res.statusBarHeight;
						// #endif
						
						that.sideBarHeight = res.windowHeight - that.headHeight;
						
						// 计算右侧列表高度
						uni.createSelectorQuery()
							.in(this)
							.select('.quick-search-tags')
							.boundingClientRect(data => {
								if(data) {
									that.rightCourseListHeight = that.sideBarHeight - data.height;
								}
							})
							.exec();
					}
				});
			}
						
		},
		 mounted() {
			 // #ifdef MP-WEIXIN
			 const menuButton = uni.getMenuButtonBoundingClientRect();
			 this.safeTopHeight = parseInt(menuButton.top + (menuButton.height/2)) - 30;
			 this.headHeight = parseInt(menuButton.top + menuButton.height + 10);
			 // #endif
			 
			 // #ifdef APP-PLUS || H5
			 const systemInfo = uni.getSystemInfoSync();
			 this.safeTopHeight = systemInfo.statusBarHeight;
			 this.headHeight = systemInfo.statusBarHeight + 35; // 这里也减小固定高度从44到35
			 // #endif
			 
			this.caculateHeight()
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		box-sizing: border-box;
		background-color: $container-bg-color;
		height: 100vh;
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.head {
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #ccc;
		justify-content: flex-start;
		position: relative;
		z-index: 1;
		padding-bottom: 6px;
	}

	.content {
		flex: 1;
		display: flex;
		width: 100%;
		position: relative;
		overflow: hidden;
		background-color:  #fff;
		
		.left {
			width: 150rpx;
			background-color: #fff;
			position: relative;
			height: 100%;
			overflow-y: auto;
			border-right: 1rpx solid #eee;

		}
		
		.right {
			flex: 1;
			position: relative;
			height: 100%;
			overflow: hidden;
			
			.quick-search-tags {
				padding: 20rpx 16rpx;
				display: flex;
				flex-wrap: wrap;
				background: #f5f5f5;
				
				.tag {
					margin-top: 22rpx;
					margin-left: 6rpx;
					padding: 12rpx 24rpx;
					color: #2E3634;
					font-size: 26rpx;
					background-color: #fff;
					border-radius: 8rpx;
				}
				
				.active-tag {
					background-color: #DCF6ED;
					color: #01997A;
				}
			}
			
			.course-container {
				box-sizing: border-box;
				padding: 22rpx 16rpx 120rpx;
				width: 100%;
				background: #f5f5f5;
				
				.course-list {
					margin-bottom: 22rpx;
					background-color: #fff;
					border-radius: 16rpx;
					padding: 20rpx;
					
					.top {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.info {
							flex: 1;
							font-size: 26rpx;
							font-weight: bold;
							color: $uni-text-main-black;
							
							.course_title {
								display: block;
								min-width: 400rpx;
							}
						}
						
						.btn-test {
							width: 120rpx;
							height: 50rpx;
							
							.btn {
								height: 100%;
								width: 100%;
								text-align: center;
								line-height: 50rpx;
								border-radius: 40rpx;
								background-color: $main-color;
								font-size: 20rpx;
								color: #fff;
							}
						}
					}
					
					.mid {
						margin-top: 16rpx;
						display: flex;
						align-items: center;
						
						.tag {
							padding: 4rpx 16rpx;
							background-color: #EEFAF6;
							color: $main-color;
							font-size: 22rpx;
							border-radius: 6rpx;
						}
						
						.date {
							margin-left: 16rpx;
							font-size: 22rpx;
							color: #A4A4A4;
							
							.time_day {
								font-weight: 700;
								font-size: 24rpx;
							}
						}
					}
					
					.bottom {
						margin-top: 20rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.teacher-list {
							display: flex;
							align-items: center;
							
							.teacher-info {
								margin-right: 24rpx;
								display: flex;
								flex-direction: column;
								align-items: center;
								font-size: 20rpx;
								color: #818181;
								
								.avatar {
									width: 60rpx;
									height: 60rpx;
									border-radius: 50%;
									margin-bottom: 6rpx;
								}
							}
						}
						
						.money {
							color: #E16965;
							font-size: 38rpx;
						}
					}
				}
			}
		}
	}
</style>
