/**
 * 请假
 * @method post
 * @return bool
 */
export const askForLeave = async (data) => {
	const ret = await uni.$u.http.post("/Student/askForLeave", data)
	if (ret.code == 1) {
		return true
	} else {
		uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取请假列表
 * @return object
 */
export const getLeaveList = async (params) => {
	const ret = await uni.$u.http.get("/Student/getLeaveList",{params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * vip 教室列表
 * @return object
 */
export const getVipRoomList = async (params) => {
	const ret = await uni.$u.http.get("/Student/getVipRoomList",{params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 搜索vip 教室的时间段
 * @param {id:number} params 
 */
export const getVipRoomTimeSegmentApi = async (params)=>{
	const ret = await uni.$u.http.get("/Student/getVipRoomTimeSegment", {params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 搜索vip 教室的时间段
 * @param  data
 */
export const addAppointmentList = async (data)=>{
	const ret = await uni.$u.http.post("/Student/AddAppointmentList", data)
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取预约列表
 * 
 */
export const getAppointmentListApi = async()=>{
	const ret = await uni.$u.http.get("/Student/getAppointmentList")
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 取消预约
 * 
 */
export const cancleAppointmentApi = async(data)=>{
	const ret = await uni.$u.http.post("/Student/cancleAppointment", data)
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 根据时间段 获取自习室信息
 */
export const getClassRoomInfoByTimeSegApi = async(params)=>{
	const ret = await uni.$u.http.get("/Student/cancleAppointment", {params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 根据时间段 获取已经预约的列表信息
 */
export const getAppointmentListByTimeSegsApi = async(params)=>{
	const ret = await uni.$u.http.get("/Student/getAppointmentListByTimeSegs", {params})
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}


/**
 * 根据天获取课表
 */
export const getCourseScheduleByDayApi = async(params)=>{
	const ret = await uni.$u.http.get("/Student/getCourseScheduleByDay", {params})
	if (ret.code == 1) {
		return ret.data
	} else {
		//uni.tip(ret.msg)
		return false
	}
}
/**
 * 获取月份中有课的日期
 */
export const getScheduleDayByMonth = async(params, loading=false)=>{
	const ret = await uni.$u.http.get("/Student/getScheduleDayByMonth", {params,  custom: {loading},}, )
	if (ret.code == 1) {
		return ret.data
	} else {
		return false
	}
}

/**
 * 获取考试分析数据
 */
export const getExamAnalysisData = async(params)=>{
	const ret = await uni.$u.http.get("/Student/getExamAnalysisData", {params})
	if (ret.code == 1 ) {
		return ret.data
	} else {
		return false
	}
}