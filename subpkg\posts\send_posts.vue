<template>
	<view class="container">
		<view class="title_input">
			<u--textarea  height="120rpx" border="none" v-model="titleValue" placeholder="主题" ></u--textarea>
		</view>
		<view class="input">
			<u--textarea  :confirmType="null" height="240rpx" border="none" v-model="value" placeholder="内容" maxlength="-1"></u--textarea>
		</view>
		<view class="tip">
			<view v-for="(item,index) in topicArr" :class="['topic',selTopic.includes(item.id)?'had':'will']" @click="addTopic(item)">#{{item.name}}#</view>
		</view>
		<view class="camera">
			<u-upload
			 height="200rpx"
			 width="200rpx"
			 uploadText="上传照片"
				:fileList="fileList1"
				@afterRead="afterRead"
				@delete="deletePic"
				name="1"
				multiple
				:maxCount="9"
				:previewFullImage="true"
			></u-upload>
		</view>
		
		
		<view class="btn" @click="submitNote">
			提交
		</view>
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	export default {
		data() {
			return {
				fileList1:[],
				value:"",
				titleValue:"",
				src:'../../static/img/praise.png',
				isAsync: 0,
				type:1, 
				topicArr:[],
				selTopic:[],
				disflag: false,
			}
		},
		onLoad(option){
			if(option.type != undefined) {
				this.type = option.type;
			}
			this.getTopic();
			
		},
		methods:{
			addTopic(item) {
				if(this.selTopic.includes(item.id)) {
					this.selTopic = this.selTopic.filter(vo => vo != item.id)
				} else  {
					this.selTopic.push(item.id);
				}
			},
			getTopic(){
				let that = this
				let  data  = uni.$u.http.get('/index/getTopicBySend/limit/5',).then(res=>{
					console.log(res)
					if(res.code == 1) {						
						that.topicArr  = res.data.topic_list
					}  else if(res.code == 0){
						uni.tip(res.msg);
						setTimeout(function(){
						uni.navigateBack({delta:1})
							
						},2000)
					} else {
						
					}
				});
			},
			submitNote() {
				if(this.disflag) {
					uni.tip('请等待图片上传完成');
					return false;
				}
				let that = this
				if(that.value == '') {
					uni.tip('请填写发布内容');
					return false;
				}
				if(that.titleValue == '') {
					uni.tip('请填写发布主题');
					return false;
				}
				
				// 确保fileList1始终是数组
				const imageArr = Array.isArray(that.fileList1) ? that.fileList1 : [];
				// 确保selTopic始终是数组
				const topicIds = Array.isArray(that.selTopic) ? that.selTopic : [];
				
				let data = uni.$u.http.post('/article/addArticle',
					{
						type: that.type,
						image_arr: imageArr,
						title: that.titleValue,
						content: that.value,
						topic_id: topicIds,
					},
				).then(res=>{
					console.log(res)
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						uni.navigateBack({
							delta:1
						})
					} else {
						uni.$u.toast(res.msg)
					}
				}).catch(err => {
					console.error('提交文章失败:', err);
					uni.$u.toast('提交失败，请稍后重试');
				});
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				
				// 先将文件添加到列表中
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				
				this.disflag = true;
				
				try {
					// 逐个上传图片
					for (let i = 0; i < lists.length; i++) {
						try {
							// 使用统一的上传方法，它会自动根据平台选择上传方式
							const result = await uploadFile(lists[i].url);
							
							let item = this[`fileList${event.name}`][fileListLen];
							this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
								url: result.fullurl || result,
								status: 'success',
								message: ''
							}));
							
							fileListLen++;
						} catch (err) {
							console.error('上传单张图片失败:', err);
							
							// 标记上传失败
							if (this[`fileList${event.name}`][fileListLen]) {
								this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(
									this[`fileList${event.name}`][fileListLen], 
									{ status: 'error', message: '上传失败' }
								));
							}
							fileListLen++;
						}
					}
				} catch (error) {
					console.error('图片上传过程出错:', error);
					uni.$u.toast('图片上传失败，请重试');
				} finally {
					this.disflag = false;
					uni.tip('图片上传完成');
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background-color: $container-bg-color;
	padding: 0 36rpx;
	height: 100vh;
	padding-top: 30rpx;
	.title_input{
		height: 120rpx;
		background-color:#fff;
		border-radius: 20rpx;
		
	}
	.input{
		margin-top: 60rpx;
		height: 240rpx;
		background-color:#fff;
		border-radius: 20rpx;
		
	}
	.camera{
		padding-left: 40rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding-top: 8px;
		justify-content: flex-start;
	}
	.tip{
		border-radius: 20rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		margin-top: 40rpx;
		padding:15rpx 0;
		.topic{
			padding:0 15rpx;
			color:#08ab8a
		}
		.had{
			color:#333
		}
	}
	.btn{
		margin: 120rpx auto;
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
		line-height: 80rpx;
		color: #fff;
		text-align: center;
	}
}
</style>
