<template>
  <view class="container">
    <view class="back-icon">
      <text class="iconfont icon-arrow-left"></text>
    </view>

    <view class="title">手机号登录</view>

    <view class="form">
      <!-- 手机号输入框 -->
      <view class="input-group">
        <text class="area-code">+86 ▾</text>
        <input v-model="form.phone" type="number" placeholder="请输入手机号码" maxlength="11" class="phone-input" />
      </view>

      <!-- 验证码输入框 -->
      <view class="input-group code-group">
        <input v-model="form.code" type="number" placeholder="请输入验证码" maxlength="6" class="code-input" />
        <button class="verify-btn" :class="{ disabled: countdown > 0 }" @click="getCode">
          {{ countdown > 0 ? `${countdown}秒后重试` : "获取验证码" }}
        </button>
      </view>

      <!-- 登录按钮 -->
      <button class="login-btn" @click="handleLogin">登录</button>

      <!-- 一键登录图标 -->
      <view class="quick-login" @click="goToQuickLogin">
        <view class="icon-container">
          <image src="/static/png/phone.png" class="icon-image" />
        </view>
        <text class="quick-login-text">手机号一键登录</text>
      </view>

      <!-- 协议区域 -->
      <view class="agreement">
        <checkbox-group @change="checkboxChange">
          <view class="checkbox">
            <checkbox :value="1" style="transform: scale(0.5)" color="#fff" activeBackgroundColor="#1BB394" />
            <text class="agreement-text">
              我已认真阅读并同意
              <text class="link" @click="privacy('userAgreement')">《用户协议》</text>
              与
              <text class="link" @click="privacy('privacyPolicy')">《隐私条款》</text>
              <!-- <text class="link" @click="privacy('childPrivacy')">《儿童隐私政策》</text> -->
            </text>
          </view>
        </checkbox-group>
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from "vuex";
import { baseUrl } from "@/utils/setting.js";

export default {
  data() {
    return {
      form: {
        phone: "",
        code: "",
      },
      counting: false,
      counter: 60,
      check: 0,
      countdown: 0,
      timer: null,
      showBackup: false,
      authCancelled: false,
      sendCode: false,
    };
  },
  computed: {
    canLogin() {
      return this.check === 1 && this.form.phone && this.form.code;
    },
  },
  methods: {
    ...mapMutations("user", ["setUser"]),

    async getCode() {
      if (this.counting) return;
      this.sendCode = true;
      if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      try {
        const res = await uni.$u.http.post("/send_sms/send", {
          mobile: this.form.phone,
        });

        if (res.code === 1) {
          this.startCounter();
          uni.showToast({
            title: "验证码已发送",
            icon: "success",
          });
          this.countdown = 60;
          this.timer = setInterval(() => {
            if (this.countdown > 0) {
              this.countdown--;
            } else {
              clearInterval(this.timer);
            }
          }, 1000);
        } else {
          throw new Error(res.msg);
        }
      } catch (error) {
        uni.showToast({
          title: error.message || "发送失败，请重试",
          icon: "none",
        });
      }
    },

    startCounter() {
      this.counting = true;
      this.counter = 60;
      const timer = setInterval(() => {
        this.counter--;
        if (this.counter <= 0) {
          clearInterval(timer);
          this.counting = false;
        }
      }, 1000);
    },

    async handleLogin() {
      if (!this.sendCode) {
        uni.showToast({
          title: "请先获取验证码",
          icon: "none",
        });
        return;
      }
      if (!this.check) {
        uni.showToast({
          title: "请同意用户协议和隐私政策",
          icon: "none",
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }
      try {
        const res = await uni.$u.http.post("/send_sms/check", this.form);
        if (res.code === 1) {
          const userData = res.data;
          this.setUser(userData);
          uni.setStorageSync("token", userData.token);

          uni.showToast({
            title: "登录成功",
            icon: "success",
          });

          setTimeout(() => {
            uni.switchTab({
              url: "/pages/index/index",
            });
          }, 1500);
        } else {
          throw new Error(res.msg);
        }
      } catch (error) {
        uni.showToast({
          title: error.message || "登录失败，请重试",
          icon: "none",
        });
      }
    },

    checkboxChange(e) {
	  if(e.detail.value[0] == 1){
		  this.check = 1
	  }else{
		   this.check = 0
	  }
    },

    privacy(type) {
      const urls = {
        privacyPolicy:
          baseUrl.replace("/api", "") + "/index/index/privacyPolicy",
        userAgreement:
          baseUrl.replace("/api", "") + "/index/index/userAgreement",
        childPrivacy: baseUrl.replace("/api", "") + "/index/index/childPrivacy",
      };
      uni.navigateTo({
        url: `/subpkg/scan_login/webview?url=${encodeURIComponent(urls[type])}`,
      });
    },

    goToQuickLogin() {
      this.showLoginPage();
    },

    showLoginPage() {
      try {
        uni.preLogin({
          provider: "univerify",
          success: () => {
            console.log("预登录成功");
            this.launchAuthPage();
          },
          fail: (err) => {
            console.log("预登录失败:", err);
            uni.showToast({
              title: "一键登录不可用，请使用验证码登录",
              icon: "none",
            });
          },
        });
      } catch (error) {
        console.error("预登录异常:", error);
        uni.showToast({
          title: "一键登录不可用，请使用验证码登录",
          icon: "none",
        });
      }
    },

    launchAuthPage() {
      uni.login({
        provider: "univerify",
        univerifyStyle: {
          fullScreen: true,
          backgroundColor: "#ffffff",
          checkBoxState: false,
          icon: {
            path: "/static/app_logo.png",
            width: "60px",
            height: "60px",
          },
          authButton: {
            normalColor: "#009c7b",
            highlightColor: "#009c7b",
            disabledColor: "#CCCCCC",
            textColor: "#ffffff",
            title: "本机号码一键登录",
          },
          privacyTerms: {
            defaultCheckBoxState: false,
            checkBoxSize: 13,
            textColor: "#999999",
            termsColor: "#009c7b",
            prefix: "我已阅读并同意",
            suffix: "并使用本机号码登录",
            privacyItems: [
              {
                url: baseUrl.replace("/api", "") + "/index/index/userAgreement",
                title: "《用户协议》",
              },
              {
                url: baseUrl.replace("/api", "") + "/index/index/privacyPolicy",
                title: "《隐私政策》",
              },
            ],
          },
          slogan: {
            textColor: "#333333",
          },
        },
        success: (res) => {
          console.log("登录成功:", res);
          if (
            res.authResult &&
            res.authResult.access_token &&
            res.authResult.openid
          ) {
            this.getPhoneNumberWithToken(
              res.authResult.access_token,
              res.authResult.openid
            );
          }
        },
        fail: (err) => {
          console.error("取消一键登录:", err);
        },
      });
    },

    getPhoneNumberWithToken(token, openid) {
      uni.showLoading({
        title: "登录中...",
      });

      uniCloud.callFunction({
        name: "getPhoneNumber",
        data: {
          access_token: token,
          openid: openid,
        },
        success: (phoneRes) => {
          console.log("phoneRes", phoneRes);
          if (phoneRes.result && phoneRes.result.code === 0) {
            const phoneNumber = phoneRes.result.phoneNumber;
            this.loginWithPhone(phoneNumber);
          } else {
            uni.hideLoading();
            uni.showToast({
              title: "获取手机号失败，请尝试验证码登录",
              icon: "none",
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          console.error("获取手机号失败:", err);
          uni.showToast({
            title: "登录失败，请尝试验证码登录",
            icon: "none",
          });
        },
      });
    },

    loginWithPhone(phoneNumber) {
      uni.showLoading({
        title: "登录中...",
      });

      uni.$u.http
        .post("/send_sms/phoneLogin", {
          phone: phoneNumber,
        })
        .then((res) => {
          uni.hideLoading();
          if (res.code === 1) {
            const userData = res.data;
            console.log(userData);
            this.setUser(userData);

            uni.setStorageSync("token", userData.token);

            uni.setStorageSync("userInfo", JSON.stringify(userData.userInfo));

            uni.showToast({
              title: "登录成功",
              icon: "success",
            });

            setTimeout(() => {
              uni.closeAuthView();
              uni.switchTab({
                url: "/pages/index/index",
              });
            }, 1500);
          } else {
            uni.showToast({
              title: res.msg || "登录失败，请尝试验证码登录",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          uni.hideLoading();
          console.error("登录接口异常:", err);
          uni.showToast({
            title: "网络异常，请重试",
            icon: "none",
          });
        });
    },
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #cdf3e7 0%, #ffffff 29%, #ffffff 100%);
  padding-top: 180rpx;
}

.back-icon {
  padding: 40rpx;

  .iconfont {
    font-size: 40rpx;
    color: #333;
  }
}

.title {
  font-weight: 800;
  font-size: 40rpx;
  padding-left: 40rpx;
  color: #201e2e;
  margin: 60rpx 0;
}

.form {
  padding: 0 40rpx;

  .input-group {
    position: relative;
    margin-bottom: 40rpx;
    border-bottom: 1px solid #eeeeee;
    padding: 20rpx 0;
    display: flex;
    align-items: center;

    .area-code {
      font-size: 32rpx;
      color: #333;
      margin-right: 20rpx;
    }

    .phone-input,
    .code-input {
      flex: 1;
      height: 60rpx;
      font-size: 32rpx;
      color: #333;
      border: none;
    }
  }

  .code-group {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .verify-btn {
      min-width: 180rpx;
      height: 60rpx;
      line-height: 60rpx;
      background: #1bb394;
      border-radius: 12rpx;
      color: #fff;
      font-size: 28rpx;
      margin: 0;
      padding: 0 30rpx;

      &.disabled {
        background: #cccccc;
      }
    }
  }

  .login-btn {
    width: 100%;
    height: 90rpx;
    background: #1bb394;
    border-radius: 45rpx;
    line-height: 90rpx;
    color: #fff;
    font-size: 32rpx;
    margin-top: 80rpx;

    &.disabled {
      background: #cccccc;
    }
  }

  .quick-login {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40rpx;

    .icon-container {
      width: 80rpx;
      height: 80rpx;
      background: #f5f5f5;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10rpx;

      .icon-image {
        height: 60rpx;
        width: 60rpx;
      }

      .iconfont {
        color: #1bb394;
        font-size: 40rpx;
      }
    }

    .quick-login-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.agreement {
  margin-top: 40rpx;
  padding: 0 20rpx;

  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;

    ::deep .uni-checkbox-input {
      margin-right: 0;
    }
  }

  .agreement-text {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .link {
      color: #1bb394;
      padding: 0 4rpx;
    }
  }
}

// 输入框placeholder样式
::-webkit-input-placeholder {
  color: #999;
  font-size: 32rpx;
}

.code-input text.disabled {
  color: #999;
}
</style>
