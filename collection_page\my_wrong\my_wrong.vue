<template>
	<view class="container u-border-top" >
		<!-- 信息面板 -->
		<view class="panel">
			<view class="info u-border-right">
				<text class="num">
					{{calculativeData.allNum|handleNull}}
					<text>题</text>
				</text>
				<text class="subject">累计错题</text>
			</view>
			<view class="info" v-for="(item, index) in calculativeData.cateData" :key="index">
				<text class="num">
					 {{item.num|handleNull}}
						<text>题</text>
					</text>
					
				</text>
				<text class="subject"> {{item.name|handleNull}}</text>
			</view>
		</view>
		
		<!-- tab选择 -->
		<view class="user-tabs">
			<view class="user-tabs-left">
				 <u-tabs :list="list" @click="tabClick"
				 lineWidth="50"
				 lineHeight="6"
				  lineColor="#009c7b"
				 ></u-tabs>
			</view>
			<view class="user-tabs-right">
				<mimicry-switch  :current.sync="recent"></mimicry-switch>
			</view>
		</view>
		
		<!-- 列表信息 -->
		<view class="list" v-if="questionList.length>0">
			<view class="item u-border-bottom" v-for="obj in questionList" :key="obj.id" @click="chooseAction(obj, pqidcontainqids[obj.id])">
				<view class="item-title">
					<text>{{obj.title}}</text>
					<text>考题数：{{pqidcontainqids[obj.id].length}}题</text>
				</view>
				<view class="enter" >
					<text>进入练习</text>
					<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
				</view>
			</view>
		</view>
		
		<u-empty v-if="loading==false && questionList.length==0"
		        mode="list"
				text="没有错题"
				textSize="24"
		>
		</u-empty>
		<!-- <view class="more" v-if="show && loading==false" @click="loadMore">
			<image src="@/static/png/down-arrow-double.png" mode="widthFix"></image>
			<text>加载更多</text>
		</view> -->
		<u-action-sheet
		 @close='showAction=false' 
		 :closeOnClickOverlay='true' 
		 :safeAreaInsetBotto='true' 
		 :actions="actionList" 
		 :show="showAction"
		  @select="selectActionClick"
		 ></u-action-sheet>
	</view>
</template>

<script>
	import mimicrySwitch from "@/components/mimicry-switch/mimicry-switch.vue"
	import {getAllWrongQuestionData, getWrongQuestionListBySubject} from "@/api/collection/index.js"
	import {getQuestionListByPqid, getProgress} from "@/api/professional/index.js"
	import {mapState} from "vuex"
	import { selectTemplate, selectTemplateAnswer } from "@/utils/tool.js"
	export default {
		data() {
			return {
				loading:false,
				calculativeData:{
				},
				page:{
					perPageNum:10,
					page:1
				},
				pqidcontainqids:{},
				videoPage:1,
				show:true,
				videoTotal:0,
				showText:['英语','政治','数学','专业课'],
				questionList:[],
				list: [
				],
				recent:false,
				dataCount:'',
				type:1,
				dataList:'',
				collectListVideo:[],
				currentSearchSubjectId:0,
				actionList: [
								{
									name:'重新刷题',
									fontSize:'30',
									id:0
								},
								{
									name:'查看解析',
									fontSize:'30',
									id:1
								},

							],
				showAction:false,
				currentClickObj:null,
				currentClickQid:-1,
				setAnswer:false,
				currentAnswerList:[]
			};
		},
		created() {
			this.getWrongStatistics();
		},
		methods:{
			 async getWrongStatistics(){
				let data = await getAllWrongQuestionData();
				if(data!=false){
					this.calculativeData = data;
					this.list = this.calculativeData['cateData'].map(item=>{
						return {
								id:item.id,
								name:item.name
							}
					})
					//默认显示第一条
					this.currentSearchSubjectId = this.list[0].id
					this.doSearch()
				}
			},
			selectActionClick(item){
				if(item.id == 0){
					this.setAnswer = false
				}else{
					this.setAnswer = true
				}
				this.tobank(this.currentClickObj, this.currentClickQid)
			},
			chooseAction(obj, pqidcontainqids){
				this.showAction = true
				this.currentClickObj = obj
				this.currentClickQid = pqidcontainqids
			},
			//切换科目初始化数据
			async tabClick(item){
				this.loading = true
				this.currentSearchSubjectId = item.id
				 this.page.page = 1
				 this.questionList = []
				 this.pqidcontainqids = {}
				 await this.doSearch()
				 this.loading = false
			},
			loadMore(){
				this.page.page ++
				this.doSearch(true)
			},
			/**
			 * 到题库
			 * @param {Object} item 题库信息
			 * @param {Array} containsIds 题库包含的题目id数组
			 */
			async tobank(item, containsIds) {
				this.$store.commit("professionalConfig/clearAnswerList")
				//记录进入题库的页面
				this.$store.commit("professionalConfig/setCurrentStartPage")
				//题目列表
				let answeringQuestion = []
				//获取题目列表
				const params = {
					pqid: item.id,
					perPageNum: this.config.num,
				}
				let questionList = await getQuestionListByPqid(params)
				//如果题目列表为空返回
				if (questionList === false) {
					return
				}
				//获取 一级分类信息
				const subject = this.list.find(item=>item.id==this.currentSearchSubjectId)
				questionList.forEach(obj => {
					if(containsIds.includes(obj.id)){
						// 构造当前 题目的列表数据
						answeringQuestion.push({
							...obj,
							cateName: subject.name,
							type: item.type,
							pqid: item.id,
							id: obj.qid,
							subtitle: obj.qtitle,
							qtype: obj.qtype,
							professionTitle: item.title
						})
					}
				
				})

				this.$store.commit("professionalConfig/setCurrentTypes", answeringQuestion)
				//判断配置信息是否跳过已做题目
				//默认第一道题开始
				let startIndex = 0
				if (this.config.do_unread == 1) {
					//获取题库该中 已经做了多少道题
					let res = await getProgress({
						pqid: item.id
					})
					if (res !== false) {
						startIndex = res.currentIndex
					}
				}
				this.$store.commit("professionalConfig/setCurrentIndex", startIndex);
				//是否同步答案
				if(this.setAnswer){
					const answerData = answeringQuestion.map((item, index)=>{
						return {
							index,
							answer: JSON.parse(this.currentAnswerList[item.id].answer) || {}
						}
					})
					//清空之前的答题记录
					this.$store.commit("professionalConfig/clearAnswerList")
					this.$store.commit("professionalConfig/setAnswerList", answerData)
					selectTemplateAnswer(answeringQuestion[startIndex],false)
				}
				selectTemplate(answeringQuestion[startIndex], item)
			},
			async doSearch(append=false){
				this.show = true
				const recent = this.recent?0:1
				const params = {
					id:this.currentSearchSubjectId,
					//...this.page,
					recent
				}
				let data = await getWrongQuestionListBySubject(params)
				if(data!=false){
					if(append){
						this.questionList = [...this.questionList ,...data.questionInfo]
						this.pqidcontainqids = { ...this.pqidcontainqids,...data.pqidcontainqids}
						this.currentAnswerList = [...this.currentAnswerList, ...data.answer]
					}else{
						this.questionList = data.questionInfo
						this.pqidcontainqids = data.pqidcontainqids
						this.currentAnswerList = data.answer
					}
					//data.questionInfo.length==0 && (this.show = false)	
				}
				//console.log(this.questionList)
				//if(this.questionList.length ==0){
				//	this.show = false
				//}
			}
		},
		filters:{
			handleNull(val){
				if(typeof val =="undefined"){
					return ""
				}
				return val
			}
		},
		components:{
			mimicrySwitch
		},
		watch:{
			recent(val){
				this.page.page = 1
				this.questionList = []
				this.pqidcontainqids={}
				this.doSearch()
			}
		},
		computed:{
				...mapState('professionalConfig', ['currentTypes', 'config']),
		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	padding-top: 34rpx;
	.panel{
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color:#F6F7FB;
		border-radius: 22rpx;
		
		.info{
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			padding: 0 14rpx;
			.num{
				color: #201E2E;
				font-size: 32rpx;
				font-weight: bold;
				text{
					font-size: 18rpx;
					color: #777;
					font-weight: normal;
				}
			}
			.subject{
				font-size: 18rpx;
				color: #777;
			}
		}
	}
	.user-tabs{
		display: flex;
		align-items: center;
		justify-content: space-between;
		.user-tabs-left{
		
		}
		.user-tabs-right{
			
		}
	}
	.list{
		.item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding:30rpx;
			.item-title{
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				flex-direction: column;
				text{
					width: 100%;
					&:first-child{
						color: #201E2E;
						font-size: 30rpx;
						font-weight: bold;
						padding: 0 4rpx;
					}
					&:last-child{
						margin-top: 4rpx;
						color: #9F9F9F;
						font-size: 22rpx;
						padding: 0 4rpx;
					}
				}
			}
			.enter{
				display: flex;
				align-items: center;
				justify-content: center;
				text{
					color: #EE7878;
					font-size: 20rpx;
					margin-right: 10rpx;
				}
			}
		}
	}
	.more{
		margin-top: 14rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		image{
			width: 20rpx;
		}
		text{
			margin-left: 6rpx;
			font-size: 30rpx;
			color: #A2A2A2;
			font-weight: bold;
		}
		
	} 
}
</style>
