<template>
	<view class="container u-border-top">
		<view class="cate-list">
			<text @click="currentSel=item.id" :class="{sel:currentSel==item.id}" v-for="item in subCatList" :key="item.id">{{item.name}}</text>
		</view>
		<view class="line">
			
		</view>
		<div class="list">
<!-- 			<view class="title-name">
				<view class="left">
					<text class="green-block"></text>
					<text>推荐</text>
				</view>
				<view class="content">
				
				</view>
			</view> -->
			
			<view class="item u-border-bottom" v-for="item in leafCateList" :key="item.id" @click="toListen(item)">
				<image :src="item.pic"></image>
				<view class="title-info">
					<text class="t-h1">{{item.name}}</text>
					<text class="tip m-2">{{item.subtitle}}</text>
					<text class="tip">已经播放{{item.sum}}次|共{{item.listen.length}}集</text>
				</view>
				<view class="play-icon">
					<u-icon name="play-circle-fill" size="50" color="#009c7b"></u-icon>
					
				</view>
			</view>
		</div>

	</view>
</template>

<script>
	import {getSubCatList,getQuestionListByCate} from "@/api/exampoint/index.js"
	export default {
		onLoad(option) {
			//更改标题
			if(typeof option.title !="undefined"){
				uni.setNavigationBarTitle({
					title:option.title
				})
			}
			this.cid = option.id || 0;	
			
			getSubCatList(this.cid).then(res=>{
				console.log(res);
				if(res!=false){
					this.subCatList = res.list;
					this.currentSel = res.list[0].id;
				}
			})
		},
		data() {
			return {
				currentSel:-1,
				cid:0,
				subCatList:[],	
				leafCateList:[
				],
				
			};
		},
		methods: {
			async getQuestionList(id){
				const ret = await getSubCatList(id)
				if(ret!=false){
					this.leafCateList = ret.list.map(item=>{
						let sum = item.listen.reduce((accumulator,obj)=>accumulator+obj.listened_num, 0)
						if(sum>=10000){
							sum = (sum/10000).toFixed(1) + "万"
						}
						return {
							sum,
							...item
						}
					});
				}
			},
			toListen(item){
				uni.navigateTo({
					url:`/subpkg/listen_audio/listen_audio?cid=${item.id}&name=${item.name}`
				})
			}
		},
		watch:{
			currentSel(val){
				this.getQuestionList(val);
			}
		}
	}
</script>

<style lang="scss" scoped>
.container{
	.cate-list{
		padding: 18rpx 0;
		padding-left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: wrap;
		text{
			font-weight: 500;
			font-size: 28rpx;
			color: #201E2E;
			line-height: 70rpx;
			text-align: center;
			width: 150rpx;
			height: 70rpx;
			background: #F6F7FB;
			border-radius: 12rpx ;
			margin-bottom: 18rpx;
			margin-right: 30rpx;
		}
		.sel{
			background: #01997A;
			color: #fff;
		}
	}
	.img-container{
		padding: 0 30rpx;
		padding-bottom: 30rpx;
		.listening-img{
			width: 100%;
		}
	}
	.line{
		width: 100%;
		height: 10rpx;
		background: #F6F7FB;
	}
	.list{
		padding: 0 30rpx;
		padding-bottom: 40rpx;
		.title-name {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			padding-bottom: 0;
		
			.left {
				color: #1F1F27;
				font-size: 32rpx;
				font-weight: bold;
				
				.green-block {
					display: inline-block;
					width: 14rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #01997A;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
					margin-right: 6rpx;
				}
			}

		}
		
		.item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 22rpx;
			padding-top: 40rpx;
			image{
				width: 100rpx;
				height: 100rpx;
				border-radius: 14rpx;
			}
			.title-info{
				width: 60%;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				text-align: left;
				margin-left: -40rpx;
				.t-h1{
					width: 100%;
					color: #201E2E;
					font-size: 32rpx;
					font-weight: bold;
				}
				.tip{
					width: 100%;
					color: #AFAFAF;
					font-size: 20rpx;
					margin-top: 12rpx;
				}
				.m-2{
					margin-top: 2rpx;
				}
			}
			.play-icon{
				margin-right: 20rpx;
			}
		}
	}
}
</style>
