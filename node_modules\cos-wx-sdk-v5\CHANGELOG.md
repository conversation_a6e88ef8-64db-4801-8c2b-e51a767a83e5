# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v1.7.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.6.2...v1.7.0) - 2024-05-24

feat
-  getObject 新增对 Key 校验

### Merged

- feat: 1.7.0 [`#103`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/103)

### Commits

- Updated CHANGELOG.md [`8c61b5d`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8c61b5dc4d38bbe7e9aab0c28748a67a4e61c05b)

## [v1.6.2](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.6.1...v1.6.2) - 2024-04-16

feat
-  支持接入  CLS 上报

### Merged

- feat: 支持 cls 上报 [`#102`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/102)

### Commits

- Updated CHANGELOG.md [`7ca0e88`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/7ca0e885ef6db9aafeec4c994c884a7dae04dcb4)

## [v1.6.1](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.6.0...v1.6.1) - 2024-03-25

fix
-  修复 util.getFileSizeByPath异常处理

### Merged

- fix: 修复 util.getFileSizeByPath异常处理 [`#101`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/101)

### Commits

- Updated CHANGELOG.md [`e260d89`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e260d89fe8ffae1edb40fd12f6afc1cebbe6363e)

## [v1.6.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.5.0...v1.6.0) - 2024-03-05

feat
-  去除内置灯塔 sdk

### Merged

- Dev/1.6.0 [`#99`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/99)

### Commits

- Updated CHANGELOG.md [`dda33bf`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/dda33bf45ad724dbdf42650853875894715771e5)

## [v1.5.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.12...v1.5.0) - 2024-01-24

feat
1、提升安全性：不再支持设置 pathStyle
2、提升安全性：wx.request 不支持默认redirect
3、提升安全性：host 强制加入签名计算
4、getObject支持设置DataType
5、重新整理万象demo

fix
1、修复 uploadFile 未对参数 Key 进行校验

### Merged

- Dev/1.5.0 [`#98`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/98)
- Dev/sign pic operations [`#97`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/97)
- Feat/ci demo [`#94`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/94)

### Commits

- Updated CHANGELOG.md [`2455c00`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/2455c00c3fe8eb44654a2e9c7876f04c51e3ca06)

## [v1.4.12](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.11...v1.4.12) - 2023-08-03

feat
-  增加代码格式化
-  提供base64方法

### Merged

- Dev/1.4.12 [`#92`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/92)

### Commits

- Updated CHANGELOG.md [`455eec9`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/455eec96a5fc2df894345e9b26537a6e3ba4ae77)

## [v1.4.11](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.10...v1.4.11) - 2023-07-26

fix
-  修复使用await异常bug

### Merged

- Dev/1.4.11 [`#91`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/91)
- Dev/demo [`#90`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/90)
- Dev/demo [`#88`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/88)
- fix: 修复错别字 [`#87`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/87)

### Commits

- Updated CHANGELOG.md [`ab7a19b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ab7a19bfde84dc82ffb632aeac0ffdadc10a7801)

## [v1.4.10](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.9...v1.4.10) - 2023-01-04

feat
-  postObject支持x-cos-forbid-overwrite头部

### Merged

- feat: postObject支持x-cos-forbid-overwrite头部 [`#84`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/84)

### Commits

- Updated CHANGELOG.md [`1a17ded`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/1a17ded728fc0409e0f298b5b9cd3a73db0efc80)

## [v1.4.9](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.8...v1.4.9) - 2022-12-29

fix
-  更新可以签入签名的headers

### Merged

- fix: 更新可以签入签名的headers [`#83`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/83)

### Commits

- 更新简单上传示例 [`18d1a02`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/18d1a02050a431d4015c28e8e7ed816a0c1e4f2a)
- Updated CHANGELOG.md [`51e4040`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/51e40408d0c57fe9571507369a470a8668244ff5)
- upd: sts demo补充condition指引 [`658b3eb`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/658b3eba99c9432f11dfa411a262f546f5a22f6c)
- 更新简单上传示例 [`ce26017`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ce26017790129e4e43f0a296558113d3f65b6f9c)

## [v1.4.8](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.7...v1.4.8) - 2022-11-21

feat
-  getObjectAcl支持传VersionId

### Merged

- feat: getObjectAcl支持VersionId [`#81`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/81)

### Commits

- Updated CHANGELOG.md [`8dc585a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8dc585a2b6c2c31084fc5952ccc29d942282e031)

## [v1.4.7](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.6...v1.4.7) - 2022-11-09

fix
-  升级依赖包@xmldom/xmldom版本

### Merged

- fix: update @xmldom [`#80`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/80)

### Commits

- Updated CHANGELOG.md [`60ad375`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/60ad37542e472d0d6f1f3eaa7dc7487c4c9890d6)

## [v1.4.6](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.5...v1.4.6) - 2022-10-25

fix
-  修复批量上传uploadFiles偶现问题

### Merged

- fix: 修复批量上传偶现异常问题 [`#79`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/79)

### Commits

- Updated CHANGELOG.md [`7e3717b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/7e3717b05835873f465c70aa6ceac96c5b5e76ea)

## [v1.4.5](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.4...v1.4.5) - 2022-10-22

fix
-  getObjectUrl暂时只支持回调写法

### Merged

- fix: getObjectUrl暂时只支持回调写法 [`#78`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/78)

### Commits

- Updated CHANGELOG.md [`8cc1986`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8cc19868dac5683cd807cfb8678c74c477b29ce5)

## [v1.4.4](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.3...v1.4.4) - 2022-10-20

-  分块复制支持续传
-  合法域名校验支持内网域名和.cn域名

### Merged

- feat1.4.4 [`#77`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/77)

### Commits

- Updated CHANGELOG.md [`cdf8af6`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/cdf8af65f6b44ae1ed8244655073b6e3003535a0)

## [v1.4.3](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.2...v1.4.3) - 2022-09-22

feat
-  支持sdk内部获取上传文件大小，不再依赖用户传入的FileSize

### Merged

- fix: sdk内部获取上传文件大小 [`#76`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/76)

### Commits

- Updated CHANGELOG.md [`4652da7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4652da7173c8f0fdc1de62955703d6e9e6efd0de)

## [v1.4.2](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.1...v1.4.2) - 2022-09-09

fix
-  修复已知问题

### Merged

- fix 1.4.2 [`#75`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/75)
- fix：修复已知问题 [`#73`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/73)

### Commits

- Updated CHANGELOG.md [`a4a3e55`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a4a3e55fbb8ae0b4739a7a9f5b85b031e726553c)

## [v1.4.1](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.4.0...v1.4.1) - 2022-09-01

fix
-  修复已知问题

### Commits

- fix：修复已知问题 [`d1a433f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/d1a433fbd787ba134a672141ae8c7d750df50704)

## [v1.4.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.3.0...v1.4.0) - 2022-08-17

feat
-  新增beacon上报

### Merged

- feat/1.4.0 [`#72`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/72)

### Commits

- Updated CHANGELOG.md [`7d52326`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/7d523262e886826755a1adf0aae207b04bee7391)

## [v1.3.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.2.1...v1.3.0) - 2022-08-09

feat
-  webpack升级到4.x
-  putObject支持FilePath参数。同时传参FilePath和Body时，优先级Body>FilePath
-  初始化支持指定SimpleUploadMethod，当调用高级上传uploadFile或批量上传uploadFiles时，对小文件执行简单上传方法postObject或putObject

### Merged

- Feat/1.3.0 [`#71`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/71)

### Commits

- Updated CHANGELOG.md [`177a965`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/177a965a3428bb05d94e4a28ef2c86e5c0ff4d8a)

## [v1.2.1](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.2.0...v1.2.1) - 2022-07-21

feat
- 初始化支持HttpDNSServiceId参数，填写后代表开启 HttpDNS 服务。HttpDNS 用法详见https://developers.weixin.qq.com/miniprogram/dev/framework/ability/HTTPDNS.html

### Merged

- feat: 1.2.1 [`#70`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/70)
- Feat/1.2.0 [`#69`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/69)

### Commits

- Merge https://github.com/livehigh/cos-wx-sdk-v5 into feat/1.2.1 [`982305a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/982305a2f4ce6b5d6e55e8dc96f1b14eedcc9297)
- upd 去掉ForcePathStyle处理 [`6ed7dcc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6ed7dccc092cde1e43c54101870191fce7eaba49)
- upd 补充HttpDNS使用说明 [`c80f440`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c80f440d2d75be962433250e824a331c7142979c)

## [v1.2.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.8...v1.2.0) - 2022-06-30

fix
- 修复使用postObject不能正确的pauseTask，cancelTask问题
- 签名回调里使用SecurityToken代替并兼容XCosSecurityToken

### Merged

- 完善内容审核demo [`#68`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/68)
- Feat/1.1.8 [`#67`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/67)

### Commits

- feat 1.2.0 [`0fc7281`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/0fc7281a43a6d9f3bf1f125c04fabdf63bcc9f4c)
- upd demo [`8e198e3`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8e198e3742c778844e82ebc8c6bca777d0ae58e6)
- upd [`8f6170f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8f6170f15972c479963c1b78816d4a8a13dd8fd8)
- del [`c12abe9`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c12abe9e93dd07607d46f2e61286a16dfb17721e)
- upd [`2035c54`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/2035c54193d7c96030bc5b364c100b952f62d627)
- upd [`a8142cc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a8142cca3aa5e822b2509bf2c3a2a96402a343d8)

## [v1.1.8](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.7...v1.1.8) - 2022-06-23

feat
- putBucketCors支持ResponseVary
- 提高签名时Expires参数的优先级

### Merged

- feat：更换依赖xmldom-&gt;@xmldom/xmldom [`#66`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/66)

### Commits

- feat 1.1.8 [`aad1bad`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/aad1badffefc14d06a1d7f0851bc7aeec09bcd51)
- upd [`42134c3`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/42134c36def10a6a84d9b9c45dc56fe5e67e880a)

## [v1.1.7](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.6...v1.1.7) - 2022-04-26

feat：更换依赖xmldom->@xmldom/xmldom

### Commits

- feat：更换依赖xmldom-&gt;@xmldom/xmldom [`ca08986`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ca08986fb0577e96fa2a924aa8ddd9e13bb5a82a)
- Updated CHANGELOG.md [`3c2359a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3c2359a2f95df088fec2ae221f43be6c58b27537)

## [v1.1.6](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.5...v1.1.6) - 2022-04-25

feat：getObjectUrl支持全球加速参数

### Merged

- Feat/get object support use accelerate [`#65`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/65)
- feat：支持host不参与签名计算 [`#64`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/64)

### Commits

- Updated CHANGELOG.md [`8988b83`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8988b83982b8e0c4634f24179d1c22ca31b2a9d1)
- rebuild [`790911c`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/790911ca780c0001c6d48b733ec96b239cae5312)

## [v1.1.5](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.4...v1.1.5) - 2022-02-23

- postObject支持服务端加密

### Merged

- feat：postObject支持服务端加密 [`#63`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/63)
- fix：修复host签名时不兼容全球加速域名 [`#60`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/60)

### Commits

- upd：version 1.1.5 [`daa1573`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/daa15732d88f65ed347aa7c3d6d3cd8731d62a5b)

## [v1.1.4](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.3...v1.1.4) - 2021-12-27

fix：修复host签名时不兼容全球加速域名

### Commits

- fix：修复host签名时不兼容全球加速域名 [`8e6e6ab`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8e6e6abafb1a3a1ba8fd249cc912f67d03162037)
- Updated CHANGELOG.md [`f12d068`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f12d0689f6d741b122a7fde194819f7114687ac1)

## [v1.1.3](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.2...v1.1.3) - 2021-12-03

feat
-sdk入口文件默认为压缩版

### Commits

- Merge https://github.com/tencentyun/cos-wx-sdk-v5 [`12c3dd0`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/12c3dd0ecce6a1a73b792dde8f395dccc06aa3f4)
- Updated CHANGELOG.md [`9d49a86`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/9d49a86a7f805ea950632392f4a878fb87ef265f)
- upd：入口文件默认为压缩版文件 [`0f8a772`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/0f8a772da7046bef25757109fba1419ff248d63f)

## [v1.1.2](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.1...v1.1.2) - 2021-12-01

- host参与签名
- getObjectUrl签名签入Query
- 新增参与签名的headers白名单
- 支持打包出.min.js

### Merged

- Feat/1.1.2 [`#57`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/57)
- fix：CIHost兼容处理 [`#56`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/56)

### Commits

- feat：1.1.2 [`cdd3f1b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/cdd3f1b8f405d8d13f990b6b4c65253df5f3f725)
- upd：getObjectUrl的qUrlParamList encode处理 [`2deb189`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/2deb1899386c66c714e88e65291278c8261c80bb)
- upd [`2c1f670`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/2c1f6701e9755dbcc34ea1e875017cecf6419bdd)
- upd：删除重复代码 [`dbaa639`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/dbaa639a51513bb548306e88b0f1858cb530067d)
- Updated CHANGELOG.md [`96d4679`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/96d467902d0a447508ea7157cf93e431bf3af07b)
- fix：request queryString [`54e7c31`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/54e7c31f4e0a115fe268fbb911c5dea959ce2397)

## [v1.1.1](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.1.0...v1.1.1) - 2021-11-18

fix：CIHost兼容处理

## [v1.1.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.13...v1.1.0) - 2021-11-17

- 新增appendObject接口
- 新增媒体处理demo
- 修复已知d.ts错误

### Merged

- Feat/1.1.0 [`#55`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/55)
- feat: 改变demo 展示形式，分类别展示。demo 增加数据万象示例。 [`#45`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/45)

### Commits

- feat:增加appendObject；增加三个媒体处理接口 [`daf4387`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/daf4387e2d69ab428e2ecac465e3e657eaba34ae)
- upd：支持ci demo [`8f87f31`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8f87f31b232b2f5e04c44609371c3f9a0829cf69)
- feat/支持appendObject；新增媒体处理三个接口； [`4cfb136`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4cfb13695cb483d97bc6f95b754c15b09fbc1a33)
- upd：obj2str方法 [`52da287`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/52da2871c5abaa288fc9f00fe9d19e0c55aaaa81)
- feat：demo整合 [`f16699f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f16699f55b40ba721060cdc52de1ef0c8a71d8a8)
- upd [`dfe356d`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/dfe356de86c5c04abb46f6758bd90aad0bfcbc99)
- feat：新增test用例与测试说明 [`8d5571f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8d5571fe498321c18461444abe09d5164287e925)
- upd [`b84d3fc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/b84d3fc0a95645486a5dc227e9c67c11de0d3a85)
- upd demo [`c10c7de`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c10c7debf9146a2719fec902b9b5637df0357fcc)
- Updated CHANGELOG.md [`dcbe236`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/dcbe236787333e694fb84ce5d6394c28cc6c700a)
- upd：test.js [`f22ce39`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f22ce39ec68ad0732b4a9b06ec86edd2efe5328e)

## [v1.0.13](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.12...v1.0.13) - 2021-11-11

### Commits

- Updated CHANGELOG.md [`6c52c38`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6c52c382a2e5ff5edf346fb94869b67097f6f9dc)
- fix：修复uploadSliceItem [`6ffd3cf`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6ffd3cf5ab21880e3db42503d2586653f0f02734)

## [v1.0.12](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.11...v1.0.12) - 2021-11-11

### Merged

- fix：getObjectUrl报错问题 [`#54`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/54)

### Commits

- 修复getBucketReferer已知错误；支持mimeLimit、trafficLimit [`91d23d7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/91d23d788d02144a8c938d6fd58bfaa796218c60)

## [v1.0.11](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.10...v1.0.11) - 2021-10-27

### Merged

- Fix/get object url [`#49`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/49)

### Commits

- fix：getObjectUrl报错问题 [`c516a9b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c516a9bb76ff07f994c1e0959c41ef0cfffa9316)
- add PostObject policy demo [`edd61cc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/edd61cc5a466594644eb4408e63602042a3fefaf)

## [v1.0.10](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.9...v1.0.10) - 2021-07-02

### Commits

- fix：修复已知getObjectUrl报错问题 [`e20f993`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e20f9934eb32cde15e942716ece3ae984a530e14)
- Updated CHANGELOG.md [`a3528bf`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a3528bff960f63a8639957a0102f09c49c834dc3)
- feat：getObjectUrl支持Headers、Query参数 [`7e19f11`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/7e19f115bf3ede00a6b473aa46913d34fd756a29)

## [v1.0.9](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.8...v1.0.9) - 2021-06-11

### Merged

- feat：新增高级上传uploadFile接口 [`#48`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/48)

### Commits

- Updated CHANGELOG.md [`9263e5f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/9263e5f29e6ad4e493c32cdfe011f8a55126818b)
- update uploadFile [`f7f8949`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f7f89495694eb07f4d19f0759320ac2962e58abe)

## [v1.0.8](https://github.com/tencentyun/cos-wx-sdk-v5/compare/1.0.7...v1.0.8) - 2021-06-04

### Commits

- update version 1.0.8 [`c89ac9e`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c89ac9ef3af9c52a87f3bca6ef15081a43c0d741)
- Updated CHANGELOG.md [`c359130`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c359130a54555bb8cffaf1e837bfb29e72798509)
- update version 1.0.8 [`3a3af79`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3a3af79437434eee549a8f2f745a53cff4e696d7)
- feat/uploadId [`023261a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/023261aedaa00c10b682c032d6b7bad830cf6641)

## [1.0.7](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.6...1.0.7) - 2021-05-08

### Merged

- Feat/accelerate and ci [`#44`](https://github.com/tencentyun/cos-wx-sdk-v5/pull/44)

### Commits

- support UseAccelerate [`a32b890`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a32b890007fbcec767e48c50b440e89a1827ed9e)
- feat: 支持全球加速及图片处理 [`81e70a0`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/81e70a098f62818de9ac4bae1a1da2680c3b8f78)
- fix: sts 服务取环境内的配置 [`e586635`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e586635fdf2857136d41988137faa8e7ae3592d1)
- fix: 优化 sts 服务 [`1cf2582`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/1cf2582834fc3f2495bcf1341fb38c5902b681a9)

## [v1.0.6](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.5...v1.0.6) - 2021-01-13

### Commits

- postObject Location support key=${filename} [`b1cb886`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/b1cb88694e885950ab3ea1bb97d3d64c54a2abb9)
- Updated CHANGELOG.md [`6673a43`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6673a4316b9f7ba86524c986021f497430ef65fe)
- postObject Location support key=${filename} [`e3681df`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e3681df6ff552e033ede7026eb4608a8a5d69412)

## [v1.0.5](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v1.0.4...v1.0.5) - 2020-12-18

### Commits

- Updated CHANGELOG.md [`572c0a7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/572c0a71752a234aa3f46403e1c8a557982aaed4)
- add auto changelog workflow [`b6b33f3`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/b6b33f35a4cc12c5126d5be116f35023f0155a0a)
- const 改为 var [`6f0e081`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6f0e0813edc514e78262812b9779d594410b8207)

## [v1.0.4](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v0.7.2...v1.0.4) - 2020-11-19

### Commits

- v1.0.4 putObject、sliceUploadFile 支持 Query/CI [`76d2b81`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/76d2b81cb32757a063544f2abb5fdfea4b8aa734)
- v1.0.2 优化支持分片的判断逻辑 [`0340b4b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/0340b4b4f2085375a7b6dae03a3cc8bc0e286d43)
- 支持分片上传，支持多个新接口 [`4bf5e15`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4bf5e15eb75a461dfbff06a5842919c2edf5373b)
- 支持 StartTime [`0b30839`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/0b30839b35ec58b6310d4469567adeefe974a60e)
- add postObject param FilePath check [`827837b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/827837b1695bce6c73b3992f2a789216a2e1b667)
- v1.0.0 大版本，主要支持分片上传 [`5052728`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/5052728cba42bc4cc5ee7d24dfea16f3d5200042)
- 更换 md5 库，修复 md5 对特殊字符，计算错误 [`5a82156`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/5a8215690c2aca639940582ef79b0adc72287f95)
- 优化 demo [`f9cd2d7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f9cd2d7c21efd0b9848476790dcb0ae44d2a826a)
- v1.0.3 postObject 支持 Headers [`9615899`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/961589969bb29bcaf384066db7ac412017932065)
- v1.0.4 putObject、sliceUploadFile 支持 Query [`86d4144`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/86d4144eb54de34e0b774c97784d3807336d5af0)
- 修复etag在微信开发工具模拟器及真机中的获取。 [`6404256`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/640425644c330a3557ef51dee2042ee9bbe554b4)
- 修复 onProgress 反馈太慢 [`e9bdeb7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e9bdeb78dbe2cd694953b3846f98f1caf86a4f35)
- v1.0.1 优化打包结果的注释 [`3fe60ce`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3fe60ce22cda0fa0b9ce097a27401027a33c5fb9)
- 修改版本判断 [`a4bc4c1`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a4bc4c1521a2a11f40c4a12880309d4fb0045efa)
- 修复接口bug： [`cb022f7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/cb022f7213046f06d994f7ac7f4445b206fe6148)
- 更新示例 [`8516675`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/851667587953de86ca911e1f58a4e07b45359395)
- 1. 修复 getService 接口 region 变量不起作用的bug。 [`8557cb6`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/8557cb62c5584d7edbbd8055a10fe50c61ceabdc)
- 去掉跨域 Headers [`aaab2c5`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/aaab2c50b1ec7055191c7c7d061575cf85c32bfd)
- 修改版本判断 [`ee40f74`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ee40f74e1946941c7e226711c677b9486ce8425f)
- 修复用 v4 签名报错问题 [`fb04d4e`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/fb04d4e22031fc2781280bb17697d1b2154ce105)

## [v0.7.2](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v0.7.1...v0.7.2) - 2019-04-17

### Commits

- STS 例子更新到云API3.0 [`c28b2f6`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c28b2f6c0c6a369366d1306462bd523866e5f684)
- 支持 Expires 参数 [`4cb2ccd`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4cb2ccdf4cc41147628c6caf65d8a9cffd063255)

## [v0.7.1](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v0.7.0...v0.7.1) - 2019-03-12

### Commits

- 修复用固定密钥报错 [`ad988a6`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ad988a663c29445b7c906d08f83f1509bcd11b41)
- 优化初始化例子 [`5324ec4`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/5324ec4e17f711a5c0034fef1392e4a274faf0d9)
- 优化初始化例子 [`3fdd111`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3fdd111aee2e617cf57fa1f15ce7e799a95ec8e6)
- 优化初始化例子 [`4268c4c`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4268c4c1b6ad38c953f1364e16f359c7a58120ff)
- 更新 README [`65e69ee`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/65e69ee58c6d51ed8f9e472eb703dd6447ba9167)
- 修复用固定密钥报错 [`307315a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/307315a9527ecc4bc06e7deb85efcfafe729512e)
- 更新链接 [`a89850f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a89850f49c55738abbcf1677b4597b29c71c2947)

## [v0.7.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v0.5.0...v0.7.0) - 2018-12-26

### Commits

- 补充接口及接口 demo [`b8c5d5f`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/b8c5d5f62f50ef5fa80b485460064eeaf5335458)
- 删除 [`bed60b8`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/bed60b87d87ef066e8d3617a4aa482c7a206953e)
- 更新 js sdk 的优化 [`f97b7ef`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f97b7ef58e3153b053136bfaf95cb94989a5d05f)
- 补充接口及接口 demo [`593cf3b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/593cf3b5562e4fa3d900101845cbbf678f764d01)
- 更新 demo 示例 [`126ef5b`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/126ef5b9b163b69dc788df422c2e34a2e44bdd5a)
- 更新 js sdk 的优化 [`eed71e4`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/eed71e491b5d90f8356264ecb5ccff8b10d83b3f)
- 自动获取 FileSize [`180b7da`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/180b7dacb062cda87e25d75c038379a94d2dd6fc)
- 修改文档 [`47357dc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/47357dc26619698b2c1b1ae38b080ba372b9a833)
- v0.6.0 修复上传中文编码问题 [`776ff38`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/776ff38df8fb28880205a1c79e11cd9034e660e2)
- DOMparser 报错 [`e9953ad`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e9953ad496a66bb6156830284abff921ef3876f6)
- 更新 build.js [`f2b41a5`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f2b41a581a0afacfb612b83e1be6b3fb81430f96)
- 修改成功判断 [`e76d06a`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/e76d06a4037847dac67680695054994eea902d0d)
- 删除无用依赖 [`6c52bab`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6c52bab719773eee3a288cc15e7c149fefa7759c)
- 更新 README [`d2ad1fc`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/d2ad1fc4e1afc25748a1653c3a54626b72f9a6ad)
- v0.6.0 修复上传中文编码问题 [`9e680c7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/9e680c735e69068e0fe59bd23cf8ec3889f90254)

## [v0.5.0](https://github.com/tencentyun/cos-wx-sdk-v5/compare/v0.4.0...v0.5.0) - 2018-04-26

### Commits

- 修复 content-type 出错问题 [`a6387a4`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/a6387a429bc94ed150c9ae09b4b9dd9c890e4c78)
- 增加签名例子 [`0fdaf0c`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/0fdaf0cd8a4d95b5c471f6cef9ae6375f46f5728)
- 增加简单上传例子 [`cd42106`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/cd42106ed7193a9895546afca3ae9b8f0e48419e)
- header 空容错 [`6e99b1c`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/6e99b1c6ca2a01653fb2ff6be5092885bccee350)
- 增加文件路径 [`69e3589`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/69e3589bdfa7a27f987a074973e7c917fe37182c)
- Update README.md [`bf446ae`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/bf446ae5e4e1958bc9c1fe670e65f597ee4b5c71)
- Update README.md [`dd53c88`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/dd53c8858005091789ab50e2e5f599c38a2fd7ee)
- Update README.md [`3778977`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3778977ebddcf52968b3b762eb2b7ce8a1e68967)
- cors [`f565788`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/f56578821d5229223a5ac6aac463c9808af9ca88)
- cors [`97b4920`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/97b49202392a778258cc125e60efb3e6a4139043)

## v0.4.0 - 2018-01-12

### Commits

- init [`294a9a9`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/294a9a9298d330e9f56da5480643a2137703007d)
- 去掉 AppId [`ea610e6`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/ea610e698673cfb0a8d281e0d789bfee441fa641)
- 支持取消上传任务 [`4802d2c`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/4802d2cbb8566f26728815d3c948e043ce1d9626)
- 支持取消上传任务 [`244cb83`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/244cb8307c654dd1790703f01818d614d3fc18b8)
- 支持 putObject、putBucket [`3f35f23`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/3f35f2354f0c18a987f48435439d2f3e1f78eeda)
- 完善介绍文档 [`5a8de27`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/5a8de276825a33e083d07626811928df9288288c)
- 修复 File 和 Blob 导致的报错 [`4797452`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/47974529c52ce98739701b95cef7445cb1cbb4d9)
- 修复 query 参数漏传 [`49edfd7`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/49edfd7a73f49d52323ee51d7fbd9ca9222df154)
- 完善介绍文档 [`5c1be86`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/5c1be86660f21c22dddd31e5139529411bd01fb8)
- 压缩代码 [`c134dc4`](https://github.com/tencentyun/cos-wx-sdk-v5/commit/c134dc461797b29149726d3e5c16bcc5c3208f37)
