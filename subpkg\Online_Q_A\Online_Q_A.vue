<template>
	<view class="container u-border-top">
		<view class="chat-list">
			<view class="notice">
				<image src="https://website-1300870289.cos.ap-nanjing.myqcloud.com/9605fbe87af37891600bb030be77d35ff0f.png">
				</image>

				<u--text text="老师在休息时间、上课期间无法及时回复，请耐心等待！" size='24'></u--text>
			</view>
			<view class="tablist">
				<u-tabs class="right" :list="list" lineWidth="50" lineHeight="8" lineColor="#009c7b" :activeStyle="{
					color: '#009c7b',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" @change="tabchange"></u-tabs>
				<view class="right">

				</view>
			</view>


			<view class="list">
				<view class="item-container" v-for="item in filterChatList" :key="item.id" v-show="filterChatList.length > 0">
					<view class="item">
						<view class="left-img">

							<image :src="item.image" mode="widthFix"></image>
							<view class="bottom-tip">
								{{ item.subject }}
							</view>

						</view>
						<view class="teacher-info">
							<view class="name">
								{{ item.chat_name }}
							</view>
							<view class="school">
								{{ item.from_school ? item.from_school : "" }}
							</view>
							<view class="line">
								<view style="width: 10rpx; height: 4rpx;border-radius: 1rpx; float: right; background-color: #5A5A5A;">

								</view>
							</view>

							<view class="introduction">
								<u-parse :content="item.content"></u-parse>
							</view>
						</view>
					</view>
					<view class="start-chat" @click="tochat(item.chat_link)">
						开始提问
					</view>
				</view>
				<view class="empty-info">
					<u-empty v-if="loading == false && filterChatList.length == 0" mode="list" text="需购买相关课程" textSize="30">
					</u-empty>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
import {
	getBuyedCourseCategory,
	addChatNum
} from "@/api/online_chat/index.js"
export default {
	data() {
		return {
			loading: true,
			chatList: [

			],
			filterChatList: [],
			cor_id: '',
			list: [{
				id: '1',
				name: '政治',
			}, {
				id: '2',
				name: '英语',
			}, {
				id: '3',
				name: '数学'
			},],
			subject: {
				"1": "数学", "2": "英语", "3": "政治", "4": "专业课"
			}
		};
	},
	created() {
		this.getChatList()
	},
	methods: {
		async getChatList() {
			this.loading = true
			let list = await getBuyedCourseCategory()
			this.loading = false
			if (list != false) {
				this.chatList = list.chatList.map(item => {
					return {
						...item,
						content: item.content.slice(0, 100) + '...',
						subject: this.subject[item.subject]
					}
				})
				this.filterChatList = this.chatList.filter(item => item.subject == this.list[0].name)
				this.cor_id = list.cor_id
			}
		},
		tochat(link) {
			let wxService = null;
			plus.share.getServices(res => {
				// 查找微信服务
				wxService = res.find(i => i.id === 'weixin');
				if (wxService) {
					wxService.openCustomerServiceChat({
						corpid: this.cor_id, // 企业 ID
						url: link // 客服链接
					});
				} else {
					uni.showToast({ title: '当前环境不支持微信客服', icon: 'none' });
				}
			}, err => {
				uni.showToast({ title: '服务获取失败: ' + JSON.stringify(err), icon: 'none' });
			});
		},
		tabchange(obj) {
			this.filterChatList = this.chatList.filter(item => item.subject == obj.name)
		}
	}
}
</script>

<style lang="less">
.container {
	min-height: 100vh;
	padding-top: 20rpx;

	.empty-info {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
	}

	.chat-list {
		padding: 0 30rpx;

		.notice {
			image {
				height: 40rpx;
				width: 40rpx;
			}

			padding-left: 20rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			height: 76rpx;
			background: #F6F7FB;
			border-radius: 20rpx;
		}

		.tablist {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.right {
				flex: 2;
			}

			.left {
				flex: 1;
			}
		}

		.list {
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			margin-left: -10rpx;

			.item-container {
				margin-top: 20rpx;
				margin-left: 10rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
			}

			.item {
				width: 338rpx;
				height: 214rpx;
				background: #FFFFFF;
				border: 1rpx solid #1BB394;
				border-radius: 20rpx;
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				position: relative;

				.left-img {
					height: 100%;
					width: 40%;
					position: relative;

					image {
						width: 100%;
						height: 100%;
					}

					.bottom-tip {
						width: 100%;
						height: 32rpx;
						background-image: url(https://website-1300870289.cos.ap-nanjing.myqcloud.com/56031adfa3cb66a4e0db0479e4e2d20c78b.png);
						background-size: 100% 100%;
						position: absolute;
						bottom: 0;
						text-align: center;
						line-height: 32rpx;
						font-size: 18rpx;
						color: #fff;
					}
				}

				.teacher-info {
					overflow: hidden;
					width: 60%;
					font-size: 24rpx;
					padding: 0 10rpx;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.name {
						width: 100%;
						font-weight: bold;
						height: 34rpx;
						line-height: 34rpx;
						font-size: 24rpx;
						color: #5A5A5A;
						text-align: left;
					}

					.school {
						width: 100%;
						font-weight: 400;
						font-size: 24rpx;
						color: #5A5A5A;
						text-align: left;
					}

					.line {
						margin-top: 10rpx;
						margin-bottom: 16rpx;
						background-color: #5A5A5A;

						width: 100%;
						background-size: 100% 100%;
						height: 2rpx;
						background-repeat: no-repeat;
					}

					.sep {
						width: 100%;
						padding: 0 10rpx;
						margin: 4rpx 0;
					}

					.introduction {
						height: 120rpx;
						font-size: 20rpx;
					}
				}
			}

			.start-chat {
				margin-top: 20rpx;
				background: #16AC8E;
				width: 216rpx;
				height: 44rpx;
				line-height: 44rpx;
				text-align: center;
				font-weight: bold;
				font-size: 26rpx;
				border-radius: 10rpx;
				color: #fff;
			}
		}
	}
}
</style>