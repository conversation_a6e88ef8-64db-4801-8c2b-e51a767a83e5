<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'  @leftClick="goBack">
		     <view class="u-nav-slot" slot="left">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
					<text>耗时{{useTime}}</text>
			</view>
		</u-navbar>
		
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>{{currentChoose+1}}</text>
					<text>/{{sCount+3}}</text>
				</view>
				<view class="opt">
					<view>
						<u-icon name="close-circle" size="38"></u-icon>
						<text>反馈</text>
					</view>
					<view>
						<u-icon name="star"  size="38"></u-icon>
						<text>收藏</text>
					</view>
					<view>
						<u-icon name="order"  size="38"></u-icon>
						<text>答题卡</text>
					</view>
				</view>
			</view>
			
			<view class="q-type">
				阅读理解
			</view>
			
			<view class="question">
				<view class="question-content"> 
					<scroll-view class="question-content" scroll-y="true">
					
						<rich-text :nodes="wordData.title"></rich-text>
						<!-- <u-parse  :content="questionContent"></u-parse> -->
					</scroll-view>
								
				</view>
			</view>
		</view>
		 <cover>
		      <scroll-view class="drawer-container" scroll-y="true">
				  <view class="title-container">
				  	<view class="title-list" :style="{height:isShowAll? Math.ceil((sCount+3)/4)*80+'rpx' :'80rpx'}">
				  		<text @click="currentChoose=i" :class="[{sel:currentChoose==i}]"  class="title" v-for="i in (sCount+3)"> 第{{i+1}}题</text>
				  	</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up"  @click="isShowAll=true" size="44"></u-icon>
					</view>
				  </view>
				  <template v-for="(qitem,index) in  qData">
					<view  v-if="currentChoose == index">
						<view class="q-container">
							<view class="q-title q-text" v-if="currentChoose == index">
								{{qitem.num}}.{{qitem.title}}
							</view>
							<view class="q-sel-list">
								<view class="q-item" v-for="item in qitem.seloption" :key="item.optionName">
									<view class="q-check-normal" :class='{"q-check-sel": currentSelArr[index] == item.optionName}'>
										{{item.optionName}}
									</view>
									<text class="q-text">{{item.text}}</text>
								</view>
							</view>
						</view>
					  <!-- 标题 -->
					  <view class="q-answer">
						<view class="title">
							<user-title :title='"参考答案"' :bgcolor="'#fff'"></user-title>
						</view>
						
						<view class="q-answer-info">
							<text>正确答案:</text>
							<text>{{qitem.answer}}</text>
							<text>我的答案:</text>
							<text>{{myAnswer.objective[index]}}</text>
						</view>
						
					  </view>
					  
					  
					  <!-- 解析 -->
					  <view class="analysis">
						<view class="title">
							<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
						</view>
						<view class="answer-info">
							<rich-text :nodes="qitem.analysis"></rich-text>
						</view>
					  </view>
				  </view>
				  </template>
				  
				  <view class="answer" v-if="(sCount) == currentChoose ">
				    	<view class="title">
				    		单词学习
				    	</view>
				    	<view class="a-content">
				    		<u--textarea disabled height="290rpx" border="none" v-model="myAnswer.answerWord"   maxlength="-1"></u--textarea>
				    	</view>
				    	
				    	<view class="img-list" v-if="myAnswer.imageWord != ''">
				    		<image style="width: 95%;"  @click="previewImage(1)" :src="myAnswer.imageWord" mode="widthFix"></image>
				    		
				    	</view>
						<view class="analysis">
							<view class="title">
								<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
							</view>
							<view class="answer-info">
								<rich-text :nodes="wordData.word_analysis"></rich-text>
							</view>
						</view>
				    	
				  </view>
				  <view class="answer" v-if="(sCount+1) == currentChoose ">
				    	<view class="title">
				    		句子结构分析
				    	</view>
				    	<view class="a-content">
				    		<u--textarea  disabled height="290rpx" border="none" v-model="myAnswer.answerSemt"   maxlength="-1"></u--textarea>
				    	</view>
				    				    	
				  		<view class="img-list" v-if="myAnswer.answerSemt != ''">
				  			<image style="width: 95%;"  @click="previewImage(3)" :src="myAnswer.imageWord" mode="aspectFill"></image>
				  			
				  		</view>
						<view class="analysis">
							<view class="title">
								<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
							</view>
							<view class="answer-info">
								<rich-text :nodes="wordData.semt_analysis"></rich-text>
							</view>
						</view>
						
				    	
				  </view>
				  <view class="answer" v-if="(sCount+2) == currentChoose ">
				    	<view class="title">
				    		翻译
				    	</view>
				    	<view class="a-content">
				    		<u--textarea disabled  height="290rpx" border="none" v-model="myAnswer.answerTran"  maxlength="-1"></u--textarea>
				    	</view>
				    	
				    	<view class="img-list" v-if="myAnswer.imageTran != ''">
				    		<image @click="previewImage(2)" style="width: 95%" :src="myAnswer.imageTran" mode="aspectFill" ></image>
				    		
				    	</view>
						<view class="analysis">
							<view class="title">
								<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
							</view>
							<view class="answer-info">
								<rich-text :nodes="wordData.tran_analysis"></rich-text>
							</view>
						</view>
				    	
				  </view>
				  <!-- 知识点 -->
				  <view class="knowledge-points">
				  	<view class="title">
				  		<user-title :title='"知识点"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	
				  	<view class="point" v-if="knowLedList.length  > 0">
				  		<view class="point-tag" v-for="(witem,windex) in wordData.knowLedgeInfo">
				  			{{witem.name}}
				  		</view>
				  	</view>
				  </view>
				  
				  <!-- 补漏 -->
				  <view class="traps">
				  	<view class="title">
				  		<user-title :title='"精准补漏"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	<enter-course :list="trapList"></enter-course>
				  </view>
				  
				  <view class="knowledge-origin" >
				  	<view class="title" v-if="wordData.sourse && wordData.sourse!= undefined">
				  		<user-title :title='"来源"' :bgcolor="'#fff'"></user-title>
				  	</view>
				  	<view class="knowledge-origin-title" v-if="wordData.sourse && wordData.sourse!= undefined">
				  		{{wordData.sourse}}
				  	</view>
				  	
					<view class="bottom-btn">
						<view class="prev" @click="next(1)">
							上一题
						</view>
						<view v-if="currentChoose != sCount+2" class="next" @click="next(2)">
							{{nextText}}
						</view>
						<view v-if="currentChoose == sCount+2" class="next" @click="next(3)">
							{{nextText}}
						</view>
					</view>
				  </view>
			  </scroll-view>
		</cover>
	</view>
</template>

<script>
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	import userTitle from "../../components/user_title/user_title.vue"
	import questionBoard from "../../components/question_board/question_board.vue"
	import enterCourse from "../../components/enter_course/enter_course.vue"
	export default {
		data() {
			return {
				nextText :'下一题',
				isShowAll:false,
				currentSel:"",
				currentChoose:0,
				currentSelArr:{},
				taskId:'',
				wordData:'',	
				sCount:0,
				myAnswer:'',
				trapList:[],
				qData:[],
				answerInfo:'',
				useTime:'',
				knowLedList:[],
							
			}
		},
		onLoad(options){
			this.taskId = options.task_id
			this.getWordDetail();
		
		},
		methods: {
			previewImage(type) {
				
				let that = this;
			      // 调用uni.previewImage API进行图片预览
				let tmpArr  =  [];
				if(type == 1) {
					tmpArr.push(this.myAnswer.imageWord)
				} else if(type == 2)  {
					tmpArr.push(this.myAnswer.imageTran)
				} else  {
					tmpArr.push(this.myAnswer.answerSemt)
				}
				uni.previewImage({
					current: 0, // 当前显示图片索引
					urls:tmpArr // 需要预览的图片 URL 列表
				});
			},
			next(type){
				if(type == 1) {
					this.currentChoose> 0?(this.currentChoose--):this.currentChoose
					if(this.sCount+2 != this.currentChoose) {
						this.nextText = '下一题'
					}
				} else if (type == 3) {
					uni.switchTab({
						url:'/pages/task/task'
					})
				}else {
			
					(this.currentChoose == this.sCount+2)?this.currentChoose:this.currentChoose++
					if(this.sCount+2 == this.currentChoose) {
						this.nextText = '返回列表'
					}
				}
				
			},
		
			goBack(){
				uni.navigateBack({
					delta:1
				})
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getReadDetail',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data;
					that.sCount = that.wordData.question_arr.length;
					that.knowLedList  =  that.wordData.knowLedgeInfo  
					let  tmpKnow =  that.wordData.knowLedgeInfo;
					if(tmpKnow.length  > 0) {
						that.wordData.knowLedgeInfo.map((item) =>  {
							if(item.attachment != null  && item.attachment.before_url != null) {
								that.trapList.push({id:item.attachment.id,fileName:item.attachment.filename,remote_url:item.attachment.before_url})
							}
						})
					}
					that.questionArr  =  that.wordData.question_arr
					
					that.questionArr.map((item) => {
						that.qData.push({
							num:item.num,
							title:item.question,
							seloption:[
								{
									optionName:"A",
									text:item.qa
								},{
									optionName:"B",
									text:item.qb
								},{
									optionName:"C",
									text:item.qc
								},{
									optionName:"D",
									text:item.qd
								},
							],
							answer:item.answer,
							analysis:item.analysis,
						})
					})
					that.myAnswer = res.data.my_answer
					that.currentSelArr = that.myAnswer.objective
					let tmp_min = parseInt(that.myAnswer.useTime/60);
					let tmp_second = that.myAnswer.useTime%60;
					that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
			
				})
			},
		},
		components:{
		    cover,
			userTitle,
			questionBoard,
			enterCourse
		  }
	}
</script>

<style lang="scss" scoped>
.container{
	box-sizing: b;
	background-color: $container-bg-color;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				//min-height: 100vh;
				max-height: 30vh;
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
				
				.num {
					display: inline-block;
					height: 34rpx;
					width: 34rpx;
					border-radius: 50%;
					line-height: 34rpx;
					text-align: center;
				}
			}
		}
	
	}
	.drawer-container{
		height: 100%;
		background-color:rgb(247, 247, 247);
	}
	.title-container{
		display: flex;
		align-items: flex-start;
		justify-content: center;
		background-color: rgb(247, 247, 247);
		.title-list{
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			transition: height 0.3s ease;
			overflow: hidden;
			background-color: rgb(247, 247, 247);
			.title{
				width: 100rpx;
				color: #777777;
				font-size: 28rpx;
				padding: 15rpx 30rpx;
			}
		}
		.right-icon{
			width: 140rpx;
			padding-top: 15rpx;
			padding-right: 30rpx;
			::v-deep .uicon-arrow-up{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
			::v-deep .uicon-arrow-down{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
		}
		.done{
			color: $main-color !important;
		}
		.sel{
			color: #0A0A0A;
			font-weight: bold;
		}
	}

	.q-container{
		padding: 0 36rpx;
		background-color: #fff;
		margin-top: 10rpx;
		
		
	}
	.answer{
		margin-top: 32rpx;
		
		margin-bottom: 36rpx;
		.title{
			padding: 0 36rpx;
			font-size: 24rpx;
			color: #777777;
			font-weight: bold;
		}
		.img-list{			
			display: flex;
			justify-content: space-around;
		}
		.a-content{
			width: 95%;
			border: 1rpx solid #707070;
			margin: 24rpx auto 36rpx;
			border-radius: 22rpx;
			padding: 10rpx;
		}
		
	}
	.q-text{
		
		line-height: 50rpx;
		color: #5A5A5A;
		font-size: 28rpx;
	}
	.q-title{
		margin-top: 36rpx;
		margin-bottom: 50rpx;
	}
	.q-sel-list{
		padding-bottom: 12rpx;
		.q-item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;
			text{
				width: 590rpx;
			}
			.q-check-normal{
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}
			.q-check-sel{			
				background: #01997A;
				border: 0;
				color: #fff;
				
			}
		}
	}
		
	.q-answer,.analysis,.knowledge-points, .traps, .knowledge-origin{
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 30rpx;
	}
	.q-answer-info{
		margin-bottom: 30rpx;
		text{
			font-size: 28rpx;
			color: #5A5A5A;
			font-weight: bold;
			letter-spacing: 4rpx;
			&:nth-child(2){
				margin-left: 10rpx;
				color: $main-color;
			}
			&:last-child{
				margin-left: 10rpx;
				color: rgb(206, 97, 117);
			}
		}
	}
	
	.analysis{
		.answer-info{
			color: #5A5A5A;
			font-size: 28rpx;
		}
	}
	.knowledge-points{
		.point{
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			.point-tag{
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				 font-weight: bold;
			}
		}
	}
	.knowledge-origin{
		.knowledge-origin-title{
			color: #5A5A5A;
			font-size: 28rpx;
		}
		}
	.bottom-btn{
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		margin-bottom: 100rpx;
		view{
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}
		.prev{
			background: #FFFFFF;
			color: $main-color;
		}
		.next{
			background-color: $main-color;
			color: #fff;
		}
	}
	
}
</style>
