<template>
	<view class="container">
		<view class="head">
			<view class="left-sel-seg">
				<uni-segmented-control :values="list" @clickItem="chooseItem" activeColor="#06A177" styleType="text"></uni-segmented-control>
			</view>
			<view class="right">
				<view class="calendar-icon" @click="show = !show" v-show="index == 1">
					<u-icon class="calendar-icon" name="calendar" color="#777" size="80"></u-icon>
				</view>
			</view>
		</view>
		<view class="content" v-show="index == 0">
			<swiper class="swiper" :current="currentMonthIndex" @change="onSwiperChange" circular="true">
				<swiper-item v-for="(calendarItem, index) in calendarItems" :key="index">
					<uni-calendar :show-red-circle="false" :selected="selectedList" :start-date="startTime" :showHeader='false' :selected-wrap='true' :insert="true" :date="calendarItem" @change="onDateChange" />
				</swiper-item>
			</swiper>
			<view class="course-list u-border-top">
				<view class="course-info" v-for="(item, index) in scheduleList" :key="item.id">
					<view class="color-block" :style="{ backgroundColor: index <= colorBlock.length ? colorBlock[index] : colorBlock[index / colorBlock.length] }">

					</view>
					<view class="left">
						<view class="start-time">
							{{ item.begin_time }}
						</view>
						<view class="line">

						</view>
						<view class="end-time">
							{{ item.end_time }}
						</view>
					</view>
					<view class="right">
						<view class="right-top">
							<text>课程： {{ item.course_title }} </text>
						</view>
						<view class="right-bottom">
							<view class="bottom-left">
								<text>主讲老师：{{ item.nickname }}</text>
							</view>
							<view class="bottom-right">
								<text>教室：{{ item.class_room }}</text>
							</view>
						</view>
					</view>
				</view>

				<u-empty mode="list" text="暂无课表" v-if="scheduleList.length == 0 && loaded == true" textSize="20">
				</u-empty>
			</view>
		</view>
		<view class="week-content" v-show="index == 1">
			<view class="week">
				<view class="month-info">
					{{ currentMonth }}
				</view>
				<view class="day-info" v-for="(item, index) in weekInfo" :key="index">
					<text>{{ item.week }}</text>
					<text>{{ item.date }}</text>
				</view>
			</view>

			<view class="week-course-info" v-for="i in 3" :key="i">


				<view class="course-info-title">
					<text style="margin-bottom: 6rpx;">{{ dayPeriod[i - 1].name }}</text>
					<text>{{ dayPeriod[i - 1].start }}</text>
					<text style="margin: 6rpx 0;">-</text>
					<text>{{ dayPeriod[i - 1].end }}</text>
				</view>

				<view v-for="(item, index) in weekInfo" :key="index" class="course-info-content" :style="{ backgroundColor: weekColors[index] }">
					<text v-if="weekCourseObj[weekInfo[index].fulldate] && weekCourseObj[weekInfo[index].fulldate][i - 1] && weekCourseObj[weekInfo[index].fulldate][i - 1]['courseInfo']">
						{{ weekCourseObj[weekInfo[index].fulldate][i - 1]['courseInfo'] }}
					</text>

				</view>

			</view>
		</view>
		<u-picker :show="show" :defaultIndex="[0]" ref="uPicker" @cancel="show = false" :columns="monthAndWeekColumns" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
import moment from "../module/moment/moment"
import {
	getCourseScheduleByDayApi,
	getScheduleDayByMonth
} from "@/api/camp/index.js"

import { getMonthWeeks } from "../utils/index.js"
export default {
	data() {
		return {
			monthAndWeekColumns: [],
			loaded: false,
			scheduleList: [],
			currentMonthIndex: 0,
			calendarItems: [],
			list: ["月度选择", "周度选择"],
			index: 0,//顶部 tab 索引
			startTime: "",
			currentSelDate: moment().format('YYYY-MM-DD'),
			colorBlock: [
				'#98EAF3',
				'#FF954E',
				'#FFB74D',
			],
			weekInfo: [

			],
			show: false,
			currentMonth: "",
			selectedList: [],

			searchPeriod: {},
			//每月的具体周数据
			monthWeeks: [],
			weekCourseObj: {},
			weekColors: [
				"#FF9DD8",
				"#AA90FC",
				"#59F0A3",
				"#FF9DD8",
				"#86AFFE",
				"#FFA17D",
				"#F9DE5B"
			],
			dayPeriod: [
				{ name: "上午", start: "09", end: "12" },
				{ name: "下午", start: "14", end: "17" },
				{ name: "晚上", start: "19", end: "22" },
			]
		}

	},

	onLoad() {
		const currentDate = new Date()
		const date = moment(currentDate).subtract(1, 'month')
		const length = 6
		this.calendarItems = Array.from({
			length
		}, (_, i) => {
			return moment(date).add(i, "month").format("YYYY-MM-DD")
		})
		this.startTime = this.calendarItems[0]
		this.currentMonthIndex = this.calendarItems.indexOf(moment().format('YYYY-MM-DD'))
		this.getScheduleList()
		//当前月份以及当前周数
		this.getWeekDays(new Date())
		this.currentMonth = moment().format("M") + "月"

		//初始化 周数据
		this.monthWeeks = getMonthWeeks()
		this.monthAndWeekColumns = [this.monthWeeks.map(item => item.weekNumber)]
		this.getScheduleDayByPeriod()
	},
	methods: {
		generateMonths() {

		},
		// 监听 swiper 滑动
		onSwiperChange(e) {
			//获取年月
			this.currentMonthIndex = e.detail.current;
		},
		confirm(e) {
			const value = e.value[0]
			const date = this.monthWeeks.find(item => item.weekNumber == value)

			this.getWeekDays(date.startDate)
			this.getScheduleDayByPeriod()
			this.show = false
		},

		onDateChange(e) {
			this.currentSelDate = e.fulldate
			this.getScheduleList()
		},
		openCalendar() {

		},
		//tabs更改
		chooseItem(e) {
			if (this.index != e.currentIndex) {
				this.index = e.currentIndex
			}
			// if (this.index == 1) {
			// 	// this.monthAndWeekColumns = [
			// 	// 	["9月", "10月"],
			// 	// 	["第一周", "第二周", "第三周", "第四周"]
			// 	// ]

			// }
		},

		getWeekDays(date) {
			// 获取当前日期
			const today = moment(date);

			// 获取本周的周一和周日
			const startOfWeek = today.clone().startOf('isoWeek'); // 周一
			const endOfWeek = today.clone().endOf('isoWeek'); // 周日

			// 周几的中文表示
			const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

			// 创建一个数组，包含从周一到周日的对象 { week: "周几", date: "几号" }
			const weekDates = [];
			let day = startOfWeek;
			let index = 0;

			while (day <= endOfWeek) {
				weekDates.push({
					week: weekDays[index], // 获取周几的中文
					month: day.format('MM'), // 获取日期中的 月份
					day: day.format('DD'), // 获取日期中的 "几号"
					date: day.format('MM/DD'), // 获取日期 月和日
					fulldate: day.format('YYYY-MM-DD') // 获取日期的 "YYYY-MM-DD" 格式
				});
				day = day.clone().add(1, 'day');
				index++;
			}

			this.weekInfo = weekDates
		},
		async getScheduleList() {
			const params = {
				day: this.currentSelDate
			}
			this.loaded = false
			const data = await getCourseScheduleByDayApi(params)
			console.log("data", data)
			this.loaded = true
			if (data != false) {
				this.scheduleList = data.list
			} else {
				this.scheduleList = []
			}
		},
		async getScheduleDayByPeriod() {
			const params = {
				startDate: this.weekInfo[0].fulldate,
				endDate: this.weekInfo[this.weekInfo.length - 1].fulldate,
				forWeek: 1//以周为单位
			}
			const data = await getScheduleDayByMonth(params, true)
			if (data != false) {
				this.weekCourseObj = data.list
			}
		}
	},
	watch: {

		//月份滑动 获取月份课表
		currentMonthIndex: {
			handler(index) {
				const params = {
					startDate: moment(this.calendarItems[index]).format("YYYY-MM"),
					endDate: moment(this.calendarItems[index + 1]).format("YYYY-MM"),
				}
				getScheduleDayByMonth(params).then(data => {
					this.selectedList = []
					if (!Array.isArray(data.list)) {
						return
					}
					data.list.forEach(item => {
						if (!this.selectedList.includes(item['date'])) {
							this.selectedList.push({
								date: item['date'],
								info: '有课'
							})
						}
					})
				})
			},
			immediate: true,
			deep: true
		},

	}
}
</script>

<style lang="scss" scoped>
::v-deep .uni-calendar-item--isDay {
	background-color: #01997A !important;
	border-radius: 50rpx !important;
}

::v-deep .uni-calendar-item--checked {
	background-color: #01997A !important;
	border-radius: 50rpx !important;
}

.container {
	padding-bottom: 40rpx;
}

.head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 26rpx;

	.left-sel-seg {
		width: 40%;
	}

	::v-deep .segmented-control__text {
		font-size: 28rpx !important;
	}

	.right {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
}

.swiper {
	height: 600rpx;
}

.content {
	padding: 0 26rpx;
}

.course-list {
	margin-top: 10rpx;

	.course-info {
		margin-top: 32rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 32rpx 1rpx rgba(133, 131, 131, 0.16);
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: flex-start;

		.color-block {
			width: 12rpx;
			height: 206rpx;
			background: #98EAF3;
			border-radius: 10rpx 0rpx 0rpx 10rpx;
		}

		.left {
			width: 180rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			font-size: 40rpx;
			color: #333;

			.line {
				width: 48rpx;
				height: 0rpx;
				border: 1rpx solid #AFAFAF;
				margin: 20rpx 0;
			}
		}

		.right {
			height: 206rpx;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: space-between;
			font-size: 30rpx;
			color: #333;

			.right-top,
			.right-bottom {
				width: 100%;
				flex: 1;
				padding-left: 27rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
			}

			.bottom-right {
				margin-left: 62rpx;
			}
		}
	}
}

.week {
	width: 100%;
	background-color: #F6F7FB;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #4C5370;
	font-size: 28rpx;
	border-bottom: 1rpx solid #A2A2A2;

	.month-info {
		width: 50rpx;
		writing-mode: vertical-rl;
		text-orientation: upright;
	}

	.day-info {
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 100%;
		flex: 1;
	}
}

.week-course-info {
	margin-bottom: 26rpx;
	height: 280rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #FFFFFF;
	font-size: 24rpx;
	color: #FFFFFF;

	.course-info-title {
		height: 100%;
		width: 50rpx;
		color: #333;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
	}

	.course-info-content {
		height: 100%;
		width: 90rpx;
		margin: 0 6rpx;
		border-radius: 6rpx;
		padding: 0 6rpx;
		padding-top: 16rpx;
	}
}
</style>