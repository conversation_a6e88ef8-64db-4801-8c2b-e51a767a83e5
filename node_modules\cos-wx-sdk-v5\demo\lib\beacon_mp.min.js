/*! For license information please see beacon_mp.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.BeaconAction=e():t.BeaconAction=e()}(window,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=6)}([function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BEACON_ATTA_REQUEST_URL=e.BEACON_ATTA_TOKEN=e.BEACON_ATTA_ID=e.BEACON_CONFIG_HTTPS_URL=e.BEACON_CONFIG_REQUEST_TIME=e.BEACON_CONFIG=e.BEACON_SENDING_IDS_KEY=e.BEACON_NORMAL_LOG_ID_KEY=e.BEACON_DRIECT_LOG_ID_KEY=e.BEACON_LASE_REPORT_TIME_KEY=e.BEACON_DEVICE_ID_KEY=e.BEACON_STORE_PREFIX=e.BEACON_LOG_ID_KEY=e.BEACON_IS_REALTIME_KEY=e.BEACON_DELAY_DEFAULT=e.BEACON_HTTPS_URL=e.BEACON_HTTP_URL=void 0,e.BEACON_HTTP_URL="http://oth.eve.mdt.qq.com:8080/analytics/v2_upload",e.BEACON_HTTPS_URL="https://otheve.beacon.qq.com/analytics/v2_upload",e.BEACON_DELAY_DEFAULT=3e3,e.BEACON_IS_REALTIME_KEY="A99",e.BEACON_LOG_ID_KEY="A100",e.BEACON_STORE_PREFIX="__BEACON_",e.BEACON_DEVICE_ID_KEY="__BEACON_deviceId",e.BEACON_LASE_REPORT_TIME_KEY="last_report_time",e.BEACON_DRIECT_LOG_ID_KEY="direct_log_id",e.BEACON_NORMAL_LOG_ID_KEY="normal_log_id",e.BEACON_SENDING_IDS_KEY="sending_event_ids",e.BEACON_CONFIG="beacon_config",e.BEACON_CONFIG_REQUEST_TIME="beacon_config_request_time",e.BEACON_CONFIG_HTTPS_URL="https://oth.str.beacon.qq.com/trpc.beacon.configserver.BeaconConfigService/QueryConfig",e.BEACON_ATTA_ID="00400014144",e.BEACON_ATTA_TOKEN="6478159937",e.BEACON_ATTA_REQUEST_URL="https://h.trace.qq.com/kv"},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getEventId=e.replaceSymbol=e.replace=e.assert=void 0;var r=n(0),o=n(9);function i(t){if("string"!=typeof t)return t;try{return t.replace(new RegExp("\\|","g"),"%7C").replace(new RegExp("\\&","g"),"%26").replace(new RegExp("\\=","g"),"%3D").replace(new RegExp("\\+","g"),"%2B")}catch(t){return""}}Object.defineProperty(e,"EventEmiter",{enumerable:!0,get:function(){return o.EventEmiter}}),e.assert=function(t,e){if(!t)throw e instanceof Error?e:new Error(e)},e.replace=function(t,e){for(var n={},r=0,o=Object.keys(t);r<o.length;r++){var s=o[r],a=t[s];if("string"==typeof a)n[i(s)]=i(a);else{if(e)throw new Error("value mast be string  !!!!");n[i(String(s))]=i(String(a))}}return n},e.replaceSymbol=i,e.getEventId=function(t){return String(t[r.BEACON_IS_REALTIME_KEY])+String(t[r.BEACON_LOG_ID_KEY])}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createPipeline=void 0;var r=function(){};e.createPipeline=function(t){if(!t||!t.reduce||!t.length)throw new TypeError("createPipeline 方法需要传入至少有一个 pipe 的数组");return 1===t.length?function(e,n){t[0](e,n||r)}:t.reduce((function(t,e){return function(n,o){return void 0===o&&(o=r),t(n,(function(t){return null==e?void 0:e(t,o)}))}}))}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o=function(){function t(t,e,n,o){this.requestParams={},this.network=o,this.requestParams.attaid=r.BEACON_ATTA_ID,this.requestParams.token=r.BEACON_ATTA_TOKEN,this.requestParams.product_id=t.appkey,this.requestParams.platform=n,this.requestParams.uin=e.deviceId,this.requestParams.model="",this.requestParams.os=n,this.requestParams.app_version=t.appVersion,this.requestParams.sdk_version=e.sdkVersion,this.requestParams.error_stack=""}return t.prototype.reportError=function(t,e){this.requestParams._dc=Math.random(),this.requestParams.error_msg=e,this.requestParams.error_code=t,this.network.get(r.BEACON_ATTA_REQUEST_URL,{params:this.requestParams})},t}();e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Config=void 0;var r=function(){};e.Config=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o=function(){function t(t,e,n,o){this.strategy={isEventUpOnOff:!0,httpsUploadUrl:r.BEACON_HTTPS_URL,requestInterval:30,blacklist:[],samplelist:[]},this.realSample={},this.appkey="",this.appkey=t.appkey,this.storage=n;try{var i=JSON.parse(this.storage.getItem(r.BEACON_CONFIG));i&&this.processData(i)}catch(t){}this.needRequestConfig()&&this.requestConfig(t.appVersion,e,o)}return t.prototype.requestConfig=function(t,e,n){var o=this;this.storage.setItem(r.BEACON_CONFIG_REQUEST_TIME,Date.now().toString()),n.post(r.BEACON_CONFIG_HTTPS_URL,{platformId:"undefined"==typeof wx?"3":"4",mainAppKey:this.appkey,appVersion:t,sdkVersion:e.sdkVersion,osVersion:e.userAgent,model:"",packageName:"",params:{A3:e.deviceId}}).then((function(t){if(0==t.data.ret)try{var e=JSON.parse(t.data.beaconConfig);e&&(o.processData(e),o.storage.setItem(r.BEACON_CONFIG,t.data.beaconConfig))}catch(t){}else o.processData(null),o.storage.setItem(r.BEACON_CONFIG,"")})).catch((function(t){}))},t.prototype.processData=function(t){var e,n,r,o,i;this.strategy.isEventUpOnOff=null!==(e=null==t?void 0:t.isEventUpOnOff)&&void 0!==e?e:this.strategy.isEventUpOnOff,this.strategy.httpsUploadUrl=null!==(n=null==t?void 0:t.httpsUploadUrl)&&void 0!==n?n:this.strategy.httpsUploadUrl,this.strategy.requestInterval=null!==(r=null==t?void 0:t.requestInterval)&&void 0!==r?r:this.strategy.requestInterval,this.strategy.blacklist=null!==(o=null==t?void 0:t.blacklist)&&void 0!==o?o:this.strategy.blacklist,this.strategy.samplelist=null!==(i=null==t?void 0:t.samplelist)&&void 0!==i?i:this.strategy.samplelist;for(var s=0,a=this.strategy.samplelist;s<a.length;s++){var c=a[s].split(",");2==c.length&&(this.realSample[c[0]]=c[1])}},t.prototype.needRequestConfig=function(){var t=Number(this.storage.getItem(r.BEACON_CONFIG_REQUEST_TIME));return Date.now()-t>60*this.strategy.requestInterval*1e3},t.prototype.getUploadUrl=function(){return this.strategy.httpsUploadUrl+"?appkey="+this.appkey},t.prototype.isBlackEvent=function(t){return-1!=this.strategy.blacklist.indexOf(t)},t.prototype.isEventUpOnOff=function(){return this.strategy.isEventUpOnOff},t.prototype.isSampleEvent=function(t){return!!Object.prototype.hasOwnProperty.call(this.realSample,t)&&this.realSample[t]<Math.floor(Math.random()*Math.floor(1e4))},t}();e.default=o},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.WeappOpts=void 0;var a=s(n(7)),c=s(n(3)),u=n(0),p=n(1),f=n(4),l=s(n(5)),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(f.Config);e.WeappOpts=d;var _=wx||qq,h=function(t){function e(e){var n=t.call(this,e)||this;return n.send=function(t,e,r){if(!n.config.appkey)throw new Error("please call init before");n.network.post(n.strategy.getUploadUrl(),t.data).then((function(r){100==r.data.result?n.delayTime=1e3*r.data.delayTime:n.delayTime=0,e&&e(t.data),n.cleanEvents(t.data.events)})).catch((function(e){var o=t.data.events;n.errorReport.reportError(e.code?e.code.toString():"600",e.message),r&&r(t.data),n.concatEvents(o)}))},n.network=new g,n.storage=new y(e.appkey),n.initCommonInfo(e,n),n.errorReport=new c.default(n.config,n.commonInfo,"weapp",n.network),n.strategy=new l.default(n.config,n.commonInfo,n.storage,n.network),n.storage.setItem(u.BEACON_SENDING_IDS_KEY,JSON.stringify([])),n.onDirectUserAction("rqd_weapp_init",{}),setTimeout((function(){return n.lifeCycle.emit("init")}),0),n.initDelayTime=e.delay?e.delay:2e3,n.cycleSend(n.initDelayTime),n}return o(e,t),e.prototype.initCommonInfo=function(t,e){_.getNetworkType({success:function(t){e.storage.setItem("nt",t.networkType)}}),this.commonInfo={deviceId:this.storage.createDeviceId(),language:"",query:"",userAgent:"",pixel:"",channelID:t.channelID?String(t.channelID):"",openid:t.openid?String(t.openid):"",unid:t.unionid?String(t.unionid):"",sdkVersion:"4.2.6-weapp"},this.config.appVersion=t.versionCode?String(t.versionCode):"",this.config.strictMode=t.strictMode},e.prototype.cycleSend=function(t){var e=this;setTimeout((function(){e.realSend(e.getEvents()),e.cycleSend(0==e.delayTime?e.initDelayTime:e.delayTime)}),t)},e.prototype.onReport=function(t,e,n){var r=[],o="4.2.6-weapp_"+(n?u.BEACON_DRIECT_LOG_ID_KEY:u.BEACON_NORMAL_LOG_ID_KEY),s=Number(this.storage.getItem(o));s=s||1,e=i(i({},e),{A99:n?"Y":"N",A100:String(s),A72:this.commonInfo.sdkVersion}),s++,this.storage.setItem(o,String(s)),r.push({eventCode:t,eventTime:Date.now().toString(),mapValue:p.replace(e,this.config.strictMode)}),n&&0==this.delayTime?this.realSend(r):this.concatEvents(r)},e.prototype.realSend=function(t){var e,n;if(0!=t.length){var r=this.getSystemInfo(),o={appVersion:this.config.appVersion?p.replaceSymbol(this.config.appVersion):"",sdkVersion:"4.2.6-weapp",sdkId:"weapp",mainAppKey:this.config.appkey,platformId:4,common:p.replace(i(i({},this.additionalParams),{env:"undefined"==typeof qq?"wechat":"qq",A2:this.commonInfo.deviceId,A8:String(this.commonInfo.openid),A9:this.getSystemInfo().brand,A10:encodeURIComponent(r.model),A12:r.language,A23:this.commonInfo.channelID,A33:this.getNetworkType(),A50:String(this.commonInfo.unid),A95:r.version,A102:null===(n=null===(e=getCurrentPages())||void 0===e?void 0:e.pop())||void 0===n?void 0:n.route,A114:encodeURIComponent(r.system),A115:encodeURIComponent(r.platform),A116:r.windowWidth+"*"+r.windowHeight+"*"+r.pixelRatio,A117:this.getUserInfo(),A118:this.getLocation()}),!1),events:t};this._normalLogPipeline(o)}},e.prototype.setUserInfo=function(t){this.storage.setItem("ui",JSON.stringify(t.userInfo))},e.prototype.setLocation=function(t){this.storage.setItem("lt",JSON.stringify(t))},e.prototype.getSystemInfo=function(){return _.getSystemInfoSync()},e.prototype.getNetworkType=function(){return this.storage.getItem("nt")},e.prototype.getUserInfo=function(){return this.storage.getItem("ui")},e.prototype.getLocation=function(){return this.storage.getItem("lt")},e.prototype.concatEvents=function(t){var e=this.getLocalEvents();e=e||{};for(var n=0,r=t;n<r.length;n++){var o=r[n],i=p.getEventId(o.mapValue);this.removeSendingId(i),e[i]=o}this.setLocalEvents(e)},e.prototype.getEvents=function(){var t=[],e=[],n=this.getLocalEvents();if(!n)return t;try{e=JSON.parse(this.storage.getItem(u.BEACON_SENDING_IDS_KEY))}catch(t){}for(var r=0,o=Object.getOwnPropertyNames(n);r<o.length;r++){var i=o[r],s=n[i];-1==e.indexOf(i)&&(t.push(s),e.push(i))}return this.storage.setItem(u.BEACON_SENDING_IDS_KEY,JSON.stringify(e)),t},e.prototype.cleanEvents=function(t){for(var e=this.getLocalEvents(),n=0,r=t;n<r.length;n++){var o=r[n],i=p.getEventId(o.mapValue);this.removeSendingId(i),delete e[i]}this.setLocalEvents(e)},e.prototype.setLocalEvents=function(t){try{this.storage.setItem("BEACON_EVENT",JSON.stringify(t))}catch(t){}},e.prototype.getLocalEvents=function(){try{return JSON.parse(this.storage.getItem("BEACON_EVENT"))}catch(t){return{}}},e}(a.default);e.default=h;var g=function(){function t(){}return t.prototype.get=function(t,e){return new Promise((function(n,r){_.request({method:"GET",data:null==e?void 0:e.params,url:t,success:function(t){n(t)},fail:function(t){r(t)}})}))},t.prototype.post=function(t,e,n){var r=this;return new Promise((function(o,i){_.request({method:"POST",url:t,data:e,success:function(t){var e={data:t.data,status:t.statusCode,statusText:"",headers:t.header,config:n,request:r};o(e)},fail:function(t){var e={message:t.errMsg,code:"600",request:r};i(e)}})}))},t}(),y=function(){function t(t){this.appkey=t}return t.prototype.getItem=function(t){try{return _.getStorageSync(this.getStoreKey(t))}catch(t){return""}},t.prototype.removeItem=function(t){try{_.removeStorageSync(this.getStoreKey(t))}catch(t){}},t.prototype.setItem=function(t,e){try{_.setStorageSync(this.getStoreKey(t),e)}catch(t){}},t.prototype.createDeviceId=function(){var t=_.getStorageSync("beacon_u");return t&&""!=t||(t=this.getRandom(36),_.setStorageSync("beacon_u",t)),t},t.prototype.getRandom=function(t){return(1e6*Date.now()+Math.floor(1e6*Math.random())).toString(t)||""},t.prototype.getStoreKey=function(t){return"beaconV2__"+this.appkey+"_"+t},t}()},function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||r(e,t,n)},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var s=i(n(8));e.default=s.default,o(n(10),e),o(n(2),e),o(n(11),e),o(n(1),e)},function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var o=n(1),i=n(2),s=n(1),a=n(0),c=function(){function t(t){var e=this;this.lifeCycle=new o.EventEmiter,this.uploadJobQueue=[],this.additionalParams={},this.delayTime=0,this._normalLogPipeline=i.createPipeline([function(t){e.send({url:e.strategy.getUploadUrl(),data:t,method:"post",contentType:"application/json;charset=UTF-8"},(function(){var n=e.config.onReportSuccess;"function"==typeof n&&n(JSON.stringify(t.events))}),(function(){var n=e.config.onReportFail;"function"==typeof n&&n(JSON.stringify(t.events))}))}]),s.assert(Boolean(t.appkey),"appkey must be initial"),this.config=r({},t)}return t.prototype.onUserAction=function(t,e){this.preReport(t,e,!1)},t.prototype.onDirectUserAction=function(t,e){this.preReport(t,e,!0)},t.prototype.preReport=function(t,e,n){t?this.strategy.isEventUpOnOff()&&(this.strategy.isBlackEvent(t)||this.strategy.isSampleEvent(t)||this.onReport(t,e,n)):this.errorReport.reportError("602"," no eventCode")},t.prototype.addAdditionalParams=function(t){for(var e=0,n=Object.keys(t);e<n.length;e++){var r=n[e];this.additionalParams[r]=t[r]}},t.prototype.setChannelId=function(t){this.commonInfo.channelID=String(t)},t.prototype.setOpenId=function(t){this.commonInfo.openid=String(t)},t.prototype.setUnionid=function(t){this.commonInfo.unid=String(t)},t.prototype.getDeviceId=function(){return this.commonInfo.deviceId},t.prototype.getCommonInfo=function(){return this.commonInfo},t.prototype.removeSendingId=function(t){var e=JSON.parse(this.storage.getItem(a.BEACON_SENDING_IDS_KEY)),n=e.indexOf(t);-1!=n&&(e.splice(n,1),this.storage.setItem(a.BEACON_SENDING_IDS_KEY,JSON.stringify(e)))},t}();e.default=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EventEmiter=void 0;var r=function(){function t(){var t=this;this.emit=function(e,n){if(t){var r,o=t.__EventsList[e];if(null==o?void 0:o.length){o=o.slice();for(var i=0;i<o.length;i++){r=o[i];try{var s=r.callback.apply(t,[n]);if(1===r.type&&t.remove(e,r.callback),!1===s)break}catch(t){throw t}}}return t}},this.__EventsList={}}return t.prototype.indexOf=function(t,e){for(var n=0;n<t.length;n++)if(t[n].callback===e)return n;return-1},t.prototype.on=function(t,e,n){if(void 0===n&&(n=0),this){var r=this.__EventsList[t];if(r||(r=this.__EventsList[t]=[]),-1===this.indexOf(r,e)){var o={name:t,type:n||0,callback:e};return r.push(o),this}return this}},t.prototype.one=function(t,e){this.on(t,e,1)},t.prototype.remove=function(t,e){if(this){var n=this.__EventsList[t];if(!n)return null;if(!e){try{delete this.__EventsList[t]}catch(t){}return null}if(n.length){var r=this.indexOf(n,e);n.splice(r,1)}return this}},t}();e.EventEmiter=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||r(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),o(n(3),e),o(n(4),e),o(n(5),e)}]).default}));