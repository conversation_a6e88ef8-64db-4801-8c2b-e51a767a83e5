<template>
	<view class="container">
		<question-content></question-content>
		<cover>
			<view class="title-container">
				<!-- <view class="title-list" :style="{height:isShowAll? Math.ceil(20/4)*70+'rpx' :'80rpx'}">
				  		<text @click="currentChoose=i" :class="[{done:hasDone(i)},{sel:currentChoose==i}]"  class="title" v-for="i in 20"> 第{{i+1}}题</text>
				  	</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up"  @click="isShowAll=true" size="44"></u-icon>
					</view> -->

				<scroll-view class="title-list" scroll-x>
					<text :id="`title-list-${i + 1}`" @click="currentChoose = i" :class="[{ done: hasDone(i) }, { sel: currentChoose == i }]" class="title" v-for="i in questionList.length"> 第{{ i + 1 }}题</text>

				</scroll-view>
			</view>
			<scroll-view class="drawer-container" scroll-y="true" style="height: 100%; background-color:#fff">
				<view class="question-wrapper">
					<view class="q-container">
						<view class="q-title q-text" v-if="questionTitle">
							<image v-if="questionTitle.includes('http')" :src="questionTitle" style="width: 100%" mode="widthFix"></image>
							<text v-else>{{ questionTitle }}</text>
						</view>
						<view class="q-sel-list">
							<swiper :current="currentChoose" :duration="100" :style="{ height: swiperHeight }" @change="CurrentIndexChange">
								<swiper-item v-for="(item, index) in questionList" :key="index">
									<view class="q-item">
										<view class="q-item-option" v-for="(v, k) in item">
											<view class="q-check-normal" :class='{ "q-check-sel": selected(index, k) }'>
												{{ k }}
											</view>
											<text class="q-option-text">{{ v }}</text>
										</view>
									</view>
								</swiper-item>

							</swiper>

						</view>

					</view>

					<!-- 标题 -->
					<view class="q-answer">
						<view class="title">
							<user-title :title='"参考答案"' :bgcolor="'#fff'"></user-title>
						</view>

						<view class="q-answer-info">
							<text>正确答案:</text>
							<text>{{ currentSysAnswer }}</text>
							<text>我的答案:</text>
							<text>{{ currentUserAnswer }}</text>
						</view>

					</view>


					<!-- 解析 -->
					<view class="analysis">
						<view class="title">
							<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
						</view>
						<view class="answer-info">
							{{ currentAnalysis }}
						</view>
					</view>

					<!-- 知识点 -->
					<!-- 					<view class="knowledge-points">
								<view class="title">
									<user-title :title='"知识点"' :bgcolor="'#fff'"></user-title>
								</view>
								
								<view class="point">
									<view class="point-tag">
										英语主谓宾
									</view>
									<view class="point-tag">
										英语主谓宾
									</view>
								</view>
							</view> -->

					<!-- 补漏 -->
					<!-- 			<view class="traps">
								<view class="title">
									<user-title :title='"精准补漏"' :bgcolor="'#fff'"></user-title>
								</view>
								<enter-course :list="trapList"></enter-course>
							</view> -->

					<view class="knowledge-origin" v-if="currentType.origin">
						<view class="title">
							<user-title :title='"来源"' :bgcolor="'#fff'"></user-title>
						</view>
						<view class="knowledge-origin-title">
							{{ currentType.origin }}
						</view>
					</view>

					<view class="bottom-btn">
						<view class="prev" @click="answerPrev">
							上一题
						</view>
						<view class="next" @click="answerNext">
							下一题
						</view>
					</view>
				</view>
			</scroll-view>
		</cover>
	</view>
</template>

<script>
import questionContent from "../components/content/content.vue"
import cover from "@/components/zhong-cover/zhong-cover.vue"
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
import userTitle from "@/components/user_title/user_title.vue"
import questionBoard from "@/components/question_board/question_board.vue"
import enterCourse from "@/components/enter_course/enter_course.vue"
export default {
	data() {
		return {
			isShowAll: false,
			swiperHeight: 0,
			scrollToTitle: '',
		};
	},
	mixins: [mixins],
	methods: {
		// 轮播组件滑动更改题目
		CurrentIndexChange(e) {
			this.currentChoose = e.detail.current
			this.scrollToTitle = '#title-list-' + this.currentChoose;
		},
		// 设置该题 选项
		setSel(index, selOption) {
			let selData = this.currentSelArr.filter(item => item.index != index)
			selData.push({
				index,
				selOption
			})
			this.currentSelArr = selData
		},
		// 判断是否选中
		selected(index, selOption) {
			if (this.currentSelArr.length > 0) {
				let sel = this.currentSelArr.find(item => item.index == index)
				if (sel) {
					return sel.selOption == selOption
				}
				return false
			}
			return false
		},
		//该题是否已经做过 更改样式
		hasDone(i) {
			if (Array.isArray(this.currentSelArr)) {
				return this.currentSelArr.some(item => item.index == i)
			}
			return false
		},
		next() {
			//保存答题结果
			let answer = {
				answer: this.currentSelArr,
				url: [],
				timeval: this.$refs.cheader.currentTime,
			}

			const data = {
				answer: answer,
				index: this.currentIndex, //保存当前 题库的进度
			}
			//当前结果保存本地
			this.$store.commit("professionalConfig/setAnswerList", data);
			//清空答题记录
			this.whetherToNext()
		}
	},
	mounted() {
		console.log(this.questionList)
	},
	computed: {
		//系统答案
		currentSysAnswer() {
			try {
				const answerArr = JSON.parse(this.currentType.answer)

				if (!Array.isArray(answerArr)) {
					return ""
				}
				const ret = answerArr.find(item => item.num == this.currentChoose + 1)

				if (ret != undefined) {
					return ret.answer
				} else {
					return ""
				}
			} catch (e) {
				console.log(e)
				return ""
			}
		},
		currentAnalysis() {
			try {
				const answerArr = JSON.parse(this.currentType.material_questions)

				if (!Array.isArray(answerArr)) {
					return ""
				}
				const ret = answerArr.find(item => item.num == this.currentChoose + 1)
				if (ret != undefined) {
					return ret.analysis
				} else {
					return ""
				}
			} catch (e) {
				console.log(e)
				return ""
			}
		},
		//用户答案
		currentUserAnswer() {
			if (!Array.isArray(this.currentSelArr)) {
				return ""
			}
			const userAnswer = this.currentSelArr.find(item => item.index == this.currentChoose)
			if (userAnswer != undefined) {
				return userAnswer.selOption
			} else {
				return ""
			}
		},
		questionTitle() {
			if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length > 0) {
				let questions = JSON.parse(this.currentType.material_questions)

				let result = questions.find(item => item.num == this.currentChoose + 1)
				if (result !== undefined) {
					return result.question
				}
			}
			return ""
		},
	},
	watch: {
		currentIndex: {
			handler(newVal) {
				console.log(newVal)
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.q-item').boundingClientRect(data => {
						if (data) {
							this.swiperHeight = data.height + 'px'
						}
					}).exec();
				})
			},
			immediate: true,
			deep: true
		}
	},
	components: {
		star,
		"question-content": questionContent,
		cover,
		userTitle,
		questionBoard,
		enterCourse
	},
}
</script>

<style lang="scss" scoped>
.container {
	padding-bottom: 164rpx;

	.center {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}

	::v-deep .cover {
		padding-bottom: 80rpx;
	}

	.content {
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;

		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;

					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}

		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}

		.question {
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;

			.title {
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}

			.question-content {
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
			}
		}

	}

	.q-option-text {
		font-size: 24rpx;
	}

	.drawer-container {
		height: 100%;
		padding-bottom: 120rpx;
		background-color: rgb(247, 247, 247);

		.question-wrapper {
			padding-bottom: 70rpx;
		}
	}

	.title-container {
		display: flex;
		align-items: flex-start;
		justify-content: center;
		background-color: rgb(247, 247, 247);

		.title-list {
			height: 78rpx;
			white-space: nowrap;
			width: 100%;
			background-color: rgb(247, 247, 247);

			.title {
				display: inline-block;
				width: 110rpx;
				color: #777777;
				font-size: 28rpx;
				padding: 15rpx 30rpx;
				padding-bottom: 15;
			}
		}

		.right-icon {
			width: 140rpx;
			padding-top: 15rpx;
			padding-right: 30rpx;

			::v-deep .uicon-arrow-up {
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}

			::v-deep .uicon-arrow-down {
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
		}

		.done {
			color: $main-color !important;
		}

		.sel {
			color: #0A0A0A;
			font-weight: bold;
		}
	}

	.q-container {
		padding: 0 36rpx;
		background-color: #fff;
		margin-top: 60rpx;


	}

	.q-text {
		line-height: 50rpx;
		color: #5A5A5A;
		font-size: 28rpx;
	}

	.q-title {
		margin-top: 36rpx;
		margin-bottom: 50rpx;
	}

	.q-sel-list {
		padding-bottom: 12rpx;

		.q-item {
			margin-bottom: 40rpx;

			text {
				width: 590rpx;
			}

			.q-item-option {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;
			}

			.q-check-normal {
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}

			.q-check-sel {
				background: #01997A;
				border: 0;
				color: #fff;

			}
		}
	}

	.q-answer,
	.analysis,
	.knowledge-points,
	.traps,
	.knowledge-origin {
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 30rpx;
	}

	.q-answer-info {
		margin-bottom: 30rpx;

		text {
			font-size: 28rpx;
			color: #5A5A5A;
			font-weight: bold;
			letter-spacing: 4rpx;

			&:nth-child(2) {
				margin-left: 10rpx;
				color: $main-color;
			}

			&:last-child {
				margin-left: 10rpx;
				color: rgb(206, 97, 117);
			}
		}
	}

	.analysis {
		.answer-info {
			color: #5A5A5A;
			font-size: 28rpx;
		}
	}

	.knowledge-points {
		.point {
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;

			.point-tag {
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}

	.knowledge-origin {
		.knowledge-origin-title {
			color: #5A5A5A;
			font-size: 28rpx;
		}
	}

	.bottom-btn {
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		margin-bottom: 100rpx;

		view {
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}

		.prev {
			background: #FFFFFF;
			color: $main-color;
		}

		.next {
			background-color: $main-color;
			color: #fff;
		}
	}

}
</style>