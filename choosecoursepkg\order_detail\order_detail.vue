<template>	
	<view>
		<u-navbar  :height="headHeight"   title="订单详情"  @leftClick="navigateBackToHome" >
			<view class="u-nav-slot" slot="left" >
				<u-icon v-if="type==1" name="home" size="44" ></u-icon>
				<u-icon v-else name="arrow-left" size="44" ></u-icon>
			</view>
			
		</u-navbar>
		<view class="container">	
			<!-- 授课信息 -->
			<view class="course-list u-border-bottom" v-for="(item,index) in orderDetail.course" :key="item.id">
				<view class="title">
					{{item.title}}
				</view>
				<view class="mid">
					<view class="tag">
						{{item.cate_text}}
					</view>
					<text class="date" v-if="item.time_type == 1">{{item.start_time_text}}-{{item.end_time_text}} | 共{{item.time_hour}}课时</text>
					<text class="date" v-else>有效期 <text class="time_day">{{item.time_day}}</text>年 | 共{{item.time_hour}}课时</text>		
					
				</view>
			
				<view class="bottom">
					<view class="teacher-list">
						<view class="teacher-info" v-for="(ites,itindex) in  item.teacher_list" :key="itindex">
							<image class="avatar" :src="ites.image_text" />
							<text>{{ites.name}}</text>
						</view>
					</view>
					<view class="course-money">
						￥{{item.price}}
					</view>
				</view>
			</view>
			
			<view class="detail">
				<view class="title u-border-bottom">
					订单信息
				</view>
				<view class="list-info u-border-bottom">
					<view class="info">
						<text>订单编号</text>
						<text>{{orderDetail.order_no}}</text>
					</view>
					<view class="info">
						<text>下单时间</text>
						<text>{{orderDetail.createtime | happenTimeFun}}</text>
					</view>
					<view class="info" v-if="orderDetail.deduction_price >0 ">
						<text>活动优惠</text>
						<text>{{orderDetail.deduction_price}}</text>
					</view>
				
					<view class="info" v-if="orderDetail.coupon_price > 0">
						<text>优惠券优惠
						</text>
						<text>{{orderDetail.coupon_price}}</text>
					</view>
				</view>
				<view class="total">
					实际支付：<text>￥{{orderDetail.price}}</text>
				</view>
			</view>
			<view class="btn-container">
				<view class="btn" @click="toUrl">
					{{type == 1? (orderDetail.is_pay == 1 ?'立即学习' :'立即购买'):'返回'}}
				</view>
			</view>
		</view>
	</view>	
</template>

<script>
	export default {
		data() {
			return {
				orderDetail:{},
				order_id:0,
				type:1,
				safeTopHeight:0,
				// headHeight:0
				
			};
		},
		onLoad(option) {
			this.type = option.type ? option.type :1;
			this.order_id = option.order_id;
			this.getOrderDetail();
			let m = uni.getMenuButtonBoundingClientRect()
			console.log(m);

			this.headHeight  =  parseInt(m.top+m.height+10);	
		},
		filters : {
			happenTimeFun(num){//时间戳数据处理
				let date = new Date(num*1000);
				 //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;//月补0
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;//天补0
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;//小时补0
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;//分钟补0
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;//秒补0
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m+ ':' + s;
			},
		},
		methods:{
			toUrl () {
				if(this.type == 2) {
					uni.navigateBack({delta:1})
				} else {
					uni.navigateTo({
						url: '/choosecoursepkg/courseDetail/courseDetail?goods_id=' + this.orderDetail.goods_id
					})
				}
			},
			navigateBackToHome() {
				// 使用uni的API返回到首页
				if(this.type == 2) {
					uni.navigateBack({delta:1})
				} else {
					uni.switchTab({
						url: '/pages/index/index' // 这里的url需要根据你的项目实际情况进行修改
					});
				}
			},
			async getOrderDetail () {
				let that  = this;
				let ret =  await uni.$u.http.get('/order/orderDetail',{params:{order_id:this.order_id }}).then(res=>{
					that.orderDetail = res.data;
				
			
				})
			}
		},
		mounted() {			
					
			
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background-color: $container-bg-color;
	padding: 0 30rpx;
	min-height: 100vh;
	padding-top: 42rpx;
	margin-bottom: 33rpx;
	margin-top:160rpx;
	.course-list {
		padding: 0 28rpx;
		margin-bottom: 22rpx;
		background-color: #fff;
		border-radius: 20rpx;
		width: 100%;
		.title{
			padding-top: 28rpx;
			padding-bottom: 18rpx;
			font-weight: bold;
			font-size: 26rpx;
			color: #060606;
		}
		.mid {
			margin-top: 6rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
	
			.tag {
				padding: 0 8rpx;
				height: 48rpx;
				background-color: #EEFAF6;
				color: $main-color;
				line-height: 48rpx;
				font-size: 22rpx;
				font-weight: bold;
				border-radius: 10rpx;
			}
	
			.date {
				margin-left: 8rpx;
				font-size: 22rpx;
				color: #A4A4A4;
				.time_day {
					font-weight: 700;
					font-size: 24rpx;
					
				}
			}
		}
	
		.bottom {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
	
			.teacher-list {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding-bottom: 20rpx;
	
				.teacher-info {
					margin-right: 24rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					color: #818181;
	
					.avatar {
						width: 60rpx;
						height: 60rpx;
						border-radius: 100%;
						margin-bottom: 2rpx;
					}
	
				}
			}
	
			.course-money {
				color: #4C5370;
				font-size: 30rpx;
				margin-right: 34rpx;
			}
		}
	
	}
	.detail{
		background-color: #fff;
		border-radius: 20rpx;
		.title{
			padding: 28rpx;
			padding-bottom: 13rpx;
			color: #201E2E;
			font-size: 28rpx;
			font-weight: bold;
		}
		.list-info{
			padding-top: 28rpx;
			.info{
				display: flex;
				align-items: center;
				 justify-content: space-between;
				 color: #A2A2A2;
				 font-size: 24rpx;
				 padding: 0 28rpx;
				 padding-bottom: 18rpx;
				 text{
					 &:last-child{
						 color: #A2A2A2;
					 }
				 }
			}
		}
		.total{
			color:#A4A4A4;
			font-size: 24rpx;
			text-align: right;
			padding-top: 26rpx;
			padding-bottom: 36rpx;
			padding-right: 15rpx;
			text{
				font-size: 32rpx;
				color: #E16965;
			}
		}
	}
	.btn-container{
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 120rpx;
		display: flex;
		align-items: center;
		
		background-color: #fff;
		.btn{
			width: 90%;
			height: 80rpx;
			background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
			border-radius: 40rpx;
			color: #fff;
			font-size: 30rpx;
			line-height: 80rpx;
			text-align: center;
		}
	}
}
</style>
