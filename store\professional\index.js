// let currentTypes = uni.getStorageSync('currentTypes') || "{}";
// let answerList = uni.getStorageSync('answerList') || "[]";
import {tabBarPages} from"@/utils/tool.js"
let professionalConfig = {
	namespaced: true,
	state: {
		config: {},
		currentTypes: [],
		currentIndex: 0,
		//当前答案列表
		currentAnswerList: [],
		//当前的答题状态
		currentAnswerStatus: {
			isSubmit: false,
			submitTime: ""
		},
		//进入题库的页面
		currentStartPage: "",
		isShowTaskAnswer: false,
		prevAllAnswerListCount: 0,
		reportId: null,
	},
	mutations: {
		//设置题目配置
		setConfig(state, payload) {
			state.config = payload
		},
		//设置当前的题目集合
		setCurrentTypes(state, payload) {
			state.currentTypes = Array.isArray(payload) ? payload : []
			//
			//uni.setStorageSync('currentTypes', JSON.stringify(payload))
		},
		//设置正在做的题目
		setCurrentIndex(state, payload) {
			state.currentIndex = payload
			//
			//uni.setStorageSync('currentIndex', payload)
		},
		
		
		 //设置是否进入任务题库答案部分
		    setShowAnswer(state, payload) {
		      state.isShowTaskAnswer = payload;
		    },
		
		    filterCurrentTypes(state) {
		      state.currentAnswerList = state.currentAnswerList.filter(
		        (item) =>
		          item.answer.answer != "" ||
				  (typeof item.answer.url != "undefined" && item.answer?.url !="")
		      );
			   //一题未答直接返回
			   if (state.prevAllAnswerListCount == state.currentAnswerList.length) {
			          return;
			    }
		      const indexArr = state.currentAnswerList.map((item) => item.index);
		      state.currentTypes = state.currentTypes.filter((item, index) =>
		        indexArr.includes(index)
		      );
		
		      //重置答题列表
		      state.currentAnswerList.forEach((item, index) => {
		        item.index = index;
		      });
		    },
		    //设置之前累计答题数量
		    setPrevAllAnswerListCount(state, payload) {
		      state.prevAllAnswerListCount = payload;
		    },
		    //本次提交过滤之前的记录数据
		    filterPreviousRecords(state) {
		      if (state.prevAllAnswerListCount == 0) {
		        return;
		      }
		      state.currentAnswerList = state.currentAnswerList.slice(
		        state.prevAllAnswerListCount
		      );
		      //重置索引
		      state.currentAnswerList.forEach((item, index) => {
		        item.index = index;
		      });
		      state.currentTypes = state.currentTypes.slice(
		        state.prevAllAnswerListCount
		      );
		      state.currentIndex = state.currentTypes.length - 1;
		    },

		
		//更改状态管理里面某道题的数据
		changeCurrentTypesByCurrentIndex(state, payload) {
			if(Object.prototype.toString.call(payload) !== '[object Object]'){
				return
			}
			Object.keys(payload).forEach(item=>{
				state.currentTypes[state.currentIndex].hasOwnProperty(item) && (state.currentTypes[state.currentIndex][item] = payload[item])
				
			})
		},
		//清空答案
		clearAnswerList(state) {
			state.currentAnswerList = []
		},
		//设置答案
		setAnswerList(state, payload) {
			if (Object.prototype.toString.call(payload) === '[object Object]') {
				const hasExisted = state.currentAnswerList.find(item => item.index == payload.index)
				//当前该题答案已经存在
				if (typeof hasExisted !== "undefined") {
					//替换已经存在的答案
					state.currentAnswerList = state.currentAnswerList.map(item => {
						if (item.index == payload.index) {
							return payload
						}
						return item
					})
				} else {
					//不存在 直接添加
					state.currentAnswerList = [...state.currentAnswerList, payload]
				}
			} else if (Array.isArray(payload)) {
				state.currentAnswerList = [...state.currentAnswerList, ...payload];
      }
		},
		//更改答题状态
		changeCurrentAnswerStatus(state, payload) {
			state.currentAnswerStatus = payload
		},
		//设置进入题库的页面
		setCurrentStartPage(state, payload) {
			let pages = getCurrentPages();
			let currentPage = pages[pages.length - 1];
			if(typeof payload !=='undefined'){
				state.currentStartPage = currentPage.route+payload
			}else{
				state.currentStartPage = currentPage.route
			}
		},
		enterCurrentStartPage(state){
			
			const tabBarPages = [
				'pages/index/index',
				"pages/listen/listen",
				"pages/task/task",
				"pages/encourage/encourage",
				"pages/center/center",
			]
			
			if(tabBarPages.includes(state.currentStartPage)){
					uni.switchTab({
						url: '/'+state.currentStartPage
					})
			}else{
				// uni.redirectTo({
				// 	url: '/'+state.currentStartPage
				// })
				
				uni.reLaunch({
					url: '/'+state.currentStartPage
				});
			}
			state.currentStartPage = ""
		},
		/**
		 * 设置报告ID
		 * @param {Object} state
		 * @param {string|number} payload - 报告ID
		 */
		setReportId(state, payload) {
			state.reportId = payload
		},
	},
	actions:{
		async getStuConfig(context, data){
			const ret = await uni.$u.http.get("/Question/getConfig")
			if (ret.code == 1) {
				console.log('获取系统配置', ret.data)
				context.commit('setConfig', ret.data)
			} else {
				//uni.tip(ret.msg)
				return false
			}
		}
	}
}
export default professionalConfig