<template>
	<view class="container">
		<!-- 头部 -->
		<view class="head"
			:style="{backgroundImage:'url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/select_bg.png)','paddingTop':topHeight}">
			<view class="title">
				<u-icon size="40" name="arrow-left" @click="back"></u-icon>
				<text>《院校专业选择分析报告》</text>
			</view>
			<view class="get">免费领取</view>
			<view class="btn">考的好更要报的好</view>
			<image src="/static/img/down.png" mode=""></image>
		</view>
		<!-- 个人专属计划 -->
		<view class="content">
			<view class="play">个人专属计划</view>
			<view class="top">请认真填写以下信息，</view>
			<view class="top">获取更精准的<text> 《院校专业选择分析报告》</text></view>
			<!-- 计划表单 -->
			<view class="formInput">
				<u-form :model="form" ref="uForm">
					<view class="title"><text>*</text> 1. 请您选择您的年级</view>
					<u-form-item prop="uname">
						<u-radio-group v-model="form.uname" size="30rpx" active-color="#01997A">
							<u-radio name='1'>高一</u-radio>
							<u-radio name='2'>高二</u-radio>
							<u-radio name='3'>高三</u-radio>
							<u-radio name='5'>其他</u-radio>
						</u-radio-group>
					</u-form-item>
					<!-- <view class="title"><text>*</text>2. 请选择您的学习经历</view>
					<u-form-item prop="education">
						<u-radio-group v-model="form.education" size="30rpx" active-color="#01997A">

							<u-radio name='1'>同等学历</u-radio>
							<u-radio name='2'>自考本科</u-radio>
							<u-radio name='3'>专升本</u-radio>
							<u-radio name='4'>大学本科</u-radio>

						</u-radio-group>
					</u-form-item> -->
					<view class="title"><text>*</text> 2.科目</view>					
					<u-checkbox-group
						v-model="form.education"
						@change="checkboxChange"
						active-color="#01997A"
					>
						<u-checkbox 
							size="12px"
							labelSize="14px"
							:customStyle="{marginRight: '16px'}"
							v-for="(item, index) in checkboxList6"
							:key="index"
							:label="item.name"
							:name="item.name"
						>
						</u-checkbox>
					</u-checkbox-group>					
					
					<view class="title"> 3. *请输入所在地区及院校</view>
					<view class="uForm">
						<u-form-item prop="academys">
							<u-input border="false" placeholder="输入所在省份" v-model="form.academys" type="select" />
						</u-form-item>						
						<u-form-item class="topForm" >
							<u-input border="false" placeholder="输入学校" v-model="form.specialty" type="select" />
						</u-form-item>
						
					</view>
					<view class="title"> 4. *请输入校测情况</view>
					<view class="uForm">
						<u-form-item prop="academy">
							<u-input border="false" placeholder="请输入校测分数" v-model="form.academy" type="select" />
						</u-form-item>
						
						<u-form-item class="specialtys">
							<u-input border="false" placeholder="请输入校测排名" v-model="form.specialtys" type="select" />
						</u-form-item>
					</view>
					<view class="title"> 5. 是否有联考测试</view>
					<u-form-item prop="lian_academys">
						<u-radio-group 
							v-model="form.is_lian" @change="changeLian"
							placement="column" size="30rpx" active-color="#01997A">
							<u-radio  name="1">是</u-radio>
							<u-radio  name="0" >否</u-radio>
						</u-radio-group>
					</u-form-item>
					<template v-if="form.is_lian == 1">
						<view class="title"> 5.1. 联考测试分数及排名</view>
						<view class="uForm">
							<u-form-item prop="lian_academys">
								<u-input border="false" placeholder="联考测试分数" v-model="form.lian_score" type="select" />
							</u-form-item>
							
							<u-form-item class="topForm">
								<u-input border="false" placeholder="联考测试排名" v-model="form.lian_rank" type="select" />
							</u-form-item>
						</view>
					</template>
					<!-- <view class="title"> 5. 请选择您的英语水平</view>
					<u-radio-group v-model="form.englist" size="30rpx" active-color="#01997A" prop="englist">
						<u-radio name='1'>英语四级</u-radio>
						<u-radio name='2'>英语六级</u-radio>
						<u-radio name='3'>专业四级</u-radio>
						<u-radio name='4'>专业八级</u-radio>


					</u-radio-group> -->

					<view class="title last"><text>*</text> 6. 请输入您的微信号，以便于及时将报告发给您？</view>
					<u-form-item class="last" prop="weixin">
						<u-input border="false" placeholder="输入微信号" v-model="form.weixin" type="select" />
					</u-form-item>
					<button class="login" v-if="!(userDetail.phone != '')" open-type="getPhoneNumber"
						@getphonenumber="decryptPhoneNumber">提交</button>
					<button class="login" v-else @click="submit">提交</button>
					<!-- <u-button shape="square" size="mini" @click="submit">提交</u-button> -->
				</u-form>
			</view>
		</view>

	</view>

</template>

<script>
	import {mapState,mapMutations} from "vuex"
	export default {
		data() {
			return {
				topHeight: 0,
				form: {
					uname: 1,
					education: "",
					academy: "",
					weixin: "",
					subject:'',
					englist: "",
					specialty: "",
					specialtys: "",
					academys: "",
					is_lian:0,
					lian_score:'',
					lian_rank:"",
				},
				checkboxList6: [{
						name: '生物',
						disabled: false
					},
					{
						name: '物理',
						disabled: false
					},
					{
						name: '化学',
						disabled: false
					},
					{
						name: '地理',
						disabled: false
					},
					{
						name: '政治',
						disabled: false
					},
					{
						name: '历史',
						disabled: false
					},
				],
				rules: {
					uname: [{
						required: true,
						message: '请选择您的年级',
						// 可以单个或者同时写两个触发验证方式 
						trigger: 'change',

					}],
					academy: [{
						required: true,
						message: '请输入您的目标院校',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],
					specialty: [{
						required: true,
						message: '请输入您的目标专业',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],
					weixin: [{
						required: true,
						message: '请输入您的微信号',
						// 可以单个或者同时写两个触发验证方式 
						trigger: ['change', 'blur'],
					}],

				},
				userDetail:{}
			}
		},
		// 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onLoad() {
			this.getUserInfo();
		},
		watch: {
			userInfo: {
				handler: function(newV, oldV) {
					this.getUserInfo()
				},
				deep: true
			}
		},
		computed:{
			...mapState('user',['userInfo']),
		},
		mounted() {
			
			let m = uni.getMenuButtonBoundingClientRect()
			this.topHeight = m.height + 'px'

		},
		methods: {
			changeLian(e) {
				console.log(e)
			},
			...mapMutations('user',['userInfo']),
			async decryptPhoneNumber(e) {
				//await getUserProfile()
				console.log(e);
				
				if (e.detail.errMsg.indexOf('ok') != -1) {
					const {
						code,
						data
					} = await uni.$u.http.post("/mini_user/getMobile", {
						code: e.detail.code
					})
					if (code == 1) {
						this.$store.commit('user/setUser', {phone:data.phone})	
						this.submit()
					}
				}
			},
			getUserInfo(){
				let that = this;
				uni.$u.http.get('/mini_user/getUserInfo').then(rest=>{
					that.userDetail  = rest.data;				
				});
			},
			back() {
				uni.navigateBack()
			},
			submit() {	
				uni.$u.http.post('/mini_user/submitUserQuestion',this.form).then(res=>{
					if(res.code == 1) {
						uni.tip('提交成功')
					}
					
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.head {

			width: 100%;
			height: 490rpx;
			position: relative;
			// padding-top: 86rpx;
			background-size: contain;
			background-repeat: no-repeat;

			.title {
				width: 100%;
				height: 50rpx;
				display: flex;

				text {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 38rpx;
					color: #FAE7B9;
					letter-spacing: 2rpx;

				}
			}

			.get {
				margin-top: 32rpx;
				margin-left: 52rpx;
				font-size: 34rpx;
				color: #fff;
				font-weight: 600;
				letter-spacing: 2rpx;

			}

			.btn {
				width: 300rpx;
				height: 70rpx;
				color: #01997A;
				line-height: 70rpx;
				text-align: center;
				background-color: #fff;
				margin-top: 80rpx;
				margin-left: 50rpx;
				border-radius: 10rpx;
			}

			image {
				width: 351rpx;
				height: 380rpx;
				position: absolute;
				z-index: 999;
				bottom: 40rpx;
				right: 20rpx
			}
		}

		.content {
			width: 100%;
			height: 500rpx;
			background-color: #fff;
			position: relative;
			margin-top: -100rpx;
			padding: 42rpx 30rpx 20rpx 30rpx;
			box-sizing: border-box;
			border-radius: 40rpx 40rpx 0rpx 0rpx;

			.play {
				font-size: 30rpx;
				color: #4C5370;
				font-weight: bold;
				text-align: center;
				margin-bottom: 42rpx;
			}

			.top {

				font-weight: bold;
				font-size: 24rpx;
				color: #5A5A5A;

				text {
					color: #EE7878;
				}
			}

			.title {
				font-weight: bold;
				font-size: 26rpx;
				color: #201E2E;
				margin-top: 40rpx;
				margin-bottom: 20rpx;

				text {
					color: #01997A;
				}
			}
		}

		::v-deep .u-radio-group {
			display: block !important;
			height: 66rpx;
			line-height: 66rpx;
			// background: #EEFAF6;

			margin-top: 10rpx;

		}

		::v-deep .u-radio {
			background: #EEFAF6;
			padding-left: 28rpx;
			margin: 10rpx 0;
			border-radius: 18rpx 18rpx 18rpx 18rpx;
		}
		::v-deep .u-checkbox-group {
			display: block !important;
	
			line-height: 66rpx;
			// background: #EEFAF6;

			margin-top: 10rpx;

		}

		::v-deep .u-checkbox  {
			background: #EEFAF6;
			padding:15rpx 0 15rpx 28rpx;
			margin: 10rpx 0;
			border-radius: 18rpx;
		}

		::v-deep .uForm .u-input--radius,
		.u-input--square {
			border-radius: 16rpx !important;
		}

		::v-deep .u-input {
			background-color: #EEFAF6;
		}

		::v-deep .uForm {
			display: flex;
			justify-content: space-between;

			.u-form-item {
				width: 45% !important;
				height: 88rpx;

			}
		}

		.formInput {
			font-weight: bold;
			font-size: 26rpx;
			color: #4C5370;
			.login {
				box-sizing: border-box;
				width: 520rpx;
				height: 80rpx;
				border-radius: 40rpx;
				background-color: $main-color;
				font-size: 24rpx;
				color: #fff;
				text-align: center;
				line-height: 80rpx;
			}
		}

		::v-deep .u-input__content__field-wrapper__field {
			font-weight: bold !important;
			font-size: 26rpx !important;
			color: #4C5370 !important;
		}

		::v-deep .u-button {
			height: 81rpx;
			width: 400rpx;
			font-size: 30rpx;
			color: #FFFFFF;
			border-radius: 50rpx;
			border: none;
			background-color: #01997A;
			margin-top: 74rpx;
			margin-bottom: 20rpx;
		}
	}
</style>