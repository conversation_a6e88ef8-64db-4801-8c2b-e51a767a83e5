<template>
  <view class="container">
    <!-- 头部 -->
    <view class="head" @tap.stop="toOtherjump()" :style="{ backgroundImage: 'url(' + topBanner.image + ')' }">
      <u-tabs :list="list" lineWidth="50" lineHeight="10" :lineColor="`url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/bottom_line.png) 100% 100%`" @click="topTabsChange" :activeStyle="{
        color: '#fff',
        fontWeight: 'bold',
        transform: 'scale(1.25)',
      }" :inactiveStyle="{
          fontSize: '30rpx',
          color: '#fff',
          fontWeight: 'bold',
          transform: 'scale(1)',
        }" itemStyle="padding-left: 20rpx; padding-right: 20rpx; height: 82rpx;">
      </u-tabs>
    </view>
    <!-- 集训营只展示宣传内容 -->
    <view v-if="nowIndex != 2">
      <!-- 快捷导航 -->
      <view class="grid">
        <view class="grid-item" v-for="item in gridList" :key="item.id" @click="toTarget(item)">
          <image mode="widthFix" :src="item.icon"></image>
          <text>{{ item.text }}</text>
        </view>
      </view>
      <!-- 轮播部分 -->
      <view class="my-swiper" v-if="swiperList.length > 0">
        <u-swiper height="100%" :list="swiperList" @click="swiperClick"></u-swiper>
      </view>

      <!-- 课程 -->
      <view class="course">
        <!-- 标题 -->
        <view class="title">
          <u-tabs :list="courseTitleList" lineWidth="0" :activeStyle="{
            fontSize: '32rpx',
            color: '#060606',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
          }" :inactiveStyle="{
              fontSize: '30rpx',
              color: '#606266',
              transform: 'scale(1)',
            }" @click="toCategory" itemStyle="padding-left: 0px; padding-right: 20rpx; height: 36rpx;">
          </u-tabs>
        </view>
        <!-- 授课信息 -->
        <view class="course-list" v-if="goodsList.length > 0" v-for="item in goodsList" :key="item.id" @click="toDetail(item)">
          <view class="top">
            <view class="info">
              <text class="course_title">{{
                item.title.length > 30
                  ? item.title.substring(0, 29) + "..."
                  : item.title
              }}</text>
            </view>
            <view class="btn-test" v-if="item.is_audition">
              <button class="btn">试听</button>
            </view>
          </view>
          <view class="mid">
            <view class="tag">
              {{ item.cate_text }}
            </view>
            <text class="date" v-if="item.time_type == 1">{{ item.start_time | happenTimeFun }}-{{
              item.end_time | happenTimeFun
            }}
              | 共{{ item.time_hour }}课时</text>
            <text class="date" v-else>有效期 <text class="time_day">{{ item.time_day }}</text>年 | 共{{ item.time_hour }}课时</text>
          </view>
          <view class="bottom">
            <view class="teacher-list">
              <view class="teacher-info" v-for="te_item in item.teacher_list" :key="te_item.name">
                <image class="avatar" :src="te_item.image_text" />
                <text>{{ te_item.name }}</text>
              </view>
            </view>
            <view class="money" v-if="!item.is_free"> ￥{{ item.price }} </view>
            <view class="money" v-else> 免费 </view>
          </view>
        </view>
      </view>

      <!-- 话题部分 -->
      <view class="topic" v-if="nowIndex != 3" v-show="parseInt(config.app_show_community) == 1">
        <view class="title">
          <view class="left">
            <text>蛙塘</text>
            <text>塘主公告</text>
            <view class="a-right">
              <u-icon color="#A2A2A2" name="arrow-right-double"></u-icon>
            </view>
          </view>
          <view class="right">
            <u-icon size="60" name="plus-circle-fill" color="#01997A" @click="toSend"></u-icon>
            <view class="do-more" @click="more">
              <u-icon size="60" name="more-dot-fill" color="#01997A"></u-icon>
              <view class="tipBox" v-if="flag">
                <view class="li" @click="gopersonal">个人主页</view>
                <view class="li news" @click="message">
                  <text>我的消息</text>
                </view>
                <view class="li" @click="frog">蛙塘公约</view>
              </view>
            </view>
          </view>
        </view>
        <view class="tabs">
          <u-tabs :list="topicTabs" lineWidth="40" lineHeight="6" lineColor="#009c7b" @click="selTopic" :activeStyle="{
            color: '#009c7b',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
          }" :inactiveStyle="{
              color: '#606266',
              transform: 'scale(1)',
            }" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"></u-tabs>
        </view>
        <view class="topic-tag">
          <view class="hot">热门话题</view>

          <text v-for="items in typicHotList" :key="items.id" @click="selTypic(items)" :class="['subject-title', index_sel == items.id ? 'sel' : '']">#{{ items.name }}</text>

          <!-- <text class="subject-title">#专业课</text>
				<text class="subject-title">#考研准备</text> -->
        </view>
        <block v-for="artItem in articleList" :key="artItem.id">
          <view class="extra-tag">
            <!-- <view class="topping" v-if="artItem.is_refined">
						置顶
					</view> -->
          </view>
          <view class="topic-list">
            <!-- 	作者信息 -->
            <view class="auth-info">
              <image class="avatar" :src="artItem.user.avatar
                  ? artItem.user.avatar
                  : 'https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/default_avatar.png'
                ">
              </image>
              <view class="info-detail">
                <view class="auth-name">
                  <text class="is_not_officle">{{
                    artItem.user.nickname
                  }}</text>
                  <text class="is_officle" v-if="artItem.is_officle">官方号</text>
                </view>
                <text class="publish-date">{{ artItem.createtime_text }}</text>
              </view>
            </view>
            <!-- 主题 -->
            <view @click="toArticleDetail(artItem.id)">
              <view class="subject">
                {{ artItem.title }}
              </view>
              <!-- 内容概述 -->
              <view class="content-overview">
                <!-- <text>如果你想要考研但不知道如何进行复习规划，如果你对专业还院校还了解甚少，你想拥有一个良好的学习环境助你专心去备考，那就来研趣25考研全年集训营吧~可提前入营哟~</text> -->
                <text> {{ artItem.content }}</text>
                <view>
                  <text class="sel-subject-title" v-if="artItem.topic_id > 0">#{{ artItem.topic.name }}</text>
                </view>
              </view>
              <!-- 缩略图 -->
              <view class="thumb-list">
                <template v-if="artItem.image_arr_text.length < 2">
                  <image v-for="imageItem in artItem.image_arr_text" :key="imageItem" class="image_one" mode="aspectFill" :src="imageItem"></image>
                </template>
                <template v-else-if="artItem.image_arr_text.length == 2">
                  <image v-for="imageItem in artItem.image_arr_text" :key="imageItem" class="image_two" mode="aspectFill" :src="imageItem"></image>
                </template>
                <template v-else-if="
                  artItem.image_arr_text.length > 2 &&
                  artItem.image_arr_text == 4
                ">
                  <image v-for="imageItem in artItem.image_arr_text" :key="imageItem" class="image_third" mode="aspectFill" :src="imageItem"></image>
                </template>
                <template v-else>
                  <image v-for="imageItem in artItem.image_arr_text" :key="imageItem" class="image_four" mode="aspectFill" :src="imageItem"></image>
                </template>
              </view>
            </view>
            <view class="bottom-opt">
              <!-- 阅读数 -->
              <view class="read-num">
                <image class="img" src="../../static/img/view.png" mode="widthFix"></image>
                <text class="info-txt">{{ artItem.see_num }}</text>
              </view>
              <!-- 分享 -->
              <view class="share">
                <button @click="appShare(artItem)" style="border: none; display: flex; align-items: center" plain>
                  <image class="img" src="../../static/img/share.png" mode="widthFix"></image>
                  <text class="info-txt">分享</text>
                </button>
              </view>
              <!-- 评论 -->
              <view class="comment" @click="toArticleDetail(artItem.id)">
                <image class="img" src="../../static/img/msg.png" mode="widthFix"></image>
                <text class="info-txt">{{ artItem.comment_num_text }}</text>
              </view>
              <!-- 点赞 -->
              <view class="praise" @click="userZan(artItem.id)">
                <image class="img" :src="zanList.includes(artItem.id) ? unpraise : praise" mode="widthFix">
                </image>
                <text class="info-txt">{{ artItem.like_num }}</text>
              </view>
            </view>
          </view>
        </block>
        <view>
          <view class="u-demo-block__content">
            <u-loadmore :line="true" status="loadmore" @loadmore="loadmore" height="100rpx" fontSize="28rpx" loadmoreText="查看更多>>>"></u-loadmore>
          </view>
        </view>
      </view>
    </view>

    <!-- 集训营展示部分 -->

    <template v-if="nowIndex == 2">
      <view style="
          padding: 0 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
        <video autoplay class="camp-video" :src="camp_video_url"></video>
      </view>

      <view class="camp-desc">
        <rich-text :nodes="camp_desc"></rich-text>
      </view>
    </template>
  </view>
</template>

<script>
import { originList } from "@/utils/setting";
import { getConsultChat, addChatNum } from "@/api/online_chat/index.js";
import { mapMutations, mapState } from "vuex";
export default {
  data() {
    return {
      flag: false,
      srcImage:
        "https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/banner.png",
      list: [],
      pageSize: 2,
      page: 1,
      index_sel: 0,
      goodsList: [],
      goodsTotal: 0,
      topBanner: "",
      chatInfo: {},
      gridList: [
        // {
        // 	id:1,
        // 	text:"课程咨询",
        // 	icon:"/static/img/chat.png"
        // },
        // {
        // 	id:2,
        // 	text:"选课中心",
        // 	icon:"/static/img/choose-school.png"
        // },
        // {
        // 	id:3,
        // 	text:"精准择校",
        // 	icon:"/static/img/select.png"
        // },
        // {
        // 	id:4,
        // 	text:"学习打卡",
        // 	icon:"/static/img/study.png"
        // },
        // {
        // 	id:5,
        // 	text:"考点随身听",
        // 	icon:"/static/img/listen.png"
        // },
        // {
        // 	id:6,
        // 	text:"难点随身学",
        // 	icon:"/static/img/target-study.png"
        // },
        // {
        // 	id:7,
        // 	text:"资料下载",
        // 	icon:"/static/img/download.png"
        // },
        // {
        // 	id:8,
        // 	text:"蝌蚪币商城",
        // 	icon:"/static/img/shop.png"
        // }
      ],
      //话题列表
      typicHotList: [],
      swiperList: [],
      courseTitleList: [
        {
          name: "热门课程",
        },
      ],
      topicTabs: [],
      articleList: [],
      selTypicIndex: "",
      nowIndex: 1,
      praise: "../../static/img/praise.png",
      unpraise: "../../static/img/praise-color.png",
      zanList: [],
      camp_video_url: "", //营地宣传视频
      camp_desc: "", //营地描述
    };
  },
  filters: {
    happenTimeFun(num) {
      //时间戳数据处理
      let date = new Date(num * 1000);
      //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM; //月补0
      let d = date.getDate();
      d = d < 10 ? "0" + d : d; //天补0
      let h = date.getHours();
      h = h < 10 ? "0" + h : h; //小时补0
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m; //分钟补0
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s; //秒补0
      return y + "-" + MM + "-" + d;
    },
  },
  created() {
    this.list = originList;
  },
  onShow() {
    getConsultChat().then((res) => {
      console.log("onShow", res);
      if (res != false) {
        this.chatInfo = res.chatInfo;
      }
    });
    let that = this;
    that.getIndexContent();
  },
  onLoad() {
    let that = this;
    // that.getIndexContent();
    that.getGoodsList();
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState("professionalConfig", ["config"]),
  },

  onShareTimeline() { },
  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      let item = res.target.dataset.item;
      return {
        title: item.title,
        path: "/subpkg/condition_details/condition_details?id=" + item.id,
        imageUrl: item.image_arr_text[0],
        desc: "好老师,好服务",
      };
    } else {
      return {
        title: "研趣智能学习平台",
        path: "/pages/index/index",
      };
    }
  },
  onReachBottom() {
    if (this.nowIndex == 3) {
      if (this.goodsList.length >= this.goodsTotal) {
        return uni.showToast({
          title: "没有更多数据",
          duration: 1500,
          icon: "none",
        });
      } else {
        this.page += 1;
        this.getGoodsList();
      }
    }
  },
  methods: {
    ...mapMutations("origin", ["setOriginName"]),
    topTabsChange(item) {
      this.goodsList = [];
      this.nowIndex = originList[item.index].label;
      if (this.nowIndex == 3) {
        this.pageSize = 4;
      } else {
        this.pageSize = 2;
        this.page = 1;
      }
      this.setOriginName(originList[item.index].label);
      this.getIndexContent();
      this.getGoodsList();
    },
    appShare(item) {
      uni.showLoading({
        title: "分享中...",
        mask: true,
      });
      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5,
        imageUrl: "/static/png/share.png",
        title: item.title,
        miniProgram: {
          id: "gh_bfc6113eb384",
          path: "/subpkg/condition_details/condition_details?id=" + item.id,
          type: 0,
          webUrl: "https://study.yanqux.com/index/course/index.html",
        },
        fail: (err) => {
          uni.hideLoading();
          console.log(err);
        },
        success: (ret) => {
          console.log(JSON.stringify(ret));
          uni.hideLoading();
        },
      });
    },
    loadmore() {
      uni.navigateTo({
        url: "/subpkg/personal_homepage/list",
      });
    },
    userZan(articleId) {
      if (
        typeof this.userInfo.token == "undefined" ||
        this.userInfo.tken == ""
      ) {
        uni.navigateTo({
          url: "/pages/login/login",
        });
      } else {
        let that = this;
        if (that.zanList.includes(articleId)) {
          uni.$u.http
            .post("/article/cancelLike", {
              type: 1,
              article_id: articleId,
            })
            .then((ret) => {
              if (ret.code == 1) {
                that.zanList = that.zanList.filter((item) => item != articleId);
                that.articleList = that.articleList.map((item) => {
                  if (item.id == articleId) {
                    item.like_num -= 1;
                  }
                  return item;
                });
                console.log(that.articleList);
              }
            });
        } else {
          uni.$u.http
            .post("/article/likeAndSee", {
              type: 1,
              article_id: articleId,
            })
            .then((ret) => {
              if (ret.code == 1) {
                uni.tip("点赞成功");
                that.zanList.push(articleId);

                that.articleList = that.articleList.map((item) => {
                  if (item.id == articleId) {
                    item.like_num += 1;
                  }
                  return item;
                });
              }
            });
        }
      }
    },
    toArticleDetail(id) {
      uni.navigateTo({
        url: "/subpkg/condition_details/condition_details?id=" + id,
      });
    },
    toSend() {
      uni.navigateTo({
        url: "/subpkg/posts/send_posts",
      });
    },
    toCategory() {
      uni.navigateTo({
        url: "/subpkg/choosecourse/choosecourse",
      });
    },
    toDetail(item) {
      uni.navigateTo({
        url: "/choosecoursepkg/courseDetail/courseDetail?goods_id=" + item.id,
      });
    },
    swiperClick() {
      console.log("click");
    },
    toTarget(item) {
      console.log(item);
      if (item.is_jump_mini == 0) {
        if (
          item.page == "/subpkg/Online_Q_A/Online_Q_A" &&
          this.config?.is_open_chat != "1"
        ) {
          return uni.tip("功能暂未开放");
        }

        //跳转到客服
        if (item.page == "chat") {
          //判断是否开启了聊天功能
          // if (this.config?.is_open_chat != "1") {
          //   return uni.tip("功能暂未开放");
          // }
          if (Object.keys(this.chatInfo).length == 0) {
            return uni.tip("暂无客服");
          }
          let wxService = null;
          plus.share.getServices(
            (res) => {
              // 查找微信服务
              wxService = res.find((i) => i.id === "weixin");
              if (wxService) {
                wxService.openCustomerServiceChat({
                  corpid: this.chatInfo.cor_id, // 企业 ID
                  url: this.chatInfo.chat_link, // 客服链接
                });
              } else {
                uni.showToast({
                  title: "当前环境不支持微信客服",
                  icon: "none",
                });
              }
            },
            (err) => {
              uni.showToast({
                title: "服务获取失败: " + JSON.stringify(err),
                icon: "none",
              });
            }
          );
        }
        uni.navigateTo({
          url: item.page,
        });
      } else {
        uni.navigateToMiniProgram({
          appId: item.jump_config,
        });
      }
    },
    selTopic(e) {
      console.log(e);
      let that = this;
      that.selTypicIndex = e.index;
      uni.$u.http
        .get("/index/getArticleListData", {
          params: {
            page: 1,
            type: e.index,
          },
        })
        .then((rest) => {
          let res = rest.data;
          that.articleList = res.data;
        });
      uni.$u.http
        .get("/index/getTypicList", {
          params: {
            type: e.index,
          },
        })
        .then((rest) => {
          let res = rest.data;
          that.typicHotList = res;
        });
    },
    selTypic(item) {
      let that = this;
      that.index_sel = item.id;
      uni.$u.http
        .get("/index/getArticleListData", {
          params: {
            page: 1,
            type: that.selTypicIndex,
            topic_id: item.id,
          },
        })
        .then((rest) => {
          let res = rest.data;
          that.articleList = res.data;
        });
    },
    getGoodsList() {
      let that = this;
      let data = uni.$u.http
        .get("/index/getGoodsList", {
          params: {
            page: that.page,
            page_size: that.pageSize,
          },
        })
        .then((rest) => {
          let res = rest.data;
          if (that.nowIndex == 3) {
            that.goodsList = [...that.goodsList, ...res.data];
            that.goodsTotal = res.total.total;
          } else {
            that.goodsList = res.data;
            that.goodsTotal = res.total.total;
          }
        });
    },
    toOtherjump() {
      let that = this;
      if (that.topBanner.is_jump_mini == 0) {
        if (that.topBanner.page != "") {
          uni.navigateTo({
            url: that.topBanner.page,
          });
        }
      } else {
        uni.navigateToMiniProgram({
          appId: that.topBanner.jump_config,
        });
      }
    },
    //uni.$u.http.get("mini_user/wxlogin")
    getIndexContent() {
      let that = this;

      let data = uni.$u.http.get("/index/getIndexContent", {}).then((rest) => {
        let res = rest.data;
        that.topicTabs = res.category;
        that.gridList = res.icon;
        that.srcImage = res.top_list[0].image;
        that.swiperList = res.banner.map((item) => item.image);
        that.typicHotList = res.hot_typic;
        //that.index_sel = that.typicHotList[0].id;
        that.articleList = res.article.data;
        (that.topBanner = {
          ...res.topBanner[0],
        }),
          (that.zanList = res.zanArr);
        that.courseTitleList = [
          {
            name: "热门课程",
          },
        ];
        let retTmp = res.recommend_category.map((item) => {
          let tmpCourse = {
            name: item.name,
          };
          return tmpCourse;
        });

        if (typeof res.camp_desc !== "undefined") {
          this.camp_desc = res.camp_desc;
        }
        if (typeof res.camp_video_url !== "undefined") {
          this.camp_video_url = res.camp_video_url;
        }
        that.courseTitleList = [...that.courseTitleList, ...retTmp];
      });
    },
    more() {
      this.flag = !this.flag;
    },
    // 个人主页
    gopersonal() {
      uni.navigateTo({
        url: "/subpkg/personal_homepage/personal_homepage",
      });
    },
    message() {
      uni.navigateTo({
        url: "/subpkg/my_message/my_message",
      });
    },
    frog() {
      uni.navigateTo({
        url: "/subpkg/personal_homepage/frog_pond",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  .head {
    width: 100%;
    height: 474rpx;
    background-size: contain;
    background-repeat: no-repeat;
    padding-top: 86rpx;
    padding-left: 40rpx;
  }

  .camp-video {
    border-radius: 30rpx;
    width: 710rpx;
    height: 400rpx;
  }

  .grid {
    // padding: 36rpx 52rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: center;
    font-size: 24rpx;
    margin-bottom: 38rpx;
    color: #323232;

    .grid-item {
      width: 25%;
      margin-top: 16rpx;

      image {
        width: 100rpx;
      }

      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
    }
  }

  .my-swiper {
    padding: 0 42rpx;
    height: 192rpx;
  }

  // 课程
  .course {
    margin-top: 42rpx;
    padding: 0 42rpx;
    padding-bottom: 40rpx;
    background-color: #f6f7fb;

    .title {
      padding: 36rpx 0;
    }

    .course-list {
      margin-bottom: 22rpx;
      background-color: #fff;
      border-radius: 16rpx;

      .top {
        padding: 16rpx 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info {
          font-size: 26rpx;
          font-weight: bold;
          color: $uni-text-main-black;
          display: flex;
          flex-direction: column;
          width: 90%;

          .course_title {
            margin-right: -20rpx;
            width: 80%;
          }
        }

        .btn-test {
          width: 120rpx;
          height: 50rpx;

          .btn {
            height: 100%;
            width: 100%;
            text-align: center;
            line-height: 50rpx;
            border-radius: 40rpx;
            background-color: $main-color;
            font-size: 22rpx;
            color: #fff;
          }
        }
      }

      .mid {
        margin-top: 6rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 36rpx;

        .tag {
          padding: 0 8rpx;
          height: 48rpx;
          background-color: #eefaf6;
          color: $main-color;
          line-height: 48rpx;
          font-size: 22rpx;
          font-weight: bold;
          border-radius: 10rpx;
        }

        .date {
          margin-left: 8rpx;
          font-size: 22rpx;
          color: #a4a4a4;

          .time_day {
            font-weight: 700;
            font-size: 24rpx;
          }
        }
      }

      .bottom {
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .teacher-list {
          padding-left: 36rpx;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-bottom: 20rpx;

          .teacher-info {
            margin-right: 24rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 22rpx;
            color: #818181;

            .avatar {
              width: 60rpx;
              height: 60rpx;
              border-radius: 100%;
              margin-bottom: 2rpx;
            }
          }
        }

        .money {
          color: #e16965;
          font-size: 38rpx;
          margin-right: 34rpx;
        }
      }
    }
  }

  .topic {
    padding: 0 30rpx;
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0;

      .left {
        display: flex;
        align-items: center;
        justify-content: space-between;

        text {
          text-align: center;

          &:first-child {
            color: #060606;
            font-size: 32rpx;
            font-weight: bold;
            margin-right: 22rpx;
          }

          &:nth-child(2) {
            color: #777777;
            font-size: 26rpx;
            margin-right: 16rpx;
          }
        }
      }

      .right {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 36rpx;
        z-index: 9999;

        .do-more {
          margin-left: 36rpx;
          position: relative;

          .tipBox {
            position: absolute;
            right: 10rpx;
            width: 202rpx;
            height: 256rpx;
            padding: 0 10rpx;
            box-sizing: border-box;
            background: #ffffff;
            box-shadow: 0rpx 10rpx 91rpx 1rpx rgba(94, 94, 94, 0.16);
            border-radius: 12rpx;

            .li {
              position: relative;
              width: 100%;
              font-size: 26rpx;
              color: #060606;
              text-align: center;
              height: 85rpx;
              line-height: 85rpx;
              border-bottom: 0.5rpx solid #f8f8f8;

              &:last-child {
                border-bottom: none;
              }

              .num {
                position: absolute;
                width: 40rpx;
                height: 40rpx;
                font-size: 18rpx;
                color: #ffffff;
                line-height: 40rpx;
                text-align: center;
                border-radius: 50%;
                background: #e62e2e;
              }
            }
          }
        }
      }
    }

    .topic-tag {
      margin-top: 26rpx;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .hot {
        font-size: 24rpx;
        background-color: #36b89d;
        border-radius: 20rpx;
        width: 136rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        color: #fff;
      }

      .subject-title {
        font-size: 26rpx;
        margin-left: 22rpx;
      }

      .sel {
        color: $main-color;
      }
    }

    .extra-tag {
      margin-top: 38rpx;

      .topping {
        width: 94rpx;
        height: 46rpx;
        line-height: 46rpx;
        text-align: center;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #fff;
        background-color: #f17268;
      }
    }

    .topic-list {
      margin-top: 26rpx;

      .auth-info {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        image {
          width: 80rpx;
          height: 80rpx;
          margin-right: 14rpx;
          border-radius: 35rpx;
        }

        .auth-name {
          .is_officle {
            width: 92rpx;
            height: 46rpx;
            line-height: 46rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            color: #10d69a;
            background-color: #eefaf6;
          }

          .is_not_officle {
            color: #060606;
            font-size: 26rpx;
            margin-right: 8rpx;
          }
        }

        .publish-date {
          color: #818181;
          font-size: 24rpx;
        }
      }

      .subject {
        margin-top: 4rpx;
        font-weight: bold;
        color: #060606;
        font-size: 28rpx;
      }

      .content-overview {
        margin-top: 10rpx;
        color: #060606;
        font-size: 26rpx;
        line-height: 1.5;

        .sel-subject-title {
          font-size: 26rpx;
          color: $main-color;
        }
      }
    }

    .thumb-list {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-content: center;

      .image_one {
        margin-left: 10rpx;
        margin-top: 10rpx;
        width: 51%;
        border-radius: 16rpx;
      }

      .image_two {
        margin-left: 10rpx;
        margin-top: 10rpx;
        width: 40%;
        border-radius: 16rpx;
      }

      .image_third {
        margin-left: 10rpx;
        margin-top: 10rpx;
        width: 40%;
        border-radius: 16rpx;
        max-height: 260rpx;
      }

      .image_four {
        margin-left: 10rpx;
        margin-top: 10rpx;
        width: 28%;
        border-radius: 16rpx;
        max-height: 260rpx;
      }
    }

    .bottom-opt {
      margin-top: 30rpx;
      border-top: 1rpx solid #ccc;
      padding: 16rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .read-num,
      .share,
      .comment,
      .praise {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .img {
          width: 50rpx;
        }

        .info-txt {
          color: #c2c2c2;
          margin-right: 8rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
