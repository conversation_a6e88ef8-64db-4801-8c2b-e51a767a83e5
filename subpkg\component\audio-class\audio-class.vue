<template>
	<view class="audio_content">
		<scroll-view class="scrolling_lyrics" :style="{ height: scrollHeight + 'px' }" scroll-y="true" :scroll-top="scrollTop" :scroll-with-animation="true">
			<text id="lyrics-title" class="lyrics-title">{{ currentSubtitle }}</text>
			<!-- <text class="normal" v-for="(line, index) in subtitleLines" :key="index">{{ line }}</text> -->
		</scroll-view>

		<view class="audio_box">
			<view class="audio_process">
				<view class='slider'>
					<slider @change='sliderChange' @changing="sliderChangeIng" backgroundColor="#A3A4A6" activeColor='#FFFFFF' block-size="12" :value='proceessWidth' />
				</view>
				<view class="time_cons">
					<view class="duration">
						{{ audioInfo.currentTime }}
					</view>
					<view class="end">
						{{ audioInfo.duration }}
					</view>
				</view>
			</view>
			<view class="utils">
				<view class="list-audio" @click="randomPlay = !randomPlay">
					<u-icon :name="randomPlay ? randomPng : listPng" size="58" color="#fff"></u-icon>
					<text class="text">{{ randomPlay ? "随机播放" : "顺序播放" }}</text>
				</view>
				<image @click="audioChange(-1)" class="change" :src="require('./pre-arrow.png')" mode="">
				</image>
				<view class="center" @click="clickAudio">
					<image :src="require('./stop.png')" mode="" v-if="!audioStatus"></image>
					<image :src="require('./paly.png')" v-else></image>
				</view>
				<image @click="audioChange(1)" class="change" :src="require('./next-arrow.png')" mode="">
				</image>
				<view v-if="audioInfo.showRate" @click="setRate" class="beisu">
					<text class="num">{{ currentRate }}X</text>
					<text class="text">倍速播放</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import {
	debounce
} from './debounce.js'
import {
	versionCompare
} from './utils.js'
const formatTime = (time) => {
	if (typeof time !== "number" || time < 0) {
		return "00:00";
	}
	const hour = parseInt(time / 3600);
	time = time % 3600;
	const minute = parseInt(time / 60);
	time = time % 60;
	const second = time;
	let list = [minute, second];
	if (hour > 1) {
		list.unshift(hour);
	}
	return list
		.map(function (n) {
			n = n.toString();
			return n[1] ? n : "0" + n;
		})
		.join(":");
};

const rateList = ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"];
let innerAudioContext = null; // 声明实例

export default {
	props: {
		startPlay: {
			tyepe: Boolean,
			default: false
		},
		classTitle: {
			type: String,
			default: ''
		},
		currentMedia: {
			type: Object,
			default: () => { },
		},
		mediaIndex: {
			// 当前媒体信息
			type: Number,
			default: 0,
		},
	},
	data() {
		return {

			scrollHeight: 140,
			scrollTop: 0,
			randomPlay: false,
			listPng: "https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/shunxu.png",
			randomPng: "https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/suiji.png",
			audioStatus: false,
			proceessWidth: 0,
			innerAudioContext: null,
			audioInfo: {
				duration: "00:00",
				currentTime: "00:00",
				showRate: true,
			},
			currentRate: "1.0",
			timer: Date.now(), // 记录时间
			currentSubtitle: '',
			subtitleLines: [],
			subtitles: [
			]
		}
	},
	created() {
		this.innerAudioContext = uni.createInnerAudioContext({
			obeyMuteSwitch: false
		});
		this.audioInfo.showRate = this.isShowbeisuNumFn();
	},
	watch: {
		currentMedia: {
			handler(newValue, oldValue) {
				if (newValue == null) {
					return
				}
				// 上报信息，开始重新处理
				this.audioInfo = {
					duration: "00:00",
					currentTime: "00:00",
					showRate: true,
				},
					this.proceessWidth = 0
				this.currentRate = 1.0
				this.parseAudioText(newValue.lyrics)
				this.handleTimer();

				// 销毁旧的音频上下文以避免问题
				if (this.innerAudioContext) {
					this.innerAudioContext.destroy();
				}

				// 创建新的音频上下文
				this.innerAudioContext = uni.createInnerAudioContext({
					obeyMuteSwitch: false
				});

				this.initAudio();
			},
			deep: true,
			immediate: true
		},
		startPlay: {
			handler(newVal) {
				// 当 startPlay 改变为 true 时尝试播放
				if (newVal && this.innerAudioContext) {
					this.innerAudioContext.play();
				}
			}
		}
	},
	onUnload() {
		this.innerAudioContext.destroy()
		this.innerAudioContext = null
	},
	beforeDestroy() {
		//this.reportProgress(this.currentMedia);
		this.innerAudioContext.destroy()
		this.innerAudioContext = null
	},
	methods: {
		// 上报数据
		// reportProgress(currentMedia) {
		// 	const duration = parseInt((Date.now() - this.timer) / 1000);
		// 	let query = {
		// 		content_id: currentMedia.id,
		// 		viewing_record: this.proceessWidth, // 进度
		// 		duration, // 此次学习秒数,初次加载和切换之后
		// 	};
		// 	this.$emit("durationsChange", {
		// 		durations: innerAudioContext.currentTime,
		// 		currentMedia,
		// 	});
		// 	// this.saveCourseDuration(query); // 上报数据到后端
		// },
		// 处理时间
		handleTimer() {
			this.timer = Date.now();
		},
		//利用版本判断是否可以开启倍速
		isShowbeisuNumFn(version) {
			//利用版本判断是否倍速
			const {
				SDKVersion
			} = uni.getSystemInfoSync();
			return versionCompare(SDKVersion, "2.33.0");
		},
		// 音频初始化
		initAudio() {
			console.log('初始化音频', this.currentMedia.url);

			if (!this.currentMedia || !this.currentMedia.url) {
				console.error('无效的音频URL');
				return;
			}

			// 设置音频源和初始属性
			this.innerAudioContext.src = this.currentMedia.url;
			this.innerAudioContext.autoplay = this.startPlay;
			this.innerAudioContext.startTime = this.currentMedia.durations;

			// 绑定事件
			this.innerAudioContext.onPlay(() => {
				console.log("开始播放");
				this.audioStatus = true;
			});

			this.innerAudioContext.onCanplay(() => {
				console.log("音频可以播放");
				let intervalID = setInterval(() => {
					if (this.innerAudioContext.duration !== 0) {
						clearInterval(intervalID);
						this.audioInfo.duration = formatTime(parseInt(this.innerAudioContext.duration));

						// 根据 startPlay 决定是否开始播放
						if (this.startPlay) {
							this.innerAudioContext.play();
							this.audioStatus = true;
						} else {
							this.innerAudioContext.pause();
							this.audioStatus = false;
						}
					}
				}, 500);
			});

			this.innerAudioContext.onError((res) => {
				console.log('音频播放错误:', res);
				uni.showToast({
					title: '音频播放失败',
					icon: 'none'
				});
			});

			this.innerAudioContext.onEnded((res) => {
				console.log("播放完毕");
				this.audioStatus = false;
				this.$emit("end", this.randomPlay)
			});
			this.innerAudioContext.onTimeUpdate((res) => {
				if (this.audioInfo == null || this.innerAudioContext == null) {
					return
				}
				this.audioInfo.currentTime = formatTime(
					parseInt(this.innerAudioContext.currentTime || 0) || 0
				);
				this.proceessWidth = parseInt(
					(this.innerAudioContext.currentTime / this.innerAudioContext.duration) *
					100
				);
				this.syncSubtitle(this.innerAudioContext.currentTime);
			});
		},
		//解析音频文本
		parseAudioText(text) {
			const lines = text.split('\n');
			this.subtitles = lines.map(line => {
				const match = line.match(/\[(\d+):(\d+\.\d+),(\d+):(\d+\.\d+)\]\s+(.*)/);
				if (match) {
					const start = parseInt(match[1], 10) * 60 + parseFloat(match[2]);
					const end = parseInt(match[3], 10) * 60 + parseFloat(match[4]);
					return {
						start,
						end,
						text: match[5].trim()
					};
				}
				return null;
			}).filter(sub => sub !== null);
			this.subtitleLines = this.subtitles.map(sub => sub.text);
		},
		//同步字幕
		syncSubtitle(currentTime) {
			const query = uni.createSelectorQuery().in(this);

			// 选择元素并获取其位置信息
			query.select('#lyrics-title').boundingClientRect(data => {
				if (data) {
					// this.elementHeight = data.height;
					//获取 lyrics-title 的 高度
					for (let i = 0; i < this.subtitles.length; i++) {
						if (currentTime >= this.subtitles[i].start && currentTime <= this.subtitles[i].end) {

							//计算当前阶段播放的百分比
							const percent = (currentTime - this.subtitles[i].start) / (this.subtitles[i].end - this.subtitles[i].start);

							if (data.height > this.scrollHeight) {
								if (parseInt(currentTime) % 10 == 0) {
									this.scrollTop = percent * (data.height - this.scrollHeight)
								}

							} else {
								this.scrollTop = 0;
							}
							//根据百分比计算字幕显示位置

							//设置字幕文本

							this.currentSubtitle = this.subtitles[i].text;
							return;
						}
					}
					this.currentSubtitle = '';




				}
			}).exec();

		},
		//拖动进度条事件
		sliderChange(e) {
			const proceess = e.detail.value;
			const duration = this.innerAudioContext.duration;
			this.innerAudioContext.seek(parseInt(duration * (proceess / 100)));
			this.audioInfo.currentTime = formatTime(parseInt(duration * (proceess / 100)));
			// if(this.audioStatus ==true){
			// 	this.innerAudioContext.play();
			// }else{
			// 	this.innerAudioContext.pause()
			// }
		},
		// 拖动中
		sliderChangeIng(e) {
			this.innerAudioContext.pause();
			this.audioStatus = false;
		},
		// 切换
		audioChange(num) {
			this.$emit("change", num);
		},
		//开始暂停
		clickAudio() {
			try {
				if (!this.audioStatus) {
					// 播放
					console.log('开始播放');
					this.innerAudioContext.play();
				} else {
					// 暂停
					console.log('暂停播放');
					this.innerAudioContext.pause();
				}
				this.audioStatus = !this.audioStatus;
			} catch (error) {
				console.error('播放控制错误:', error);
				uni.showToast({
					title: '播放控制失败',
					icon: 'none'
				});
			}
		},
		// 设置播放速度
		setRate() {
			let index = rateList.findIndex((num) => this.currentRate === num);
			this.currentRate = rateList[index + 1] || rateList[0];
			let current = parseInt(this.innerAudioContext.currentTime) || 0;
			this.innerAudioContext.playbackRate = +this.currentRate;
			this.innerAudioContext.seek(current);
			this.audioStatus = true;
		},
	},
}
</script>

<style lang="less" scoped>
.audio_content {
	width: 100%;
	padding: 24rpx;
	box-sizing: border-box;

	.scrolling_lyrics {
		border-top: 1rpx solid #B9B9B9;
		padding-top: 46rpx;
		color: #fff;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		// height: 240rpx;

		.lyrics-title {
			font-size: 32rpx;
		}

		.normal {
			padding: 10rpx 0;
			font-size: 28rpx;
			color: #ccc;
		}
	}

	.audio_box {
		min-height: 390rpx;
		background: transparent;
		border-radius: 42rpx;
		width: 100%;
		display: flex;
		flex-direction: column;
		margin-bottom: 16rpx;
		align-items: center;
		box-sizing: border-box;
		position: relative;

		.beisu {
			display: flex;
			flex-direction: column;
			justify-content: center;
			text-align: center;
			height: 70rpx;
			margin-left: 20rpx;

			.num {
				height: 29px;
				line-height: 29px;
				font-size: 30rpx;
				font-weight: 500;
				color: #FFFFFF;
			}

			.text {
				margin-top: 10rpx;
				font-size: 22rpx;
				color: #FFFFFF;
				line-height: 22rpx;
			}
		}

		.course_name {
			width: 100%;
			font-size: 26rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 37rpx;
			text-align: center;
			margin-bottom: 10rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.audio_title {
			// margin: 10rpx;
			width: 100%;
			font-size: 34rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.audio_process {
			position: relative;
			top: 0;
			left: 0;
			transform: translateY(24rpx);
			margin-bottom: 6rpx;

			.audio_line {
				width: 582rpx;
				height: 4rpx;
				background: #A3A4A6;
				// height: 8rpx;
				// width: 520rpx;
				// background: rgba(37, 48, 91, 0.21);
				// border-radius: 5rpx;
				// opacity: 0.58;

			}

			.audio_line_on {
				position: absolute;
				top: 0;
				left: 0;
				width: 50%;
				height: 4rpx;
				background: #FFFFFF;

			}

			.audio_point {
				position: absolute;
				top: -12rpx;
				left: 0;
				width: 24rpx;
				height: 24rpx;
				background: #FFFFFF;
				border-radius: 50%;
				font-size: 22rpx;
				line-height: 22rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.time_cons {
				padding: 0 18px;
				width: 100%;
				margin-top: 10rpx;
				display: flex;
				justify-content: space-between;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 33rpx;
			}
		}

		.utils {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 16rpx;

			.list-audio {
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				height: 70rpx;
				margin-right: 20rpx;

				.text {
					margin-top: 10rpx;
					font-size: 22rpx;
					color: #FFFFFF;
					line-height: 22rpx;
				}
			}

			.center {
				margin: 0 70rpx;

				image {
					display: block;
					width: 96rpx;
					height: 96rpx;
				}
			}

			.change {
				display: block;
				width: 48rpx;
				height: 36rpx;
			}
		}

	}
}

.slider {
	width: 750rpx;
}
</style>