<template>
	<view class = "container">
		<my-video :src='url'></my-video>
	</view>
</template>

<script>
	import myVideo from '@/components/my-video/my-video.vue';
	export default {
		data() {
			return {
				url:''
			};
		},
		onLoad(option) {
			const {url} = option
			if(typeof url =="undefined"){
				uni.showModal({
					showCancel:false,
					content:"视频地址为空",
					success:function(res){
						if(res.confirm){
							uni.navigateBack()
						}
					}
				})
			}
			this.url = url
		},
		components:{
			myVideo
		}
	}
</script>

<style lang="scss" scoped>
.container{
	
}
</style>
