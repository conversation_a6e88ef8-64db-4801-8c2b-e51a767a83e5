<template>
	<view class="container">
		<view class="item" v-for="i in 9" :key="i">
			<image src="https://test-1300870289.cos.ap-nanjing.myqcloud.com/book.png" mode="widthFix"></image>
			<text class="title">高等数学辅导讲义</text>
			<text class="date">2025.12.15</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
.container{
	background-color: $container-bg-color;
	min-height: 100vh;
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	justify-content: space-between;
	padding: 30px;
	.item{
		padding: 20rpx 2rpx;
		margin-bottom: 30rpx;
		background-color: #fff;
		width: 30%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		image{
			width: 80%;
		}
		text.title{
			font-size: 24rpx;
			color: #1F1F27;
			margin-top: 16rpx;
			margin-bottom: 6rpx;
		}
		text.date{
			color: #AFAFAF;
			font-size: 22rpx;
		}
		
	}
}
</style>
