import cos from "./cos"
import {uploadConfig} from "@/utils/setting.js"
import {getUserInfo}from "@/utils/storage.js"
import store from "@/store/index.js"
//图片压缩级别
const img_compress =  store.state.professionalConfig.config.img_compress || 0.6
const userInfo = getUserInfo()
let fileParentPath = "other"
if( typeof userInfo.phone !="undefined"){
	fileParentPath =  userInfo.phone
}

/**
 * 检测是否为APP平台
 */
function isAppPlatform() {
    return uni.getSystemInfoSync().platform === 'android' || uni.getSystemInfoSync().platform === 'ios';
}

/**
 * APP平台专用上传方法 - 腾讯云COS对象存储
 * @param {String} filePath 文件路径
 * @return {Promise}
 */
function appUploadToCOS(filePath) {
    return new Promise((resolve, reject) => {
        if (typeof plus === 'undefined') {
            reject('plus对象不存在，请确保在APP环境下运行');
            return;
        }
        
        try {
            // 处理文件路径和文件名
            const fileName = filePath.slice(filePath.lastIndexOf("/") + 1);
            const objectKey = fileParentPath + "/" + fileName;
            
            // 使用plus.io处理文件
            plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
                entry.file((file) => {
                    // 使用原生上传到COS对象存储
                    // 这里可以直接使用plus.uploader上传到自定义服务器，然后由服务器转发到COS
                    // 或者集成腾讯云COS的APP端SDK
                    
                    // 方案1: 使用plus.uploader上传到自己的中转服务器
                    const task = plus.uploader.createUpload(
                        uni.$u.http.config.baseURL + '/common/upload', // 假设有个接口专门处理APP上传到COS
                        {
                            method: 'POST',
                            blocksize: 204800,
                            priority: 100,
                            timeout: 60000
                        },
                        (uploadTask, status) => {
							console.log("uploadTask",uploadTask)
							console.log("status",status)
                            if (status === 200) {
                                try {
                                    let responseText = uploadTask.responseText;
                                    let data = JSON.parse(responseText);

                                    if (data.code === 1) {
                                        resolve({
                                            fullurl: data.data.fullurl
                                        });
                                    } else {
                                        reject(data.msg || '上传失败');
                                    }
                                } catch (e) {
                                    reject('解析响应数据失败: ' + e.message);
                                }
                            } else {
                                reject('上传失败，状态码: ' + status);
                            }
                        }
                    );
                    
                    // 添加上传文件
                    task.addFile(filePath, {
                        key: 'file',
                        name: fileName
                    });
                    
                    // 添加其他表单数据
                    task.addData('token', uni.getStorageSync('token') || '');
                    task.addData('objectKey', objectKey);
                    task.addData('bucket', uploadConfig.Bucket);
                    task.addData('region', uploadConfig.Region);
                    
                    // 开始上传
                    task.start();
                }, (fileError) => {
                    reject('获取文件对象失败: ' + fileError.message);
                });
            }, (error) => {
                reject('解析文件路径失败: ' + error.message);
            });
        } catch (e) {
            reject('plus.io操作异常: ' + e.message);
        }
    });
}

/**
 * 上传文件通用方法 - 支持APP和其他平台
 * @param {String} fileName 文件路径
 * @returns {Promise}
 */
export const uploadFile = async (fileName) => {
    try {
        // 判断平台
        let isApp = isAppPlatform() && typeof plus !== 'undefined';
        
        // 先压缩图片（仅在非APP平台，或APP平台但压缩可用时执行）
        let compressedFilePath = fileName;
        if (!isApp || (isApp && plus.os.name.toLowerCase() !== 'android')) {
            try {
                let res = await uni.compressImage({
                    src: fileName,
                    quality: parseInt(img_compress * 100),
                });
                
                if (res[1] && res[1].errMsg && res[1].errMsg.includes("ok")) {
                    compressedFilePath = res[1].tempFilePath || fileName;
                }
            } catch (compressError) {
                console.warn("图片压缩失败，将使用原图:", compressError);
                // 使用原图继续
            }
        }
        
        // 根据平台选择不同的上传方式
        if (isApp) {
            // APP平台使用专用上传方法
            return await appUploadToCOS(compressedFilePath);
        } else {
            // 非APP平台使用原有的COS上传
            return new Promise((resolve, reject) => {
                cos.putObject({
                    Bucket: uploadConfig.Bucket,
                    Region: uploadConfig.Region,
                    Key: fileParentPath + "/" + compressedFilePath.slice(compressedFilePath.lastIndexOf("/") + 1),
                    FilePath: compressedFilePath,
                    SliceSize: uploadConfig.maxSingleSize,
                    onProgress: function(progressData) {
                        console.log(JSON.stringify(progressData));
                    }
                }, function(err, data) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            fullurl: "https://" + data.Location.replace(/https?:\/\//g, '')
                        });
                    }
                });
            });
        }
    } catch (error) {
        console.error("图片上传处理失败:", error);
        return Promise.reject(error);
    }
}

/**
 * @param{ string url}
 * @return {promise}
 */
export const uploadFiletmp = (url)=>{
	return new Promise((resolve, reject) => {
		let a = uni.uploadFile({
			url:  uni.$u.http.config.baseURL+'/common/upload', 
			filePath: url,
			name: 'file',
			success: (res) => {
				setTimeout(() => {
						const result = JSON.parse(res.data)
					if(result.code==1){
						resolve(result.data)
					}else{
						uni.tip(result.msg)
						reject(new Error(result.msg))
					}
					
				}, 1000)
			},
			fail(res) {
				uni.tip("网络错误")
			}
		});
	})
}

export const getAppConfig = async (versionCode)=>{
    const ret = await uni.$u.http.get("/sys_config/getAppConfig", {
        params:{
           versionCode
        }
    })
    if(ret.code==1){
        return ret.data
    }else{
        return false
    }
}
