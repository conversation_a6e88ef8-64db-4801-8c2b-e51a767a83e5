## 简介

本项目是一个可拖动的底部抽屉，
拖动的范围有最高点，中间点，最低点，可使用rpx单位
抽屉内部可任意填充内容

## 使用

```html
<template>
            <view>
                  <cover>
                              <view style="height: 100%;"></view>
                        </cover>
            </view>
</template>
```

<script>
// 此处将路径替换为你放置该组件的路径
import cover from '.zhong-cover/zhong-cover.vue'
export default {
            components:{
                        cover
            }
}
</script>

## 插槽         

| 属性       | 类型   | 默认     | 可选值                               | 说明                                                       |
| ---------- | ------ | -------- | ------------------------------------ | ---------------------------------------------------------- |
| defaultTop | String | "midTop" | 'minTop' \|\| "midTop" \|\| "maxTop" | 抽屉默认的位置, 有最高点minTop，中间点midTop，最低点maxTop |
| minTop     | Number | 200      |                                      | 抽屉往上移动，距离顶部多少rpx不能继续移动                  |
| midTop     | Number | 400      |                                      | 抽屉的中间位置                                             |
| bottom     | Number | 200      |                                      | 抽屉往下移动，距离底部多少rpx不能继续移动， 控制着maxTop   |

