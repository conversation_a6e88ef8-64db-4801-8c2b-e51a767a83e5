<template>
	<view class="login-container">
		<view class="logo-box">
			<image src="/static/logo.png" mode="aspectFit" class="logo"></image>
		</view>

		<view class="form-box">
			<view class="input-group">
				<input type="text" v-model="form.phone" placeholder="请输入手机号" maxlength="11" />
			</view>
			<view class="input-group">
				<input type="text" v-model="form.code" placeholder="请输入验证码" maxlength="6" />
				<text class="code-btn" :class="{ disabled: counting }" @click="getCode">
					{{ counting ? `${counter}s后重试` : '获取验证码' }}
				</text>
			</view>

			<button class="login-btn" @click="handleLogin">登录</button>

			<view class="agreement">
				登录即代表同意
				<text class="link" @click="showAgreement">《用户协议》</text>
				和
				<text class="link" @click="showPrivacy">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
import { mapMutations } from 'vuex'

export default {
	data() {
		return {
			form: {
				phone: '',
				code: ''
			},
			counting: false,
			counter: 60
		}
	},
	methods: {
		...mapMutations('user', ['setUser']),

		// 获取验证码
		async getCode() {
			if (this.counting) return

			// 验证手机号
			if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}

			try {
				// 调用获取验证码接口
				const res = await uni.request({
					url: '/api/sms/send',
					method: 'POST',
					data: {
						phone: this.form.phone
					}
				})

				if (res.data.code === 200) {
					this.startCounter()
					uni.showToast({
						title: '验证码已发送',
						icon: 'success'
					})
				} else {
					throw new Error(res.data.msg)
				}
			} catch (error) {
				uni.showToast({
					title: error.message || '发送失败，请重试',
					icon: 'none'
				})
			}
		},

		// 开始倒计时
		startCounter() {
			this.counting = true
			this.counter = 60
			const timer = setInterval(() => {
				this.counter--
				if (this.counter <= 0) {
					clearInterval(timer)
					this.counting = false
				}
			}, 1000)
		},

		// 处理登录
		async handleLogin() {
			if (!this.form.phone || !this.form.code) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				})
				return
			}

			try {
				const res = await uni.request({
					url: '/api/app/login',
					method: 'POST',
					data: this.form
				})

				if (res.data.code === 200) {
					const userData = res.data.data
					// 保存用户信息
					this.setUser(userData)
					// 保存token到本地存储
					uni.setStorageSync('token', userData.token)

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})

					// 延迟返回，让用户看到提示
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					throw new Error(res.data.msg)
				}
			} catch (error) {
				uni.showToast({
					title: error.message || '登录失败，请重试',
					icon: 'none'
				})
			}
		},

		// 显示用户协议
		showAgreement() {
			// 实现查看用户协议的逻辑
		},

		// 显示隐私政策
		showPrivacy() {
			// 实现查看隐私政策的逻辑
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	padding: 40rpx;

	.logo-box {
		text-align: center;
		margin: 60rpx 0;

		.logo {
			width: 200rpx;
			height: 200rpx;
		}
	}

	.form-box {
		.input-group {
			position: relative;
			margin-bottom: 30rpx;
			width: 80%;

			input {
				height: 90rpx;
				padding: 0 30rpx;
				background: #f8f8f8;
				border-radius: 45rpx;
			}

			.code-btn {
				position: absolute;
				right: 30rpx;
				top: 50%;
				transform: translateY(-50%);
				color: #007AFF;

				&.disabled {
					color: #999;
				}
			}
		}

		.login-btn {
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			background: #007AFF;
			color: #fff;
			border-radius: 45rpx;
			margin: 60rpx 0;
		}

		.agreement {
			text-align: center;
			font-size: 24rpx;
			color: #999;

			.link {
				color: #007AFF;
			}
		}
	}
}
</style>