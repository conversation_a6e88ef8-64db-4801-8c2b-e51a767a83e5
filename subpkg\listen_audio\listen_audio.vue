<template>

	<view class="wraper">

		<view class="container" v-if="firstLoad == false && mediaList.length == 0">
			<view class="top">
				<u-navbar :bgColor="'transparent'" :placeholder='true'>

					<view class="title" slot="left">
						<u-icon @click="back()" name="arrow-left" color="#fff"></u-icon>
						<text>
							{{ catename }}
						</text>
					</view>
				</u-navbar>
				<u-empty mode="list" text="该分类下没有数据" textSize="24">
				</u-empty>
			</view>
		</view>

		<view v-else class="container">
			<view class="top">
				<u-navbar :bgColor="'transparent'" :placeholder='true'>

					<view class="title" slot="left">
						<u-icon @click="back()" name="arrow-left" color="#fff"></u-icon>
						<text>
							{{ catename }}
						</text>
					</view>
				</u-navbar>

				<class-audio ref="audioClass" :classTitle="classTitle" :startPlay="startPlay" :currentMedia="mediaList[mediaIndex]" :mediaIndex="mediaIndex" @change="mediaChange" @durationsChange="durationsChange" @end="mediaEnd">
				</class-audio>
			</view>

			<view class="current-item-info">
				<view class="item-title" @click="changeStar" v-if="mediaList.length > 0">
					<text>{{ mediaList[mediaIndex].title }}</text>
					<view class="left-icon">
						<!-- 		<u-icon class="star" name="star" size="58"></u-icon> -->
						<u-icon v-if="isStar" name="star-fill" size="58" color="#009c7b"></u-icon>
						<u-icon v-else name="star" size="58"></u-icon>
					</view>
				</view>

				<text class="text-tip" v-if="mediaList.length > 0">{{ mediaList[mediaIndex].title }}</text>
				<view class="tip" v-if="mediaList.length > 0">
					<u-icon name="play-right" size="18"></u-icon>
					<text>已播放{{ mediaList[mediaIndex].num }}次</text>
				</view>

			</view>
			<view class="line">

			</view>


			<view class="course-list">

				<view class="course-info u-border-bottom" v-for="(item, index) in mediaList" :key="item.id" @click="selectItem(index)">
					<view class="course-info-left">
						<view class="course-num">{{ index + 1 }}</view>
						<view class="course-title">
							<text class="course-tip">{{ item.title }}</text>
							<view class="tip">
								<u-icon name="play-right" size="18"></u-icon>
								<text>已播放{{ item.num }}次</text>
							</view>
						</view>
					</view>
					<image v-show="mediaIndex == index" src="@/static/img/soundWave.png" mode="widthFix"></image>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
import {
	getPointListByCateId,
	setStar,
	cancelStar,
	addListenCount
} from "@/api/exampoint/index.js"
import classAudio from "../component/audio-class/audio-class.vue"
export default {
	data() {
		return {
			startPlay: false,
			cid: 0,
			catename: '',
			mediaList: [],
			mediaIndex: 0,
			classTitle: '课程名称',
			mediaId: 0,
			firstLoad: true,
			starList: []
		};
	},
	onLoad(option) {
		uni.setNavigationBarTitle({
			title: option.name || '名称'
		})
		this.catename = option.name || '名称'
		this.cid = option.cid || 0
		const point_ids = option.point_ids || ''
		const params = {
			point_ids: point_ids,
			cid: this.cid,
		}
		this.firstLoad = true
		getPointListByCateId(params).then(data => {
			this.firstLoad = false
			this.mediaList = data.pointList.map(item => {
				return {
					durations: 0,
					id: item.id,
					cid: item.cid,
					title: item.point_name,
					url: item.audio_file,
					lyrics: item.content,
					viewing_record: 0,
					num: item.listened_num,
				}
			})
			this.startPlay = true
			//收藏列表
			this.starList = data.starList
		})
	},
	created() { },
	methods: {
		back() {
			uni.navigateBack()
		},
		//选择列表
		selectItem(index) {
			this.mediaIndex = index
			// 修改为开始自动播放
			this.startPlay = true
			this.$nextTick(() => {
				this.mediaId = this.mediaList[this.mediaIndex].id
			})
		},
		//收藏/取消收藏
		async changeStar() {
			const data = {
				points_id: this.mediaList[this.mediaIndex].id,
				type: 1,
				category_id: this.mediaList[this.mediaIndex].cid,
			}
			//已经都藏 取消收藏
			if (this.isStar) {
				const ret = await cancelStar(data)
				if (ret != false) {
					this.starList = this.starList.filter(item => item != this.mediaList[this.mediaIndex].id)
				}
			} else {

				const ret = await setStar(data)
				if (ret != false) {
					this.starList = [...this.starList, this.mediaList[this.mediaIndex].id]
				}
			}
		},
		//音频播放结束
		mediaEnd(randomPlay) {
			const data = {
				id: this.mediaList[this.mediaIndex].id,
			}
			addListenCount(data)
			// 随机播放
			if (randomPlay) {
				this.mediaIndex = Math.floor(Math.random() * this.mediaList.length)
			} else {
				if (this.mediaIndex < this.mediaList.length - 1) {
					this.mediaIndex++
				}
			}
			this.startPlay = true
		},
		mediaChange(step) {
			const mindex = this.mediaIndex
			if (step === -1 && mindex === 0) {
				uni.showToast({
					title: "当前是第一节哦～",
					icon: "none",
				});
				return;
			}
			if (step === 1 && mindex === this.mediaList.length - 1) {
				uni.showToast({
					title: "没有更多了～",
					icon: "none",
				});
				return;
			}
			this.mediaIndex = mindex + step
			this.$nextTick(() => {
				this.mediaId = this.mediaList[this.mediaIndex].id
			})
			this.startPlay = true
		},
		durationsChange(params) {

			const {
				durations,
				currentMedia,
			} = params;
		},
	},
	components: {
		classAudio
	},
	computed: {
		isStar() {
			if (this.starList.length > 0 && this.mediaList.length > 0) {
				return this.starList.includes(this.mediaList[this.mediaIndex].id)
			} else {
				return false
			}

		}
	}
}
</script>

<style lang="scss" scoped>
.container {


	.top {
		width: 750rpx;
		height: 800rpx;
		background: linear-gradient(180deg, #367E62 0%, #154B36 100%);
		border-radius: 0rpx 0rpx 30rpx 30rpx;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #fff;
			font-size: 32rpx;
		}

		.scrolling_lyrics {
			border-top: 1rpx solid #B9B9B9;
			padding: 0 30rpx;
			padding-top: 46rpx;
			color: #fff;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			.lyrics-title {
				font-size: 32rpx;
			}
		}
	}


	.current-item-info {

		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: column;
		padding: 0 30rpx;
		padding-bottom: 24rpx;

	}

	.text-tip {
		width: 100%;
		color: #AFAFAF;
		font-size: 20rpx;
		margin-top: 12rpx;
	}

	.tip {
		margin-top: 4rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		color: #AFAFAF;
		font-size: 20rpx;
	}

	.item-title {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;

		width: 100%;
		margin-top: 44rpx;

		text {
			color: #201E2E;
			font-size: 30rpx;
			font-weight: bold;
		}

		.left-icon {
			display: flex;
			align-items: center;
			justify-content: space-between;

			::v-deep .u-icon__icon {
				margin-left: 36rpx;
			}
		}
	}


	.line {
		width: 100%;
		height: 10rpx;
		background: #F6F7FB;
	}

	.course-list {
		padding: 0 30rpx;

		.course-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;

			.course-info-left {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.course-num {
					width: 120rpx;
					font-weight: bold;
					font-size: 36rpx;
					color: #5A5A5A;
					text-align: center;

					image {
						width: 60rpx;
					}
				}

				.course-title {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					flex-direction: column;

					.course-tip {
						width: 100%;
						color: #201E2E;
						font-size: 30rpx;
						margin-top: 12rpx;
					}

					.tip {
						margin-top: 4rpx;
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: flex-start;

						text {
							color: #AFAFAF;
							font-size: 20rpx !important;
						}

					}
				}
			}

			.down {
				flex: 1;
			}

			image {
				width: 60rpx;
			}
		}
	}

	.title-name {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 0;
		padding-bottom: 0;

		.left {
			color: #1F1F27;
			font-size: 32rpx;
			font-weight: bold;

			.green-block {
				display: inline-block;
				width: 14rpx;
				height: 28rpx;
				line-height: 28rpx;
				background: #01997A;
				border-radius: 0rpx 8rpx 0rpx 8rpx;
				margin-right: 6rpx;
			}
		}
	}
}
</style>