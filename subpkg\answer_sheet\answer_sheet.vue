<template>
	<view class="container">
		<u-navbar bgColor="#009c7b" :border='true' :placeholder='true' @leftClick="back">
			<view class="u-nav-slot" slot="left">
				<u-icon name="arrow-left" size="38" color="#fff">
				</u-icon>
			</view>
			<view class="center" slot="center">
				<text>答题卡</text>
			</view>
		</u-navbar>

		<view class="tag-flag">
			<view class="cricle-wraper">
				<view class="cricle-undo">
				</view>
				<text class="tip">未做</text>
			</view>
			<view class="cricle-wraper">
				<view class="cricle-done">
				</view>
				<text class="tip">已做</text>
			</view>
		</view>
		<view class="list">
			<view class="item-container" v-for="(item, index) in currentTypes">
				<view @click="toTemplate(item, index)" class="item" :class='{ "item-sel": hasAnswerIndexList.includes(index) || correctionCompleted || isFromRecordsList }'>
					{{ index + 1 }}
				</view>
			</view>
		</view>
		<view class="btn-container" v-if="currentType.type != 3">
			<view class="btn" @click="submit">
				{{ (correctionCompleted == 1 || isFromRecordsList) ? '查看答题报告' : '提交' }}
			</view>
		</view>
	</view>
</template>

<script>
import {
	selectTemplate,
	selectTemplateAnswer
} from "@/utils/tool.js"
import mixin from "@/mixins/index.js"
import {
	subjectTypeArr
} from "@/utils/setting.js"
import { sendTaskSubmitTemplateMessage } from "@/api/professional/index.js"
import {
	mapState
} from "vuex"
export default {
	data() {
		return {
			forbidToQuestion: 0,
		};
	},
	mixins: [mixin],
	computed: {
		hasAnswerIndexList() {
			return this.currentAnswerList
				.filter(item => item.answer.answer != "" || (typeof item.answer.url != "undefined" && item.answer?.url != ""))
				.map(item => item.index)
		},
		...mapState('professionalConfig', ['currentTypeSet'])
	},
	onLoad(options) {
		typeof options.forbidToQuestion !== 'undefined' && (this.forbidToQuestion = options.forbidToQuestion)
	},
	methods: {
		back() {
			//uni.navigateBack()
			// uni.redirectTo({
			// 	url:"/subpkg/allquestion/allquestion"
			// })
			uni.showModal({
				title: '提示',
				content: '返回上一页， 将会丢失当前答题进度',
				showCancel: true,
				success: (res) => {
					console.log(res)
					if (res.confirm) {
						this.$store.commit("professionalConfig/enterCurrentStartPage")
					}
				}
			});
		},
		//跳转进度，当前科目信息
		toTemplate(item, index) {
			//判断是否禁止跳转
			if (this.forbidToQuestion == 1) {
				uni.showToast({
					title: '本次考试时间已用完，请提交答题卡！',
					icon: 'none',
				})
				return;
			}
			//设置当前题目序号
			this.setCurrentIndex(index)
			// 跳转答题卡
			if (this.currentAnswerStatus.isSubmit == true) {
				selectTemplateAnswer(item)
			} else {
				if (typeof this.$store.state.professionalConfig.currentAnswerList[index]?.submitTime != "undefined") {
					return uni.alert('之前做题记录在解析界面查看')
				}

				selectTemplate(item)
			}
		},
		async submit() {
			//已经批改完成
			if (this.correctionCompleted == 1) {
				return uni.redirectTo({
					url: `/subpkg/examination_report/examination_report?report_id=${this.currentType.answer_records.report_id}&hideBtn=1`
				})
			}

			if (this.isFromRecordsList) {
				return uni.redirectTo({
					url: `/subpkg/examination_report/examination_report?report_id=${this.currentType.report_id}&hideBtn=1`
				})
			}

			//模拟试卷 直接进入答案
			if (this.currentType.type == 2) {
				//重置答题索引
				this.$store.commit("professionalConfig/setCurrentIndex", 0)
				uni.showModal({
					title: '提示',
					content: '提交后通知老师批改',
					success: async (res) => {
						if (res.confirm) {
							// 先生成报告
							try {
								const ret = await this.addRecordsData()
								if (!ret || ret === false) {
									return uni.showToast({
										title: '提交失败，请重试',
										icon: 'none'
									})
								}

								// 通知老师批改
								const notifyRes = await sendTaskSubmitTemplateMessage({
									report_id: ret.report_id
								})
								console.log(notifyRes)
								// 保存 report_id 到状态管理
								this.$store.commit("professionalConfig/setReportId", ret.report_id)
								if (!notifyRes) {
									return uni.showToast({
										title: '通知老师失败，请重试',
										icon: 'none'
									})
								}

								// 提示用户
								uni.showModal({
									title: "提示",
									content: "已通知老师批改，是否查看答案解析？",
									cancelText: "返回列表",
									confirmText: "查看答案",
									success: (res) => {
										if (res.confirm) {
											// 查看答案
											selectTemplateAnswer(this.currentTypes[this.currentIndex], false)
										} else {
											this.$store.commit("professionalConfig/enterCurrentStartPage")
										}
									}
								})
							} catch (error) {
								console.error('提交错误:', error)
								uni.showToast({
									title: '操作失败，请重试',
									icon: 'none'
								})
							}
						}
					}
				});
			} else {
				//判断当前全部是客观题
				const result = this.currentTypes.every(item => subjectTypeArr.includes(item.qtype))
				//含有主观题不生成答题报告直接到 答案
				if (result == false) {
					//重置答题索引
					this.$store.commit("professionalConfig/setCurrentIndex", 0)
					const ret = await this.addRecordsData()

					selectTemplateAnswer(this.currentTypes[this.currentIndex], false)
					return;
				}
				const ret = await this.addRecordsData()
				if (ret != false) {
					uni.redirectTo({
						url: `/subpkg/examination_report/examination_report?report_id=${ret.report_id}`
					})
				}

			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: $container-bg-color;

	.center {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 28rpx;
		color: #fff;
	}

	.tag-flag {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;

		.cricle-wraper {
			width: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.cricle-undo {
				width: 20rpx;
				height: 20rpx;
				background-color: #b4b3b1;
				border-radius: 50%;
			}

			.cricle-done {
				width: 20rpx;
				height: 20rpx;
				background-color: #08AB8A;
				border-radius: 50%;
			}

			.tip {
				padding: 0 10rpx;
				font-size: 20rpx;
			}
		}

	}

	.list {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-content: space-between;
		padding: 0 36rpx;
		padding-bottom: 120rpx;

		.item-container {
			width: 20%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 20rpx 0;

			.item {
				background-color: #b4b3b1;
				color: #fff;
				height: 74rpx;
				width: 74rpx;
				border-radius: 50%;
				line-height: 74rpx;
				text-align: center;
				font-size: 28rpx;
			}

			.item-sel {
				background-color: $main-color;
			}

		}
	}
}

.btn-container {
	box-sizing: border-box;
	position: fixed;
	bottom: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;

	.btn {
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient(268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
		color: #fff;
		line-height: 80rpx;
		text-align: center;
	}
}
</style>