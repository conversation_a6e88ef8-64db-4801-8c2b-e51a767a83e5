!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.COS=t():e.COS=t()}(window,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/Users/<USER>/Documents/projects/cos-sdk/cos-wx-sdk-v5/demo/lib",r(r.s=7)}([function(e,t,r){"use strict";function n(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){c=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(c)throw o}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",p=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var i=t&&t.prototype instanceof x?t:x,o=Object.create(i.prototype),s=new N(n||[]);return a(o,"_invoke",{value:A(e,r,s)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function x(){}function b(){}function k(){}var w={};u(w,c,(function(){return this}));var C=Object.getPrototypeOf,S=C&&C(C(D([])));S&&S!==r&&n.call(S,c)&&(w=S);var T=k.prototype=x.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function R(e,t){function r(a,i,o,c){var l=f(e[a],e,i);if("throw"!==l.type){var p=l.arg,u=p.value;return u&&"object"==s(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,o,c)}),(function(e){r("throw",e,o,c)})):t.resolve(u).then((function(e){p.value=e,o(p)}),(function(e){return r("throw",e,o,c)}))}c(l.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function A(t,r,n){var a=m;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=B(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=f(t,r,n);if("normal"===l.type){if(a=n.done?v:h,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function B(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(s(t)+" is not iterable")}return b.prototype=k,a(T,"constructor",{value:k,configurable:!0}),a(k,"constructor",{value:b,configurable:!0}),b.displayName=u(k,p,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,u(e,p,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},E(R.prototype),u(R.prototype,l,(function(){return this})),t.AsyncIterator=R,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new R(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(T),u(T,p,"Generator"),u(T,c,(function(){return this})),u(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function o(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}var c=r(9),l=r(12),p=r(13),u=r(18),d=r(19),f=d.btoa,m=wx.getFileSystemManager(),h=r(3);function g(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function v(e,t){var r=[];for(var n in e)e.hasOwnProperty(n)&&r.push(t?g(n).toLowerCase():n);return r.sort((function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1}))}var y=["cache-control","content-disposition","content-encoding","content-length","content-md5","expect","expires","host","if-match","if-modified-since","if-none-match","if-unmodified-since","origin","range","transfer-encoding","pic-operations"],x=function(){},b=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&void 0!==e[r]&&null!==e[r]&&(t[r]=e[r]);return t};function k(e){return T(e,(function(e){return"object"===s(e)&&null!==e?k(e):e}))}function w(e,t){return S(t,(function(r,n){e[n]=t[n]})),e}function C(e){return e instanceof Array}function S(e,t){for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)}function T(e,t){var r=C(e)?[]:{};for(var n in e)e.hasOwnProperty(n)&&(r[n]=t(e[n],n));return r}var E=function(e,t){if(t=w({},t),"getAuth"!==e&&"getV4Auth"!==e&&"getObjectUrl"!==e){var r=t.Headers||{};if(t&&"object"===s(t)){!function(){for(var e in t)t.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(r[e]=t[e])}();N.each({"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-traffic-limit":"TrafficLimit","x-cos-mime-limit":"MimeLimit","x-cos-forbid-overwrite":"ForbidOverwrite","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext","Pic-Operations":"PicOperations"},(function(e,n){void 0!==t[e]&&(r[n]=t[e])})),t.Headers=b(r)}}return t},R=function(e){return new Promise((function(t,r){m.readFile({filePath:e,success:function(e){t(e.data)},fail:function(e){r((null==e?void 0:e.errMsg)||"")}})}))},A=function(){var e,t=(e=i().mark((function e(t,r,n){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("postObject"!==t){e.next=4;break}n(),e.next=21;break;case 4:if("putObject"!==t){e.next=20;break}if(void 0!==r.Body||!r.FilePath){e.next=17;break}return e.prev=6,e.next=9,R(r.FilePath);case 9:r.Body=e.sent,e.next=17;break;case 12:return e.prev=12,e.t0=e.catch(6),r.Body=void 0,n({error:"readFile error, ".concat(e.t0)}),e.abrupt("return");case 17:void 0!==r.Body?(r.ContentLength=r.Body.byteLength,n(null,r.ContentLength)):n({error:"missing param Body"}),e.next=21;break;case 20:r.FilePath?m.stat({path:r.FilePath,success:function(e){var t=e.stats;r.FileStat=t,r.FileStat.FilePath=r.FilePath;var a=t.isDirectory()?0:t.size;r.ContentLength=a=a||0,n(null,a)},fail:function(e){n(e)}}):n({error:"missing param FilePath"});case 21:case"end":return e.stop()}}),e,null,[[6,12]])})),function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function s(e){o(i,n,a,s,c,"next",e)}function c(e){o(i,n,a,s,c,"throw",e)}s(void 0)}))});return function(e,r,n){return t.apply(this,arguments)}}(),B=function(e){return Date.now()+(e||0)},_=function(e,t){e=e.split("."),t=t.split(".");for(var r=Math.max(e.length,t.length);e.length<r;)e.push("0");for(;t.length<r;)t.push("0");for(var n=0;n<r;n++){var a=parseInt(e[n]),i=parseInt(t[n]);if(a>i)return 1;if(a<i)return-1}return 0},O=function(){var e=wx.getSystemInfoSync(),t=_(e.SDKVersion,"2.10.0")>=0;!t&&e.platform;return function(){return!1,t}}(),N={noop:x,formatParams:E,apiWrapper:function(e,t){return function(r,n){var a,i=this;if("function"==typeof r&&(n=r,r={}),r=E(e,r),i.options.EnableReporter)if("sliceUploadFile"===r.calledBySdk||"sliceCopyFile"===r.calledBySdk)a=r.tracker&&r.tracker.generateSubTracker({apiName:e});else if(["uploadFile","uploadFiles"].includes(e))a=null;else{var o=0;r.Body&&(o="string"==typeof r.Body?r.Body.length:r.Body.size||r.Body.byteLength||0);var s=i.options.UseAccelerate||"string"==typeof i.options.Domain&&i.options.Domain.includes("accelerate.");a=new h({Beacon:i.options.BeaconReporter,clsReporter:i.options.ClsReporter,bucket:r.Bucket,region:r.Region,apiName:e,realApi:e,accelerate:s,fileKey:r.Key,fileSize:o,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})}r.tracker=a;var c=function(e){return e&&e.headers&&(e.headers["x-cos-request-id"]&&(e.RequestId=e.headers["x-cos-request-id"]),e.headers["x-ci-request-id"]&&(e.RequestId=e.headers["x-ci-request-id"]),e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},l=function(e,t){a&&a.report(e,t),n&&n(c(e),c(t))},p=function(){if("getService"!==e&&"abortUploadTask"!==e){var t=function(e,t){var r=t.Bucket,n=t.Region,a=t.Key;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(!r)return"Bucket";if(!n)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e||"uploadFile"===e){if(!r)return"Bucket";if(!n)return"Region";if(!a)return"Key"}return!1}(e,r);if(t)return"missing param "+t;if(r.Region){if(r.Region.indexOf("cos.")>-1)return'param Region should not be start with "cos."';if(!/^([a-z\d-]+)$/.test(r.Region))return"Region format error.";!i.options.CompatibilityMode&&-1===r.Region.indexOf("-")&&"yfb"!==r.Region&&"default"!==r.Region&&r.Region}if(r.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(r.Bucket))if(r.AppId)r.Bucket=r.Bucket+"-"+r.AppId;else{if(!i.options.AppId)return'Bucket should format as "test-1250000000".';r.Bucket=r.Bucket+"-"+i.options.AppId}r.AppId&&delete r.AppId}r.Key&&"/"===r.Key.substr(0,1)&&(r.Key=r.Key.substr(1))}}(),u=["getAuth","getObjectUrl"].includes(e);if(!u&&!n)return new Promise((function(e,a){if(n=function(t,r){t?a(t):e(r)},p)return l({error:p});t.call(i,r,l)}));if(p)return l({error:p});var d=t.call(i,r,l);return u?d:void 0}},xml2json:p,json2xml:u,md5:c,clearKey:b,fileSlice:function(e,t,r,n){e?m.readFile({filePath:e,position:t,length:r-t,success:function(e){n(e.data)},fail:function(){n(null)}}):n(null)},getBodyMd5:function(e,t,r){r=r||x,e&&t&&t instanceof ArrayBuffer?N.getFileMd5(t,(function(e,t){r(t)})):r()},getFileMd5:function(e,t){var r=c(e);return t&&t(r),r},binaryBase64:function(e){var t,r,n,a="";for(t=0,r=e.length/2;t<r;t++)n=parseInt(e[2*t]+e[2*t+1],16),a+=String.fromCharCode(n);return f(a)},extend:w,isArray:C,isInArray:function(e,t){for(var r=!1,n=0;n<e.length;n++)if(t===e[n]){r=!0;break}return r},makeArray:function(e){return C(e)?e:[e]},each:S,map:T,filter:function(e,t){var r=C(e),n=r?[]:{};for(var a in e)e.hasOwnProperty(a)&&t(e[a],a)&&(r?n.push(e[a]):n[a]=e[a]);return n},clone:k,attr:function(e,t,r){return e&&t in e?e[t]:r},uuid:function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},camSafeUrlEncode:g,throttleOnProgress:function(e,t){var r,n,a=this,i=0,o=0,s=Date.now();function c(){if(n=0,t&&"function"==typeof t){r=Date.now();var a,c=Math.max(0,Math.round((o-i)/((r-s)/1e3)*100)/100)||0;a=0===o&&0===e?1:Math.floor(o/e*100)/100||0,s=r,i=o;try{t({loaded:o,total:e,speed:c,percent:a})}catch(e){}}}return function(t,r){if(t&&(o=t.loaded,e=t.total),r)clearTimeout(n),c();else{if(n)return;n=setTimeout(c,a.options.ProgressInterval)}}},getFileSize:A,getFileSizeByPath:function(e){return new Promise((function(t,r){m.stat({path:e,success:function(e){var r=e.stats,n=r.isDirectory()?0:r.size;t(n)},fail:function(e){r((null==e?void 0:e.errMsg)||"")}})}))},getSkewTime:B,obj2str:function(e,t){var r,n,a,i=[],o=v(e);for(r=0;r<o.length;r++)a=void 0===e[n=o[r]]||null===e[n]?"":""+e[n],n=t?g(n).toLowerCase():g(n),a=g(a)||"",i.push(n+"="+a);return i.join("&")},getAuth:function(e){var t,r=(e=e||{}).SecretId,n=e.SecretKey,a=e.KeyTime,i=(e.method||e.Method||"get").toLowerCase(),o=k(e.Query||e.params||{}),s=function(e){var t={};for(var r in e){var n=r.toLowerCase();(n.indexOf("x-cos-")>-1||y.indexOf(n)>-1)&&(t[r]=e[r])}return t}(k(e.Headers||e.headers||{})),c=e.Key||"";e.UseRawKey?t=e.Pathname||e.pathname||"/"+c:0!==(t=e.Pathname||e.pathname||c).indexOf("/")&&(t="/"+t);var p=!1!==e.ForceSignHost;if(!s.Host&&!s.host&&e.Bucket&&e.Region&&p&&(s.Host=e.Bucket+".cos."+e.Region+".myqcloud.com"),r&&n){var u=Math.round(B(e.SystemClockOffset)/1e3)-1,d=u,f=e.Expires||e.expires;d+=void 0===f?900:1*f||0;var m=r,h=a||u+";"+d,g=a||u+";"+d,x=v(s,!0).join(";").toLowerCase(),b=v(o,!0).join(";").toLowerCase(),w=l.HmacSHA1(g,n).toString(),C=[i,t,N.obj2str(o,!0),N.obj2str(s,!0),""].join("\n"),S=["sha1",h,l.SHA1(C).toString(),""].join("\n");return["q-sign-algorithm=sha1","q-ak="+m,"q-sign-time="+h,"q-key-time="+g,"q-header-list="+x,"q-url-param-list="+b,"q-signature="+l.HmacSHA1(S,w).toString()].join("&")}},compareVersion:_,canFileSlice:O,isCIHost:function(e){return/^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(e)},error:function(e,t){var r=e;return e.message=e.message||null,"string"==typeof t?(e.error=t,e.message=t):"object"===s(t)&&null!==t&&(w(e,t),(t.code||t.name)&&(e.code=t.code||t.name),t.message&&(e.message=t.message),t.stack&&(e.stack=t.stack)),"function"==typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=t&&t.name||e.name||e.code||"Error",e.code||(e.code=e.name),e.error||(e.error=k(r)),e},getSourceParams:function(e){var t=this.options.CopySourceParser;if(t)return t(e);var r=e.match(/^([^.]+-\d+)\.cos(v6|-cdc|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);return r?{Bucket:r[1],Region:r[3],Key:r[7]}:null},encodeBase64:function(e,t){var r=d.encode(e);return t&&(r=r.replaceAll("+","-").replaceAll("/","_").replaceAll("=","")),r},simplifyPath:function(e){var t,r=[],a=n(e.split("/"));try{for(a.s();!(t=a.n()).done;){var i=t.value;".."===i?r.length&&r.pop():i.length&&"."!==i&&r.push(i)}}catch(e){a.e(e)}finally{a.f()}return"/"+r.join("/")},arrayBufferToString:function(e){return new TextDecoder("utf-8").decode(e)},parseResBody:function(e){var t;if(e&&"string"==typeof e){var r=e.trim(),n=0===r.indexOf("<"),a=0===r.indexOf("{");if(n)t=N.xml2json(e)||{};else if(a)try{var i=e.replace(/\n/g," "),o=JSON.parse(i);t="[object Object]"===Object.prototype.toString.call(o)?o:e}catch(r){t=e}else t=e}else t=e||{};return t}};e.exports=N},function(e,t,r){"use strict";function n(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.freeze?t.freeze(e):e}var a=n({HTML:"text/html",isHTML:function(e){return e===a.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),i=n({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===i.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var n=0;n<e.length;n++)if(Object.prototype.hasOwnProperty.call(e,n)){var a=e[n];if(t.call(void 0,a,n,e))return a}},t.freeze=n,t.MIME_TYPE=a,t.NAMESPACE=i},function(e,t,r){var n=r(1),a=n.find,i=n.NAMESPACE;function o(e){return""!==e}function s(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function c(e){if(!e)return[];var t=function(e){return e?e.split(/[\t\n\f\r ]+/).filter(o):[]}(e);return Object.keys(t.reduce(s,{}))}function l(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function p(e,t){var r=e.prototype;if(!(r instanceof t)){function n(){}n.prototype=t.prototype,l(r,n=new n),e.prototype=r=n}r.constructor!=e&&(r.constructor=e)}var u={},d=u.ELEMENT_NODE=1,f=u.ATTRIBUTE_NODE=2,m=u.TEXT_NODE=3,h=u.CDATA_SECTION_NODE=4,g=u.ENTITY_REFERENCE_NODE=5,v=u.ENTITY_NODE=6,y=u.PROCESSING_INSTRUCTION_NODE=7,x=u.COMMENT_NODE=8,b=u.DOCUMENT_NODE=9,k=u.DOCUMENT_TYPE_NODE=10,w=u.DOCUMENT_FRAGMENT_NODE=11,C=u.NOTATION_NODE=12,S={},T={},E=(S.INDEX_SIZE_ERR=(T[1]="Index size error",1),S.DOMSTRING_SIZE_ERR=(T[2]="DOMString size error",2),S.HIERARCHY_REQUEST_ERR=(T[3]="Hierarchy request error",3)),R=(S.WRONG_DOCUMENT_ERR=(T[4]="Wrong document",4),S.INVALID_CHARACTER_ERR=(T[5]="Invalid character",5),S.NO_DATA_ALLOWED_ERR=(T[6]="No data allowed",6),S.NO_MODIFICATION_ALLOWED_ERR=(T[7]="No modification allowed",7),S.NOT_FOUND_ERR=(T[8]="Not found",8)),A=(S.NOT_SUPPORTED_ERR=(T[9]="Not supported",9),S.INUSE_ATTRIBUTE_ERR=(T[10]="Attribute in use",10));S.INVALID_STATE_ERR=(T[11]="Invalid state",11),S.SYNTAX_ERR=(T[12]="Syntax error",12),S.INVALID_MODIFICATION_ERR=(T[13]="Invalid modification",13),S.NAMESPACE_ERR=(T[14]="Invalid namespace",14),S.INVALID_ACCESS_ERR=(T[15]="Invalid access",15);function B(e,t){if(t instanceof Error)var r=t;else r=this,Error.call(this,T[e]),this.message=T[e],Error.captureStackTrace&&Error.captureStackTrace(this,B);return r.code=e,t&&(this.message=this.message+": "+t),r}function _(){}function O(e,t){this._node=e,this._refresh=t,N(this)}function N(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==t){var r=e._refresh(e._node);if(xe(e,"length",r.length),!e.$$length||r.length<e.$$length)for(var n=r.length;n in e;n++)Object.prototype.hasOwnProperty.call(e,n)&&delete e[n];l(r,e),e._inc=t}}function D(){}function P(e,t){for(var r=e.length;r--;)if(e[r]===t)return r}function I(e,t,r,n){if(n?t[P(t,n)]=r:t[t.length++]=r,e){r.ownerElement=e;var a=e.ownerDocument;a&&(n&&z(a,e,n),function(e,t,r){e&&e._inc++;var n=r.namespaceURI;n===i.XMLNS&&(t._nsMap[r.prefix?r.localName:""]=r.value)}(a,e,r))}}function L(e,t,r){var n=P(t,r);if(!(n>=0))throw new B(R,new Error(e.tagName+"@"+r));for(var a=t.length-1;n<a;)t[n]=t[++n];if(t.length=a,e){var i=e.ownerDocument;i&&(z(i,e,r),r.ownerElement=null)}}function j(){}function U(){}function q(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function M(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(M(e,t))return!0}while(e=e.nextSibling)}function F(){this.ownerDocument=this}function z(e,t,r,n){e&&e._inc++,r.namespaceURI===i.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function H(e,t,r){if(e&&e._inc){e._inc++;var n=t.childNodes;if(r)n[n.length++]=r;else{for(var a=t.firstChild,i=0;a;)n[i++]=a,a=a.nextSibling;n.length=i,delete n[n.length]}}}function K(e,t){var r=t.previousSibling,n=t.nextSibling;return r?r.nextSibling=n:e.firstChild=n,n?n.previousSibling=r:e.lastChild=r,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,H(e.ownerDocument,e),t}function G(e){return e&&e.nodeType===U.DOCUMENT_TYPE_NODE}function V(e){return e&&e.nodeType===U.ELEMENT_NODE}function W(e){return e&&e.nodeType===U.TEXT_NODE}function X(e,t){var r=e.childNodes||[];if(a(r,V)||G(t))return!1;var n=a(r,G);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function Q(e,t){var r=e.childNodes||[];if(a(r,(function(e){return V(e)&&e!==t})))return!1;var n=a(r,G);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function $(e,t,r){if(!function(e){return e&&(e.nodeType===U.DOCUMENT_NODE||e.nodeType===U.DOCUMENT_FRAGMENT_NODE||e.nodeType===U.ELEMENT_NODE)}(e))throw new B(E,"Unexpected parent node type "+e.nodeType);if(r&&r.parentNode!==e)throw new B(R,"child not in parent");if(!function(e){return e&&(V(e)||W(e)||G(e)||e.nodeType===U.DOCUMENT_FRAGMENT_NODE||e.nodeType===U.COMMENT_NODE||e.nodeType===U.PROCESSING_INSTRUCTION_NODE)}(t)||G(t)&&e.nodeType!==U.DOCUMENT_NODE)throw new B(E,"Unexpected node type "+t.nodeType+" for parent node type "+e.nodeType)}function J(e,t,r){var n=e.childNodes||[],i=t.childNodes||[];if(t.nodeType===U.DOCUMENT_FRAGMENT_NODE){var o=i.filter(V);if(o.length>1||a(i,W))throw new B(E,"More than one element or text in fragment");if(1===o.length&&!X(e,r))throw new B(E,"Element in fragment can not be inserted before doctype")}if(V(t)&&!X(e,r))throw new B(E,"Only one element can be added and only after doctype");if(G(t)){if(a(n,G))throw new B(E,"Only one doctype is allowed");var s=a(n,V);if(r&&n.indexOf(s)<n.indexOf(r))throw new B(E,"Doctype can only be inserted before an element");if(!r&&s)throw new B(E,"Doctype can not be appended since element is present")}}function Y(e,t,r){var n=e.childNodes||[],i=t.childNodes||[];if(t.nodeType===U.DOCUMENT_FRAGMENT_NODE){var o=i.filter(V);if(o.length>1||a(i,W))throw new B(E,"More than one element or text in fragment");if(1===o.length&&!Q(e,r))throw new B(E,"Element in fragment can not be inserted before doctype")}if(V(t)&&!Q(e,r))throw new B(E,"Only one element can be added and only after doctype");if(G(t)){if(a(n,(function(e){return G(e)&&e!==r})))throw new B(E,"Only one doctype is allowed");var s=a(n,V);if(r&&n.indexOf(s)<n.indexOf(r))throw new B(E,"Doctype can only be inserted before an element")}}function Z(e,t,r,n){$(e,t,r),e.nodeType===U.DOCUMENT_NODE&&(n||J)(e,t,r);var a=t.parentNode;if(a&&a.removeChild(t),t.nodeType===w){var i=t.firstChild;if(null==i)return t;var o=t.lastChild}else i=o=t;var s=r?r.previousSibling:e.lastChild;i.previousSibling=s,o.nextSibling=r,s?s.nextSibling=i:e.firstChild=i,null==r?e.lastChild=o:r.previousSibling=o;do{i.parentNode=e}while(i!==o&&(i=i.nextSibling));return H(e.ownerDocument||e,e),t.nodeType==w&&(t.firstChild=t.lastChild=null),t}function ee(){this._nsMap={}}function te(){}function re(){}function ne(){}function ae(){}function ie(){}function oe(){}function se(){}function ce(){}function le(){}function pe(){}function ue(){}function de(){}function fe(e,t){var r=[],n=9==this.nodeType&&this.documentElement||this,a=n.prefix,i=n.namespaceURI;if(i&&null==a&&null==(a=n.lookupPrefix(i)))var o=[{namespace:i,prefix:null}];return ge(this,r,e,t,o),r.join("")}function me(e,t,r){var n=e.prefix||"",a=e.namespaceURI;if(!a)return!1;if("xml"===n&&a===i.XML||a===i.XMLNS)return!1;for(var o=r.length;o--;){var s=r[o];if(s.prefix===n)return s.namespace!==a}return!0}function he(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,q),'"')}function ge(e,t,r,n,a){if(a||(a=[]),n){if(!(e=n(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case d:var o=e.attributes,s=o.length,c=e.firstChild,l=e.tagName,p=l;if(!(r=i.isHTML(e.namespaceURI)||r)&&!e.prefix&&e.namespaceURI){for(var u,v=0;v<o.length;v++)if("xmlns"===o.item(v).name){u=o.item(v).value;break}if(!u)for(var C=a.length-1;C>=0;C--){if(""===(S=a[C]).prefix&&S.namespace===e.namespaceURI){u=S.namespace;break}}if(u!==e.namespaceURI)for(C=a.length-1;C>=0;C--){var S;if((S=a[C]).namespace===e.namespaceURI){S.prefix&&(p=S.prefix+":"+l);break}}}t.push("<",p);for(var T=0;T<s;T++){"xmlns"==(E=o.item(T)).prefix?a.push({prefix:E.localName,namespace:E.value}):"xmlns"==E.nodeName&&a.push({prefix:"",namespace:E.value})}for(T=0;T<s;T++){var E,R,A;if(me(E=o.item(T),0,a))he(t,(R=E.prefix||"")?"xmlns:"+R:"xmlns",A=E.namespaceURI),a.push({prefix:R,namespace:A});ge(E,t,r,n,a)}if(l===p&&me(e,0,a))he(t,(R=e.prefix||"")?"xmlns:"+R:"xmlns",A=e.namespaceURI),a.push({prefix:R,namespace:A});if(c||r&&!/^(?:meta|link|img|br|hr|input)$/i.test(l)){if(t.push(">"),r&&/^script$/i.test(l))for(;c;)c.data?t.push(c.data):ge(c,t,r,n,a.slice()),c=c.nextSibling;else for(;c;)ge(c,t,r,n,a.slice()),c=c.nextSibling;t.push("</",p,">")}else t.push("/>");return;case b:case w:for(c=e.firstChild;c;)ge(c,t,r,n,a.slice()),c=c.nextSibling;return;case f:return he(t,e.name,e.value);case m:return t.push(e.data.replace(/[<&>]/g,q));case h:return t.push("<![CDATA[",e.data,"]]>");case x:return t.push("\x3c!--",e.data,"--\x3e");case k:var B=e.publicId,_=e.systemId;if(t.push("<!DOCTYPE ",e.name),B)t.push(" PUBLIC ",B),_&&"."!=_&&t.push(" ",_),t.push(">");else if(_&&"."!=_)t.push(" SYSTEM ",_,">");else{var O=e.internalSubset;O&&t.push(" [",O,"]"),t.push(">")}return;case y:return t.push("<?",e.target," ",e.data,"?>");case g:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function ve(e,t,r){var n;switch(t.nodeType){case d:(n=t.cloneNode(!1)).ownerDocument=e;case w:break;case f:r=!0}if(n||(n=t.cloneNode(!1)),n.ownerDocument=e,n.parentNode=null,r)for(var a=t.firstChild;a;)n.appendChild(ve(e,a,r)),a=a.nextSibling;return n}function ye(e,t,r){var n=new t.constructor;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var i=t[a];"object"!=typeof i&&i!=n[a]&&(n[a]=i)}switch(t.childNodes&&(n.childNodes=new _),n.ownerDocument=e,n.nodeType){case d:var o=t.attributes,s=n.attributes=new D,c=o.length;s._ownerElement=n;for(var l=0;l<c;l++)n.setAttributeNode(ye(e,o.item(l),!0));break;case f:r=!0}if(r)for(var p=t.firstChild;p;)n.appendChild(ye(e,p,r)),p=p.nextSibling;return n}function xe(e,t,r){e[t]=r}B.prototype=Error.prototype,l(S,B),_.prototype={length:0,item:function(e){return e>=0&&e<this.length?this[e]:null},toString:function(e,t){for(var r=[],n=0;n<this.length;n++)ge(this[n],r,e,t);return r.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},O.prototype.item=function(e){return N(this),this[e]||null},p(O,_),D.prototype={length:0,item:_.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var r=this[t];if(r.nodeName==e)return r}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new B(A);var r=this.getNamedItem(e.nodeName);return I(this._ownerElement,this,e,r),r},setNamedItemNS:function(e){var t,r=e.ownerElement;if(r&&r!=this._ownerElement)throw new B(A);return t=this.getNamedItemNS(e.namespaceURI,e.localName),I(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return L(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var r=this.getNamedItemNS(e,t);return L(this._ownerElement,this,r),r},getNamedItemNS:function(e,t){for(var r=this.length;r--;){var n=this[r];if(n.localName==t&&n.namespaceURI==e)return n}return null}},j.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var n=new F;if(n.implementation=this,n.childNodes=new _,n.doctype=r||null,r&&n.appendChild(r),t){var a=n.createElementNS(e,t);n.appendChild(a)}return n},createDocumentType:function(e,t,r){var n=new oe;return n.name=e,n.nodeName=e,n.publicId=t||"",n.systemId=r||"",n}},U.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return Z(this,e,t)},replaceChild:function(e,t){Z(this,e,t,Y),t&&this.removeChild(t)},removeChild:function(e){return K(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return ye(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==m&&e.nodeType==m?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r)for(var n in r)if(Object.prototype.hasOwnProperty.call(r,n)&&r[n]===e)return n;t=t.nodeType==f?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];t=t.nodeType==f?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},l(u,U),l(u,U.prototype),F.prototype={nodeName:"#document",nodeType:b,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==w){for(var r=e.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,t),r=n}return e}return Z(this,e,t),e.ownerDocument=this,null===this.documentElement&&e.nodeType===d&&(this.documentElement=e),e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),K(this,e)},replaceChild:function(e,t){Z(this,e,t,Y),e.ownerDocument=this,t&&this.removeChild(t),V(e)&&(this.documentElement=e)},importNode:function(e,t){return ve(this,e,t)},getElementById:function(e){var t=null;return M(this.documentElement,(function(r){if(r.nodeType==d&&r.getAttribute("id")==e)return t=r,!0})),t},getElementsByClassName:function(e){var t=c(e);return new O(this,(function(r){var n=[];return t.length>0&&M(r.documentElement,(function(a){if(a!==r&&a.nodeType===d){var i=a.getAttribute("class");if(i){var o=e===i;if(!o){var s=c(i);o=t.every((l=s,function(e){return l&&-1!==l.indexOf(e)}))}o&&n.push(a)}}var l})),n}))},createElement:function(e){var t=new ee;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new _,(t.attributes=new D)._ownerElement=t,t},createDocumentFragment:function(){var e=new pe;return e.ownerDocument=this,e.childNodes=new _,e},createTextNode:function(e){var t=new ne;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new ae;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new ie;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new ue;return r.ownerDocument=this,r.tagName=r.nodeName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){var t=new te;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new le;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=new ee,n=t.split(":"),a=r.attributes=new D;return r.childNodes=new _,r.ownerDocument=this,r.nodeName=t,r.tagName=t,r.namespaceURI=e,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,a._ownerElement=r,r},createAttributeNS:function(e,t){var r=new te,n=t.split(":");return r.ownerDocument=this,r.nodeName=t,r.name=t,r.namespaceURI=e,r.specified=!0,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,r}},p(F,U),ee.prototype={nodeType:d,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var r=this.ownerDocument.createAttribute(e);r.value=r.nodeValue=""+t,this.setAttributeNode(r)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===w?this.insertBefore(e,null):function(e,t){return t.parentNode&&t.parentNode.removeChild(t),t.parentNode=e,t.previousSibling=e.lastChild,t.nextSibling=null,t.previousSibling?t.previousSibling.nextSibling=t:e.firstChild=t,e.lastChild=t,H(e.ownerDocument,e,t),t}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);r&&this.removeAttributeNode(r)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);return r&&r.value||""},setAttributeNS:function(e,t,r){var n=this.ownerDocument.createAttributeNS(e,t);n.value=n.nodeValue=""+r,this.setAttributeNode(n)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new O(this,(function(t){var r=[];return M(t,(function(n){n===t||n.nodeType!=d||"*"!==e&&n.tagName!=e||r.push(n)})),r}))},getElementsByTagNameNS:function(e,t){return new O(this,(function(r){var n=[];return M(r,(function(a){a===r||a.nodeType!==d||"*"!==e&&a.namespaceURI!==e||"*"!==t&&a.localName!=t||n.push(a)})),n}))}},F.prototype.getElementsByTagName=ee.prototype.getElementsByTagName,F.prototype.getElementsByTagNameNS=ee.prototype.getElementsByTagNameNS,p(ee,U),te.prototype.nodeType=f,p(te,U),re.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(T[E])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},p(re,U),ne.prototype={nodeName:"#text",nodeType:m,splitText:function(e){var t=this.data,r=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var n=this.ownerDocument.createTextNode(r);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},p(ne,re),ae.prototype={nodeName:"#comment",nodeType:x},p(ae,re),ie.prototype={nodeName:"#cdata-section",nodeType:h},p(ie,re),oe.prototype.nodeType=k,p(oe,U),se.prototype.nodeType=C,p(se,U),ce.prototype.nodeType=v,p(ce,U),le.prototype.nodeType=g,p(le,U),pe.prototype.nodeName="#document-fragment",pe.prototype.nodeType=w,p(pe,U),ue.prototype.nodeType=y,p(ue,U),de.prototype.serializeToString=function(e,t,r){return fe.call(e,t,r)},U.prototype.toString=fe;try{if(Object.defineProperty){function be(e){switch(e.nodeType){case d:case w:var t=[];for(e=e.firstChild;e;)7!==e.nodeType&&8!==e.nodeType&&t.push(be(e)),e=e.nextSibling;return t.join("");default:return e.nodeValue}}Object.defineProperty(O.prototype,"length",{get:function(){return N(this),this.$$length}}),Object.defineProperty(U.prototype,"textContent",{get:function(){return be(this)},set:function(e){switch(this.nodeType){case d:case w:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),xe=function(e,t,r){e["$$"+t]=r}}}catch(ke){}t.DocumentType=oe,t.DOMException=B,t.DOMImplementation=j,t.Element=ee,t.Node=U,t.NodeList=_,t.XMLSerializer=de},function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",p=s.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof x?t:x,i=Object.create(a.prototype),s=new N(n||[]);return o(i,"_invoke",{value:A(e,r,s)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function x(){}function b(){}function k(){}var w={};u(w,c,(function(){return this}));var C=Object.getPrototypeOf,S=C&&C(C(D([])));S&&S!==r&&i.call(S,c)&&(w=S);var T=k.prototype=x.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function R(e,t){function r(a,o,s,c){var l=f(e[a],e,o);if("throw"!==l.type){var p=l.arg,u=p.value;return u&&"object"==n(u)&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(u).then((function(e){p.value=e,s(p)}),(function(e){return r("throw",e,s,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,a){r(e,n,t,a)}))}return a=a?a.then(i,i):i()}})}function A(t,r,n){var a=m;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=B(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=f(t,r,n);if("normal"===l.type){if(a=n.done?v:h,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function B(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(i.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(n(t)+" is not iterable")}return b.prototype=k,o(T,"constructor",{value:k,configurable:!0}),o(k,"constructor",{value:b,configurable:!0}),b.displayName=u(k,p,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,u(e,p,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},E(R.prototype),u(R.prototype,l,(function(){return this})),t.AsyncIterator=R,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new R(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(T),u(T,p,"Generator"),u(T,c,(function(){return this})),u(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function i(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function s(e){i(o,n,a,s,c,"next",e)}function c(e){i(o,n,a,s,c,"throw",e)}s(void 0)}))}}function s(e,t,r){return(t=l(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l(n.key),n)}}function l(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}var p=r(4),u=null,d=function(e){return!e||e<0?0:(e/1e3).toFixed(3)},f=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},m=function(){return new Promise((function(e){if(wx.canIUse("getNetworkType"))try{wx.getNetworkType({success:function(t){e(t.networkType)}})}catch(t){e("can_not_get_network_type")}else e("can_not_get_network_type")}))},h={devicePlatform:"",wxVersion:"",wxSystem:"",wxSdkVersion:""};(function(){var e={devicePlatform:"",wxVersion:"",wxSystem:"",wxSdkVersion:""};return new Promise((function(t){if(wx.canIUse("getSystemInfo"))try{wx.getSystemInfo({success:function(r){var n=r.platform,a=r.version,i=r.system,o=r.SDKVersion;Object.assign(e,{devicePlatform:n,wxVersion:a,wxSystem:i,wxSdkVersion:o}),t(e)}})}catch(e){t({devicePlatform:"can_not_get_system_info",wxVersion:"can_not_get_system_info",wxSystem:"can_not_get_system_info",wxSdkVersion:"can_not_get_system_info"})}else t({devicePlatform:"can_not_get_system_info",wxVersion:"can_not_get_system_info",wxSystem:"can_not_get_system_info",wxSdkVersion:"can_not_get_system_info"})}))})().then((function(e){Object.assign(h,e)}));function g(e){return e.replace(/([A-Z])/g,"_$1").toLowerCase()}function v(e){var t,r={},n=["sdkVersionName","sdkVersionCode","osName","networkType","requestName","requestResult","bucket","region","appid","accelerate","url","host","requestPath","userAgent","httpMethod","httpSize","httpSpeed","httpTookTime","httpMd5","httpSign","httpFullTime","httpDomain","partNumber","httpRetryTimes","customId","traceId","realApi"],a=[].concat(n,["errorNode","errorCode","errorName","errorMessage","errorRequestId","errorHttpCode","errorServiceName","errorType","fullError"]),i="Success"===e.requestResult?n:a;for(var o in e){if(i.includes(o))r[g(o)]=e[o]}return r.request_name=e.realApi?(t=e.realApi,["putObject","sliceUploadFile","uploadFile","uploadFiles"].includes(t)?"UploadTask":"getObject"===t?"DownloadTask":["putObjectCopy","sliceCopyFile"].includes(t)?"CopyTask":t):e.requestName,r}var y=function(){function e(t){var r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=t.parent,a=t.traceId,i=t.bucket,o=t.region,c=t.apiName,l=t.realApi,d=t.httpMethod,m=t.fileKey,g=t.fileSize,v=t.accelerate,y=t.customId,x=t.delay,b=t.deepTracker,k=t.Beacon,w=t.clsReporter,C=i&&i.substr(i.lastIndexOf("-")+1)||"";this.parent=n,this.deepTracker=b,this.delay=x,w&&!this.clsReporter&&(this.clsReporter=w),this.params=(s(r={sdkVersionName:"cos-wx-sdk-v5",sdkVersionCode:p.version,osName:h.devicePlatform,networkType:"",requestName:c||"",requestResult:"",realApi:l,bucket:i,region:o,accelerate:v,httpMethod:d,url:"",host:"",httpDomain:"",requestPath:m||"",errorType:"",errorCode:"",errorName:"",errorMessage:"",errorRequestId:"",errorHttpCode:0,errorServiceName:"",errorNode:"",httpTookTime:0,httpSize:g||0,httpMd5:0,httpSign:0,httpFullTime:0,httpSpeed:0,size:g||0},"httpMd5",0),s(r,"httpSign",0),s(r,"httpFull",0),s(r,"name",c||""),s(r,"tookTime",0),s(r,"md5StartTime",0),s(r,"md5EndTime",0),s(r,"signStartTime",0),s(r,"signEndTime",0),s(r,"httpStartTime",0),s(r,"httpEndTime",0),s(r,"startTime",(new Date).getTime()),s(r,"endTime",0),s(r,"traceId",a||f()),s(r,"appid",C),s(r,"partNumber",0),s(r,"httpRetryTimes",0),s(r,"customId",y||""),s(r,"partTime",0),r),k&&(this.beacon=function(e,t){if(!u){if("function"!=typeof e)throw new Error("Beacon not found");u=new e({appkey:"0WEB05PY6MHRGK0U",versionCode:p.version,channelID:"mp_sdk",openid:"openid",unionid:"unid",strictMode:!1,delay:t,sessionDuration:6e4})}return u}(k,x))}var t,r,n,i,l;return t=e,r=[{key:"formatResult",value:(l=o(a().mark((function e(t,r){var n,i,o,s,c,l,p,u,f,h,g,v,y,x,b,k,w,C,S,T,E,R,A,B,_,O,N;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return h=(new Date).getTime(),e.next=3,m();case 3:if(g=e.sent,v=t?(null==t||null===(n=t.error)||void 0===n||null===(i=n.error)||void 0===i?void 0:i.Code)||"Error":"",y=t&&((null==t||null===(o=t.error)||void 0===o||null===(s=o.error)||void 0===s?void 0:s.Message)||(null==t||null===(c=t.error)||void 0===c?void 0:c.error)||(null==t?void 0:t.error))||"",x=y,b=t?null==t||null===(l=t.error)||void 0===l?void 0:l.statusCode:r.statusCode,k=t?null==t||null===(p=t.error)||void 0===p||null===(u=p.error)||void 0===u?void 0:u.Resource:"",w=t?(null==t||null===(f=t.error)||void 0===f?void 0:f.RequestId)||"":(null==r?void 0:r.RequestId)||"",C=t?w?"Server":"Client":"","getObject"===this.params.requestName&&(this.params.httpSize=r?r.headers&&r.headers["content-length"]:0),S="sliceUploadFile"===this.params.realApi,T="sliceCopyFile"===this.params.realApi,S||T?(E=this.params.httpSize/1024/this.params.partTime,Object.assign(this.params,{httpSpeed:E<0?0:E.toFixed(3)})):(R=h-this.params.startTime,A=this.params.httpEndTime-this.params.httpStartTime,B=this.params.httpSize/1024/(A/1e3),_=this.params.md5EndTime-this.params.md5StartTime,O=this.params.signEndTime-this.params.signStartTime,this.parent&&(this.parent.addParamValue("httpTookTime",d(A)),this.parent.addParamValue("httpFullTime",d(R)),this.parent.addParamValue("httpMd5",d(_)),this.parent.addParamValue("httpSign",d(O)),["multipartUpload","uploadPartCopy","putObjectCopy"].includes(this.params.requestName)&&this.parent.addParamValue("partTime",d(A))),Object.assign(this.params,{httpFullTime:d(R),httpMd5:d(_),httpSign:d(O),httpTookTime:d(A),httpSpeed:B<0?0:B.toFixed(3)})),Object.assign(this.params,{networkType:g,requestResult:t?"Failure":"Success",errorType:C,errorCode:v,errorHttpCode:b,errorName:x,errorMessage:y,errorServiceName:k,errorRequestId:w}),!t||v&&y||(this.params.fullError=t?JSON.stringify(t):""),"getObject"===this.params.name&&(this.params.size=r?r.headers&&r.headers["content-length"]:-1),this.params.url){try{N=/^http(s)?:\/\/(.*?)\//.exec(this.params.url),this.params.host=N[2]}catch(e){this.params.host=this.params.url}this.params.httpDomain=this.params.host}case 19:case"end":return e.stop()}}),e,this)}))),function(e,t){return l.apply(this,arguments)})},{key:"report",value:(i=o(a().mark((function e(t,r){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.beacon||this.clsReporter){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,this.formatResult(t,r);case 4:n=v(this.params),this.beacon&&this.sendEventsToBeacon(n),this.clsReporter&&this.sendEventsToCLS(n);case 7:case"end":return e.stop()}}),e,this)}))),function(e,t){return i.apply(this,arguments)})},{key:"setParams",value:function(e){Object.assign(this.params,e)}},{key:"addParamValue",value:function(e,t){this.params[e]=(+this.params[e]+ +t).toFixed(3)}},{key:"sendEventsToBeacon",value:function(e){if("sliceUploadFile"!==this.params.requestName&&"sliceUploadFile"!==this.params.realApi||this.deepTracker){var t="qcloud_track_cos_sdk";0===this.delay?this.beacon&&this.beacon.onDirectUserAction(t,e):this.beacon&&this.beacon.onUserAction(t,e)}}},{key:"sendEventsToCLS",value:function(e){var t=!(0!==this.delay);this.clsReporter.log(e,t)}},{key:"generateSubTracker",value:function(t){return Object.assign(t,{parent:this,deepTracker:this.deepTracker,traceId:this.params.traceId,bucket:this.params.bucket,region:this.params.region,accelerate:this.params.accelerate,fileKey:this.params.requestPath,customId:this.params.customId,delay:this.params.delay,clsReporter:this.clsReporter}),new e(t)}}],r&&c(t.prototype,r),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();e.exports=y},function(e){e.exports=JSON.parse('{"name":"cos-wx-sdk-v5","version":"1.7.1","description":"小程序 SDK for [腾讯云对象存储服务](https://cloud.tencent.com/product/cos)","main":"demo/lib/cos-wx-sdk-v5.min.js","scripts":{"prettier":"prettier --write src demo/demo-sdk.js demo/test.js demo/ciDemo","dev":"cross-env NODE_ENV=development node build.js --mode=development","build":"cross-env NODE_ENV=production node build.js --mode=production","sts.js":"node server/sts.js"},"repository":{"type":"git","url":"http://github.com/tencentyun/cos-wx-sdk-v5.git"},"author":"carsonxu","license":"ISC","dependencies":{"@xmldom/xmldom":"^0.8.6","mime":"^2.4.6"},"devDependencies":{"@babel/core":"7.17.9","@babel/preset-env":"7.16.11","babel-loader":"8.2.5","body-parser":"^1.18.3","cross-env":"^7.0.3","express":"^4.17.1","prettier":"^3.0.1","qcloud-cos-sts":"^3.0.2","terser-webpack-plugin":"4.2.3","webpack":"4.46.0","webpack-cli":"4.10.0"}}')},function(e,t){var r=function(e){var t={},r=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){r(e).push(t)},e.off=function(e,t){for(var n=r(e),a=n.length-1;a>=0;a--)t===n[a]&&n.splice(a,1)},e.emit=function(e,t){for(var n=r(e).map((function(e){return e})),a=0;a<n.length;a++)n[a](t)}};e.exports.init=r,e.exports.EventProxy=function(){r(this)}},function(e,t,r){var n,a,i=r(0),o="cos_sdk_upload_cache",s=function(){try{n.length?wx.setStorageSync(o,JSON.stringify(n)):wx.removeStorageSync(o)}catch(e){}},c=function(){if(!n){n=function(){try{var e=JSON.parse(wx.getStorageSync(o))}catch(e){}return e||(e=[]),e}();for(var e=!1,t=Math.round(Date.now()/1e3),r=n.length-1;r>=0;r--){var a=n[r][2];(!a||a+2592e3<t)&&(n.splice(r,1),e=!0)}e&&s()}},l=function(){a||(a=setTimeout((function(){s(),a=null}),400))},p={using:{},setUsing:function(e){p.using[e]=!0},removeUsing:function(e){delete p.using[e]},getFileId:function(e,t,r,n){return e.FilePath&&e.size&&e.lastModifiedTime&&t?i.md5([e.FilePath].join("::"))+"-"+i.md5([e.size,e.mode,e.lastAccessedTime,e.lastModifiedTime,t,r,n].join("::")):null},getCopyFileId:function(e,t,r,n,a){var o=t["content-length"],s=t.etag||"",c=t["last-modified"];return e&&r?i.md5([e,o,s,c,r,n,a].join("::")):null},getUploadIdList:function(e){if(!e)return null;c();for(var t=[],r=0;r<n.length;r++)n[r][0]===e&&t.push(n[r][1]);return t.length?t:null},saveUploadId:function(e,t,r){if(c(),e){for(var a=e.substr(0,e.indexOf("-")+1),i=n.length-1;i>=0;i--){var o=n[i];(o[0]===e&&o[1]===t||e!==o[0]&&0===o[0].indexOf(a))&&n.splice(i,1)}n.unshift([e,t,Math.round(Date.now()/1e3)]),n.length>r&&n.splice(r),l()}},removeUploadId:function(e){c(),delete p.using[e];for(var t=n.length-1;t>=0;t--)n[t][1]===e&&n.splice(t,1);l()}};e.exports=p},function(e,t,r){var n=r(8);e.exports=n},function(e,t,r){"use strict";var n=r(0),a=r(5),i=r(20),o=r(21),s=r(27),c=r(4),l={SecretId:"",SecretKey:"",SecurityToken:"",ChunkRetryTimes:2,FileParallelLimit:3,ChunkParallelLimit:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,MaxPartNumber:1e4,ProgressInterval:1e3,UploadQueueSize:1e4,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadAddMetaMd5:!1,UploadIdCacheLimit:50,UseAccelerate:!1,ForceSignHost:!0,HttpDNSServiceId:"",SimpleUploadMethod:"postObject",AutoSwitchHost:!1,CopySourceParser:null,ObjectKeySimplifyCheck:!0,DeepTracker:!1,TrackerDelay:5e3,CustomId:"",BeaconReporter:null,ClsReporter:null},p=function(e){if(this.options=n.extend(n.clone(l),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.EnableReporter=this.options.BeaconReporter||this.options.ClsReporter,this.options.AppId,this.options.SecretId&&this.options.SecretId.indexOf(" "),this.options.SecretKey&&this.options.SecretKey.indexOf(" "),this.options.ForcePathStyle)throw new Error("ForcePathStyle is not supported");a.init(this),i.init(this)};o.init(p,i),s.init(p,i),p.util={md5:n.md5,xml2json:n.xml2json,json2xml:n.json2xml,encodeBase64:n.encodeBase64},p.getAuthorization=n.getAuth,p.version=c.version,e.exports=p},function(e,t,r){(function(e){var t;function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}!function(){"use strict";var a="input is invalid type",i="object"===("undefined"==typeof window?"undefined":n(window)),o=i?window:{};o.JS_MD5_NO_WINDOW&&(i=!1),!i&&"object"===("undefined"==typeof self?"undefined":n(self))&&(o=self);var s,c=!o.JS_MD5_NO_COMMON_JS&&"object"===n(e)&&e.exports,l=r(11),p=!o.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,u="0123456789abcdef".split(""),d=[128,32768,8388608,-2147483648],f=[0,8,16,24],m=["hex","array","digest","buffer","arrayBuffer","base64"],h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),g=[];if(p){var v=new ArrayBuffer(68);s=new Uint8Array(v),g=new Uint32Array(v)}!o.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!p||!o.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"===n(e)&&e.buffer&&e.buffer.constructor===ArrayBuffer});var y=function(e){return function(t){return new x(!0).update(t)[e]()}};function x(e){if(e)g[0]=g[16]=g[1]=g[2]=g[3]=g[4]=g[5]=g[6]=g[7]=g[8]=g[9]=g[10]=g[11]=g[12]=g[13]=g[14]=g[15]=0,this.blocks=g,this.buffer8=s;else if(p){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}x.prototype.update=function(e){if(!this.finalized){var t,r=n(e);if("string"!==r){if("object"!==r)throw a;if(null===e)throw a;if(!p||e.constructor!==ArrayBuffer&&"ArrayBuffer"!==e.constructor.name){if(!(Array.isArray(e)||p&&ArrayBuffer.isView(e)))throw a}else e=new Uint8Array(e);t=!0}for(var i,o,s=0,c=e.length,l=this.blocks,u=this.buffer8;s<c;){if(this.hashed&&(this.hashed=!1,l[0]=l[16],l[16]=l[1]=l[2]=l[3]=l[4]=l[5]=l[6]=l[7]=l[8]=l[9]=l[10]=l[11]=l[12]=l[13]=l[14]=l[15]=0),t)if(p)for(o=this.start;s<c&&o<64;++s)u[o++]=e[s];else for(o=this.start;s<c&&o<64;++s)l[o>>2]|=e[s]<<f[3&o++];else if(p)for(o=this.start;s<c&&o<64;++s)(i=e.charCodeAt(s))<128?u[o++]=i:i<2048?(u[o++]=192|i>>6,u[o++]=128|63&i):i<55296||i>=57344?(u[o++]=224|i>>12,u[o++]=128|i>>6&63,u[o++]=128|63&i):(i=65536+((1023&i)<<10|1023&e.charCodeAt(++s)),u[o++]=240|i>>18,u[o++]=128|i>>12&63,u[o++]=128|i>>6&63,u[o++]=128|63&i);else for(o=this.start;s<c&&o<64;++s)(i=e.charCodeAt(s))<128?l[o>>2]|=i<<f[3&o++]:i<2048?(l[o>>2]|=(192|i>>6)<<f[3&o++],l[o>>2]|=(128|63&i)<<f[3&o++]):i<55296||i>=57344?(l[o>>2]|=(224|i>>12)<<f[3&o++],l[o>>2]|=(128|i>>6&63)<<f[3&o++],l[o>>2]|=(128|63&i)<<f[3&o++]):(i=65536+((1023&i)<<10|1023&e.charCodeAt(++s)),l[o>>2]|=(240|i>>18)<<f[3&o++],l[o>>2]|=(128|i>>12&63)<<f[3&o++],l[o>>2]|=(128|i>>6&63)<<f[3&o++],l[o>>2]|=(128|63&i)<<f[3&o++]);this.lastByteIndex=o,this.bytes+=o-this.start,o>=64?(this.start=o-64,this.hash(),this.hashed=!0):this.start=o}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},x.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=d[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},x.prototype.hash=function(){var e,t,r,n,a,i,o=this.blocks;this.first?t=((t=((e=((e=o[0]-680876937)<<7|e>>>25)-271733879<<0)^(r=((r=(-271733879^(n=((n=(-1732584194^2004318071&e)+o[1]-117830708)<<12|n>>>20)+e<<0)&(-271733879^e))+o[2]-1126478375)<<17|r>>>15)+n<<0)&(n^e))+o[3]-1316259209)<<22|t>>>10)+r<<0:(e=this.h0,t=this.h1,r=this.h2,t=((t+=((e=((e+=((n=this.h3)^t&(r^n))+o[0]-680876936)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+o[1]-389564586)<<12|n>>>20)+e<<0)&(e^t))+o[2]+606105819)<<17|r>>>15)+n<<0)&(n^e))+o[3]-1044525330)<<22|t>>>10)+r<<0),t=((t+=((e=((e+=(n^t&(r^n))+o[4]-176418897)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+o[5]+1200080426)<<12|n>>>20)+e<<0)&(e^t))+o[6]-1473231341)<<17|r>>>15)+n<<0)&(n^e))+o[7]-45705983)<<22|t>>>10)+r<<0,t=((t+=((e=((e+=(n^t&(r^n))+o[8]+1770035416)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+o[9]-1958414417)<<12|n>>>20)+e<<0)&(e^t))+o[10]-42063)<<17|r>>>15)+n<<0)&(n^e))+o[11]-1990404162)<<22|t>>>10)+r<<0,t=((t+=((e=((e+=(n^t&(r^n))+o[12]+1804603682)<<7|e>>>25)+t<<0)^(r=((r+=(t^(n=((n+=(r^e&(t^r))+o[13]-40341101)<<12|n>>>20)+e<<0)&(e^t))+o[14]-1502002290)<<17|r>>>15)+n<<0)&(n^e))+o[15]+1236535329)<<22|t>>>10)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+o[1]-165796510)<<5|e>>>27)+t<<0)^t))+o[6]-1069501632)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+o[11]+643717713)<<14|r>>>18)+n<<0)^n))+o[0]-373897302)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+o[5]-701558691)<<5|e>>>27)+t<<0)^t))+o[10]+38016083)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+o[15]-660478335)<<14|r>>>18)+n<<0)^n))+o[4]-405537848)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+o[9]+568446438)<<5|e>>>27)+t<<0)^t))+o[14]-1019803690)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+o[3]-187363961)<<14|r>>>18)+n<<0)^n))+o[8]+1163531501)<<20|t>>>12)+r<<0,t=((t+=((n=((n+=(t^r&((e=((e+=(r^n&(t^r))+o[13]-1444681467)<<5|e>>>27)+t<<0)^t))+o[2]-51403784)<<9|n>>>23)+e<<0)^e&((r=((r+=(e^t&(n^e))+o[7]+1735328473)<<14|r>>>18)+n<<0)^n))+o[12]-1926607734)<<20|t>>>12)+r<<0,t=((t+=((i=(n=((n+=((a=t^r)^(e=((e+=(a^n)+o[5]-378558)<<4|e>>>28)+t<<0))+o[8]-2022574463)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+o[11]+1839030562)<<16|r>>>16)+n<<0))+o[14]-35309556)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((a=t^r)^(e=((e+=(a^n)+o[1]-1530992060)<<4|e>>>28)+t<<0))+o[4]+1272893353)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+o[7]-155497632)<<16|r>>>16)+n<<0))+o[10]-1094730640)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((a=t^r)^(e=((e+=(a^n)+o[13]+681279174)<<4|e>>>28)+t<<0))+o[0]-358537222)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+o[3]-722521979)<<16|r>>>16)+n<<0))+o[6]+76029189)<<23|t>>>9)+r<<0,t=((t+=((i=(n=((n+=((a=t^r)^(e=((e+=(a^n)+o[9]-640364487)<<4|e>>>28)+t<<0))+o[12]-421815835)<<11|n>>>21)+e<<0)^e)^(r=((r+=(i^t)+o[15]+530742520)<<16|r>>>16)+n<<0))+o[2]-995338651)<<23|t>>>9)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+o[0]-198630844)<<6|e>>>26)+t<<0)|~r))+o[7]+1126891415)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+o[14]-1416354905)<<15|r>>>17)+n<<0)|~e))+o[5]-57434055)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+o[12]+1700485571)<<6|e>>>26)+t<<0)|~r))+o[3]-1894986606)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+o[10]-1051523)<<15|r>>>17)+n<<0)|~e))+o[1]-2054922799)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+o[8]+1873313359)<<6|e>>>26)+t<<0)|~r))+o[15]-30611744)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+o[6]-1560198380)<<15|r>>>17)+n<<0)|~e))+o[13]+1309151649)<<21|t>>>11)+r<<0,t=((t+=((n=((n+=(t^((e=((e+=(r^(t|~n))+o[4]-145523070)<<6|e>>>26)+t<<0)|~r))+o[11]-1120210379)<<10|n>>>22)+e<<0)^((r=((r+=(e^(n|~t))+o[2]+718787259)<<15|r>>>17)+n<<0)|~e))+o[9]-343485551)<<21|t>>>11)+r<<0,this.first?(this.h0=e+1732584193<<0,this.h1=t-271733879<<0,this.h2=r-1732584194<<0,this.h3=n+271733878<<0,this.first=!1):(this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+r<<0,this.h3=this.h3+n<<0)},x.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,n=this.h3;return u[e>>4&15]+u[15&e]+u[e>>12&15]+u[e>>8&15]+u[e>>20&15]+u[e>>16&15]+u[e>>28&15]+u[e>>24&15]+u[t>>4&15]+u[15&t]+u[t>>12&15]+u[t>>8&15]+u[t>>20&15]+u[t>>16&15]+u[t>>28&15]+u[t>>24&15]+u[r>>4&15]+u[15&r]+u[r>>12&15]+u[r>>8&15]+u[r>>20&15]+u[r>>16&15]+u[r>>28&15]+u[r>>24&15]+u[n>>4&15]+u[15&n]+u[n>>12&15]+u[n>>8&15]+u[n>>20&15]+u[n>>16&15]+u[n>>28&15]+u[n>>24&15]},x.prototype.toString=x.prototype.hex,x.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,n=this.h3;return[255&e,e>>8&255,e>>16&255,e>>24&255,255&t,t>>8&255,t>>16&255,t>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255]},x.prototype.array=x.prototype.digest,x.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},x.prototype.buffer=x.prototype.arrayBuffer,x.prototype.base64=function(){for(var e,t,r,n="",a=this.array(),i=0;i<15;)e=a[i++],t=a[i++],r=a[i++],n+=h[e>>>2]+h[63&(e<<4|t>>>4)]+h[63&(t<<2|r>>>6)]+h[63&r];return e=a[i],n+=h[e>>>2]+h[e<<4&63]+"=="};var b=function(){var e=y("hex");e.getCtx=e.create=function(){return new x},e.update=function(t){return e.create().update(t)};for(var t=0;t<m.length;++t){var r=m[t];e[r]=y(r)}return e}();c?e.exports=b:(o.md5=b,l&&(void 0===(t=function(){return b}.call(b,r,b,e))||(e.exports=t)))}()}).call(this,r(10)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t){var r,n,a,i,o,s,c,l=l||function(e,t){var r={},n=r.lib={},a=function(){},i=n.Base={extend:function(e){a.prototype=this;var t=new a;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=n.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes;if(e=e.sigBytes,this.clamp(),n%4)for(var a=0;a<e;a++)t[n+a>>>2]|=(r[a>>>2]>>>24-a%4*8&255)<<24-(n+a)%4*8;else if(65535<r.length)for(a=0;a<e;a+=4)t[n+a>>>2]=r[a>>>2];else t.push.apply(t,r);return this.sigBytes+=e,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var r=[],n=0;n<t;n+=4)r.push(4294967296*e.random()|0);return new o.init(r,t)}}),s=r.enc={},c=s.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var r=[],n=0;n<e;n++){var a=t[n>>>2]>>>24-n%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new o.init(r,t/2)}},l=s.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var r=[],n=0;n<e;n++)r.push(String.fromCharCode(t[n>>>2]>>>24-n%4*8&255));return r.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new o.init(r,t)}},p=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},u=n.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r=this._data,n=r.words,a=r.sigBytes,i=this.blockSize,s=a/(4*i);if(t=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,a=e.min(4*t,a),t){for(var c=0;c<t;c+=i)this._doProcessBlock(n,c);c=n.splice(0,t),r.sigBytes-=a}return new o.init(c,a)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});n.Hasher=u.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){u.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new d.HMAC.init(e,r).finalize(t)}}});var d=r.algo={};return r}(Math);n=(o=(r=l).lib).WordArray,a=o.Hasher,i=[],o=r.algo.SHA1=a.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],a=r[1],o=r[2],s=r[3],c=r[4],l=0;80>l;l++){if(16>l)i[l]=0|e[t+l];else{var p=i[l-3]^i[l-8]^i[l-14]^i[l-16];i[l]=p<<1|p>>>31}p=(n<<5|n>>>27)+c+i[l],p=20>l?p+(1518500249+(a&o|~a&s)):40>l?p+(1859775393+(a^o^s)):60>l?p+((a&o|a&s|o&s)-1894007588):p+((a^o^s)-899497514),c=s,s=o,o=a<<30|a>>>2,a=n,n=p}r[0]=r[0]+n|0,r[1]=r[1]+a|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=a._createHelper(o),r.HmacSHA1=a._createHmacHelper(o),function(){var e=l,t=e.enc.Utf8;e.algo.HMAC=e.lib.Base.extend({init:function(e,r){e=this._hasher=new e.init,"string"==typeof r&&(r=t.parse(r));var n=e.blockSize,a=4*n;r.sigBytes>a&&(r=e.finalize(r)),r.clamp();for(var i=this._oKey=r.clone(),o=this._iKey=r.clone(),s=i.words,c=o.words,l=0;l<n;l++)s[l]^=1549556828,c[l]^=909522486;i.sigBytes=o.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}})}(),c=(s=l).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var a=[],i=0;i<r;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<r;s++)a.push(n.charAt(o>>>6*(3-s)&63));var c=n.charAt(64);if(c)for(;a.length%4;)a.push(c);return a.join("")},parse:function(e){var t=e.length,r=this._map,n=r.charAt(64);if(n){var a=e.indexOf(n);-1!=a&&(t=a)}for(var i=[],o=0,s=0;s<t;s++)if(s%4){var l=r.indexOf(e.charAt(s-1))<<s%4*2,p=r.indexOf(e.charAt(s))>>>6-s%4*2;i[o>>>2]|=(l|p)<<24-o%4*8,o++}return c.create(i,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=l},function(e,t,r){var n=r(14).DOMParser,a=function(e){"use strict";e=e||{},function(){void 0===e.escapeMode&&(e.escapeMode=!0);e.attributePrefix=e.attributePrefix||"_",e.arrayAccessForm=e.arrayAccessForm||"none",e.emptyNodeForm=e.emptyNodeForm||"text",void 0===e.enableToStringFunc&&(e.enableToStringFunc=!0);e.arrayAccessFormPaths=e.arrayAccessFormPaths||[],void 0===e.skipEmptyTextNodesForObj&&(e.skipEmptyTextNodesForObj=!0);void 0===e.stripWhitespaces&&(e.stripWhitespaces=!0);e.datetimeAccessFormPaths=e.datetimeAccessFormPaths||[],void 0===e.useDoubleQuotes&&(e.useDoubleQuotes=!1);e.xmlElementsFilter=e.xmlElementsFilter||[],e.jsonPropertiesFilter=e.jsonPropertiesFilter||[],void 0===e.keepCData&&(e.keepCData=!1)}();var t={ELEMENT_NODE:1,TEXT_NODE:3,CDATA_SECTION_NODE:4,COMMENT_NODE:8,DOCUMENT_NODE:9};function r(e){var t=e.localName;return null==t&&(t=e.baseName),null!=t&&""!=t||(t=e.nodeName),t}function a(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"):e}function i(e,t,r,n){for(var a=0;a<e.length;a++){var i=e[a];if("string"==typeof i){if(i==n)break}else if(i instanceof RegExp){if(i.test(n))break}else if("function"==typeof i&&i(t,r,n))break}return a!=e.length}function o(t,r,n){if("property"===e.arrayAccessForm)t[r]instanceof Array?t[r+"_asArray"]=t[r]:t[r+"_asArray"]=[t[r]];!(t[r]instanceof Array)&&e.arrayAccessFormPaths.length>0&&i(e.arrayAccessFormPaths,t,r,n)&&(t[r]=[t[r]])}function s(e){var t=e.split(/[-T:+Z]/g),r=new Date(t[0],t[1]-1,t[2]),n=t[5].split(".");if(r.setHours(t[3],t[4],n[0]),n.length>1&&r.setMilliseconds(n[1]),t[6]&&t[7]){var a=60*t[6]+Number(t[7]);a=0+("-"==(/\d\d-\d\d:\d\d$/.test(e)?"-":"+")?-1*a:a),r.setMinutes(r.getMinutes()-a-r.getTimezoneOffset())}else-1!==e.indexOf("Z",e.length-1)&&(r=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds())));return r}function c(r,n,a,o){return!(n==t.ELEMENT_NODE&&e.xmlElementsFilter.length>0)||i(e.xmlElementsFilter,r,a,o)}function l(n,a){if(n.nodeType==t.DOCUMENT_NODE){for(var p=new Object,u=n.childNodes,d=0;d<u.length;d++){if((f=u.item(d)).nodeType==t.ELEMENT_NODE)p[m=r(f)]=l(f,m)}return p}if(n.nodeType==t.ELEMENT_NODE){(p=new Object).__cnt=0;for(u=n.childNodes,d=0;d<u.length;d++){var f,m=r(f=u.item(d));if(f.nodeType!=t.COMMENT_NODE){var h=a+"."+m;c(p,f.nodeType,m,h)&&(p.__cnt++,null==p[m]?(p[m]=l(f,h),o(p,m,h)):(null!=p[m]&&(p[m]instanceof Array||(p[m]=[p[m]],o(p,m,h))),p[m][p[m].length]=l(f,h)))}}for(var g=0;g<n.attributes.length;g++){var v=n.attributes.item(g);p.__cnt++,p[e.attributePrefix+v.name]=v.value}var y=function(e){return e.prefix}(n);return null!=y&&""!=y&&(p.__cnt++,p.__prefix=y),null!=p["#text"]&&(p.__text=p["#text"],p.__text instanceof Array&&(p.__text=p.__text.join("\n")),e.stripWhitespaces&&(p.__text=p.__text.trim()),delete p["#text"],"property"==e.arrayAccessForm&&delete p["#text_asArray"],p.__text=function(t,r,n){if(e.datetimeAccessFormPaths.length>0){var a=n.split(".#")[0];return i(e.datetimeAccessFormPaths,t,r,a)?s(t):t}return t}(p.__text,m,a+"."+m)),null!=p["#cdata-section"]&&(p.__cdata=p["#cdata-section"],delete p["#cdata-section"],"property"==e.arrayAccessForm&&delete p["#cdata-section_asArray"]),0==p.__cnt&&"text"==e.emptyNodeForm?p="":1==p.__cnt&&null!=p.__text?p=p.__text:1!=p.__cnt||null==p.__cdata||e.keepCData?p.__cnt>1&&null!=p.__text&&e.skipEmptyTextNodesForObj&&(e.stripWhitespaces&&""==p.__text||""==p.__text.trim())&&delete p.__text:p=p.__cdata,delete p.__cnt,!e.enableToStringFunc||null==p.__text&&null==p.__cdata||(p.toString=function(){return(null!=this.__text?this.__text:"")+(null!=this.__cdata?this.__cdata:"")}),p}if(n.nodeType==t.TEXT_NODE||n.nodeType==t.CDATA_SECTION_NODE)return n.nodeValue}function p(t,r,n,i){var o="<"+(null!=t&&null!=t.__prefix?t.__prefix+":":"")+r;if(null!=n)for(var s=0;s<n.length;s++){var c=n[s],l=t[c];e.escapeMode&&(l=a(l)),o+=" "+c.substr(e.attributePrefix.length)+"=",e.useDoubleQuotes?o+='"'+l+'"':o+="'"+l+"'"}return o+=i?"/>":">"}function u(e,t){return"</"+(null!=e.__prefix?e.__prefix+":":"")+t+">"}function d(t,r){return"property"==e.arrayAccessForm&&(n=r.toString(),a="_asArray",-1!==n.indexOf(a,n.length-a.length))||0==r.toString().indexOf(e.attributePrefix)||0==r.toString().indexOf("__")||t[r]instanceof Function;var n,a}function f(e){var t=0;if(e instanceof Object)for(var r in e)d(e,r)||t++;return t}function m(t,r,n){return 0==e.jsonPropertiesFilter.length||""==n||i(e.jsonPropertiesFilter,t,r,n)}function h(t){var r=[];if(t instanceof Object)for(var n in t)-1==n.toString().indexOf("__")&&0==n.toString().indexOf(e.attributePrefix)&&r.push(n);return r}function g(t){var r="";return t instanceof Object?r+=function(t){var r="";return null!=t.__cdata&&(r+="<![CDATA["+t.__cdata+"]]>"),null!=t.__text&&(e.escapeMode?r+=a(t.__text):r+=t.__text),r}(t):null!=t&&(e.escapeMode?r+=a(t):r+=t),r}function v(e,t){return""===e?t:e+"."+t}function y(e,t,r,n){var a="";if(0==e.length)a+=p(e,t,r,!0);else for(var i=0;i<e.length;i++)a+=p(e[i],t,h(e[i]),!1),a+=x(e[i],v(n,t)),a+=u(e[i],t);return a}function x(e,t){var r="";if(f(e)>0)for(var n in e)if(!d(e,n)&&(""==t||m(e,n,v(t,n)))){var a=e[n],i=h(a);if(null==a||null==a)r+=p(a,n,i,!0);else if(a instanceof Object)if(a instanceof Array)r+=y(a,n,i,t);else if(a instanceof Date)r+=p(a,n,i,!1),r+=a.toISOString(),r+=u(a,n);else{f(a)>0||null!=a.__text||null!=a.__cdata?(r+=p(a,n,i,!1),r+=x(a,v(t,n)),r+=u(a,n)):r+=p(a,n,i,!0)}else r+=p(a,n,i,!1),r+=g(a),r+=u(a,n)}return r+=g(e)}this.parseXmlString=function(e){var t;if(void 0===e)return null;if(n){var r=new n,a=null;try{a=r.parseFromString("INVALID","text/xml").getElementsByTagName("parsererror")[0].namespaceURI}catch(e){a=null}try{t=r.parseFromString(e,"text/xml"),null!=a&&t.getElementsByTagNameNS(a,"parsererror").length>0&&(t=null)}catch(e){t=null}}else 0==e.indexOf("<?")&&(e=e.substr(e.indexOf("?>")+2)),(t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e);return t},this.asArray=function(e){return void 0===e||null==e?[]:e instanceof Array?e:[e]},this.toXmlDateTime=function(e){return e instanceof Date?e.toISOString():"number"==typeof e?new Date(e).toISOString():null},this.asDateTime=function(e){return"string"==typeof e?s(e):e},this.xml2json=function(e){return l(e)},this.xml_str2json=function(e){var t=this.parseXmlString(e);return null!=t?this.xml2json(t):null},this.json2xml_str=function(e){return x(e,"")},this.json2xml=function(e){var t=this.json2xml_str(e);return this.parseXmlString(t)},this.getVersion=function(){return"1.2.0"}};e.exports=function(e){if(!e)return null;var t=(new n).parseFromString(e,"text/xml"),r=(new a).xml2json(t);return r.html&&r.getElementsByTagName("parsererror").length?null:r}},function(e,t,r){var n=r(2);t.DOMImplementation=n.DOMImplementation,t.XMLSerializer=n.XMLSerializer,t.DOMParser=r(15).DOMParser},function(e,t,r){var n=r(1),a=r(2),i=r(16),o=r(17),s=a.DOMImplementation,c=n.NAMESPACE,l=o.ParseError,p=o.XMLReader;function u(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function d(e){this.options=e||{locator:{}}}function f(){this.cdata=!1}function m(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function h(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function g(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}d.prototype.parseFromString=function(e,t){var r=this.options,n=new p,a=r.domBuilder||new f,o=r.errorHandler,s=r.locator,l=r.xmlns||{},d=/\/x?html?$/.test(t),m=d?i.HTML_ENTITIES:i.XML_ENTITIES;s&&a.setDocumentLocator(s),n.errorHandler=function(e,t,r){if(!e){if(t instanceof f)return t;e=t}var n={},a=e instanceof Function;function i(t){var i=e[t];!i&&a&&(i=2==e.length?function(r){e(t,r)}:e),n[t]=i&&function(e){i("[xmldom "+t+"]\t"+e+function(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}(r))}||function(){}}return r=r||{},i("warning"),i("error"),i("fatalError"),n}(o,a,s),n.domBuilder=r.domBuilder||a,d&&(l[""]=c.HTML),l.xml=l.xml||c.XML;var h=r.normalizeLineEndings||u;return e&&"string"==typeof e?n.parse(h(e),l,m):n.errorHandler.error("invalid doc source"),a.doc},f.prototype={startDocument:function(){this.doc=(new s).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,r,n){var a=this.doc,i=a.createElementNS(e,r||t),o=n.length;g(this,i),this.currentElement=i,this.locator&&m(this.locator,i);for(var s=0;s<o;s++){e=n.getURI(s);var c=n.getValue(s),l=(r=n.getQName(s),a.createAttributeNS(e,r));this.locator&&m(n.getLocator(s),l),l.value=l.nodeValue=c,i.setAttributeNode(l)}},endElement:function(e,t,r){var n=this.currentElement;n.tagName;this.currentElement=n.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var r=this.doc.createProcessingInstruction(e,t);this.locator&&m(this.locator,r),g(this,r)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){if(e=h.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(e);else n=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&m(this.locator,n)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,r){e=h.apply(this,arguments);var n=this.doc.createComment(e);this.locator&&m(this.locator,n),g(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r){var n=this.doc.implementation;if(n&&n.createDocumentType){var a=n.createDocumentType(e,t,r);this.locator&&m(this.locator,a),g(this,a),this.doc.doctype=a}},warning:function(e){},error:function(e){},fatalError:function(e){throw new l(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,(function(e){f.prototype[e]=function(){return null}})),t.__DOMHandler=f,t.normalizeLineEndings=u,t.DOMParser=d},function(e,t,r){"use strict";var n=r(1).freeze;t.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=n({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}),t.entityMap=t.HTML_ENTITIES},function(e,t,r){var n=r(1).NAMESPACE,a=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,i=new RegExp("[\\-\\.0-9"+a.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),o=new RegExp("^"+a.source+i.source+"*(?::"+a.source+i.source+"*)?$"),s=0,c=1,l=2,p=3,u=4,d=5,f=6,m=7;function h(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,h)}function g(){}function v(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function y(e,t,r,a,i,o){function h(e,t,n){r.attributeNames.hasOwnProperty(e)&&o.fatalError("Attribute "+e+" redefined"),r.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,i),n)}for(var g,v=++t,y=s;;){var x=e.charAt(v);switch(x){case"=":if(y===c)g=e.slice(t,v),y=p;else{if(y!==l)throw new Error("attribute equal must after attrName");y=p}break;case"'":case'"':if(y===p||y===c){if(y===c&&(o.warning('attribute value must after "="'),g=e.slice(t,v)),t=v+1,!((v=e.indexOf(x,t))>0))throw new Error("attribute value no end '"+x+"' match");h(g,b=e.slice(t,v),t-1),y=d}else{if(y!=u)throw new Error('attribute value must after "="');h(g,b=e.slice(t,v),t),o.warning('attribute "'+g+'" missed start quot('+x+")!!"),t=v+1,y=d}break;case"/":switch(y){case s:r.setTagName(e.slice(t,v));case d:case f:case m:y=m,r.closed=!0;case u:case c:break;case l:r.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return o.error("unexpected end of input"),y==s&&r.setTagName(e.slice(t,v)),v;case">":switch(y){case s:r.setTagName(e.slice(t,v));case d:case f:case m:break;case u:case c:"/"===(b=e.slice(t,v)).slice(-1)&&(r.closed=!0,b=b.slice(0,-1));case l:y===l&&(b=g),y==u?(o.warning('attribute "'+b+'" missed quot(")!'),h(g,b,t)):(n.isHTML(a[""])&&b.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+b+'" missed value!! "'+b+'" instead!!'),h(b,b,t));break;case p:throw new Error("attribute value missed!!")}return v;case"":x=" ";default:if(x<=" ")switch(y){case s:r.setTagName(e.slice(t,v)),y=f;break;case c:g=e.slice(t,v),y=l;break;case u:var b=e.slice(t,v);o.warning('attribute "'+b+'" missed quot(")!!'),h(g,b,t);case d:y=f}else switch(y){case l:r.tagName;n.isHTML(a[""])&&g.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+g+'" missed value!! "'+g+'" instead2!!'),h(g,g,t),t=v,y=c;break;case d:o.warning('attribute space is required"'+g+'"!!');case f:y=c,t=v;break;case p:y=u,t=v;break;case m:throw new Error("elements closed character '/' and '>' must be connected to")}}v++}}function x(e,t,r){for(var a=e.tagName,i=null,o=e.length;o--;){var s=e[o],c=s.qName,l=s.value;if((f=c.indexOf(":"))>0)var p=s.prefix=c.slice(0,f),u=c.slice(f+1),d="xmlns"===p&&u;else u=c,p=null,d="xmlns"===c&&"";s.localName=u,!1!==d&&(null==i&&(i={},w(r,r={})),r[d]=i[d]=l,s.uri=n.XMLNS,t.startPrefixMapping(d,l))}for(o=e.length;o--;){(p=(s=e[o]).prefix)&&("xml"===p&&(s.uri=n.XML),"xmlns"!==p&&(s.uri=r[p||""]))}var f;(f=a.indexOf(":"))>0?(p=e.prefix=a.slice(0,f),u=e.localName=a.slice(f+1)):(p=null,u=e.localName=a);var m=e.uri=r[p||""];if(t.startElement(m,u,a,e),!e.closed)return e.currentNSMap=r,e.localNSMap=i,!0;if(t.endElement(m,u,a),i)for(p in i)Object.prototype.hasOwnProperty.call(i,p)&&t.endPrefixMapping(p)}function b(e,t,r,n,a){if(/^(?:script|textarea)$/i.test(r)){var i=e.indexOf("</"+r+">",t),o=e.substring(t+1,i);if(/[&<]/.test(o))return/^script$/i.test(r)?(a.characters(o,0,o.length),i):(o=o.replace(/&#?\w+;/g,n),a.characters(o,0,o.length),i)}return t+1}function k(e,t,r,n){var a=n[r];return null==a&&((a=e.lastIndexOf("</"+r+">"))<t&&(a=e.lastIndexOf("</"+r)),n[r]=a),a<t}function w(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function C(e,t,r,n){if("-"===e.charAt(t+2))return"-"===e.charAt(t+3)?(a=e.indexOf("--\x3e",t+4))>t?(r.comment(e,t+4,a-t-4),a+3):(n.error("Unclosed comment"),-1):-1;if("CDATA["==e.substr(t+3,6)){var a=e.indexOf("]]>",t+9);return r.startCDATA(),r.characters(e,t+9,a-t-9),r.endCDATA(),a+3}var i=function(e,t){var r,n=[],a=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;a.lastIndex=t,a.exec(e);for(;r=a.exec(e);)if(n.push(r),r[1])return n}(e,t),o=i.length;if(o>1&&/!doctype/i.test(i[0][0])){var s=i[1][0],c=!1,l=!1;o>3&&(/^public$/i.test(i[2][0])?(c=i[3][0],l=o>4&&i[4][0]):/^system$/i.test(i[2][0])&&(l=i[3][0]));var p=i[o-1];return r.startDTD(s,c,l),r.endDTD(),p.index+p[0].length}return-1}function S(e,t,r){var n=e.indexOf("?>",t);if(n){var a=e.substring(t,n).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(a){a[0].length;return r.processingInstruction(a[1],a[2]),n+2}return-1}return-1}function T(){this.attributeNames={}}h.prototype=new Error,h.prototype.name=h.name,g.prototype={parse:function(e,t,r){var a=this.domBuilder;a.startDocument(),w(t,t={}),function(e,t,r,a,i){function o(e){if(e>65535){var t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}function s(e){var t=e.slice(1,-1);return Object.hasOwnProperty.call(r,t)?r[t]:"#"===t.charAt(0)?o(parseInt(t.substr(1).replace("x","0x"))):(i.error("entity not found:"+e),e)}function c(t){if(t>w){var r=e.substring(w,t).replace(/&#?\w+;/g,s);f&&l(w),a.characters(r,0,t-w),w=t}}function l(t,r){for(;t>=u&&(r=d.exec(e));)p=r.index,u=p+r[0].length,f.lineNumber++;f.columnNumber=t-p+1}var p=0,u=0,d=/.*(?:\r\n?|\n)|.*$/g,f=a.locator,m=[{currentNSMap:t}],g={},w=0;for(;;){try{var E=e.indexOf("<",w);if(E<0){if(!e.substr(w).match(/^\s*$/)){var R=a.doc,A=R.createTextNode(e.substr(w));R.appendChild(A),a.currentElement=A}return}switch(E>w&&c(E),e.charAt(E+1)){case"/":var B=e.indexOf(">",E+3),_=e.substring(E+2,B).replace(/[ \t\n\r]+$/g,""),O=m.pop();B<0?(_=e.substring(E+2).replace(/[\s<].*/,""),i.error("end tag name: "+_+" is not complete:"+O.tagName),B=E+1+_.length):_.match(/\s</)&&(_=_.replace(/[\s<].*/,""),i.error("end tag name: "+_+" maybe not complete"),B=E+1+_.length);var N=O.localNSMap,D=O.tagName==_;if(D||O.tagName&&O.tagName.toLowerCase()==_.toLowerCase()){if(a.endElement(O.uri,O.localName,_),N)for(var P in N)Object.prototype.hasOwnProperty.call(N,P)&&a.endPrefixMapping(P);D||i.fatalError("end tag name: "+_+" is not match the current start tagName:"+O.tagName)}else m.push(O);B++;break;case"?":f&&l(E),B=S(e,E,a);break;case"!":f&&l(E),B=C(e,E,a,i);break;default:f&&l(E);var I=new T,L=m[m.length-1].currentNSMap,j=(B=y(e,E,I,L,s,i),I.length);if(!I.closed&&k(e,B,I.tagName,g)&&(I.closed=!0,r.nbsp||i.warning("unclosed xml attribute")),f&&j){for(var U=v(f,{}),q=0;q<j;q++){var M=I[q];l(M.offset),M.locator=v(f,{})}a.locator=U,x(I,a,L)&&m.push(I),a.locator=f}else x(I,a,L)&&m.push(I);n.isHTML(I.uri)&&!I.closed?B=b(e,B,I.tagName,s,a):B++}}catch(e){if(e instanceof h)throw e;i.error("element parse error: "+e),B=-1}B>w?w=B:c(Math.max(E,w)+1)}}(e,t,r,a,this.errorHandler),a.endDocument()}},T.prototype={setTagName:function(e){if(!o.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!o.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=g,t.ParseError=h},function(e,t){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}var n="a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�",a=new RegExp("^([^"+n+"])|^((x|X)(m|M)(l|L))|([^"+n+"-.0-9·̀-ͯ‿⁀])","g"),i=/[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm,o=function(e){var t=[];if(e instanceof Object)for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t},s=function(e,t){var n=function(e,r,n,i,o){var s=void 0!==t.indent?t.indent:"\t",c=t.prettyPrint?"\n"+new Array(i).join(s):"";t.removeIllegalNameCharacters&&(e=e.replace(a,"_"));var l=[c,"<",e,n||""];return r&&r.length>0?(l.push(">"),l.push(r),o&&l.push(c),l.push("</"),l.push(e),l.push(">")):l.push("/>"),l.join("")};return function e(a,s,c){var l=r(a);switch((Array.isArray?Array.isArray(a):a instanceof Array)?l="array":a instanceof Date&&(l="date"),l){case"array":var p=[];return a.map((function(t){p.push(e(t,1,c+1))})),t.prettyPrint&&p.push("\n"),p.join("");case"date":return a.toJSON?a.toJSON():a+"";case"object":var u=[];for(var d in a)if(a.hasOwnProperty(d))if(a[d]instanceof Array)for(var f in a[d])a[d].hasOwnProperty(f)&&u.push(n(d,e(a[d][f],0,c+1),null,c+1,o(a[d][f]).length));else u.push(n(d,e(a[d],0,c+1),null,c+1));return t.prettyPrint&&u.length>0&&u.push("\n"),u.join("");case"function":return a();default:return t.escape?(""+a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(i,""):""+a}}(e,0,0)},c=function(e){var t=['<?xml version="1.0" encoding="UTF-8"'];return e&&t.push(' standalone="yes"'),t.push("?>"),t.join("")};e.exports=function(e,t){if(t||(t={xmlHeader:{standalone:!0},prettyPrint:!0,indent:"  "}),"string"==typeof e)try{e=JSON.parse(e.toString())}catch(e){return!1}var n="",a="";return t&&("object"==r(t)?(t.xmlHeader&&(n=c(!!t.xmlHeader.standalone)),void 0!==t.docType&&(a="<!DOCTYPE "+t.docType+">")):n=c()),[n,(t=t||{}).prettyPrint&&a?"\n":"",a,s(e,t)].join("").replace(/\n{2,}/g,"\n").replace(/\s+$/g,"")}},function(e,t){var r=function(e){var t=(e=e||{}).Base64,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=function(e){for(var t={},r=0,n=e.length;r<n;r++)t[e.charAt(r)]=r;return t}(r),a=String.fromCharCode,i=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?a(192|t>>>6)+a(128|63&t):a(224|t>>>12&15)+a(128|t>>>6&63)+a(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return a(240|t>>>18&7)+a(128|t>>>12&63)+a(128|t>>>6&63)+a(128|63&t)},o=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,s=function(e){return e.replace(o,i)},c=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[r.charAt(n>>>18),r.charAt(n>>>12&63),t>=2?"=":r.charAt(n>>>6&63),t>=1?"=":r.charAt(63&n)].join("")},l=e.btoa?function(t){return e.btoa(t)}:function(e){return e.replace(/[\s\S]{1,3}/g,c)},p=function(e){return l(s(e))},u=function(e,t){return t?p(String(e)).replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=/g,""):p(String(e))},d=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),f=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return a(55296+(t>>>10))+a(56320+(1023&t));case 3:return a((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return a((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},m=function(e){return e.replace(d,f)},h=function(e){var t=e.length,r=t%4,i=(t>0?n[e.charAt(0)]<<18:0)|(t>1?n[e.charAt(1)]<<12:0)|(t>2?n[e.charAt(2)]<<6:0)|(t>3?n[e.charAt(3)]:0),o=[a(i>>>16),a(i>>>8&255),a(255&i)];return o.length-=[0,0,2,1][r],o.join("")},g=e.atob?function(t){return e.atob(t)}:function(e){return e.replace(/[\s\S]{1,4}/g,h)},v=function(e){return m(g(e))},y=function(e){return v(String(e).replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,""))};return{VERSION:"2.1.9",atob:g,btoa:l,fromBase64:y,toBase64:u,utob:s,encode:u,encodeURI:function(e){return u(e,!0)},btou:m,decode:y,noConflict:function(){var r=e.Base64;return e.Base64=t,r}}}();e.exports=r},function(e,t,r){var n=r(6),a=r(0),i={};e.exports.transferToTaskMethod=function(e,t){i[t]=e[t],e[t]=function(e,r){e.SkipTask?i[t].call(this,e,r):this._addTask(t,e,r)}},e.exports.init=function(e){var t,r,o=[],s={},c=0,l=0,p=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),t},u=(r=function(){t=0,e.emit("task-list-update",{list:a.map(o,p)}),e.emit("list-update",{list:a.map(o,p)})},function(){t||(t=setTimeout(r))}),d=function(){if(!(o.length<=e.options.UploadQueueSize)){for(var t=0;t<l&&t<o.length&&o.length>e.options.UploadQueueSize;){var r="waiting"===o[t].state||"checking"===o[t].state||"uploading"===o[t].state;o[t]&&r?t++:(s[o[t].id]&&delete s[o[t].id],o.splice(t,1),l--)}u()}},f=function t(){if(!(c>=e.options.FileParallelLimit)){for(;o[l]&&"waiting"!==o[l].state;)l++;if(!(l>=o.length)){var r=o[l];l++,c++,r.state="checking",r.params.onTaskStart&&r.params.onTaskStart(p(r)),!r.params.UploadData&&(r.params.UploadData={});var n=a.formatParams(r.api,r.params);i[r.api].call(e,n,(function(n,a){e._isRunningTask(r.id)&&("checking"!==r.state&&"uploading"!==r.state||(r.state=n?"error":"success",n&&(r.error=n),c--,u(),t(),r.callback&&r.callback(n,a),"success"===r.state&&(r.params&&(delete r.params.UploadData,delete r.params.Body,delete r.params),delete r.callback)),d())})),u(),setTimeout(t)}}},m=function(t,r){var a=s[t];if(a){var i=a&&"waiting"===a.state,o=a&&("checking"===a.state||"uploading"===a.state);if("canceled"===r&&"canceled"!==a.state||"paused"===r&&i||"paused"===r&&o){if("paused"===r&&a.params.Body&&"function"==typeof a.params.Body.pipe)return;a.state=r,e.emit("inner-kill-task",{TaskId:t,toState:r});try{var l=a&&a.params&&a.params.UploadData.UploadId}catch(e){}"canceled"===r&&l&&n.removeUsing(l),u(),o&&(c--,f()),"canceled"===r&&(a.params&&(delete a.params.UploadData,delete a.params.Body,delete a.params),delete a.callback)}d()}};e._addTasks=function(t){a.each(t,(function(t){e._addTask(t.api,t.params,t.callback,!0)})),u()},e._addTask=function(t,r,n,i){var c="postObject"===e.options.SimpleUploadMethod?"postObject":"putObject";"sliceUploadFile"!==t||a.canFileSlice()||(t=c),r=a.formatParams(t,r);var l=a.uuid();r.TaskId=l,r.onTaskReady&&r.onTaskReady(l);var p={params:r,callback:n,api:t,index:o.length,id:l,Bucket:r.Bucket,Region:r.Region,Key:r.Key,FilePath:r.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null},m=r.onHashProgress;r.onHashProgress=function(t){e._isRunningTask(p.id)&&(p.hashPercent=t.percent,m&&m(t),u())};var h=r.onProgress;return r.onProgress=function(t){e._isRunningTask(p.id)&&("checking"===p.state&&(p.state="uploading"),p.loaded=t.loaded,p.size=t.total,p.speed=t.speed,p.percent=t.percent,h&&h(t),u())},a.getFileSize(t,r,(function(e,t){e?n(e):(s[l]=p,o.push(p),p.size=t,!i&&u(),f(),d())})),l},e._isRunningTask=function(e){var t=s[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return a.map(o,p)},e.cancelTask=function(e){m(e,"canceled")},e.pauseTask=function(e){m(e,"paused")},e.restartTask=function(e){var t=s[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",u(),l=Math.min(l,t.index),f())},e.isUploadRunning=function(){return c||l<o.length}}},function(e,t,r){"use strict";var n=r(22),a=r(0),i=r(23);function o(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},r={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},n=(e&&e.AccessControlList||{}).Grant;n&&(n=a.isArray(n)?n:[n]);var i={READ:0,WRITE:0,FULL_CONTROL:0};return n&&n.length&&a.each(n,(function(n){"qcs::cam::anyone:anyone"===n.Grantee.ID||"http://cam.qcloud.com/groups/global/AllUsers"===n.Grantee.URI?i[n.Permission]=1:n.Grantee.ID!==e.Owner.ID&&t[r[n.Permission]].push('id="'+n.Grantee.ID+'"')})),i.FULL_CONTROL||i.WRITE&&i.READ?t.ACL="public-read-write":i.READ?t.ACL="public-read":t.ACL="private",a.each(r,(function(e){t[e]=s(t[e].join(","))})),t}function s(e){var t,r,n=e.split(","),a={};for(t=0;t<n.length;)a[r=n[t].trim()]?n.splice(t,1):(a[r]=!0,n[t]=r,t++);return n.join(",")}function c(e){var t=e.bucket,r=t.substr(0,t.lastIndexOf("-")),n=t.substr(t.lastIndexOf("-")+1),i=e.domain,o=e.region,s=e.object;i||(i=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(o)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(i="{Bucket}."+i)),i=(i=i.replace(/\{\{AppId\}\}/gi,n).replace(/\{\{Bucket\}\}/gi,r).replace(/\{\{Region\}\}/gi,o).replace(/\{\{.*?\}\}/gi,"")).replace(/\{AppId\}/gi,n).replace(/\{BucketName\}/gi,r).replace(/\{Bucket\}/gi,t).replace(/\{Region\}/gi,o).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(i)||(i="https://"+i),"/"===i.slice(-1)&&(i=i.slice(0,-1));var c=i;return e.ForcePathStyle&&(c+="/"+t),c+="/",s&&(c+=a.camSafeUrlEncode(s).replace(/%2F/g,"/")),e.isLocation&&(c=c.replace(/^https?:\/\//,"")),c}var l=function(e){if(!e.Bucket||!e.Region)return"";var t=void 0===e.UseAccelerate?this.options.UseAccelerate:e.UseAccelerate;return(e.Url||c({ForcePathStyle:this.options.ForcePathStyle,protocol:this.options.Protocol,domain:this.options.Domain,bucket:e.Bucket,region:t?"accelerate":e.Region})).replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1")};function p(e,t){var r=a.clone(e.Headers),n="";a.each(r,(function(e,t){(""===e||["content-type","cache-control"].indexOf(t.toLowerCase())>-1)&&delete r[t],"host"===t.toLowerCase()&&(n=e)}));var i=!1!==e.ForceSignHost;!n&&e.SignHost&&i&&(r.Host=e.SignHost);var o=!1,s=function(e,r){o||(o=!0,r&&r.XCosSecurityToken&&!r.SecurityToken&&((r=a.clone(r)).SecurityToken=r.XCosSecurityToken,delete r.XCosSecurityToken),t&&t(e,r))},c=this,l=e.Bucket||"",p=e.Region||"",u="name/cos:PostObject"!==e.Action&&e.Key?e.Key:"";c.options.ForcePathStyle&&l&&(u=l+"/"+u);var d="/"+u,f={},m=e.Scope;if(!m){var h=e.Action||"",g=e.ResourceKey||e.Key||"";m=e.Scope||[{action:h,bucket:l,region:p,prefix:g}]}var v=a.md5(JSON.stringify(m));c._StsCache=c._StsCache||[],function(){var e,t;for(e=c._StsCache.length-1;e>=0;e--){t=c._StsCache[e];var r=Math.round(a.getSkewTime(c.options.SystemClockOffset)/1e3)+30;if(t.StartTime&&r<t.StartTime||r>=t.ExpiredTime)c._StsCache.splice(e,1);else if(!t.ScopeLimit||t.ScopeLimit&&t.ScopeKey===v){f=t;break}}}();var y,x=function(){var t="";f.StartTime&&e.Expires?t=f.StartTime+";"+(f.StartTime+1*e.Expires):f.StartTime&&f.ExpiredTime&&(t=f.StartTime+";"+f.ExpiredTime);var n={Authorization:a.getAuth({SecretId:f.TmpSecretId,SecretKey:f.TmpSecretKey,Method:e.Method,Pathname:d,Query:e.Query,Headers:r,Expires:e.Expires,SystemClockOffset:c.options.SystemClockOffset,KeyTime:t,ForceSignHost:i}),SecurityToken:f.SecurityToken||f.XCosSecurityToken||"",Token:f.Token||"",ClientIP:f.ClientIP||"",ClientUA:f.ClientUA||"",SignFrom:"client"};s(null,n)},b=function(e){if(e.Authorization){var t=!1,r=e.Authorization;if(r)if(r.indexOf(" ")>-1)t=!1;else if(r.indexOf("q-sign-algorithm=")>-1&&r.indexOf("q-ak=")>-1&&r.indexOf("q-sign-time=")>-1&&r.indexOf("q-key-time=")>-1&&r.indexOf("q-url-param-list=")>-1)t=!0;else try{(r=atob(r)).indexOf("a=")>-1&&r.indexOf("k=")>-1&&r.indexOf("t=")>-1&&r.indexOf("r=")>-1&&r.indexOf("b=")>-1&&(t=!0)}catch(e){}if(!t)return a.error(new Error("getAuthorization callback params format error"))}else{if(!e.TmpSecretId)return a.error(new Error('getAuthorization callback params missing "TmpSecretId"'));if(!e.TmpSecretKey)return a.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));if(!e.SecurityToken&&!e.XCosSecurityToken)return a.error(new Error('getAuthorization callback params missing "SecurityToken"'));if(!e.ExpiredTime)return a.error(new Error('getAuthorization callback params missing "ExpiredTime"'));if(e.ExpiredTime&&10!==e.ExpiredTime.toString().length)return a.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));if(e.StartTime&&10!==e.StartTime.toString().length)return a.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'))}return!1};if(f.ExpiredTime&&f.ExpiredTime-a.getSkewTime(c.options.SystemClockOffset)/1e3>60)x();else if(c.options.getAuthorization)c.options.getAuthorization.call(c,{Bucket:l,Region:p,Method:e.Method,Key:u,Pathname:d,Query:e.Query,Headers:r,Scope:m,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:i},(function(e){"string"==typeof e&&(e={Authorization:e});var t=b(e);if(t)return s(t);e.Authorization?s(null,e):((f=e||{}).Scope=m,f.ScopeKey=v,c._StsCache.push(f),x())}));else{if(!c.options.getSTS)return y={Authorization:a.getAuth({SecretId:e.SecretId||c.options.SecretId,SecretKey:e.SecretKey||c.options.SecretKey,Method:e.Method,Pathname:d,Query:e.Query,Headers:r,Expires:e.Expires,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:i}),SecurityToken:c.options.SecurityToken||c.options.XCosSecurityToken,SignFrom:"client"},s(null,y),y;c.options.getSTS.call(c,{Bucket:l,Region:p},(function(e){(f=e||{}).Scope=m,f.ScopeKey=v,f.TmpSecretId||(f.TmpSecretId=f.SecretId),f.TmpSecretKey||(f.TmpSecretKey=f.SecretKey);var t=b(f);if(t)return s(t);c._StsCache.push(f),x()}))}return""}function u(e){var t=!1,r=!1,n=!1,i=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var o=e.error.Code,s=e.error.Message;("RequestTimeTooSkewed"===o||"AccessDenied"===o&&"Request has expired"===s)&&(n=!0)}catch(e){}if(e){if(n&&i){var c=Date.parse(i);this.options.CorrectClockSkew&&Math.abs(a.getSkewTime(this.options.SystemClockOffset)-c)>=3e4&&(this.options.SystemClockOffset=c-Date.now(),t=!0)}else 5===Math.floor(e.statusCode/100)&&(t=!0);if(e.statusCode){var l=Math.floor(e.statusCode/100),p=(null==e?void 0:e.headers)&&(null==e?void 0:e.headers["x-cos-request-id"]);[3,4,5].includes(l)&&!p&&(t=this.options.AutoSwitchHost,r=!0)}else t=this.options.AutoSwitchHost,r=!0}return{canRetry:t,networkError:r}}function d(e){var t=e.requestUrl,r=e.clientCalcSign,n=e.networkError;if(!this.options.AutoSwitchHost)return!1;if(!t)return!1;if(!r)return!1;if(!n)return!1;return/^https?:\/\/[^\/]*\.cos\.[^\/]*\.myqcloud\.com(\/.*)?$/.test(t)&&!/^https?:\/\/[^\/]*\.cos\.accelerate\.myqcloud\.com(\/.*)?$/.test(t)}function f(e,t){var r=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=a.clearKey(e.qs),e.headers&&(e.headers=a.clearKey(e.headers)),e.qs&&(e.qs=a.clearKey(e.qs));var n=a.clone(e.qs);e.action&&(n[e.action]="");var i=e.url||e.Url,o=e.SignHost||l.call(this,{Bucket:e.Bucket,Region:e.Region,Url:i}),s=e.tracker;!function a(i){var c=r.options.SystemClockOffset;e.SwitchHost&&(o=o.replace(/myqcloud.com/,"tencentcos.cn")),s&&s.setParams({signStartTime:(new Date).getTime(),httpRetryTimes:i-1}),p.call(r,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:n,Headers:e.headers,SignHost:o,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope,ForceSignHost:r.options.ForceSignHost},(function(n,o){n?t(n):(s&&s.setParams({signEndTime:(new Date).getTime(),httpStartTime:(new Date).getTime()}),e.AuthData=o,m.call(r,e,(function(n,l){var p=!1,f=!1;if(n){var m=u.call(r,n);p=m.canRetry||c!==r.options.SystemClockOffset,f=m.networkError}if(s&&s.setParams({httpEndTime:(new Date).getTime()}),n&&i<2&&p){e.headers&&(delete e.headers.Authorization,delete e.headers.token,delete e.headers.clientIP,delete e.headers.clientUA,e.headers["x-cos-security-token"]&&delete e.headers["x-cos-security-token"],e.headers["x-ci-security-token"]&&delete e.headers["x-ci-security-token"]);var h=d.call(r,{requestUrl:(null==n?void 0:n.url)||"",clientCalcSign:"client"===(null==o?void 0:o.SignFrom),networkError:f});e.SwitchHost=h,a(i+1)}else t(n,l)})))}))}(1)}function m(e,t){var r=this,i=e.TaskId;if(!i||r._isRunningTask(i)){var o=e.Bucket,s=e.Region,l=e.Key,p=e.method||"GET",u=e.url||e.Url,d=e.body,f=e.json,m=e.rawBody,h=e.dataType,g=r.options.HttpDNSServiceId;r.options.UseAccelerate&&(s="accelerate"),u=u||c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:o,region:s,object:l}),e.SwitchHost&&(u=u.replace(/myqcloud.com/,"tencentcos.cn"));var v=l?u:"";e.action&&(u=u+"?"+e.action),e.qsStr&&(u=u.indexOf("?")>-1?u+"&"+e.qsStr:u+"?"+e.qsStr);var y={method:p,url:u,headers:e.headers,qs:e.qs,filePath:e.filePath,body:d,json:f,httpDNSServiceId:g,dataType:h},x="x-cos-security-token";a.isCIHost(u)&&(x="x-ci-security-token"),y.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(y.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(y.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(y.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(y.headers[x]=e.AuthData.SecurityToken),y.headers&&(y.headers=a.clearKey(y.headers)),y=a.clearKey(y),e.onProgress&&"function"==typeof e.onProgress&&(y.onProgress=function(t){if(!i||r._isRunningTask(i)){var n=t?t.loaded:0;e.onProgress({loaded:n,total:t.total})}}),this.options.Timeout&&(y.timeout=this.options.Timeout),r.options.ForcePathStyle&&(y.pathStyle=r.options.ForcePathStyle),r.emit("before-send",y);var b,k=y.url.includes("accelerate."),w=y.qs?Object.keys(y.qs).map((function(e){return"".concat(e,"=").concat(y.qs[e])})).join("&"):"",C=w?y.url+"?"+w:y.url;if(e.tracker)e.tracker.setParams({url:C,httpMethod:y.method,accelerate:k,httpSize:(null===(b=y.body)||void 0===b?void 0:b.size)||0}),e.tracker.parent&&!e.tracker.parent.params.url&&e.tracker.parent.setParams({url:v,accelerate:k});var S=n(y,(function(e,n,o){if("abort"!==e){var s,c=function(e,o){if(i&&r.off("inner-kill-task",T),!s){s=!0;var c={};n&&n.statusCode&&(c.statusCode=n.statusCode),n&&n.headers&&(c.headers=n.headers),e?(y.url&&(c.url=y.url),y.method&&(c.method=y.method),e=a.extend(e||{},c),t(e,null)):(o=a.extend(o||{},c),t(null,o)),S=null}};if(e)c({error:e});else{var l=n.statusCode,p=2===Math.floor(l/100);if(m){if(p)return c(null,{body:o});if(o instanceof ArrayBuffer){var u=a.arrayBufferToString(o),d=a.parseResBody(u);return c({error:d.Error||d})}}var f=a.parseResBody(o);p?f.Error?c({error:f.Error}):c(null,f):c({error:f.Error||f})}}})),T=function e(t){t.TaskId===i&&(S&&S.abort&&S.abort(),r.off("inner-kill-task",e))};i&&r.on("inner-kill-task",T)}}var h={getService:function(e,t){"function"==typeof e&&(t=e,e={});var r="https:",n=this.options.ServiceDomain,i=e.Region;n?(n=n.replace(/\{\{Region\}\}/gi,i||"").replace(/\{\{.*?\}\}/gi,""),/^[a-zA-Z]+:\/\//.test(n)||(n=r+"//"+n),"/"===n.slice(-1)&&(n=n.slice(0,-1))):n=i?r+"//cos."+i+".myqcloud.com":r+"//service.cos.myqcloud.com";n.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1"),f.call(this,{Action:"name/cos:GetService",url:n,method:"GET",headers:e.Headers,tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r&&r.ListAllMyBucketsResult&&r.ListAllMyBucketsResult.Buckets&&r.ListAllMyBucketsResult.Buckets.Bucket||[];n=a.isArray(n)?n:[n];var i=r&&r.ListAllMyBucketsResult&&r.ListAllMyBucketsResult.Owner||{};t(null,{Buckets:n,Owner:i,statusCode:r.statusCode,headers:r.headers})}))},putBucket:function(e,t){var r=this,n="";if(e.BucketAZConfig){var i={BucketAZConfig:e.BucketAZConfig};n=a.json2xml({CreateBucketConfiguration:i})}f.call(this,{Action:"name/cos:PutBucket",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,body:n,tracker:e.tracker},(function(n,a){if(n)return t(n);var i=c({protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,isLocation:!0});t(null,{Location:i,statusCode:a.statusCode,headers:a.headers})}))},headBucket:function(e,t){f.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD",tracker:e.tracker},(function(e,r){t(e,r)}))},getBucket:function(e,t){var r={};r.prefix=e.Prefix||"",r.delimiter=e.Delimiter,r.marker=e.Marker,r["max-keys"]=e.MaxKeys,r["encoding-type"]=e.EncodingType,f.call(this,{Action:"name/cos:GetBucket",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r,tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.ListBucketResult||{},i=n.Contents||[],o=n.CommonPrefixes||[];i=a.isArray(i)?i:[i],o=a.isArray(o)?o:[o];var s=a.clone(n);a.extend(s,{Contents:i,CommonPrefixes:o,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},deleteBucket:function(e,t){f.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketAcl:function(e,t){var r=e.Headers,n="";if(e.AccessControlPolicy){var i=a.clone(e.AccessControlPolicy||{}),o=i.Grants||i.Grant;o=a.isArray(o)?o:[o],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:o},n=a.json2xml({AccessControlPolicy:i}),r["Content-Type"]="application/xml",r["Content-MD5"]=a.binaryBase64(a.md5(n))}a.each(r,(function(e,t){0===t.indexOf("x-cos-grant-")&&(r[t]=s(r[t]))})),f.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:r,action:"acl",body:n,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketAcl:function(e,t){f.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl",tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.AccessControlPolicy||{},i=n.Owner||{},s=n.AccessControlList.Grant||[];s=a.isArray(s)?s:[s];var c=o(n);r.headers&&r.headers["x-cos-acl"]&&(c.ACL=r.headers["x-cos-acl"]),c=a.extend(c,{Owner:i,Grants:s,statusCode:r.statusCode,headers:r.headers}),t(null,c)}))},putBucketCors:function(e,t){var r=(e.CORSConfiguration||{}).CORSRules||e.CORSRules||[];r=a.clone(a.isArray(r)?r:[r]),a.each(r,(function(e){a.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var r=t+"s",n=e[r]||e[t]||[];delete e[r],e[t]=a.isArray(n)?n:[n]}))}));var n={CORSRule:r};e.ResponseVary&&(n.ResponseVary=e.ResponseVary);var i=a.json2xml({CORSConfiguration:n}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=a.binaryBase64(a.md5(i)),f.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"cors",headers:o,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketCors:function(e,t){f.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},(function(e,r){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var n={CORSRules:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var i=r.CORSConfiguration||{},o=i.CORSRules||i.CORSRule||[];o=a.clone(a.isArray(o)?o:[o]);var s=i.ResponseVary;a.each(o,(function(e){a.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var r=t+"s",n=e[r]||e[t]||[];delete e[t],e[r]=a.isArray(n)?n:[n]}))})),t(null,{CORSRules:o,ResponseVary:s,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketCors:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode||e.statusCode,headers:r.headers})}))},getBucketLocation:function(e,t){f.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location",tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,r)}))},getBucketPolicy:function(e,t){f.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0,tracker:e.tracker},(function(e,r){if(e)return e.statusCode&&403===e.statusCode?t({ErrorStatus:"Access Denied"}):e.statusCode&&405===e.statusCode?t({ErrorStatus:"Method Not Allowed"}):e.statusCode&&404===e.statusCode?t({ErrorStatus:"Policy Not Found"}):t(e);var n={};try{n=JSON.parse(r.body)}catch(e){}t(null,{Policy:n,statusCode:r.statusCode,headers:r.headers})}))},putBucketPolicy:function(e,t){var r=e.Policy,n=r;try{"string"==typeof r?r=JSON.parse(n):n=JSON.stringify(r)}catch(e){t({error:"Policy format error"})}var i=e.Headers;i["Content-Type"]="application/json",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:n,headers:i,json:!0,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},deleteBucketPolicy:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode||e.statusCode,headers:r.headers})}))},putBucketTagging:function(e,t){var r=e.Tagging||{},n=r.TagSet||r.Tags||e.Tags||[];n=a.clone(a.isArray(n)?n:[n]);var i=a.json2xml({Tagging:{TagSet:{Tag:n}}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=a.binaryBase64(a.md5(i)),f.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"tagging",headers:o,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketTagging:function(e,t){f.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var n={Tags:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else{var i=[];try{i=r.Tagging.TagSet.Tag||[]}catch(e){}i=a.clone(a.isArray(i)?i:[i]),t(null,{Tags:i,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketTagging:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketLifecycle:function(e,t){var r=(e.LifecycleConfiguration||{}).Rules||e.Rules||[];r=a.clone(r);var n=a.json2xml({LifecycleConfiguration:{Rule:r}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"lifecycle",headers:i,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketLifecycle:function(e,t){f.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},(function(e,r){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var n={Rules:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var i=[];try{i=r.LifecycleConfiguration.Rule||[]}catch(e){}i=a.clone(a.isArray(i)?i:[i]),t(null,{Rules:i,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketLifecycle:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketVersioning:function(e,t){if(e.VersioningConfiguration){var r=e.VersioningConfiguration||{},n=a.json2xml({VersioningConfiguration:r}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"versioning",headers:i,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t({error:"missing param VersioningConfiguration"})},getBucketVersioning:function(e,t){f.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning",tracker:e.tracker},(function(e,r){e||!r.VersioningConfiguration&&(r.VersioningConfiguration={}),t(e,r)}))},putBucketReplication:function(e,t){var r=a.clone(e.ReplicationConfiguration),n=a.json2xml({ReplicationConfiguration:r});n=(n=n.replace(/<(\/?)Rules>/gi,"<$1Rule>")).replace(/<(\/?)Tags>/gi,"<$1Tag>");var i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"replication",headers:i,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketReplication:function(e,t){f.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var n={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else e||!r.ReplicationConfiguration&&(r.ReplicationConfiguration={}),r.ReplicationConfiguration.Rule&&(r.ReplicationConfiguration.Rules=r.ReplicationConfiguration.Rule,delete r.ReplicationConfiguration.Rule),t(e,r)}))},deleteBucketReplication:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketWebsite:function(e,t){if(e.WebsiteConfiguration){var r=a.clone(e.WebsiteConfiguration||{}),n=r.RoutingRules||r.RoutingRule||[];n=a.isArray(n)?n:[n],delete r.RoutingRule,delete r.RoutingRules,n.length&&(r.RoutingRules={RoutingRule:n});var i=a.json2xml({WebsiteConfiguration:r}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=a.binaryBase64(a.md5(i)),f.call(this,{Action:"name/cos:PutBucketWebsite",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"website",headers:o,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t({error:"missing param WebsiteConfiguration"})},getBucketWebsite:function(e,t){f.call(this,{Action:"name/cos:GetBucketWebsite",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"website",tracker:e.tracker},(function(e,r){if(e)if(404===e.statusCode&&"NoSuchWebsiteConfiguration"===e.error.Code){var n={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var i=r.WebsiteConfiguration||{};if(i.RoutingRules){var o=a.clone(i.RoutingRules.RoutingRule||[]);o=a.makeArray(o),i.RoutingRules=o}t(null,{WebsiteConfiguration:i,statusCode:r.statusCode,headers:r.headers})}}))},deleteBucketWebsite:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketWebsite",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"website",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketReferer:function(e,t){if(e.RefererConfiguration){var r=a.clone(e.RefererConfiguration||{}),n=r.DomainList||{},i=n.Domains||n.Domain||[];(i=a.isArray(i)?i:[i]).length&&(r.DomainList={Domain:i});var o=a.json2xml({RefererConfiguration:r}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=a.binaryBase64(a.md5(o)),f.call(this,{Action:"name/cos:PutBucketReferer",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"referer",headers:s,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t({error:"missing param RefererConfiguration"})},getBucketReferer:function(e,t){f.call(this,{Action:"name/cos:GetBucketReferer",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"referer",tracker:e.tracker},(function(e,r){if(e)if(404===e.statusCode&&"NoSuchRefererConfiguration"===e.error.Code){var n={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else t(e);else{var i=r.RefererConfiguration||{};if(i.DomainList){var o=a.makeArray(i.DomainList.Domain||[]);i.DomainList={Domains:o}}t(null,{RefererConfiguration:i,statusCode:r.statusCode,headers:r.headers})}}))},putBucketDomain:function(e,t){var r=(e.DomainConfiguration||{}).DomainRule||e.DomainRule||[];r=a.clone(r);var n=a.json2xml({DomainConfiguration:{DomainRule:r}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketDomain",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"domain",headers:i,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketDomain:function(e,t){f.call(this,{Action:"name/cos:GetBucketDomain",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},(function(e,r){if(e)return t(e);var n=[];try{n=r.DomainConfiguration.DomainRule||[]}catch(e){}n=a.clone(a.isArray(n)?n:[n]),t(null,{DomainRule:n,statusCode:r.statusCode,headers:r.headers})}))},deleteBucketDomain:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketDomain",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketOrigin:function(e,t){var r=(e.OriginConfiguration||{}).OriginRule||e.OriginRule||[];r=a.clone(r);var n=a.json2xml({OriginConfiguration:{OriginRule:r}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Action:"name/cos:PutBucketOrigin",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"origin",headers:i,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketOrigin:function(e,t){f.call(this,{Action:"name/cos:GetBucketOrigin",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},(function(e,r){if(e)return t(e);var n=[];try{n=r.OriginConfiguration.OriginRule||[]}catch(e){}n=a.clone(a.isArray(n)?n:[n]),t(null,{OriginRule:n,statusCode:r.statusCode,headers:r.headers})}))},deleteBucketOrigin:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketOrigin",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketLogging:function(e,t){var r=a.json2xml({BucketLoggingStatus:e.BucketLoggingStatus||""}),n=e.Headers;n["Content-Type"]="application/xml",n["Content-MD5"]=a.binaryBase64(a.md5(r)),f.call(this,{Action:"name/cos:PutBucketLogging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"logging",headers:n,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketLogging:function(e,t){f.call(this,{Action:"name/cos:GetBucketLogging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"logging",tracker:e.tracker},(function(e,r){if(e)return t(e);delete r.BucketLoggingStatus._xmlns,t(null,{BucketLoggingStatus:r.BucketLoggingStatus,statusCode:r.statusCode,headers:r.headers})}))},putBucketInventory:function(e,t){var r=a.clone(e.InventoryConfiguration);if(r.OptionalFields){var n=r.OptionalFields||[];r.OptionalFields={Field:n}}if(r.Destination&&r.Destination.COSBucketDestination&&r.Destination.COSBucketDestination.Encryption){var i=r.Destination.COSBucketDestination.Encryption;Object.keys(i).indexOf("SSECOS")>-1&&(i["SSE-COS"]=i.SSECOS,delete i.SSECOS)}var o=a.json2xml({InventoryConfiguration:r}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=a.binaryBase64(a.md5(o)),f.call(this,{Action:"name/cos:PutBucketInventory",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"inventory",qs:{id:e.Id},headers:s,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getBucketInventory:function(e,t){f.call(this,{Action:"name/cos:GetBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id},tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.InventoryConfiguration;if(n&&n.OptionalFields&&n.OptionalFields.Field){var i=n.OptionalFields.Field;a.isArray(i)||(i=[i]),n.OptionalFields=i}if(n.Destination&&n.Destination.COSBucketDestination&&n.Destination.COSBucketDestination.Encryption){var o=n.Destination.COSBucketDestination.Encryption;Object.keys(o).indexOf("SSE-COS")>-1&&(o.SSECOS=o["SSE-COS"],delete o["SSE-COS"])}t(null,{InventoryConfiguration:n,statusCode:r.statusCode,headers:r.headers})}))},listBucketInventory:function(e,t){f.call(this,{Action:"name/cos:ListBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{"continuation-token":e.ContinuationToken},tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.ListInventoryConfigurationResult,i=n.InventoryConfiguration||[];i=a.isArray(i)?i:[i],delete n.InventoryConfiguration,a.each(i,(function(e){if(e&&e.OptionalFields&&e.OptionalFields.Field){var t=e.OptionalFields.Field;a.isArray(t)||(t=[t]),e.OptionalFields=t}if(e.Destination&&e.Destination.COSBucketDestination&&e.Destination.COSBucketDestination.Encryption){var r=e.Destination.COSBucketDestination.Encryption;Object.keys(r).indexOf("SSE-COS")>-1&&(r.SSECOS=r["SSE-COS"],delete r["SSE-COS"])}})),n.InventoryConfigurations=i,a.extend(n,{statusCode:r.statusCode,headers:r.headers}),t(null,n)}))},deleteBucketInventory:function(e,t){f.call(this,{Action:"name/cos:DeleteBucketInventory",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id},tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},putBucketAccelerate:function(e,t){if(e.AccelerateConfiguration){var r={AccelerateConfiguration:e.AccelerateConfiguration||{}},n=a.json2xml(r),i={"Content-Type":"application/xml"};i["Content-MD5"]=a.binaryBase64(a.md5(n)),f.call(this,{Interface:"putBucketAccelerate",Action:"name/cos:PutBucketAccelerate",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"accelerate",headers:i,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))}else t({error:"missing param AccelerateConfiguration"})},getBucketAccelerate:function(e,t){f.call(this,{Interface:"getBucketAccelerate",Action:"name/cos:GetBucketAccelerate",method:"GET",Bucket:e.Bucket,Region:e.Region,action:"accelerate",tracker:e.tracker},(function(e,r){e||!r.AccelerateConfiguration&&(r.AccelerateConfiguration={}),t(e,r)}))},getObject:function(e,t){if(this.options.ObjectKeySimplifyCheck&&"/"===a.simplifyPath(e.Key))return void t(a.error(new Error("The Getobject Key is illegal")));var r=e.Query||{},n=e.QueryString||"",i=e.tracker;i&&i.setParams({signStartTime:(new Date).getTime()}),r["response-content-type"]=e.ResponseContentType,r["response-content-language"]=e.ResponseContentLanguage,r["response-expires"]=e.ResponseExpires,r["response-cache-control"]=e.ResponseCacheControl,r["response-content-disposition"]=e.ResponseContentDisposition,r["response-content-encoding"]=e.ResponseContentEncoding,f.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,qs:r,qsStr:n,rawBody:!0,dataType:e.DataType,tracker:i},(function(r,n){if(r){var i=r.statusCode;return e.Headers["If-Modified-Since"]&&i&&304===i?t(null,{NotModified:!0}):t(r)}t(null,{Body:n.body,ETag:a.attr(n.headers,"etag",""),statusCode:n.statusCode,headers:n.headers})}))},headObject:function(e,t){f.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},(function(r,n){if(r){var i=r.statusCode;return e.Headers["If-Modified-Since"]&&i&&304===i?t(null,{NotModified:!0,statusCode:i}):t(r)}n.ETag=a.attr(n.headers,"etag",""),t(null,n)}))},listObjectVersions:function(e,t){var r={};r.prefix=e.Prefix||"",r.delimiter=e.Delimiter,r["key-marker"]=e.KeyMarker,r["version-id-marker"]=e.VersionIdMarker,r["max-keys"]=e.MaxKeys,r["encoding-type"]=e.EncodingType,f.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r,action:"versions",tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.ListVersionsResult||{},i=n.DeleteMarker||[];i=a.isArray(i)?i:[i];var o=n.Version||[];o=a.isArray(o)?o:[o];var s=a.clone(n);delete s.DeleteMarker,delete s.Version,a.extend(s,{DeleteMarkers:i,Versions:o,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},putObject:function(e,t){var r=this,n=e.ContentLength,o=a.throttleOnProgress.call(r,n,e.onProgress),s=e.Headers;s["Cache-Control"]||s["cache-control"]||(s["Cache-Control"]=""),s["Content-Type"]||s["content-type"]||(s["Content-Type"]=i.getType(e.Key)||"application/octet-stream");var l=e.UploadAddMetaMd5||r.options.UploadAddMetaMd5||r.options.UploadCheckContentMd5,p=e.tracker;l&&p&&p.setParams({md5StartTime:(new Date).getTime()}),a.getBodyMd5(l,e.Body,(function(i){i&&(p&&p.setParams({md5EndTime:(new Date).getTime()}),r.options.UploadCheckContentMd5&&(s["Content-MD5"]=a.binaryBase64(i)),(e.UploadAddMetaMd5||r.options.UploadAddMetaMd5)&&(s["x-cos-meta-md5"]=i)),void 0!==e.ContentLength&&(s["Content-Length"]=e.ContentLength),o(null,!0),f.call(r,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:e.Query,body:e.Body,onProgress:o,tracker:p},(function(i,s){if(i)return o(null,!0),t(i);o({loaded:n,total:n},!0);var l=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:r.options.UseAccelerate?"accelerate":e.Region,object:e.Key});l=l.substr(l.indexOf("://")+3),s.Location=l,s.ETag=a.attr(s.headers,"etag",""),t(null,s)}))}))},postObject:function(e,t){var r=this,n={},i=e.FilePath;if(i){for(var o in n["Cache-Control"]=e.CacheControl,n["Content-Disposition"]=e.ContentDisposition,n["Content-Encoding"]=e.ContentEncoding,n["Content-MD5"]=e.ContentMD5,n["Content-Length"]=e.ContentLength,n["Content-Type"]=e.ContentType,n.Expect=e.Expect,n.Expires=e.Expires,n["x-cos-acl"]=e.ACL,n["x-cos-grant-read"]=e.GrantRead,n["x-cos-grant-write"]=e.GrantWrite,n["x-cos-grant-full-control"]=e.GrantFullControl,n["x-cos-storage-class"]=e.StorageClass,n["x-cos-mime-limit"]=e.MimeLimit,n["x-cos-traffic-limit"]=e.TrafficLimit,n["x-cos-forbid-overwrite"]=e.ForbidOverwrite,n["x-cos-server-side-encryption-customer-algorithm"]=e.SSECustomerAlgorithm,n["x-cos-server-side-encryption-customer-key"]=e.SSECustomerKey,n["x-cos-server-side-encryption-customer-key-MD5"]=e.SSECustomerKeyMD5,n["x-cos-server-side-encryption"]=e.ServerSideEncryption,n["x-cos-server-side-encryption-cos-kms-key-id"]=e.SSEKMSKeyId,n["x-cos-server-side-encryption-context"]=e.SSEContext,delete n["Content-Length"],delete n["content-length"],e)o.indexOf("x-cos-meta-")>-1&&(n[o]=e[o]);var s=a.throttleOnProgress.call(r,n["Content-Length"],e.onProgress);f.call(this,{Action:"name/cos:PostObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:n,qs:e.Query,filePath:i,TaskId:e.TaskId,onProgress:s,tracker:e.tracker},(function(n,a){if(s(null,!0),n)return t(n);if(a&&a.headers){var o=a.headers,l=o.etag||o.Etag||o.ETag||"",p=i.substr(i.lastIndexOf("/")+1),u=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key.replace(/\$\{filename\}/g,p),isLocation:!0});return t(null,{Location:u,statusCode:a.statusCode,headers:o,ETag:l})}t(null,a)}))}else t({error:"missing param FilePath"})},deleteObject:function(e,t){f.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId,tracker:e.tracker},(function(e,r){if(e){var n=e.statusCode;return n&&204===n?t(null,{statusCode:n}):n&&404===n?t(null,{BucketNotFound:!0,statusCode:n}):t(e)}t(null,{statusCode:r.statusCode,headers:r.headers})}))},getObjectAcl:function(e,t){var r={};e.VersionId&&(r.versionId=e.VersionId),f.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r,action:"acl",tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.AccessControlPolicy||{},i=n.Owner||{},s=n.AccessControlList&&n.AccessControlList.Grant||[];s=a.isArray(s)?s:[s];var c=o(n);r.headers&&r.headers["x-cos-acl"]&&(c.ACL=r.headers["x-cos-acl"]),c=a.extend(c,{Owner:i,Grants:s,statusCode:r.statusCode,headers:r.headers}),t(null,c)}))},putObjectAcl:function(e,t){var r=e.Headers,n="";if(e.AccessControlPolicy){var i=a.clone(e.AccessControlPolicy||{}),o=i.Grants||i.Grant;o=a.isArray(o)?o:[o],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:o},n=a.json2xml({AccessControlPolicy:i}),r["Content-Type"]="application/xml",r["Content-MD5"]=a.binaryBase64(a.md5(n))}a.each(r,(function(e,t){0===t.indexOf("x-cos-grant-")&&(r[t]=s(r[t]))})),f.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:r,body:n,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},optionsObject:function(e,t){var r=e.Headers;r.Origin=e.Origin,r["Access-Control-Request-Method"]=e.AccessControlRequestMethod,r["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,f.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:r,tracker:e.tracker},(function(e,r){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var n=r.headers||{};t(null,{AccessControlAllowOrigin:n["access-control-allow-origin"],AccessControlAllowMethods:n["access-control-allow-methods"],AccessControlAllowHeaders:n["access-control-allow-headers"],AccessControlExposeHeaders:n["access-control-expose-headers"],AccessControlMaxAge:n["access-control-max-age"],statusCode:r.statusCode,headers:r.headers})}))},putObjectCopy:function(e,t){var r=e.Headers;!r["Cache-Control"]&&r["cache-control"]&&(r["Cache-Control"]="");var n=e.CopySource||"",i=a.getSourceParams.call(this,n);if(i){var o=i.Bucket,s=i.Region,c=decodeURIComponent(i.Key);f.call(this,{Scope:[{action:"name/cos:GetObject",bucket:o,region:s,prefix:c},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},(function(e,r){if(e)return t(e);var n=a.clone(r.CopyObjectResult||{});a.extend(n,{statusCode:r.statusCode,headers:r.headers}),t(null,n)}))}else t({error:"CopySource format error"})},deleteMultipleObject:function(e,t){var r=e.Objects||[],n=e.Quiet;r=a.isArray(r)?r:[r];var i=a.json2xml({Delete:{Object:r,Quiet:n||!1}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=a.binaryBase64(a.md5(i));var s=a.map(r,(function(t){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:t.Key}}));f.call(this,{Scope:s,method:"POST",Bucket:e.Bucket,Region:e.Region,body:i,action:"delete",headers:o,tracker:e.tracker},(function(e,r){if(e)return t(e);var n=r.DeleteResult||{},i=n.Deleted||[],o=n.Error||[];i=a.isArray(i)?i:[i],o=a.isArray(o)?o:[o];var s=a.clone(n);a.extend(s,{Error:o,Deleted:i,statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},restoreObject:function(e,t){var r=e.Headers;if(e.RestoreRequest){var n=e.RestoreRequest||{},i=a.json2xml({RestoreRequest:n});r["Content-Type"]="application/xml",r["Content-MD5"]=a.binaryBase64(a.md5(i)),f.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:i,action:"restore",headers:r,tracker:e.tracker},(function(e,r){t(e,r)}))}else t({error:"missing param RestoreRequest"})},putObjectTagging:function(e,t){var r=e.Tagging||{},n=r.TagSet||r.Tags||e.Tags||[];n=a.clone(a.isArray(n)?n:[n]);var i=a.json2xml({Tagging:{TagSet:{Tag:n}}}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=a.binaryBase64(a.md5(i)),f.call(this,{Interface:"putObjectTagging",Action:"name/cos:PutObjectTagging",method:"PUT",Bucket:e.Bucket,Key:e.Key,Region:e.Region,body:i,action:"tagging",headers:o,VersionId:e.VersionId,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},getObjectTagging:function(e,t){f.call(this,{Interface:"getObjectTagging",Action:"name/cos:GetObjectTagging",method:"GET",Key:e.Key,Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},(function(e,r){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var n={Tags:[],statusCode:e.statusCode};e.headers&&(n.headers=e.headers),t(null,n)}else{var i=[];try{i=r.Tagging.TagSet.Tag||[]}catch(e){}i=a.clone(a.isArray(i)?i:[i]),t(null,{Tags:i,statusCode:r.statusCode,headers:r.headers})}}))},deleteObjectTagging:function(e,t){f.call(this,{Interface:"deleteObjectTagging",Action:"name/cos:DeleteObjectTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},(function(e,r){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:r.statusCode,headers:r.headers})}))},appendObject:function(e,t){f.call(this,{Action:"name/cos:AppendObject",method:"POST",Bucket:e.Bucket,Region:e.Region,action:"append",Key:e.Key,body:e.Body,qs:{position:e.Position},headers:e.Headers,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,r)}))},uploadPartCopy:function(e,t){var r=e.CopySource||"",n=a.getSourceParams.call(this,r);if(n){var i=n.Bucket,o=n.Region,s=decodeURIComponent(n.Key);f.call(this,{Scope:[{action:"name/cos:GetObject",bucket:i,region:o,prefix:s},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,tracker:e.tracker},(function(e,r){if(e)return t(e);var n=a.clone(r.CopyPartResult||{});a.extend(n,{statusCode:r.statusCode,headers:r.headers}),t(null,n)}))}else t({error:"CopySource format error"})},multipartInit:function(e,t){var r=e.Headers,n=e.tracker;r["Cache-Control"]||r["cache-control"]||(r["Cache-Control"]=""),r["Content-Type"]||r["content-type"]||(r["Content-Type"]=i.getType(e.Key)||"application/octet-stream"),f.call(this,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,qs:e.Query,tracker:n},(function(e,r){return e?(n&&n.parent&&n.parent.setParams({errorNode:"multipartInit"}),t(e)):(r=a.clone(r||{}))&&r.InitiateMultipartUploadResult?t(null,a.extend(r.InitiateMultipartUploadResult,{statusCode:r.statusCode,headers:r.headers})):void t(null,r)}))},multipartUpload:function(e,t){var r=this;a.getFileSize("multipartUpload",e,(function(){var n=e.tracker,i=r.options.UploadCheckContentMd5;i&&n&&n.setParams({md5StartTime:(new Date).getTime()}),a.getBodyMd5(i,e.Body,(function(o){o&&(e.Headers["Content-MD5"]=a.binaryBase64(o),i&&n&&n.setParams({md5EndTime:(new Date).getTime()})),n&&n.setParams({partNumber:e.PartNumber}),f.call(r,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null,tracker:n},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartUpload"}),t(e);t(null,{ETag:a.attr(r.headers,"etag",{}),statusCode:r.statusCode,headers:r.headers})}))}))}))},multipartComplete:function(e,t){for(var r=this,n=e.UploadId,i=e.Parts,o=e.tracker,s=0,l=i.length;s<l;s++)0!==i[s].ETag.indexOf('"')&&(i[s].ETag='"'+i[s].ETag+'"');var p=a.json2xml({CompleteMultipartUpload:{Part:i}}),u=e.Headers;u["Content-Type"]="application/xml",u["Content-MD5"]=a.binaryBase64(a.md5(p)),f.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:n},body:p,headers:u,tracker:o},(function(n,i){if(n)return o&&o.parent&&o.parent.setParams({errorNode:"multipartComplete"}),t(n);var s=c({ForcePathStyle:r.options.ForcePathStyle,protocol:r.options.Protocol,domain:r.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0}),l=i.CompleteMultipartUploadResult||{},p=a.extend(l,{Location:s,statusCode:i.statusCode,headers:i.headers});t(null,p)}))},multipartList:function(e,t){var r={};r.delimiter=e.Delimiter,r["encoding-type"]=e.EncodingType,r.prefix=e.Prefix||"",r["max-uploads"]=e.MaxUploads,r["key-marker"]=e.KeyMarker,r["upload-id-marker"]=e.UploadIdMarker,r=a.clearKey(r);var n=e.tracker;n&&n.setParams({signStartTime:(new Date).getTime()}),f.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:r.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:r,action:"uploads",tracker:n},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartList"}),t(e);if(r&&r.ListMultipartUploadsResult){var i=r.ListMultipartUploadsResult.Upload||[],o=r.ListMultipartUploadsResult.CommonPrefixes||[];o=a.isArray(o)?o:[o],i=a.isArray(i)?i:[i],r.ListMultipartUploadsResult.Upload=i,r.ListMultipartUploadsResult.CommonPrefixes=o}var s=a.clone(r.ListMultipartUploadsResult||{});a.extend(s,{statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},multipartListPart:function(e,t){var r={},n=e.tracker;r.uploadId=e.UploadId,r["encoding-type"]=e.EncodingType,r["max-parts"]=e.MaxParts,r["part-number-marker"]=e.PartNumberMarker,f.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r,tracker:n},(function(e,r){if(e)return n&&n.parent&&n.parent.setParams({errorNode:"multipartListPart"}),t(e);var i=r.ListPartsResult||{},o=i.Part||[];o=a.isArray(o)?o:[o],i.Part=o;var s=a.clone(i);a.extend(s,{statusCode:r.statusCode,headers:r.headers}),t(null,s)}))},multipartAbort:function(e,t){var r={};r.uploadId=e.UploadId,f.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:r,tracker:e.tracker},(function(e,r){if(e)return t(e);t(null,{statusCode:r.statusCode,headers:r.headers})}))},request:function(e,t){f.call(this,{method:e.Method,Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:e.Action,headers:e.Headers,qs:e.Query,body:e.Body,Url:e.Url,rawBody:e.RawBody,dataType:e.DataType,tracker:e.tracker},(function(e,r){if(e)return t(e);r&&r.body&&(r.Body=r.body,delete r.body),t(e,r)}))},getObjectUrl:function(e,t){var r=this,n=void 0===e.UseAccelerate?r.options.UseAccelerate:e.UseAccelerate,i=c({ForcePathStyle:r.options.ForcePathStyle,protocol:e.Protocol||r.options.Protocol,domain:e.Domain||r.options.Domain,bucket:e.Bucket,region:n?"accelerate":e.Region,object:e.Key}),o="";e.Query&&(o+=a.obj2str(e.Query)),e.QueryString&&(o+=(o?"&":"")+e.QueryString);var s=i;if(void 0!==e.Sign&&!e.Sign)return o&&(s+="?"+o),t(null,{Url:s}),s;var u=l.call(this,{Bucket:e.Bucket,Region:e.Region,UseAccelerate:e.UseAccelerate,Url:i}),d=p.call(this,{Action:"PUT"===(e.Method||"").toUpperCase()?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires,Headers:e.Headers,Query:e.Query,SignHost:u,ForceSignHost:!1!==e.ForceSignHost&&r.options.ForceSignHost},(function(e,r){if(t)if(e)t(e);else{var n=i;n+="?"+(r.Authorization.indexOf("q-signature")>-1?function(e){var t=e.match(/q-url-param-list.*?(?=&)/g)[0],r="q-url-param-list="+encodeURIComponent(t.replace(/q-url-param-list=/,"")).toLowerCase(),n=new RegExp(t,"g");return e.replace(n,r)}(r.Authorization):"sign="+encodeURIComponent(r.Authorization)),r.SecurityToken&&(n+="&x-cos-security-token="+r.SecurityToken),r.ClientIP&&(n+="&clientIP="+r.ClientIP),r.ClientUA&&(n+="&clientUA="+r.ClientUA),r.Token&&(n+="&token="+r.Token),o&&(n+="&"+o),setTimeout((function(){t(null,{Url:n})}))}}));return d?(s+="?"+d.Authorization+(d.SecurityToken?"&x-cos-security-token="+d.SecurityToken:""),o&&(s+="&"+o)):o&&(s+="?"+o),s},getAuth:function(e){return a.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Bucket:e.Bucket,Region:e.Region,Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,SystemClockOffset:this.options.SystemClockOffset})}};e.exports.init=function(e,t){t.transferToTaskMethod(h,"postObject"),t.transferToTaskMethod(h,"putObject"),a.each(h,(function(t,r){e.prototype[r]=a.apiWrapper(r,t)}))}},function(e,t){function r(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}var n=function(e,t){var n,a,i,o=[],s=function(e,t){var n=[];for(var a in e)e.hasOwnProperty(a)&&n.push(t?r(a).toLowerCase():a);return n.sort((function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1}))}(e);for(n=0;n<s.length;n++)i=void 0===e[a=s[n]]||null===e[a]?"":""+e[a],a=t?r(a).toLowerCase():r(a),i=r(i)||"",o.push(a+"="+i);return o.join("&")};e.exports=function(e,t){var r,a=e.filePath,i=e.headers||{},o=e.url||e.Url,s=e.method,c=e.onProgress,l=e.httpDNSServiceId,p=function(e,r){var n=r.header,a={};if(n)for(var i in n)n.hasOwnProperty(i)&&(a[i.toLowerCase()]=n[i]);t(e,{statusCode:r.statusCode,headers:a},r.data)};if(a){var u,d=o.match(/^(https?:\/\/[^/]+\/)([^/]*\/?)(.*)$/);e.pathStyle?(u=decodeURIComponent(d[3]||""),o=d[1]+d[2]):(u=decodeURIComponent(d[2]+d[3]||""),o=d[1]);var f={key:u,success_action_status:200,Signature:i.Authorization},m=["Cache-Control","Content-Type","Content-Disposition","Content-Encoding","Expires","x-cos-storage-class","x-cos-security-token","x-ci-security-token"];for(var h in e.headers)e.headers.hasOwnProperty(h)&&(h.indexOf("x-cos-meta-")>-1||m.indexOf(h)>-1)&&(f[h]=e.headers[h]);i["x-cos-acl"]&&(f.acl=i["x-cos-acl"]),!f["Content-Type"]&&(f["Content-Type"]=""),(r=wx.uploadFile({url:o,method:s,name:"file",header:i,filePath:a,formData:f,timeout:e.timeout,success:function(e){p(null,e)},fail:function(e){p(e.errMsg,e)}})).onProgressUpdate((function(e){c&&c({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend,progress:e.progress/100})}))}else{var g=e.qs&&n(e.qs)||"";g&&(o+=(o.indexOf("?")>-1?"&":"?")+g),i["Content-Length"]&&delete i["Content-Length"];var v={url:o,method:s,header:i,dataType:"text",data:e.body,responseType:e.dataType||"text",timeout:e.timeout,redirect:"manual",success:function(e){p(null,e)},fail:function(e){p(e.errMsg,e)}};l&&Object.assign(v,{enableHttpDNS:!0,httpDNSServiceId:l}),r=wx.request(v)}return r}},function(e,t,r){"use strict";let n=r(24);e.exports=new n(r(25),r(26))},function(e,t,r){"use strict";function n(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}n.prototype.define=function(e,t){for(let r in e){let n=e[r].map((function(e){return e.toLowerCase()}));r=r.toLowerCase();for(let e=0;e<n.length;e++){const a=n[e];if("*"!==a[0]){if(!t&&a in this._types)throw new Error('Attempt to change mapping for "'+a+'" extension from "'+this._types[a]+'" to "'+r+'". Pass `force=true` to allow this, otherwise remove "'+a+'" from the list of extensions for "'+r+'".');this._types[a]=r}}if(t||!this._extensions[r]){const e=n[0];this._extensions[r]="*"!==e[0]?e:e.substr(1)}}},n.prototype.getType=function(e){let t=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),r=t.replace(/^.*\./,"").toLowerCase(),n=t.length<e.length;return(r.length<t.length-1||!n)&&this._types[r]||null},n.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null},e.exports=n},function(e,t){e.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}},function(e,t){e.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}},function(e,t,r){function n(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */n=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",p=s.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof x?t:x,i=Object.create(a.prototype),s=new N(n||[]);return o(i,"_invoke",{value:A(e,r,s)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function x(){}function b(){}function k(){}var w={};u(w,c,(function(){return this}));var C=Object.getPrototypeOf,S=C&&C(C(D([])));S&&S!==r&&i.call(S,c)&&(w=S);var T=k.prototype=x.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function R(e,t){function r(n,o,s,c){var l=f(e[n],e,o);if("throw"!==l.type){var p=l.arg,u=p.value;return u&&"object"==a(u)&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(u).then((function(e){p.value=e,s(p)}),(function(e){return r("throw",e,s,c)}))}c(l.arg)}var n;o(this,"_invoke",{value:function(e,a){function i(){return new t((function(t,n){r(e,a,t,n)}))}return n=n?n.then(i,i):i()}})}function A(t,r,n){var a=m;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=B(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=f(t,r,n);if("normal"===l.type){if(a=n.done?v:h,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function B(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return b.prototype=k,o(T,"constructor",{value:k,configurable:!0}),o(k,"constructor",{value:b,configurable:!0}),b.displayName=u(k,p,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,u(e,p,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},E(R.prototype),u(R.prototype,l,(function(){return this})),t.AsyncIterator=R,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new R(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(T),u(T,p,"Generator"),u(T,c,(function(){return this})),u(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function s(e){i(o,n,a,s,c,"next",e)}function c(e){i(o,n,a,s,c,"throw",e)}s(void 0)}))}}var s=r(6),c=r(28),l=r(5).EventProxy,p=r(0),u=r(3);function d(e,t){var r=e.TaskId,n=e.Bucket,a=e.Region,i=e.Key,o=e.StorageClass,u=this,d={},h=e.FileSize,g=e.SliceSize,v=Math.ceil(h/g),y=0,x=p.throttleOnProgress.call(u,h,e.onHashProgress),b=function(t,r){var n=t.length;if(0===n)return r(null,!0);if(n>v)return r(null,!1);if(n>1&&Math.max(t[0].Size,t[1].Size)!==g)return r(null,!1);!function a(i){if(i<n){var o=t[i];!function(t,r){var n=g*(t-1),a=Math.min(n+g,h),i=a-n;d[t]?r(null,{PartNumber:t,ETag:d[t],Size:i}):p.fileSlice(e.FilePath,n,a,(function(e){try{var n=p.getFileMd5(e)}catch(e){return r(e)}var a='"'+n+'"';d[t]=a,1,y+=i,r(null,{PartNumber:t,ETag:a,Size:i}),x({loaded:y,total:h})}))}(o.PartNumber,(function(e,t){t&&t.ETag===o.ETag&&t.Size===o.Size?a(i+1):r(null,!1)}))}else r(null,!0)}(0)},k=new l;k.on("error",(function(e){if(u._isRunningTask(r))return t(e)})),k.on("upload_id_available",(function(e){var r={},n=[];p.each(e.PartList,(function(e){r[e.PartNumber]=e}));for(var a=1;a<=v;a++){var i=r[a];i?(i.PartNumber=a,i.Uploaded=!0):i={PartNumber:a,ETag:null,Uploaded:!1},n.push(i)}e.PartList=n,t(null,e)})),k.on("no_available_upload_id",(function(){if(u._isRunningTask(r)){var s=p.extend({Bucket:n,Region:a,Key:i,Headers:p.clone(e.Headers),Query:p.clone(e.Query),StorageClass:o,calledBySdk:"sliceUploadFile",tracker:e.tracker},e);u.multipartInit(s,(function(e,n){if(u._isRunningTask(r)){if(e)return k.emit("error",e);var a=n.UploadId;if(!a)return t({Message:"no upload id"});k.emit("upload_id_available",{UploadId:a,PartList:[]})}}))}})),k.on("has_and_check_upload_id",(function(t){t=t.reverse(),c.eachLimit(t,1,(function(t,o){u._isRunningTask(r)&&(s.using[t]?o():m.call(u,{Bucket:n,Region:a,Key:i,UploadId:t,tracker:e.tracker},(function(e,n){if(u._isRunningTask(r)){if(e)return s.removeUsing(t),k.emit("error",e);var a=n.PartList;a.forEach((function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""})),b(a,(function(e,n){if(u._isRunningTask(r))return e?k.emit("error",e):void(n?o({UploadId:t,PartList:a}):o())}))}})))}),(function(e){u._isRunningTask(r)&&(x(null,!0),e&&e.UploadId?k.emit("upload_id_available",e):k.emit("no_available_upload_id"))}))})),k.on("seek_local_avail_upload_id",(function(t){var o=s.getFileId(e.FileStat,e.ChunkSize,n,i),c=s.getUploadIdList(o);if(o&&c){!function o(l){if(l>=c.length)k.emit("has_and_check_upload_id",t);else{var d=c[l];if(!p.isInArray(t,d))return s.removeUploadId(d),void o(l+1);s.using[d]?o(l+1):m.call(u,{Bucket:n,Region:a,Key:i,UploadId:d,tracker:e.tracker},(function(e,t){u._isRunningTask(r)&&(e?(s.removeUploadId(d),o(l+1)):k.emit("upload_id_available",{UploadId:d,PartList:t.PartList}))}))}}(0)}else k.emit("has_and_check_upload_id",t)})),k.on("get_remote_upload_id_list",(function(){f.call(u,{Bucket:n,Region:a,Key:i,tracker:e.tracker},(function(t,a){if(u._isRunningTask(r)){if(t)return k.emit("error",t);var c=p.filter(a.UploadList,(function(e){return e.Key===i&&(!o||e.StorageClass.toUpperCase()===o.toUpperCase())})).reverse().map((function(e){return e.UploadId||e.UploadID}));if(c.length)k.emit("seek_local_avail_upload_id",c);else{var l,d=s.getFileId(e.FileStat,e.ChunkSize,n,i);d&&(l=s.getUploadIdList(d))&&p.each(l,(function(e){s.removeUploadId(e)})),k.emit("no_available_upload_id")}}}))})),k.emit("get_remote_upload_id_list")}function f(e,t){var r=this,n=[],a={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key,calledBySdk:e.calledBySdk||"sliceUploadFile",tracker:e.tracker};!function e(){r.multipartList(a,(function(r,i){if(r)return t(r);n.push.apply(n,i.Upload||[]),"true"===i.IsTruncated?(a.KeyMarker=i.NextKeyMarker,a.UploadIdMarker=i.NextUploadIdMarker,e()):t(null,{UploadList:n})}))}()}function m(e,t){var r=this,n=[],a={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId,calledBySdk:"sliceUploadFile",tracker:e.tracker};!function e(){r.multipartListPart(a,(function(r,i){if(r)return t(r);n.push.apply(n,i.Part||[]),"true"===i.IsTruncated?(a.PartNumberMarker=i.NextPartNumberMarker,e()):t(null,{PartList:n})}))}()}function h(e,t){var r=this,n=e.TaskId,a=e.Bucket,i=e.Region,o=e.Key,s=e.UploadData,l=e.FileSize,u=e.SliceSize,d=Math.min(e.AsyncLimit||r.options.ChunkParallelLimit||1,256),f=e.FilePath,m=Math.ceil(l/u),h=0,v=e.ServerSideEncryption,y=p.filter(s.PartList,(function(e){return e.Uploaded&&(h+=e.PartNumber>=m&&l%u||u),!e.Uploaded})),x=e.onProgress;c.eachLimit(y,d,(function(t,c){if(r._isRunningTask(n)){var p=t.PartNumber,d=Math.min(l,t.PartNumber*u)-(t.PartNumber-1)*u,m=0;g.call(r,{TaskId:n,Bucket:a,Region:i,Key:o,SliceSize:u,FileSize:l,PartNumber:p,ServerSideEncryption:v,FilePath:f,UploadData:s,onProgress:function(e){h+=e.loaded-m,m=e.loaded,x({loaded:h,total:l})},tracker:e.tracker},(function(e,a){r._isRunningTask(n)&&(e?h-=m:(h+=d-m,t.ETag=a.ETag),x({loaded:h,total:l}),c(e||null,a))}))}}),(function(e){if(r._isRunningTask(n))return e?t(e):void t(null,{UploadId:s.UploadId,SliceList:s.PartList})}))}function g(e,t){var r=this,n=e.TaskId,a=e.Bucket,i=e.Region,o=e.Key,s=e.FileSize,l=e.FilePath,u=1*e.PartNumber,d=e.SliceSize,f=e.ServerSideEncryption,m=e.UploadData,h=r.options.ChunkRetryTimes+1,g=e.Headers||{},v=d*(u-1),y=d,x=v+d;x>s&&(y=(x=s)-v);var b=["x-cos-traffic-limit","x-cos-mime-limit"],k={};p.each(g,(function(e,t){b.indexOf(t)>-1&&(k[t]=e)})),p.fileSlice(l,v,x,(function(s){var l=p.getFileMd5(s),d=l?p.binaryBase64(l):null,g=m.PartList[u-1];c.retry(h,(function(t){r._isRunningTask(n)&&r.multipartUpload({TaskId:n,Bucket:a,Region:i,Key:o,ContentLength:y,PartNumber:u,UploadId:m.UploadId,ServerSideEncryption:f,Body:s,Headers:k,onProgress:e.onProgress,ContentMD5:d,calledBySdk:"sliceUploadFile",tracker:e.tracker},(function(e,a){if(r._isRunningTask(n))return e?t(e):(g.Uploaded=!0,t(null,a))}))}),(function(e,a){if(r._isRunningTask(n))return t(e,a)}))}))}function v(e,t){var r=e.Bucket,n=e.Region,a=e.Key,i=e.UploadId,o=e.SliceList,s=this,l=this.options.ChunkRetryTimes+1,p=o.map((function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));c.retry(l,(function(t){s.multipartComplete({Bucket:r,Region:n,Key:a,UploadId:i,Parts:p,calledBySdk:"sliceUploadFile",Headers:e.Headers||{},tracker:e.tracker},t)}),(function(e,r){t(e,r)}))}function y(e,t){var r=e.Bucket,n=e.Region,a=e.Key,i=e.AbortArray,o=e.AsyncLimit||1,s=this,l=0,p=new Array(i.length);c.eachLimit(i,o,(function(t,i){var o=l;if(a&&a!==t.Key)return p[o]={error:{KeyNotMatch:!0}},void i(null);var c=t.UploadId||t.UploadID;s.multipartAbort({Bucket:r,Region:n,Key:t.Key,Headers:e.Headers,UploadId:c},(function(e){var a={Bucket:r,Region:n,Key:t.Key,UploadId:c};p[o]={error:e,task:a},i(null)})),l++}),(function(e){if(e)return t(e);for(var r=[],n=[],a=0,i=p.length;a<i;a++){var o=p[a];o.task&&(o.error?n.push(o.task):r.push(o.task))}return t(null,{successList:r,errorList:n})}))}function x(){return(x=o(n().mark((function e(t,r){var i,o,s,c,l,d,f,m,h,g,v,y;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=this,o=void 0===t.SliceSize?i.options.SliceSize:t.SliceSize,s=[],e.prev=3,e.next=6,p.getFileSizeByPath(t.FilePath);case 6:c=e.sent,e.next=13;break;case 9:return e.prev=9,e.t0=e.catch(3),r({error:e.t0}),e.abrupt("return");case 13:l={TaskId:""},i.options.EnableReporter&&(d=i.options.UseAccelerate||"string"==typeof i.options.Domain&&i.options.Domain.includes("accelerate."),f=c>o?"sliceUploadFile":"putObject",t.tracker=new u({Beacon:i.options.BeaconReporter,clsReporter:i.options.ClsReporter,bucket:t.Bucket,region:t.Region,apiName:"uploadFile",realApi:f,fileKey:t.Key,fileSize:c,accelerate:d,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})),p.each(t,(function(e,t){"object"!==a(e)&&"function"!=typeof e&&(l[t]=e)})),m=t.onTaskReady,t.onTaskReady=function(e){l.TaskId=e,m&&m(e)},h=t.onFileFinish,g=function(e,n){t.tracker&&t.tracker.report(e,n),h&&h(e,n,l),r&&r(e,n)},v="postObject"===i.options.SimpleUploadMethod?"postObject":"putObject",y=c>o?"sliceUploadFile":v,s.push({api:y,params:t,callback:g}),i._addTasks(s);case 24:case"end":return e.stop()}}),e,this,[[3,9]])})))).apply(this,arguments)}function b(){return b=o(n().mark((function e(t,r){var i,s,c,l,d,f,m,h,g,v,y;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=this,s=void 0===t.SliceSize?i.options.SliceSize:t.SliceSize,c=0,l=0,d=p.throttleOnProgress.call(i,l,t.onProgress),f=t.files.length,m=t.onFileFinish,h=Array(f),g=function(e,t,n){d(null,!0),m&&m(e,t,n),h[n.Index]={options:n,error:e,data:t},--f<=0&&r&&r(null,{files:h})},v=[],y=function(){return t.files.map((function(e,t){return new Promise(function(){var r=o(n().mark((function r(o){var f,m,h,y,x,b,k,w,C,S,T;return n().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return f=0,r.prev=1,r.next=4,p.getFileSizeByPath(e.FilePath);case 4:f=r.sent,r.next=9;break;case 7:r.prev=7,r.t0=r.catch(1);case 9:m={Index:t,TaskId:""},c+=f,i.options.EnableReporter&&(h=i.options.UseAccelerate||"string"==typeof i.options.Domain&&i.options.Domain.includes("accelerate."),y=f>s?"sliceUploadFile":"putObject",e.tracker=new u({Beacon:i.options.BeaconReporter,clsReporter:i.options.ClsReporter,bucket:e.Bucket,region:e.Region,apiName:"uploadFiles",realApi:y,fileKey:e.Key,fileSize:f,accelerate:h,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})),p.each(e,(function(e,t){"object"!==a(e)&&"function"!=typeof e&&(m[t]=e)})),x=e.onTaskReady,e.onTaskReady=function(e){m.TaskId=e,x&&x(e)},b=0,k=e.onProgress,e.onProgress=function(e){l=l-b+e.loaded,b=e.loaded,k&&k(e),d({loaded:l,total:c})},w=e.onFileFinish,C=function(t,r){e.tracker&&e.tracker.report(t,r),w&&w(t,r),g&&g(t,r,m)},S="postObject"===i.options.SimpleUploadMethod?"postObject":"putObject",T=f>s?"sliceUploadFile":S,v.push({api:T,params:e,callback:C}),o(!0);case 24:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}())}))},e.next=13,Promise.all(y());case 13:i._addTasks(v);case 14:case"end":return e.stop()}}),e,this)}))),b.apply(this,arguments)}function k(e,t){var r=e.TaskId,n=e.Bucket,a=e.Region,i=e.Key,o=e.CopySource,s=e.UploadId,l=1*e.PartNumber,p=e.CopySourceRange,u=this.options.ChunkRetryTimes+1,d=this;c.retry(u,(function(t){d.uploadPartCopy({TaskId:r,Bucket:n,Region:a,Key:i,CopySource:o,UploadId:s,PartNumber:l,CopySourceRange:p,onProgress:e.onProgress,tracker:e.tracker,calledBySdk:e.calledBySdk},(function(e,r){t(e||null,r)}))}),(function(e,r){return t(e,r)}))}var w={sliceUploadFile:function(e,t){var r=this;if(!p.canFileSlice())return e.SkipTask=!0,void("postObject"===r.options.SimpleUploadMethod?r.postObject(e,t):r.putObject(e,t));var n,a,i=new l,o=e.TaskId,c=e.Bucket,u=e.Region,f=e.Key,m=e.FilePath,g=e.ChunkSize||e.SliceSize||r.options.ChunkSize,y=e.AsyncLimit,x=e.StorageClass,b=e.ServerSideEncryption,k=e.onHashProgress,w=e.tracker;w&&w.setParams({chunkSize:g}),i.on("error",(function(n){if(r._isRunningTask(o)){var a={UploadId:e.UploadData.UploadId||"",err:n,error:n};return t(a)}})),i.on("upload_complete",(function(r){var n=p.extend({UploadId:e.UploadData.UploadId||""},r);t(null,n)})),i.on("upload_slice_complete",(function(t){var l={};p.each(e.Headers,(function(e,t){var r=t.toLowerCase();0!==r.indexOf("x-cos-meta-")&&"pic-operations"!==r||(l[t]=e)})),v.call(r,{Bucket:c,Region:u,Key:f,UploadId:t.UploadId,SliceList:t.SliceList,Headers:l,tracker:w},(function(e,c){if(r._isRunningTask(o)){if(s.removeUsing(t.UploadId),e)return a(null,!0),i.emit("error",e);s.removeUploadId(t.UploadId),a({loaded:n,total:n},!0),i.emit("upload_complete",c)}}))})),i.on("get_upload_data_finish",(function(t){var l=s.getFileId(e.FileStat,e.ChunkSize,c,f);l&&s.saveUploadId(l,t.UploadId,r.options.UploadIdCacheLimit),s.setUsing(t.UploadId),a(null,!0),h.call(r,{TaskId:o,Bucket:c,Region:u,Key:f,FilePath:m,FileSize:n,SliceSize:g,AsyncLimit:y,ServerSideEncryption:b,UploadData:t,onProgress:a,tracker:w},(function(e,t){if(r._isRunningTask(o))return e?(a(null,!0),i.emit("error",e)):void i.emit("upload_slice_complete",t)}))})),i.on("get_file_size_finish",(function(){if(a=p.throttleOnProgress.call(r,n,e.onProgress),e.UploadData.UploadId)i.emit("get_upload_data_finish",e.UploadData);else{var t=p.extend({TaskId:o,Bucket:c,Region:u,Key:f,Headers:e.Headers,StorageClass:x,FilePath:m,FileSize:n,SliceSize:g,onHashProgress:k,tracker:w},e);t.FileSize=n,d.call(r,t,(function(t,n){if(r._isRunningTask(o)){if(t)return i.emit("error",t);e.UploadData.UploadId=n.UploadId,e.UploadData.PartList=n.PartList,i.emit("get_upload_data_finish",e.UploadData)}}))}})),n=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),p.each(e.Headers,(function(t,r){"content-length"===r.toLowerCase()&&delete e.Headers[r]})),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],a=1048576,i=0;i<t.length&&!(n/(a=1024*t[i]*1024)<=r.options.MaxPartNumber);i++);e.ChunkSize=e.SliceSize=g=Math.max(g,a)}(),0===n?(e.Body="",e.ContentLength=0,e.SkipTask=!0,r.putObject(e,(function(e,r){if(e)return t(e);t(null,r)}))):i.emit("get_file_size_finish")},abortUploadTask:function(e,t){var r=e.Bucket,n=e.Region,a=e.Key,i=e.UploadId,o=e.Level||"task",s=e.AsyncLimit,c=this,p=new l;if(p.on("error",(function(e){return t(e)})),p.on("get_abort_array",(function(i){y.call(c,{Bucket:r,Region:n,Key:a,Headers:e.Headers,AsyncLimit:s,AbortArray:i},(function(e,r){if(e)return t(e);t(null,r)}))})),"bucket"===o)f.call(c,{Bucket:r,Region:n,calledBySdk:"abortUploadTask"},(function(e,r){if(e)return t(e);p.emit("get_abort_array",r.UploadList||[])}));else if("file"===o){if(!a)return t({error:"abort_upload_task_no_key"});f.call(c,{Bucket:r,Region:n,Key:a,calledBySdk:"abortUploadTask"},(function(e,r){if(e)return t(e);p.emit("get_abort_array",r.UploadList||[])}))}else{if("task"!==o)return t({error:"abort_unknown_level"});if(!i)return t({error:"abort_upload_task_no_id"});if(!a)return t({error:"abort_upload_task_no_key"});p.emit("get_abort_array",[{Key:a,UploadId:i}])}},uploadFile:function(e,t){return x.apply(this,arguments)},uploadFiles:function(e,t){return b.apply(this,arguments)},sliceCopyFile:function(e,t){var r=new l,n=this,a=e.Bucket,i=e.Region,o=e.Key,u=e.CopySource,d=p.getSourceParams.call(this,u);if(d){var f=d.Bucket,h=d.Region,g=decodeURIComponent(d.Key),v=void 0===e.CopySliceSize?n.options.CopySliceSize:e.CopySliceSize;v=Math.max(0,v);var y,x,b=e.CopyChunkSize||this.options.CopyChunkSize,w=this.options.CopyChunkParallelLimit,C=this.options.ChunkRetryTimes+1,S=0,T=0,E={},R={},A={};r.on("copy_slice_complete",(function(r){var l={};p.each(e.Headers,(function(e,t){0===t.toLowerCase().indexOf("x-cos-meta-")&&(l[t]=e)}));var u=p.map(r.PartList,(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));c.retry(C,(function(t){n.multipartComplete({Bucket:a,Region:i,Key:o,UploadId:r.UploadId,Parts:u,tracker:e.tracker,calledBySdk:"sliceCopyFile"},t)}),(function(e,n){if(s.removeUsing(r.UploadId),e)return x(null,!0),t(e);s.removeUploadId(r.UploadId),x({loaded:y,total:y},!0),t(null,n)}))})),r.on("get_copy_data_finish",(function(l){var d=s.getCopyFileId(u,E,b,a,o);d&&s.saveUploadId(d,l.UploadId,n.options.UploadIdCacheLimit),s.setUsing(l.UploadId);var f=p.filter(l.PartList,(function(e){return e.Uploaded&&(T+=e.PartNumber>=S&&y%b||b),!e.Uploaded}));c.eachLimit(f,w,(function(t,r){var s=t.PartNumber,p=t.CopySourceRange,d=t.end-t.start,f=0;c.retry(C,(function(t){k.call(n,{Bucket:a,Region:i,Key:o,CopySource:u,UploadId:l.UploadId,PartNumber:s,CopySourceRange:p,tracker:e.tracker,calledBySdk:"sliceCopyFile",onProgress:function(e){T+=e.loaded-f,f=e.loaded,x({loaded:T,total:y})}},t)}),(function(e,n){if(e)return r(e);x({loaded:T,total:y}),T+=d-f,t.ETag=n.ETag,r(e||null,n)}))}),(function(e){if(e)return s.removeUsing(l.UploadId),x(null,!0),t(e);r.emit("copy_slice_complete",l)}))})),r.on("get_chunk_size_finish",(function(){var c=function(){n.multipartInit({Bucket:a,Region:i,Key:o,Headers:A,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(n,a){if(n)return t(n);e.UploadId=a.UploadId,r.emit("get_copy_data_finish",{UploadId:e.UploadId,PartList:e.PartList})}))},l=s.getCopyFileId(u,E,b,a,o),d=s.getUploadIdList(l);if(!l||!d)return c();!function t(l){if(l>=d.length)return c();var u=d[l];if(s.using[u])return t(l+1);m.call(n,{Bucket:a,Region:i,Key:o,UploadId:u,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(n,a){if(n)s.removeUploadId(u),t(l+1);else{if(s.using[u])return t(l+1);var i={},o=0;p.each(a.PartList,(function(e){var t=parseInt(e.Size),r=o+t-1;i[e.PartNumber+"|"+o+"|"+r]=e.ETag,o+=t})),p.each(e.PartList,(function(e){var t=i[e.PartNumber+"|"+e.start+"|"+e.end];t&&(e.ETag=t,e.Uploaded=!0)})),r.emit("get_copy_data_finish",{UploadId:u,PartList:e.PartList})}}))}(0)})),r.on("get_file_size_finish",(function(){var a;if(function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,a=0;a<t.length&&!(y/(r=1024*t[a]*1024)<=n.options.MaxPartNumber);a++);e.ChunkSize=b=Math.max(b,r),S=Math.ceil(y/b);for(var i=[],o=1;o<=S;o++){var s=(o-1)*b,c=o*b<y?o*b-1:y-1,l={PartNumber:o,start:s,end:c,CopySourceRange:"bytes="+s+"-"+c};i.push(l)}e.PartList=i}(),(a="Replaced"===e.Headers["x-cos-metadata-directive"]?e.Headers:R)["x-cos-storage-class"]=e.Headers["x-cos-storage-class"]||R["x-cos-storage-class"],a=p.clearKey(a),"ARCHIVE"===R["x-cos-storage-class"]||"DEEP_ARCHIVE"===R["x-cos-storage-class"]){var i=R["x-cos-restore"];if(!i||'ongoing-request="true"'===i)return void t({error:"Unrestored archive object is not allowed to be copied"})}delete a["x-cos-copy-source"],delete a["x-cos-metadata-directive"],delete a["x-cos-copy-source-If-Modified-Since"],delete a["x-cos-copy-source-If-Unmodified-Since"],delete a["x-cos-copy-source-If-Match"],delete a["x-cos-copy-source-If-None-Match"],r.emit("get_chunk_size_finish")})),n.headObject({Bucket:f,Region:h,Key:g,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(a,i){if(a)a.statusCode&&404===a.statusCode?t({ErrorStatus:g+" Not Exist"}):t(a);else if(void 0!==(y=e.FileSize=i.headers["content-length"])&&y)if(e.tracker&&e.tracker.setParams({httpSize:y}),x=p.throttleOnProgress.call(n,y,e.onProgress),y<=v)e.Headers["x-cos-metadata-directive"]||(e.Headers["x-cos-metadata-directive"]="Copy"),n.putObjectCopy(Object.assign(e,{calledBySdk:"sliceCopyFile"}),(function(e,r){if(e)return x(null,!0),t(e);x({loaded:y,total:y},!0),t(e,r)}));else{var o=i.headers;E=o,R={"Cache-Control":o["cache-control"],"Content-Disposition":o["content-disposition"],"Content-Encoding":o["content-encoding"],"Content-Type":o["content-type"],Expires:o.expires,"x-cos-storage-class":o["x-cos-storage-class"]},p.each(o,(function(e,t){var r="x-cos-meta-";0===t.indexOf(r)&&t.length>11&&(R[t]=e)})),r.emit("get_file_size_finish")}else t({error:'get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.'})}))}else t({error:"CopySource format error"})}};e.exports.init=function(e,t){t.transferToTaskMethod(w,"sliceUploadFile"),p.each(w,(function(t,r){e.prototype[r]=p.apiWrapper(r,t)}))}},function(e,t){var r={eachLimit:function(e,t,r,n){if(n=n||function(){},!e.length||t<=0)return n();var a=0,i=0,o=0;!function s(){if(a>=e.length)return n();for(;o<t&&i<e.length;)o+=1,r(e[(i+=1)-1],(function(t){t?(n(t),n=function(){}):(o-=1,(a+=1)>=e.length?n():s())}))}()},retry:function(e,t,r){e<1?r():function n(a){t((function(t,i){t&&a<e?n(a+1):r(t,i)}))}(1)}};e.exports=r}])}));