/**
 * 获取已经购买的课程分类
 */
export const getBuyedCourseCategory = async () => {
	const ret = await uni.$u.http.get("/Question/getChatList")
	if (ret.code == 1) {
		return ret.data
	} else {
		uni.tip(ret.msg)
		return false
	}
}

/**
 * 获取咨询客服
 */
export const getConsultChat = async () => {
	const ret = await uni.$u.http.get("/Question/getConsultChat")
	if (ret.code == 1) {
		return ret.data
	} else {
		return false
	}
}

/**
 * 添加咨询次数
 */
export const addChatNum = async () => {
	const ret = await uni.$u.http.get("/Question/addChatNum")
	if (ret.code == 1) {
		return ret.data
	} else {
		return false
	}
}