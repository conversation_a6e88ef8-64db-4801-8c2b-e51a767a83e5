<template>
	<view class="container">	
		<view class="all-course-h1">
			<view class="left">
				<view class="green-block-big">
					
				</view>
				<text>明细列表</text>
			</view>
	
		</view>
		<view class="title">
			<view class="demo-uni-col">变化值</view>
			<view class="demo-uni-col">来源/用途</view>
			<view class="demo-uni-col">时间</view>
		</view>
		<template v-for="item in list">
			<view class="show_line">	
				<view class="content">
					<view class="demo-uni-col">{{item.num}}</view>
					<view class="demo-uni-col">系统扣除</view>
					<view class="demo-uni-col">{{item.createtime_text}}</view>
				</view>
				<view class="remark">备注:{{item.remark}}</view>
			</view>
		</template>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				total: 0,
				page: 1,
				points: 180,
			}
		},
		onLoad() {
			this.getList();
		},
		methods: {
			getList() {
				uni.$u.http.get('/mini_user/getPointList', {
					params: {
						page: this.page
					}
				}).then(rest => {

					this.list = [...this.list, ...rest.data.data];
					this.total = rest.data.total;
					this.points = rest.data.points;
				});
			},
			
		},
		onReachBottom() {
			if (this.list.length >= this.total) {
				return uni.showToast({
					title: '没有更多数据',
					duration: 1500,
					icon: 'none'
				})
			} else {
				this.page += 1
				this.getList();
			}

		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	padding-top: 30rpx;
	height: 100vh;

	.all-course-h1 {
		background-color: #F6F7FB;
		padding: 26rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.left{
			display: flex;
			align-items: center;
			justify-content: flex-start;
			.green-block-big {
				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;
				
			}
				
			text {
				padding-left: 10rpx;
				width: 128rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}
		image{
			width: 40rpx;
		}

	}
	.title {
		display: flex;
		background:#01997A;
		height: 70rpx;
		border-radius: 24rpx 24rpx 0px 0px;
		align-items:center;
		.demo-uni-col{
			color:#fff;
			flex:1;
			justify-content: space-between;
			text-align: center;
		
			
		}
		
	}
	.remark {
		font-size: 22rpx;
		margin-left: 76rpx;
		color:#AFAFAF;
	}
	.show_line{
		padding-bottom: 16rpx;
		border-bottom: 1px solid #eee;
	}
	.content{
		display: flex;
		height: 70rpx;
		border-radius: 24rpx 24rpx 0px 0px;
		align-items:center;
		.demo-uni-col{
			flex:1;
			justify-content: space-between;
			text-align: center;
			color:#313131;
			
		}
		
	}
	.more{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		image{
			width: 20rpx;
		}
		text{
			margin-left: 6rpx;
			font-size: 30rpx;
			color: #A2A2A2;
			font-weight: bold;
		}
		
	}
}
</style>


