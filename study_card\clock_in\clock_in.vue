<template>
	<view class="container">
		<view class="input">
			<u--textarea :confirmType="null" maxlength="-1" height="240rpx" border="none" v-model="value" placeholder="给自己一句鼓励的话！"></u--textarea>
		</view>

		<view class="camera">
			<u-upload height="180rpx" width="180rpx" uploadText="上传照片" :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple :maxCount="1"></u-upload>
		</view>
		<view class="tip">
			<image :src="src" mode="widthFix" @click="isclick"></image>
			<text :class="[isAsync ? 'sync' : 'notsync']">同步到蛙塘</text>
		</view>

		<view class="btn" @click="submitNote">
			提交
		</view>
	</view>
</template>

<script>
import { uploadFile } from "@/api/common/common.js"
export default {
	data() {
		return {
			fileList1: [],
			value: "",
			src: '../../static/img/praise.png',
			isAsync: 0,
			type: 1, //1 早打卡  2 晚打卡
		}
	},
	onLoad(options) {

		if (options.type != undefined) {
			this.type = options.type
			let date = new Date;
			if (date.getHours() > 17) {
				this.type = 2
			} else {
				this.type = 1;
				this.value = ''
			}
		} else {
			let date = new Date;
			if (date.getHours() > 17) {
				this.type = 2
			} else {
				this.type = 1
			}
		}

		if (this.type == 1) {

			this.value = '元气满满的一天开始啦~今天也要努力学习111！';
			if (options.text != undefined) {
				this.value = options.text;
			}
		} else {
			if (options.text != undefined) {
				this.value = options.text;
			}
		}
	},
	methods: {
		isclick() {
			this.isAsync = !this.isAsync;
			if (this.isAsync) {
				this.src = '../../static/img/praise-color.png'
			} else {
				this.src = '../../static/img/praise.png'
			}

		},
		submitNote() {
			if (this.disflag) {
				uni.tip('请等待图片上传完成');
				return false;
			}
			let that = this
			let data = uni.$u.http.post('/student/studentColck', { type: that.type, imageList: that.fileList1, text: that.value, is_async: that.isAsync }).then(res => {
				console.log(res)
				if (res.code == 1) {
					uni.tip('操作成功')
					uni.navigateBack({
						delta: 1
					})
				} else {
					uni.tip(res.msg)
				}
			});
		},
		// 删除图片
		deletePic(event) {
			this[`fileList${event.name}`].splice(event.index, 1)
		},
		// 新增图片
		async afterRead(event) {
			// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
			let lists = [].concat(event.file)
			let fileListLen = this[`fileList${event.name}`].length
			lists.map((item) => {
				this[`fileList${event.name}`].push({
					...item,
				})
			})
			for (let i = 0; i < lists.length; i++) {
				this.disflag = true;
				const result = await uploadFile(lists[i].url)

				let item = this[`fileList${event.name}`][fileListLen]
				this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
					url: result.fullurl
				}))
				console.log(this[`fileList${event.name}`])
				fileListLen++
				if (i + 1 == lists.length) {
					this.disflag = false;
					uni.tip('图片上传完成');
				}
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: $container-bg-color;
	padding: 0 36rpx;
	height: 100vh;
	padding-top: 30rpx;

	.input {
		height: 240rpx;
		background-color: #fff;
		border-radius: 20rpx;

	}

	::v-deep .u-upload {
		margin-top: 20rpx;

	}

	.camera {
		padding-left: 40rpx;
		margin-top: 80rpx;
		min-height: 290rpx;
		border-radius: 20rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.tip {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		margin-top: 38rpx;

		image {
			width: 60rpx;
		}

		.sync {
			margin-left: 10rpx;
			color: #01997A;
			font-size: 26rpx;
		}

		.notsync {
			margin-left: 10rpx;
			color: #909193;
			font-size: 26rpx;
		}
	}

	.btn {
		margin: 120rpx auto;
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient(268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
		line-height: 80rpx;
		color: #fff;
		text-align: center;
	}
}
</style>
