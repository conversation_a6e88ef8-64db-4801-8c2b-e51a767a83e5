<template>
	<view class="container">
		<view class="video-container">
			<my-video :src="nowCourse.video_url" :video_id="videoId"  :play_time="playTime"  :course_type="courseType" @custom-event="changeSecond"></my-video>
		</view>
		<view class="title">
			<text>{{nowCourse.point_name}}</text>
			<view class="left-opt">
				
				<template v-if="nowCourse.association_file != null">
					<u-icon name="download" size="54" @click="download"></u-icon>
				</template>
				<u-icon :name="star" :color="starColor" size="54" @click="collection"></u-icon>
				
			</view>
		</view>
		<view class="content">
			<view class="course-container">
				<view class="title-name">
					<view class="left">
						<text class="green-block"></text>
						<text>列表</text>
					</view>
					
				</view>
				<view class="course-list" :style="{height}">
					<view class="list-item u-border-top" v-for="(sitem,sindex) in courseList">
						<view class="title-text">
							<view>
								<view>{{sitem.point_name}}</view>
								<view class="view_nunm"><u-icon name="play-right"  size="30"></u-icon>已播放{{sitem.view_num != null?sitem.view_num:0}}次</view>
							</view>
							
						</view>
						<template v-if="sitem.id==videoId">
							<view class="title-text">
								<image src="@/static/png/sound-wave.png" mode="widthFix"></image>
								<text>学习中</text>
							</view>
						</template>
						<template v-else>
							<view class="enter" v-if="sitem.id!=videoId" @click="toListen(sitem.id)">
								立即学习
							</view>
						</template>
						
					</view>
					
					
					<!-- <view class="list-item u-border-top">
						<view class="title-text">
							<text>第1节 基本知识</text>
							<template v-if="false">
							<image src="@/static/png/sound-wave.png" mode="widthFix"></image>
							<text>听课中</text>
							</template>
						</view>
						<view class="enter">
							进入课堂
						</view>
					</view> -->
				</view>
			</view>			
		
		</view>
	</view>
</template>

<script>
	import myVideo from '@/components/my-video/my-video.vue';
	export default {
		
		data() {
			return {
				definitionText:'',
				cateType:1,			
				src:'',
				starColor:'',
				star:'star',
				styles: [{
						value: 'button',
						text: '按钮',
						checked: true
					},
					{
						value: 'text',
						text: '文字'
					}
				],
				colors: ['#007aff', '#4cd964', '#dd524d'],
				current: 0,
				colorIndex: 0,
				activeColor: '#009c7b',
				styleType: 'button',
				turnOver: false,
				animation: {},
				animationData: {},
				listen:true,
				height:'auto',
				screenWidth:375,
				courseId:0,
				chapterId:0,
				courseList:[],
				nowCourse:'',
				videoId:'',
				taskId:'',
				playTime:0,
				conlyCouseId:0,
				showAll:true,
				courseType:'',
				cid:0,
				rid:[],
			}
		},
		onLoad(options){
			
			
			
			this.cid = options.cid; 
			if(options.points_id !=undefined) {
				this.rid =  options.points_id.split(',');
			} 
			this.getStudyPoints();
			let that = this;
			uni.getSystemInfo({
			  success: function (info) {
			    console.log('屏幕宽度：' + info.windowWidth);
			    // 你可以将info.windowWidth值保存到一个变量中供后续使用
			    that.screenWidth = info.windowWidth;
				console.log(that.screenWidth)
			  }
			});	
			this.animation.rotate(0).step()
			this.height="auto";			
			this.animationData = this.animation.export()
		},
		mounted() {
			this.videoContent = uni.createVideoContext('local-vedio')
		},
		methods: {
			download(){
				let that  = this;
				uni.downloadFile({
					url:that.nowCourse.association.file_url, //仅为示例，并非真实的资源
					success: (res) => {
						
						var filePath = res.tempFilePath;
						uni.$u.http.post('/course/dowmLoadNum',{id:that.nowCourse.association_file},{custom:{loading:false}}).then(res=>{
							
						})	
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function (res) {
								uni.tip('下载成功');
							}
						});
					}
				});
			},
			getStudyPoints(){
				let that = this;
				let collectionData = {
					cid :this.cid
					
				};
				uni.$u.http.get('/course/getStudyPoints',{params:collectionData}).then(res=>{
					console.log(res)
					that.courseList = res.data.data;
					if(that.rid.length > 0) {
						that.courseList =  that.courseList.filter(item=>that.rid.includes(item.id));
						that.nowCourse = that.courseList[0];
						that.videoId = that.nowCourse.id;
					} else {
						that.videoId = that.courseList[0].id;
						that.nowCourse =  that.courseList[0];
						
					}
					that.changeSeeNum()
					
				})
			},		
			
			collection() {
				let status = 1
				let  url = '/course/userCollectStudyPoints'
				if(this.star != 'star-fill') { //收藏
					this.starColor='#009c7b'
					this.star='star-fill'
				} else  { //未收藏
					this.starColor=''
					this.star='star'
					let  url = '/course/userCancelCollectStudyPoints'
					status = 2
				}
				let collectionData = {
					category_id : this.nowCourse.cid,
					type : 2,
					points_id:this.nowCourse.id
				};
				let  data  = uni.$u.http.post(url,collectionData).then(res=>{
					if(res.code == 1) {
						if(status == 1) {
							uni.tip('收藏成功');
						} else {
							uni.tip('取消成功');
						}
					}
				})
				
			},
			toListen(videoId) {
				let that = this;
				this.videoId = videoId;
				let  tmpCourse = that.courseList.filter(item=>item.id == that.videoId)
				that.nowCourse = tmpCourse[0]
				console.log(that.nowCourse)
				that.changeSeeNum()
				
			},
			changeSecond(e){
				console.log(e)
			},
			changeSeeNum(id) {
				let that = this;

				this.starColor=''
				this.star='star'				
				uni.$u.http.post('/course/seeNumUpdate',{id:that.nowCourse.id}).then(res=>{
					if(res.data == 1) {
						this.starColor='#009c7b'
						this.star='star-fill'
					} 
				})
			}
		},
		components:{
			myVideo
		},
		created() {
			var animation = uni.createAnimation({
				duration: 500,
				timingFunction: 'ease',
			})
			this.animation = animation
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.video-container {
		
		
		}
		min-height: 100vh;
		background-color: $container-bg-color;
		::v-deep .u-popup__content{
			border-radius: 16rpx 16rpx 0rpx 0rpx !important;
		}
		.v-top {
			height: 420rpx;
			width: 100%;
			.backBtn{
					position: absolute;
					left: 16rpx;
					top: 45rpx;
					width: 45rpx;
					height: 45rpx;
				}
				.speed{
					position: absolute;
					right: 20rpx;
					top: 16rpx;
					.doubleSpeed{
						color: #fff;
						font-size: 14rpx;
						background-color: rgba(0,0,0,0.6);
						padding: 4rpx 6rpx;
					}
				}
				
				// 倍速的蒙版
				.speedModal{
					background-color: rgba(0,0,0,0.7);
				}
				.speedNumBox{
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					align-items: center;
					background-color: #2c2c2c;
					width: 120rpx;
					position: absolute;
					right: 0rpx;
					top: 0;
					.number{
						width: 120rpx;
						font-weight: 700;
						font-size: 14rpx;
						padding: 18rpx 0;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
					}
					.active{
						color: red;
					}
					.noActive{
						color: #fff;
					}
				}
		}

		.title {
			margin-top: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;

			text {
				font-size: 32rpx;
				font-weight: bold;
				color: #060606;
			}
			.left-opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
			.text-icon {
				display: flex;
				align-items: center;
				justify-content: space-between;

				image {
					width: 50rpx;
				}

				text {
					color: #777777;
					font-size: 20rpx;
				}
			}
			}
		}

		.content {
			padding: 0 30rpx;
			margin-top: 10rpx;

			.course-container {
				margin-top: 46rpx;
				background-color: #fff;
				border-radius: 20rpx;

				.title-name {

					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 28rpx;

					.left {
						color: #4A4A4C;
						font-size: 28rpx;
						font-weight: bold;

						.green-block {
							display: inline-block;
							width: 14rpx;
							height: 28rpx;
							line-height: 28rpx;
							background: #01997A;
							border-radius: 0rpx 8rpx 0rpx 8rpx;
							margin-right: 6rpx;
						}
					}

					.expand{
						width: 30rpx;
						height: 30rpx;
						background: #01997A;
						border-radius: 50%;
						display: flex;
						align-items: center;
						transition: transform 0.5s ease;
						justify-content: center;

						.rotate {
							transform: rotate(180deg);
						}
					}
				}
			}
			.course-list{
				height: auto;
				overflow: hidden;
				transition: height 0.8s ease;
				.list-item{
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 28rpx;
					.title-text{
						display: flex;
						align-items: center;
						justify-content: flex-start;
						font-size: 34rpx;
						font-weight: 700;
						color: $main-color;
						image{
							margin: 0 10rpx;
							width: 16rpx;
						}
						text{
							&:last-child{
								font-size: 22rpx;
							}
						}
						.view_nunm{
							margin-top: 10rpx;;
							display: flex;
							font-size: 22rpx;
							color:#afafaf
						}
					}
					.enter{
						width: 130rpx;
						height: 44rpx;
						background: linear-gradient( 90deg, #10C19D 0%, #16AC8E 100%);
						border-radius: 60rpx;
						color: #fff;
						text-align: center;
						line-height: 44rpx;
						font-weight: bold;
						font-size: 26rpx;
					}
				}
			}
		
			.practise-container{
				margin-top: 46rpx;
				background-color: #fff;
				border-radius: 20rpx;
				padding-top: 30rpx;
				padding-bottom: 1rpx;
				.lesson-list{
					.lesson-info{
						padding: 0 30rpx;
						margin-bottom: 40rpx;
						color: #4A4A4C;
						font-size: 28rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						.lesson-title{
							display: flex;
							align-items: center;
							justify-content: space-between;
							.samll-cricle{
								height: 20rpx;
								width: 20rpx;
								border-radius: 50%;
								background-color: #CDF3E7;
								margin-right: 10rpx;
							}
							font-size: 28rpx;
							font-weight: bold;
							color: #4A4A4C;
						}
						.btn{
							width: 130rpx;
							height: 44rpx;
							line-height: 44rpx;
							font-size: 24rpx;
							color: #fff;
							background-color: #01997A;
							text-align: center;
							border-radius: 365rpx;
							position: relative;
							.tip{
								position: absolute;
								bottom: -40rpx;
								left: 50%;
								transform: translateX(-50%);
								font-size: 20rpx;
								color: #A4A4A4;
								width: 72rpx;
							}
						}
						.done{
							background-color: #F39898;
							
						}
					}
				}
			}
		}

	}
</style>