{"name": "cos-wx-sdk-v5", "version": "1.7.1", "description": "小程序 SDK for [腾讯云对象存储服务](https://cloud.tencent.com/product/cos)", "main": "demo/lib/cos-wx-sdk-v5.min.js", "scripts": {"prettier": "prettier --write src demo/demo-sdk.js demo/test.js demo/ciDemo", "dev": "cross-env NODE_ENV=development node build.js --mode=development", "build": "cross-env NODE_ENV=production node build.js --mode=production", "sts.js": "node server/sts.js"}, "repository": {"type": "git", "url": "http://github.com/tencentyun/cos-wx-sdk-v5.git"}, "author": "carsonxu", "license": "ISC", "dependencies": {"@xmldom/xmldom": "^0.8.6", "mime": "^2.4.6"}, "devDependencies": {"@babel/core": "7.17.9", "@babel/preset-env": "7.16.11", "babel-loader": "8.2.5", "body-parser": "^1.18.3", "cross-env": "^7.0.3", "express": "^4.17.1", "prettier": "^3.0.1", "qcloud-cos-sts": "^3.0.2", "terser-webpack-plugin": "4.2.3", "webpack": "4.46.0", "webpack-cli": "4.10.0"}}