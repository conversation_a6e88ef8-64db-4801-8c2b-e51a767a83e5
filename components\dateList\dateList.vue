<template>
	 <view class="nav-bar">
	    <view @click="sel(0)" :class="['nav-item', type==0?'active':'']">今日</view>
	    <view @click="sel(1)" :class="['nav-item', type==1?'active':'']">本周</view>
	    <view @click="sel(2)" :class="['nav-item', type==2?'active':'']">本月</view>
	    <view @click="sel(3)" :class="['nav-item', type==3?'active':'']">累计</view>
	  </view>
</template>

<script>
	export default {
		name:"dateList",
		data() {
			return {
				type:2,
			};
		},
		mounted() {
			// 组件挂载后发送初始类型
			this.$emit('custom-event', this.type);
		},
		methods:{
			sel(type) {
				this.type = type;
			    this.$emit('custom-event', type);
				console.log('日期类型已选择:', type);
			}
		}
	}
</script>

<style scoped lang="scss">
  .nav-bar {
    display: flex;
	border: 1rpx solid #01997A;
	border-radius: 12rpx;
  }
  .nav-item {
    flex: 1;
    text-align: center;
    padding: 9rpx; /* 上下填充，可根据实际需要调整 */
    color: #01997A; /* 文字颜色 */
	font-size:24rpx;
	border-left: 1px solid  #01997A;
	&:first-child {
		border-left: none;
	}
  }
  .nav-item.active {
    background-color: #01997A; /* 激活项的背景色，可根据实际需要调整 */
	color: #fff;
	&:first-child {
		border-left: none;
		padding: 9rpx;
		border-radius: 10rpx 0 0 10rpx;
	}
	&:last-child {
		border-left: none;
		padding: 9rpx 9rpx 9rpx 10rpx;
		border-radius: 0 10rpx 10rpx 0;
	}
  }
</style>