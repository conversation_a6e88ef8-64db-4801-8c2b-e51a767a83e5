<template>
	<view class="container">
		<cheader :initTimeVal="timeval" ref="cheader"></cheader>
		<content></content>
		<cover>
			<scroll-view class="drawer-container" scroll-y="true" style="height: 100%; background-color:#fff">
			<view style="height: 100%; background-color:#fff">
				<view class="title-container">
		<!-- 			<view class="title-list"
						:style="{height:isShowAll? Math.ceil(questionList.length/4)*70+'rpx' :'80rpx'}">
						
						<text v-for="i in questionList.length" @click="currentChoose=i" :class="[{done:hasDone(i)},{sel:currentChoose==i}]" class="title"
							> 第{{i+1}}题</text>
					</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up" @click="isShowAll=true" size="44"></u-icon>
					</view> -->
					<scroll-view class="title-list" :scroll-into-view="scrollToTitle"  scroll-x v-if="questionList.length > 1">
							<text :id="`title-list-${i+1}`" @click="currentChoose=i" :class="[{done:hasDone(i)},{sel:currentChoose==i}]"  class="title" v-for="i in questionList.length"> 第{{i+1}}题</text>
					</scroll-view>
				</view>
				<view class="answer">
					<swiper :current="currentChoose" :duration="100" :style={height:swiperHeight}
						@change="CurrentIndexChange">
						<swiper-item v-for="(item,index) in questionList" :key="index">
							<view class="item">
								<view class="q-text q-title">
									<image v-if="questionTitle.includes('https://')" :src="questionTitle" style="width: 100%" mode="widthFix"></image>
									<text v-else>{{ questionTitle }}</text>
									
								</view>
								<view class="title">
									我的答案
								</view>
								<view class="a-content" v-if="answerShow">
									<u--textarea  height="290rpx" border="none"  v-model="answer[currentChoose]"
										placeholder="请输入答案"></u--textarea>
								</view>
								<view class="q-upload">
									<u-upload height="200rpx" width="200rpx" uploadText="上传照片" :fileList="fileList[currentChoose]" @afterRead="afterRead"
										@delete="deletePic" name="1" multiple :maxCount="10"></u-upload>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</view>
				<view class="bottom-btn">
					<view class="prev" @click="prevAction">
						上一题
					</view>
					<view class="next" @click="next">
						下一题
					</view>
				</view>
			</view>
			</scroll-view>
		</cover>
	</view>
</template>

<script>
	import content from "../components/content/content.vue"
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	import cheader from "@/components/c_head/c_head.vue"
	import star from "@/components/star/star.vue"
	import mixins from "@/mixins/index.js"
	import {uploadFile} from "@/api/common/common.js"
	export default {
		mixins: [mixins],
		data() {
			return {
				isShowAll: false,
				swiperHeight: 0,
				loading:false,
				imagesList:[],
				scrollToTitle:'',
			}
		},
		methods: {
			CurrentIndexChange(e) {
				this.currentChoose = e.detail.current
					this.scrollToTitle = '#title-list-'+this.currentChoose;
			},
			handleSwiperHeight(){
				this.$nextTick(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.item').boundingClientRect(data => {
						if (data) {
							this.swiperHeight = data.height + 'px'
						}
					}).exec();
				})
			},
			selected(index, selOption) {
				if(!Array.isArray(this.fileList[this.currentChoose])){
					return
				}
				if(this.fileList[this.currentChoose].length>0 || this.answer[this.currentChoose].length>0){
					return true
				}else{
					return false
				}
			},
			hasDone(i) {
				if(!Array.isArray(this.fileList[i]) || !Array.isArray(this.answer)){
					return false
				}
				if(this.fileList[i].length>0 || this.answer[i].length>0){
					return true
				}else{
					return false
				}
			},
			prevAction() {
				this.save()
				this.prev(false)
			},
			next() {
				this.save()
				//清空答题记录
				this.whetherToNext()
			},
			save(){
				this.fileList.forEach((item,index)=>{
					this.imagesList[index] = item?.length>0 ?item.map(item=>item.url).join(","):""
				})
				//保存答题结果
				let answer = {
					answer: this.answer,
					url: this.imagesList,
					timeval: this.$refs.cheader.currentTime,
				}
						
				const data = {
					answer: answer,
					index: this.currentIndex, //保存当前 题库的进度
				}
				//当前结果保存本地
				this.$store.commit("professionalConfig/setAnswerList", data);
			},
			deletePic(event) {
				this.fileList[this.currentChoose].splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList[this.currentChoose].length
				lists.map((item) => {
					this.fileList[this.currentChoose].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading = true;
				for (let i = 0; i < lists.length; i++) {
					try {
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[this.currentChoose][fileListLen]
						this.fileList[this.currentChoose].splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					} catch (err) {
						//移除图片
						setTimeout(() => {
							this.fileList[this.currentChoose].splice(fileListLen, 1)
						}, 1500)
					}
				}
				this.loading = false
			}

		},
		computed: {
			questionTitle() {
				if (typeof this.currentType.material_questions == "string" && this.currentType.material_questions.length >0) {
					let questions = JSON.parse(this.currentType.material_questions)
					let result = questions.find(item => item.num == this.currentChoose + 1)
					if (result !== undefined) {
						return result.question
					}
				}
				return ""
			},
			// urlList(){
				
			// 	//return this.fileList[this.currentChoose].map(item=>item.url).join(",")
			// }
			answerShow(){
				return typeof this.answer[this.currentChoose]=='string'
			}
		},
		watch: {
			questionTitle(newval) {
				console.log(newval)
			},
			currentChoose: {
				handler(newVal) {
					this.handleSwiperHeight()
				},
				immediate: true,
				deep: true
			},
			currentIndex:{
				handler(newVal) {
					this.handleSwiperHeight()
					// 恢复保存到 vuex 的答题数据
	
					//this.fileList[this.currentChoose] = this.fileList
					//this.answer[this.currentChoose] = this.answer
				},
				immediate: true,
				deep: true
			}
		},
		components: {
			cheader,
			star,
			content,
			cover
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 164rpx;
		.center {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #2D2D2D;
			font-size: 32rpx;
		}
	.drawer-container{
		height: 100%;
		background-color:rgb(247, 247, 247);
	}
		.content {
			background-color: #fff;
			padding: 0 36rpx;
			padding-bottom: 80rpx;

			.top-opt {
				width: 100%;
				padding: 10rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.progress-text {
					text {
						color: #777777;
						font-size: 26rpx;

						&:first-child {
							font-size: 34rpx;
							color: #2D2D2D;
							font-weight: bold;
						}
					}
				}

				.opt {
					display: flex;
					align-items: center;
					justify-content: space-between;

					view {
						margin-left: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						text {
							font-size: 18rpx;
							color: #777;
						}
					}
				}
			}

			.q-type {
				margin-top: 12rpx;
				background-color: #CDF3E7;
				border-radius: 12rpx;
				color: $main-color;
				font-size: 24rpx;
				width: 120rpx;
				height: 46rpx;
				text-align: center;
				line-height: 46rpx;
			}

			.question {
				display: flex;
				flex-direction: column;
				align-content: space-between;
				justify-content: center;

				.title {
					width: 100%;
					color: #5A5A5A;
					font-size: 28rpx;
					font-weight: bold;
					margin-top: 16rpx;
				}

				.question-content {
					color: #5A5A5A;
					font-size: 28rpx;
					margin-top: 24rpx;
					line-height: 40rpx;
				}
			}

			.q-text {
				line-height: 50rpx;
				color: #5A5A5A;
				font-size: 24rpx;
			}

			.q-title {
				margin-top: 20rpx;
				 margin-bottom: 28rpx;
			}

			.q-sel-list {

				.q-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 40rpx;

					text {
						width: 590rpx;
					}

					.q-check-normal {
						width: 70rpx;
						height: 70rpx;
						border: 1rpx solid #AFAFAF;
						color: #5A5A5A;
						background-color: #fff;
						border-radius: 16rpx;
						line-height: 70rpx;
						text-align: center;
					}

					.q-check-sel {
						background: #01997A;
						border: 0;
						color: #fff;

					}
				}
			}

		}

		.title-container {
			display: flex;
			align-items: flex-start;
			justify-content: center;
			background-color: rgb(247, 247, 247);

			.title-list {
				height: 78rpx;
				background-color: rgb(247, 247, 247);
				width: 100%;
				white-space: nowrap;
				.title {
					width: 110rpx;
					display: inline-block;
					color: #777777;
					font-size: 28rpx;
					padding: 15rpx 30rpx;
					padding-bottom: 15;
				}
			}

			.right-icon {
				width: 140rpx;
				padding-top: 15rpx;
				padding-right: 30rpx;

				::v-deep .uicon-arrow-up {
					display: inline-block;
					border-radius: 50%;
					border: 1rpx solid #777;
				}

				::v-deep .uicon-arrow-down {
					display: inline-block;
					border-radius: 50%;
					border: 1rpx solid #777;
				}
			}

			.done {
				color: $main-color !important;
			}

			.sel {
				color: #0A0A0A;
				font-weight: bold;
			}
		}

		.answer {

			padding: 0 36rpx;
			.title {
				font-size: 24rpx;
				color: #777777;
				font-weight: bold;
			}

			.q-text {
				line-height: 50rpx;
				color: #5A5A5A;
				font-size: 24rpx;
			}

			.q-title {
				margin-top: 20rpx;
				margin-bottom: 28rpx;
			}

			.a-content {
				margin-bottom: 36rpx;
				border: 1rpx solid #707070;
				margin-top: 24rpx;
				border-radius: 22rpx;
				padding: 10rpx;
			}

		}

		.bottom-btn {
			padding: 0 36rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 56rpx;
			padding-bottom: 100rpx;
			view {
				width: 170rpx;
				height: 70rpx;
				border-radius: 16rpx;
				border: 1rpx solid #01997A;
				line-height: 70rpx;
				text-align: center;
				font-size: 28rpx;
			}

			.prev {
				background: #FFFFFF;
				color: $main-color;
			}

			.next {
				background-color: $main-color;
				color: #fff;
			}
		}

	}
</style>