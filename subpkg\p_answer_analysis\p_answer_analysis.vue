<template>
	<view class="container">

		<view class="content">
			<question-content></question-content>
		</view>

		<view class="answer">
			<view class="title">
				我的答案
			</view>
			<view class="a-content" v-if="answer.constructor === String">
				<text>{{ answer }} </text>
			</view>

			<view class="img-list">
				<view class="img-container">
					<image v-for="item in fileList" v-if="item.url" :key="item.url" :src="item.url" @click="showImage(item.url)" mode="widthFix">
					</image>
				</view>
			</view>
		</view>
		<view class="analysis">
			<view class="title">
				<user-title :title='"答案解析"' :bgcolor="'#fff'"></user-title>
				<view class="qanswer">
					<rich-text :nodes="currentType.explain"></rich-text>
				</view>
			</view>

		</view>

		<view class="analysis" v-if="isShowTaskAnswer == true">
			<view class="title">
				<user-title :title='"判断对错"' :bgcolor="'#fff'"></user-title>
			</view>
			<view class="qanswer">
				<u-radio-group activeColor="#01997A" v-model="isRight" placement="row">
					<u-radio iconSize='20' labelSize="30" size='30' :name="1" label="对"></u-radio>
					<text style="margin: 0 10rpx;"></text>
					<u-radio iconSize='20' labelSize="30" size='30' :name="0" label="错"></u-radio>
				</u-radio-group>
			</view>
		</view>

		<!-- 	老师点评 -->
		<view class="comment" v-if="currentType.answer_records">
			<view class="title">
				<user-title :title='"老师点评"' :bgcolor="'#fff'"></user-title>
				<view class="a-content" v-if="currentType.answer_records.marking_comment">
					<text>{{ currentType.answer_records.marking_comment }}</text>
				</view>
			</view>
			<view class="img-list" v-if="currentType.answer_records.marking_pic">
				<user-title :title='"老师批改"' :bgcolor="'#fff'"></user-title>
				<view class="img-container">
					<image @click="showImage(imgsrc)" v-for="(imgsrc) in currentType.answer_records.marking_pic" :key="imgsrc" style="margin-top: 6rpx; display: ;" :src="imgsrc" mode="widthFix"></image>
				</view>
			</view>

			<view class="edit-score" style="margin-top: 46rpx;" v-if="currentType.answer_records.marking_score">
				<user-title :title='"老师打分"' :bgcolor="'#fff'"></user-title>
				<u--input :disabled='true' :disabledColor="'#fff'" type="number" border="surround" v-model="currentType.answer_records.marking_score"></u--input>
			</view>
		</view>

		<!-- 试卷显示自测评分 -->
		<view class="comment" v-if="currentType.type == 2">
			<view class="title">
				<user-title :title='"自测评分(满分" + currentType.score + ")"' :bgcolor="'#fff'"></user-title>
			</view>
			<view class="edit-score">
				<u--input :disabledColor="'#fff'" type="number" border="surround" placeholder="请输入分数" v-model="selfScore"></u--input>
			</view>
		</view>




		<!-- 知识点 -->
		<view class="knowledge-points">
			<view class="title">
				<user-title :title='"知识点"' :bgcolor="'#fff'"></user-title>
			</view>

			<view class="point">
				<view class="point-tag" v-for="(item, index) in currentType.knowledgeInfo" :key="index">
					{{ item.knowledge_name | handleNullValue }}
				</view>
			</view>
		</view>


		<!-- 补漏 -->
		<view class="traps">
			<view class="title">
				<user-title :title='"精准补漏"' :bgcolor="'#fff'"></user-title>
			</view>
			<enter-course :list='currentType.knowledgeInfo'></enter-course>
		</view>
		<!-- 试卷来源 -->
		<view class="knowledge-origin">
			<template v-if="currentType.origin">
				<view class="title">
					<user-title :title='"来源"' :bgcolor="'#fff'"></user-title>
				</view>
				<view class="knowledge-origin-title">
					{{ currentType.origin }}
				</view>
			</template>

		</view>

		<bottom-btn @prev="answerPrev" @next="answerNext"></bottom-btn>

	</view>
</template>

<script>
import star from "@/components/star/star.vue"
import userTitle from "../../components/user_title/user_title.vue"
import enterCourse from "../../components/enter_course/enter_course.vue"
import mixins from "@/mixins/index.js"
import {
	getQuestionAnswer,
	getSystemAnswer
} from "@/api/professional/index.js"
import {
	selectTemplate,
	selectTemplateAnswer
} from "@/utils/tool.js"
import bottomBtn from "@/components/bottomBtn/bottomBtn.vue"
import QuestionContent from "../component/content/content.vue"
export default {
	mixins: [mixins],
	data() {
		return {
			isRight: -1,
			show: false,
			title: '自主评分',
			answer: "",
			selfScore: {}, //翻译自测评分是对象
			fileList: [],
		};
	},
	components: {
		userTitle,
		enterCourse,
		star,
		bottomBtn,
		QuestionContent
	},
	methods: {
		showImage(url) {
			// 预览图片
			uni.previewImage({
				urls: [url],
			});
		},
	},
	onLoad(option) {
		const {
			id
		} = option
		this.id = id
	},
}
</script>

<style lang="scss" scoped>
.container {
	background-color: $container-bg-color;
	padding-bottom: 120rpx;

	.content {
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;

		.top-opt {
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.progress-text {
				text {
					color: #777777;
					font-size: 26rpx;

					&:first-child {
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}

			.opt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				view {
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}

		.q-type {
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 86rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
	}

	.answer {
		margin-top: -50rpx;
		padding: 0 36rpx;
		background-color: #fff;

		.title {
			font-size: 28rpx;
			color: #777777;
		}
	}


	.a-content {
		margin-top: 12rpx;
		margin-bottom: 36rpx;
		padding: 26rpx;
		min-height: 233rpx;
		background: #FFFFFF;
		border-radius: 22rpx 22rpx 22rpx 22rpx;
		border: 1rpx solid #707070;
		font-weight: 400;
		font-size: 28rpx;
		color: #5A5A5A;
		line-height: 50rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;

	}

	.analysis {
		margin-top: 16rpx;
		padding: 0 36rpx;
		background-color: #fff;
		padding-bottom: 60rpx;

		.qanswer {
			font-size: 28rpx;
			color: #5A5A5A;
		}
	}

	.img-list {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;

		.img-container {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			align-content: center;
		}

		image {
			margin: 10rpx;
			padding: 0 16rpx;
			width: 20%;
		}
	}

	.comment {
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 60rpx;

		.qanswer {
			font-size: 28rpx;
			color: #5A5A5A;
		}

		.edit-score {}
	}



	.q-answer,
	.analysis,
	.knowledge-points,
	.traps,
	.knowledge-origin {
		padding: 0 36rpx;
		margin-top: 16rpx;
		background-color: #fff;
		padding-bottom: 60rpx;
	}

	.traps {
		margin-bottom: 38rpx;
	}

	.knowledge-points {
		.point {
			padding-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;

			.point-tag {
				margin-left: 10rpx;
				margin-top: 10rpx;
				padding: 8rpx 18rpx;
				border: 4rpx solid $main-color;
				border-radius: 16rpx;
				color: $main-color;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}

	.knowledge-origin {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: column;

		.title {
			width: 100%;
		}

		.knowledge-origin-title {
			width: 100%;
			color: #5A5A5A;
			font-size: 28rpx;
		}


	}
}
</style>