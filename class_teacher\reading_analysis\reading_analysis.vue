<template>
	<view class="container">
		<u-navbar :border='true' :placeholder='true'>
		     <view class="u-nav-slot" slot="left"  @click="goBack()">
		                <u-icon
		                    name="arrow-left"
		                    size="38"
		                ></u-icon>
		    </view>
			
			<view class="center" slot="center">
						<text>耗时{{useTime}}</text>
			</view>
		</u-navbar>
		
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>{{currentChoose+1}}</text>
					<text>/{{s_count+3}}</text>
				</view>
				<view class="opt">
					<view>
						<u-icon name="close-circle" size="38"></u-icon>
						<text>反馈</text>
					</view>
					<view>
						<u-icon name="star"  size="38"></u-icon>
						<text>收藏</text>
					</view>
					<view>
						<u-icon name="order"  size="38"></u-icon>
						<text>答题卡</text>
					</view>
				</view>
			</view>
			
			<view class="q-type">
				阅读理解
			</view>
			
			<view class="question">
				<scroll-view class="question-content" scroll-y="true">
				
					<rich-text :nodes="wordData.title"></rich-text>
					<!-- <u-parse  :content="questionContent"></u-parse> -->
				</scroll-view>
			
			</view>
		</view>
		<cover>
		    <view style="height: 100%; background-color:#fff;padding-bottom: 100rpx;overflow: auto;">
				<view class="title-container">
					<view class="title-list" :style="{height:isShowAll? Math.ceil(8/4)*60+'rpx' :'80rpx'}">
				  		<text @click="currentChoose=i" :class="[{done:isSelArr.includes(i)},{sel:currentChoose==i}]"  class="title" v-for="i in (s_count+3)"> 第{{i+1}}题</text>
				  	</view>
					<view class="right-icon">
						<u-icon v-if="isShowAll==true" name="arrow-down" @click="isShowAll=false" size="44"></u-icon>
						<u-icon v-else name="arrow-up"  @click="isShowAll=true" size="44"></u-icon>
					</view>
				</view>
				  
				<template v-for="(qitem,index) in  qData">	
					<view v-if="currentChoose == index">
						<view class="q-title q-text">
							{{qitem.num}}.{{qitem.title}}
						</view>
						<view class="q-container">
							<view class="q-sel-list">
								<view class="q-item" v-for="item in qitem.seloption" :key="item.optionName">
									<view class="q-check-normal" :class='{"q-check-sel": currentSelArr[index] == item.optionName}'  
													@click="setSel(item,index)">
										{{item.optionName}}
									</view>
									<text class="q-text">{{item.text}}</text>
								</view>
							</view>
						</view>
										
					</view>
					
				</template>  
				
				  
				<view class="answer" v-if="(s_count) == currentChoose ">
				  	<view class="title">
				  		单词学习
				  	</view>
				  	<view class="a-content">
				  		<u--textarea @input="(e)=>changeText(e,s_count)" height="290rpx" border="none" v-model="wordAnswer" placeholder="请输入答案"  maxlength="-1"></u--textarea>
				  	</view>
				  	
				  	<view class="q-upload">
						<u-upload 
						 height="200rpx"
						 width="200rpx"
						 uploadText="上传照片"
							:fileList="fileList"
							@afterRead="afterRead"
							@delete="deletePic"
							name="1"
							multiple
							:maxCount="10"
						></u-upload>
				  	</view>
				  	
				</view>
				<view class="answer" v-if="(s_count+1) == currentChoose ">
				  	<view class="title">
				  		句子结构分析
				  	</view>
				  	<view class="a-content">
				  		<u--textarea  @input="(e)=>changeText(e,(s_count+1))" height="290rpx" border="none" v-model="semtAnswer" placeholder="请输入答案"  maxlength="-1"></u--textarea>
				  	</view>
				  	
				  	<view class="q-upload">
						<u-upload 
						 height="200rpx"
						 width="200rpx"
						 uploadText="上传照片"
							:fileList="fileList1"
							@afterRead="afterRead1"
							@delete="deletePic1"
							name="1"
							multiple
							:maxCount="10"
						></u-upload>
				  	</view>
				  	
				</view>
				<view class="answer" v-if="(s_count+2) == currentChoose ">
				  	<view class="title">
				  		翻译
				  	</view>
				  	<view class="a-content">
				  		<u--textarea @input="(e)=>changeText(e,s_count+2)"  height="290rpx" border="none" v-model="tranAnswer" placeholder="请输入答案"  maxlength="-1"></u--textarea>
				  	</view>
				  	
				  	<view class="q-upload">
						<u-upload 
						 height="200rpx"
						 width="200rpx"
						 uploadText="上传照片"
							:fileList="fileList2"
							@afterRead="afterRead2"
							@delete="deletePic2"
							name="1"
							multiple
							:maxCount="10"
						></u-upload>
				  	</view>
				  	
				</view>
				<view class="bottom-btn">
				  	<view class="prev"  @click="next(1)">
				  		{{prevText}}
				  	</view>
				  	<view class="next" @click="next(2)" v-if="currentChoose < (s_count+2)">
				  		{{nextText}}
				  	</view>
					<view class="next" @click="submitData" v-else>
						提 交
					</view>
				</view>
			</view>
		</cover>
	</view>
</template>

<script>
	import cover from "../../components/zhong-cover/zhong-cover.vue"
	import {uploadFile} from "@/api/common/common.js"
	export default {
		data() {
			return {
				nextText:'下一题',
				prevText:'上一题',
				isShowAll:false,
				currentChoose:0,
				currentSelArr:{},
				taskId:'',
				wordData:[],
				fileList:[],
				fileList1:[],
				fileList2:[],
				answer:'',
				s_count:0,
				currentSel:1,
				questionArr:[],
				qData:[],
				tranAnswer:'',
				wordAnswer:'',
				semtAnswer:'',
				isSelArr:[],
				useTime:'00:00',
				secondTime:0,
			}
		},
		async  onLoad(options){
			this.taskId = options.task_id
			this.getWordDetail();
			let that = this;
			await setInterval(function(){
				that.secondTime++;
				let tmp_min = parseInt(that.secondTime/60);
				let tmp_second = that.secondTime%60;
				that.useTime = tmp_min+':'+ (tmp_second>9 ?tmp_second :('0'+tmp_second) )
			},1000)
		},
		methods: {
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			changeText(e,index){
				console.log(e);
				if(e == '') {
					let tmp_flag = true;
					if(index == this.s_count && this.fileList.length >0) {
						tmp_flag = false;
					} else if (index == (this.s_count+1) && this.fileList1.length >0) {
						tmp_flag = false;
					} else if (index == (this.s_count+2) && this.fileList2.length >0) {
						tmp_flag = false;
					}
					if(tmp_flag) {
						this.isSelArr =  this.isSelArr.filter(item=>item!= index)
					}
				} else  {
					if(!this.isSelArr.includes(index)){
						this.isSelArr.push(index);
					}
				}
			},
			next(type){
				if(type == 1) {
					this.currentChoose> 0?(this.currentChoose--):this.currentChoose
				} else {
					(this.currentChoose == this.s_count+2)?this.currentChoose:this.currentChoose++
				}
				
			},
			submitData(){
				let tmpFlag  = true;
				// for(let j=0;j<this.s_count;j++) {
				// 	if(!this.isSelArr.includes(j)){
				// 		this.currentChoose = j;
				// 		tmpFlag = false;
				// 		break;
				// 	}
				// }
				// if(!tmpFlag) {
				// 	return uni.$u.toast("请完成客观题答题")
				// 	return false;
				// }
				
				console.log();
				let pData = {
					useTime:this.secondTime,
					about_id:this.taskId,
					type:'read',
					objective:this.currentSelArr,
					answerWord:this.wordAnswer,
					imageWord:this.fileList,
					answerSemt:this.semtAnswer,
					imageSemt:this.fileList1,
					answerTran:this.tranAnswer,
					imageTran:this.fileList2
				}
				let that = this;
				let  data  = uni.$u.http.post('/task/taskOtherRecord',pData).then(res=>{
					console.log(res)
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						if(res.data.pre == 100) {
							setTimeout(function(){
								uni.navigateTo({
									url:'/class_teacher/reading_analysis_answer/reading_analysis_answer?task_id='+that.taskId
								})
							},1000)	
						}
					} else {
						uni.$u.toast(res.msg)
					}
				});
				console.log(pData);
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			//图片上传
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList[fileListLen]
						this.fileList.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
			deletePic1(event) {
				this.fileList1.splice(event.index, 1)
			},
			//图片上传
			async afterRead1(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList1.length
				lists.map((item) => {
					this.fileList1.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList1[fileListLen]
						this.fileList1.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList1.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
			deletePic2(event) {
				this.fileList2.splice(event.index, 1)
			},
			//图片上传
			async afterRead2(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList2.length
				lists.map((item) => {
					this.fileList2.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				this.loading=true;
				for (let i = 0; i < lists.length; i++) {
					try{
						const result = await uploadFile(lists[i].url)
						let item = this.fileList2[fileListLen]
						this.fileList2.splice(fileListLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							url: result.fullurl
						}))
						fileListLen++
					}catch(err){
						//移除图片
						setTimeout(()=>{
							this.fileList2.splice(fileListLen, 1)
						}, 1500)
						
					}
				}
				this.loading = false
			},
			
			
			
			goBack(){
				uni.navigateBack({
					delta:2
				})
			},
			async getWordDetail() {
				let that = this;
				const  data  = await  uni.$u.http.get('/task/getReadDetail',{params:{task_id:that.taskId}}).then(res=>{
					that.wordData = res.data;
					that.s_count =that.wordData.question_arr.length;
					that.questionArr  =  that.wordData.question_arr
					that.questionArr.map((item) => {
						that.qData.push({
							num:item.num,
							title:item.question,
							seloption:[
								{
									optionName:"A",
									text:item.qa
								},{
									optionName:"B",
									text:item.qb
								},{
									optionName:"C",
									text:item.qc
								},{
									optionName:"D",
									text:item.qd
								},
							]
						})
					})
					if(that.wordData.my_answer != undefined) {
						let tmp = that.wordData.my_answer;
						this.currentSelArr  =  tmp.objective
						this.wordAnswer = tmp.answerWord;
						if(tmp.imageWord != '') {
							this.fileList.push({url:tmp.imageWord})	
						}
						this.semtAnswer  =  tmp.answerSemt
						if(tmp.imageSemt != '') {
							this.fileList1.push({url:tmp.imageSemt})
						}
						this.tranAnswer = tmp.answerTran
						if(tmp.imageTran != '') {
							this.fileList2.push({url:tmp.imageTran})
						}
						this.secondTime  = tmp.useTime;
		
					}
				})
			},
			setSel(item,index){
				//切换选项
				// this.currentSelArr[index] = item.optionName;
				this.$set(this.currentSelArr,index, item.optionName);
				this.isSelArr.push(index)
				console.log(this.currentSelArr)
			}
		},
		components:{
		    cover
		  }
	}
</script>

<style lang="scss" scoped>
.container{
	box-sizing: b;
	background-color: $container-bg-color;
	.center{
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #2D2D2D;
		font-size: 32rpx;
	}
	.content{
		background-color: #fff;
		padding: 0 36rpx;
		padding-bottom: 80rpx;
		.top-opt{
			width: 100%;
			padding: 10rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.progress-text{
				text{
					color: #777777;
					font-size: 26rpx;
					&:first-child{
						font-size: 34rpx;
						color: #2D2D2D;
						font-weight: bold;
					}
				}
			}
			.opt{
				display: flex;
				align-items: center;
				justify-content: space-between;
				view{
					margin-left: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					text{
						font-size: 18rpx;
						color: #777;
					}
				}
			}
		}
		.q-type{
			margin-top: 12rpx;
			background-color: #CDF3E7;
			border-radius: 12rpx;
			color: $main-color;
			font-size: 24rpx;
			width: 120rpx;
			height: 46rpx;
			text-align: center;
			line-height: 46rpx;
		}
		.question{
			display: flex;
			flex-direction: column;
			align-content: space-between;
			justify-content: center;
			.title{
				width: 100%;
				color: #5A5A5A;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: 16rpx;
			}
			.question-content{
				//min-height: 100vh;
				max-height: 30vh;
				color: #5A5A5A;
				font-size: 28rpx;
				margin-top: 24rpx;
				line-height: 40rpx;
				
				.num {
					display: inline-block;
					height: 34rpx;
					width: 34rpx;
					border-radius: 50%;
					line-height: 34rpx;
					text-align: center;
				}
			}
		}
		
		.q-sel-list{
			
			.q-item{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;
				text{
					width: 590rpx;
				}
				.q-check-normal{
					width: 70rpx;
					height: 70rpx;
					border: 1rpx solid #AFAFAF;
					color: #5A5A5A;
					background-color: #fff;
					border-radius: 16rpx;
					line-height: 70rpx;
					text-align: center;
				}
				.q-check-sel{			
					background: #01997A;
					border: 0;
					color: #fff;
					
				}
			}
		}
	
	}
	.q-text{
		padding:10rpx 30rpx;
		line-height: 50rpx;
		color: #5A5A5A;
		font-size: 28rpx;
	}
	.q-title{
		margin-top: 36rpx;
		margin-bottom: 50rpx;
	}
	.q-container{
		padding: 0 36rpx;
		background-color: #fff;
		margin-top: 60rpx;
	}
	.q-sel-list{
		padding-bottom: 12rpx;
		.q-item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;
			text{
				width: 590rpx;
			}
			.q-check-normal{
				width: 70rpx;
				height: 70rpx;
				border: 1rpx solid #AFAFAF;
				color: #5A5A5A;
				background-color: #fff;
				border-radius: 50%;
				line-height: 70rpx;
				text-align: center;
			}
			.q-check-sel{			
				background: #01997A;
				border: 0;
				color: #fff;
				
			}
		}
	}
	.title-container{
		display: flex;
		align-items: flex-start;
		justify-content: center;
		background-color: rgb(247, 247, 247);
		.title-list{
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			justify-content: flex-start;
			transition: height 0.3s ease;
			overflow: hidden;
			background-color: rgb(247, 247, 247);
			.title{
				width: 100rpx;
				color: #777777;
				font-size: 28rpx;
				padding: 15rpx 30rpx;
			}
		}
		.right-icon{
			width: 140rpx;
			padding-top: 15rpx;
			padding-right: 30rpx;
			::v-deep .uicon-arrow-up{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
			::v-deep .uicon-arrow-down{
				display: inline-block;
				border-radius: 50%;
				border: 1rpx solid #777;
			}
		}
		.done{
			color: $main-color !important;
		}
		.sel{
			color: #0A0A0A;
			font-weight: bold;
		}
	}
	.answer{
		margin-top: 32rpx;
		padding: 0 36rpx;
		margin-bottom: 36rpx;
		.title{
			font-size: 24rpx;
			color: #777777;
			font-weight: bold;
		}
		.a-content{
			margin-bottom: 36rpx;
			border: 1rpx solid #707070;
			margin-top: 24rpx;
			border-radius: 22rpx;
			padding: 10rpx;
		}
		
	}
	
	.bottom-btn{
		padding: 0 36rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 56rpx;
		view{
			width: 170rpx;
			height: 70rpx;
			border-radius: 16rpx;
			border: 1rpx solid #01997A;
			line-height: 70rpx;
			text-align: center;
			font-size: 28rpx;
		}
		.prev{
			background: #FFFFFF;
			color: $main-color;
		}
		.next{
			background-color: $main-color;
			color: #fff;
		}
	}
	
}
</style>
