<template>
	<view class="edit-container">
		<view class="avatar-wrapper" @click="chooseImage">
			<view class="avatar-wrapper-content">
				<image v-if="avatarUrl != ''" :src="avatarUrl" mode="widthFix"></image>
			</view>
			<text v-if="loaded">点击修改</text>
		</view>
		<view class="form-wrapper">
			<view class="form-item">
				<text class="label">请输入昵称:</text>
				<u--input class="input-field" type="nickname" placeholder="请输入昵称" border="bottom" v-model="nickName" :value="nickName" @blur="change"></u--input>
			</view>

			<view class="form-item">
				<text class="label">请填写生日:</text>
				<view class="birthday-input" @click="openCalendar">
					<text :class="['birthday-text', !birthday ? 'placeholder' : '']">
						{{ birthday || '请选择生日' }}
					</text>
				</view>
			</view>
		</view>

		<button class="submit-btn" @click="edit">点击修改</button>
		<template>
			<u-datetime-picker ref="datetimePicker" :show="show" v-model="value1" mode="date" :minDate="Number(315504000000)" @confirm="confirm" @cancel="closeCalendar" :formatter="formatter"></u-datetime-picker>
		</template>
	</view>
</template>

<script>
import img from "../../static/img/head.png"
import { mapState } from "vuex"
import { uploadFile } from "@/api/common/common.js"


export default {
	computed: {
		...mapState('user', ['userInfo']),
	},
	data() {
		return {
			nickName: '',
			avatarUrl: "",
			avatar: '',
			img: img,
			show: false,
			mode: 'single',
			birthday: '',
			value1: Date.now(),
			userDetail: '',
			loaded: false,
			minDate: Number(315504000000)
		}
	},
	onLoad() {
		this.getUserInfo();
	},
	onReady() {
		// 微信小程序需要用此写法
		this.$refs.datetimePicker.setFormatter(this.formatter)
	},
	methods: {
		getUserInfo() {
			uni.$u.http.get('/mini_user/getUserInfo').then(rest => {
				this.userDetail = rest.data;
				this.nickName = this.userDetail.nickname;
				this.avatarUrl = this.avatar = this.userDetail.avatar ? this.userDetail.avatar : this.img;
				this.birthday = this.userDetail.birthday;
				this.loaded = true;
			});
		},
		formatter(type, value) {
			if (type === 'year') {
				return `${value}年`
			}
			if (type === 'month') {
				return `${value}月`
			}
			if (type === 'day') {
				return `${value}日`
			}
			return value
		},
		// clickBirth(){
		// 	that.show = true;					

		// },
		hideKeyBorder(e) {
			setTimeout(function () {
				uni.hideKeyboard();

			}, 10)
		},
		change(e) {
			this.nickName = e;
			// this.birthday  = e.
		},

		confirm(e) {
			try {
				const timeFormat = uni.$u.timeFormat;
				this.birthday = timeFormat(e.value, 'yyyy-mm-dd');
				this.show = false;
			} catch (error) {
				console.error('日期格式化错误:', error);
				uni.tip('日期设置失败');
			}
		},
		closeCalendar() {
			this.show = false;
		},
		async chooseImage() {
			try {
				const [err, res] = await uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera']
				});

				if (err) {
					uni.tip('已取消');
					return;
				}

				this.avatarUrl = res.tempFilePaths[0];

				// 使用common.js中的uploadFile方法上传
				try {
					const uploadResult = await uploadFile(this.avatarUrl);
					if (uploadResult && uploadResult.fullurl) {
						this.avatar = uploadResult.fullurl;
						console.log('上传成功:', this.avatar);
					} else {
						uni.tip('上传失败');
					}
				} catch (uploadError) {
					console.error('上传错误:', uploadError);
					uni.tip('上传失败');
				}
			} catch (e) {
				console.error('选择图片错误:', e);
				uni.tip('选择图片失败');
			}
		},

		async edit() {
			if (!this.avatar) {
				uni.tip('请选择头像');
				return false;
			}
			if (!this.nickName) {
				uni.tip('请填写昵称');
				return false;
			}

			try {
				let ret = await uni.$u.http.post("/mini_user/editUser", {
					nickName: this.nickName,
					avatarUrl: this.avatar,
					birthday: this.birthday
				});

				if (ret.code == 1) {
					uni.tip("修改成功");
					uni.navigateBack();
				} else {
					uni.tip(ret.msg || '修改失败');
				}
			} catch (error) {
				console.error('保存失败:', error);
				uni.tip('保存失败');
			}
		},

		openCalendar() {
			this.show = true;
		}
	}
}
</script>

<style lang="scss">
.edit-container {
	padding: 0 40rpx;

	.avatar-wrapper {
		width: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		margin-bottom: 20rpx;
		font-size: 18rpx;
		color: gray;

		.avatar-wrapper-content {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 100rpx;
				border-radius: 50%;
			}
		}


	}

	.form-wrapper {
		margin: 30rpx 0;

		.form-item {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.label {
				min-width: 160rpx;
				font-size: 28rpx;
				color: #333;
			}

			.birthday-input {
				flex: 1;
				margin-left: 20rpx;
				padding: 20rpx 0;
				border-bottom: 1px solid #dadbde;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.birthday-text {
					font-size: 28rpx;
					color: #333;

					&.placeholder {
						color: #999;
					}
				}

				.arrow {
					color: #999;
					font-size: 24rpx;
					transform: rotate(90deg);
					margin-right: 10rpx;
				}
			}
		}
	}

	.submit-btn {
		margin-top: 50rpx;
		background-color: #06A177;
		color: #fff;
	}
}
</style>
