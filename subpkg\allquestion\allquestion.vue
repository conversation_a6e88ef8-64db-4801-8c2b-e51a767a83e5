<template>
	<view class="container">
		<!-- 	简介 -->
		<view class="introduction-container">
			<view class="introduction">
				<view class="item">
					<view class="title">
						<text>{{statisticsData['allNum'] | handleData}}</text>
						<text>张</text>
					</view>
					<text>试卷数</text>
				</view>
				<view class="item">
					<view class="title">
						<text>{{statisticsData['completeNum']| handleData}}</text>
						<text>题</text>
					</view>
					<text>刷题量</text>
				</view>
				<view class="item">
					<view class="title">
						<text>{{statisticsData['days']| handleData}}</text>
						<text>天</text>
					</view>
					<text>练习天数</text>
				</view>
			</view>
		</view>
		<!-- 中间部分tab -->
		<view class="mid-tabs">
			<view class="all-course-h1">
				<view class="left-title">
					<view class="green-block-big">

					</view>
					<text>{{typeTitle}}</text>
				</view>
				<u-icon v-if="showSetting" name="setting" @click="show=true" size="56"></u-icon>

			</view>
			<view class="tabs" style="margin-left: 92rpx;">
				<u-tabs :list="professionalTitleList" lineWidth="50" lineHeight="8" lineColor="#01997A" :activeStyle="{
						  color: '#01997A',
						  fontSize:'32rpx'
			        }" :inactiveStyle="{
			             color: '#777777',
						  fontSize:'32rpx'
			        }" itemStyle="padding-left: 37rpx; padding-right: 37rpx; height: 34px;" @change="titleChange">
				</u-tabs>
			</view>

		</view>
		<view class="content" :style="{height:bottomHeight}">
			<view class="left">
				<side-bar :list="professionalSublist" :currentSel="sel" @changeItem="changeItem"
					:height="bottomHeight"></side-bar>
			</view>
			<view class="right">
				<tag-list v-show="type==0" :current="currentTag" @changeCurrentSel="tagchange" class="tag-items"
					:tagList="allType"></tag-list>
				<scroll-view :scroll-y="true" class="practise-container">
					<practise-list-com :list="pratiseList"></practise-list-com>
				</scroll-view>
			</view>
		</view>

		<u-popup :show="show"  mode="bottom" @close="close" @open="open">
			<view v-if="show">
				<practise-setting :show.sync="show" :config="stuConfig" @save="saveConfig"></practise-setting>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import sideBar from "@/components/Sidebar/Sidebar.vue"
	import tagList from "@/components/tagList/tagList.vue"
	import practiseListCom from "@/components/practiseList/practiseList.vue"
	import practiseSetting from "@/components/pratiseSetting/pratiseSetting.vue"
	import {
		getProfessionalQuestion,
		getQuestionList,
		getAllTypes,
		getStatistics,
		getConfig,
		updateConfig
	} from "@/api/professional/index.js"
	export default {
		data() {
			return {
				type:0,
				filterTaglist: [], //
				//弹出层
				show: false,
				//底部高度 动态获取
				bottomHeight: 0,
				allType: [], //题库分类
				bottomScroolHeight: 0,
				allProfessionalList: [], //专项题库所有数据
				professionalTitleList: [], //专项题库标题
				professionalSublist: [], //专项题库子标题
				sel: 1,
				currentTag: "ALLOVER", //当前选中的题库类型
				currentCid: 0, //选中的题库分类
				searchParam: {},
				pratiseList: [

				],
				statisticsData: {},
				stuConfig: {},
				showSetting:false,
			};
		},
		components: {
			sideBar,
			tagList,
			practiseListCom,
			practiseSetting
		},
		methods: {
			changeItem(item) {
				this.sel = item.id
			},
			tagchange(id) {
				this.currentTag = id
			},
			open() {
				// console.log('open');
			},
			close() {
				this.show = false
				// console.log('close');
			},
			//大标题切换
			titleChange(item) {
				
				let currentItem = this.allProfessionalList.find(obj => obj.id == item.id)
				this.currentTitleName = item.name 
				if (typeof currentItem !== "undefined") {
					this.professionalSublist = currentItem.childlist
					this.getAllTypes(item.name)
				}
			},
			//获取统计信息
			async getStatisticsData() {
				const params = {type:this.type}
				let data = await getStatistics(params)

				if (data !== false) {
					this.statisticsData = data
				} else {
					this.statisticsData = {}
				}
			},
			//获取试题配置信息
			async getConfig() {
				const config = await getConfig()
				if (config !== false) {
					this.stuConfig = config
					this.$store.commit("professionalConfig/setConfig", config);
				} else {
					this.stuConfig = {}
				}
			},
			//更新配置信息
			async saveConfig(data) {
				const res = await updateConfig(data)
				if (res == true) {
					await this.getConfig()
					uni.tip("更改成功", () => this.show = false)
				}
			},
			//加载类型
			async getAllTypes(name) {
				this.allType = []
				console.log("currentTitleName", this.currentTitleName)
				const params = {
					name
				}
				//加载类型
				const allType = await getAllTypes(params);
				if (allType != false) {
					for (let [key, value] of Object.entries(allType)) {
						
						this.allType.push({
							id: key,
							name: value
						})
					}
				}
			},

			//初始化专项题库
			async getProfessionalQuestion() {
				let data = await getProfessionalQuestion(this.type)
				if (data != false) {
					this.allProfessionalList = data
					this.professionalTitleList = data.map((item, index) => {
						return {
							name: item.name,
							id: item.id
						}
					})
					//默认是 显示第一栏标题 第一个分类
					if (data.length > 0) {
						this.professionalSublist = data[0].childlist
						this.getAllTypes(data[0].name)
					}
				}
			}
		},
		async onShow() {
			this.$store.commit("professionalConfig/clearAnswerList")
			//重置答题状态
			this.$store.commit("professionalConfig/changeCurrentAnswerStatus", {
			isSubmit:false,
			submitTime:"",
		})
			//清空当前答题进度 
			this.$store.commit("professionalConfig/setCurrentIndex", 0)
		},
		onLoad(option) {
			option.type && (this.type = option.type)
			let title = ""
			
			if(this.type==0){
				title = "全部专项"
				this.showSetting = true
			}else if(this.type==1){
				title = "全部真题"
			}else{
				title = "全部模拟"
			}
			uni.setNavigationBarTitle({
				title
			})
			setTimeout(() => {
				//获取所有类型
				//this.getAllTypes()
				//初始化专项题库
				this.getProfessionalQuestion()
				this.getStatisticsData()
				//获取答题配置
				this.getConfig()
			})

		},
		mounted() {
			uni.getSystemInfo({
				success: (res) => {
					uni.createSelectorQuery().select('.introduction-container').boundingClientRect(data => {
						//计算右侧课程 列表高度
						uni.createSelectorQuery().select('.mid-tabs').boundingClientRect(midData => {
							this.bottomHeight = res.windowHeight - data.height - midData
								.height + 'px'
							//课程列表高度
							uni.createSelectorQuery().select('.tag-items').boundingClientRect(
								tagsData => {
									this.bottomScroolHeight = res.windowHeight - data
										.height - midData.height - tagsData.height + 'px'


								}).exec();


						}).exec();


					}).exec();


				}
			});
		},

		filters: {
			handleData(data) {
				if (typeof data !== "undefined") {
					return data
				} else {
					return 0
				}
			}
		},
		
		computed:{
			typeTitle(){
				if(this.type==0){
					return "全部专项"
				}else if(this.type==1){
					return "全部真题"
				}else if(this.type==2){
					return "全部模考"
				}else{
					return "其他类型"
				}
			}
		},
		watch: {
			//监听子标题变化
			professionalSublist: {
				immediate: true,
				deep: true,
				handler(newVal) {
					//默认选中第一个子标题
					this.sel = newVal.length > 0 ? newVal[0].id : 0
				}
			},
			//左侧选中变化 请求数据
			async sel(newval) {
				//更显选择的分类
				this.currentCid = newval
				this.currentTag = "ALLOVER"
				this.searchParam = {
					type: this.currentTag,
					cid: newval,
					qtype:this.type//题库类型，真题题库或者专项题库
				}
			},
			//标签改变 更新搜索参数
			async currentTag(newval) {


				this.searchParam = {
					...this.searchParam,
					type: newval
				}
				// let params = {
				// 	cid:this.currentCid,
				// 	type:newval
				// }
				// let data = await getQuestionList(params)
				// if(data!=false){
				// 	console.log(data)
				// }
			},

			//参数改变发送最终请求
			searchParam: {
				deep: true,
				async handler(val) {
					this.pratiseList = []
					let data = await getQuestionList(val)
					if (data !== false) {
						this.pratiseList = data.map(item => {
							return {
								id: item.id,
								title: item.title,
								all: item.question_num,
								type:item.type,
								total_time:item.total_time,
								rate: typeof item.right_rate !== "undefined" ? item.right_rate : "0%",
								doing: item.doing,
								complete: typeof item.hasDoneNum !== "undefined" ? item.hasDoneNum : 0,
								pname: item.parentCatName,
								types: item.types,
							}
						})
					}
				}

			}
		},

	}
</script>

<style lang="scss" scoped>
	.container {
		box-sizing: border-box;
		height: 100vh;
		overflow: hidden;

		.introduction-container {
			box-sizing: border-box;
			padding: 0 30rpx;
		}

		.introduction {
			height: 120rpx;
			margin-top: 32rpx;
			background-color: #F6F7FB;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 22rpx 22rpx 0rpx 0rpx;

			.item {
				width: 25%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;

				.title {
					text {
						&:first-child {
							font-weight: bold;
							color: #201E2E;
							font-size: 32rpx;
						}

						&:last-child {
							font-size: 22rpx;
							color: #777777;
						}
					}
				}

				text {
					font-size: 18rpx;
					color: #777777;
				}
			}

		}

		.mid-tabs {
			background: #FFFFFF;
			box-shadow: 0rpx -4rpx 4rpx 1rpx rgba(184, 184, 184, 0.16);
			padding: 0 30rpx;

			.all-course-h1 {
				padding: 32rpx 0;
				padding-bottom: 0;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left-title {
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.green-block-big {
						width: 18rpx;
						height: 37rpx;
						background: #01997A;
						border-radius: 0rpx 10rpx 0rpx 10rpx;

					}

					text {
						padding-left: 10rpx;
						width: 128rpx;
						height: 45rpx;
						font-weight: bold;
						font-size: 32rpx;
						color: #1F1F27;
					}
				}


			}
		}

		.content {
			display: flex;
			align-items: flex-start;
			box-sizing: border-box;
			background-color: $container-bg-color;

			.left {
				background-color: #fff;
			}

			.right {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: flex-start;
				padding: 0 20rpx;
				width: 100%;
				.practise-container {
					margin-top: 22rpx;
					height: 70vh;
				}
			}
		}
	}
</style>