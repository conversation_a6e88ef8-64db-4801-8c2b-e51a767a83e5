<template>
	<view class="container">
		<view class="all-course-h1" :style="{backgroundColor:bgcolor}">
			<view class="green-block-big">
				
			</view>
			<text>{{title}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name:"user_title",
		data() {
			return {
				
			};
		},
		props:['title', 'bgcolor']
	}
</script>

<style lang="scss" scoped>
.container{
	.all-course-h1 {
		background-color: #F6F7FB;
		padding: 32rpx 0;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	
		.green-block-big {
	
			width: 18rpx;
			height: 37rpx;
			background: #01997A;
			border-radius: 0rpx 10rpx 0rpx 10rpx;
	
		}
	
		text {
			padding-left: 10rpx;
			height: 45rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #1F1F27;
		}
	}
}
</style>