<template>
	<view class="container">
		<view class="content">
			<view class="top-opt">
				<view class="progress-text">
					<text>{{ currentIndex - 0 + 1 }}</text>
					<text>/{{ currentTypes.length }}</text>
				</view>
				<star v-if="currentType.correction_completed != 1" :isstar.sync="question.is_star"></star>
			</view>

			<view class="q-type">
				{{ currentType.subtitle }}
			</view>

			<!-- <view class="question" :style="{paddingBottom:isInMiddle?'calc(100vh - 200px)':'100px'}"> -->
			<view class="question">
				<view class="question-content">

					<rich-text :nodes="questionContent"></rich-text>
					<!-- <u-parse  :content="questionContent"></u-parse> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import star from "@/components/star/star.vue"
import mixins from "@/mixins/index.js"
//import {mapState} from "vuex"
export default {
	name: 'QuestionContent',
	data() {
		return {

		};
	},
	components: {
		star
	},
	methods: {
		saveCurrentAnswer() {

		}
	},
	mixins: [mixins],
	computed: {
		questionContent() {
			if (!this.currentType || !this.currentType.title) {
				return '';
			}

			// 清理 MS Office 标签
			let content = this.currentType.title
				.replace(/<o:p>.*?<\/o:p>/g, '')
				.replace(/<\/span>/g, '')
				.replace(/<span.*?>/g, '')
				// 保留基本格式
				.replace(/<p.*?>/g, '<p>')
				.replace(/<br\s*\/?>/g, '<br>');

			console.log('处理后的内容:', content);
			return content;
		},
		paddingBottomVal() {
			console.log(this.isInMiddle)
			return this.isInMiddle
		},

		//...mapState('cover', ['isInMiddle'])
	}
}
</script>

<style scoped lang="scss">
.content {
	background-color: #fff;
	margin-bottom: 50rpx;
	padding-top: 78rpx;

	.top-opt {
		width: 100%;
		padding: 10rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.progress-text {
			text {
				color: #777777;
				font-size: 26rpx;

				&:first-child {
					font-size: 34rpx;
					color: #2D2D2D;
					font-weight: bold;
				}
			}
		}

		.opt {
			display: flex;
			align-items: center;
			justify-content: space-between;

			view {
				margin-left: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					font-size: 18rpx;
					color: #777;
				}
			}
		}
	}

	.q-type {
		display: inline-block;
		margin-top: 12rpx;
		background-color: #CDF3E7;
		border-radius: 12rpx;
		color: $main-color;
		font-size: 24rpx;
		min-width: 120rpx;
		height: 46rpx;
		text-align: center;
		line-height: 46rpx;
		padding: 0 10rpx;
	}

	.question {
		display: flex;
		flex-direction: column;
		align-content: space-between;
		justify-content: center;

		.title {
			width: 100%;
			color: #5A5A5A;
			font-size: 28rpx;
			font-weight: bold;
			margin-top: 16rpx;
		}

		.question-content {
			//min-height: 100vh;
			color: #5A5A5A;
			font-size: 28rpx;
			margin-top: 24rpx;
			line-height: 40rpx;

			.num {
				display: inline-block;
				height: 34rpx;
				width: 34rpx;
				border-radius: 50%;
				line-height: 34rpx;
				text-align: center;
			}
		}
	}

}
</style>