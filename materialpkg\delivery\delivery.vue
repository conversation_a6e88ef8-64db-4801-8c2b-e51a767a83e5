<template>
	<view class="container">
		<view class="meterial-list">
			<view class="title-name">
				<view class="left">
					<text class="green-block"></text>
					<text>发放中</text>
				</view>
				<view class="content">
				{{logInfo.add_time}}
				</view>
			</view>
			<view class="item-list">
				<view class="item" v-for="i in infoDetail" :key="i.id">
					<image :src="i.image" mode="widthFix"></image>
					<text class="title">{{i.name}}</text>
				</view>
			</view>
		</view>
		<cc-define-step :colors="colors" :stepData="stepData"></cc-define-step>
	</view>
</template>

<script>
	import ccDefineStep from "../components/cc-defineStep/cc-defineStep.vue"
	export default {
		data() {
			return {
				logId:'',
				logInfo:'',
				colors:"#009c7b",
				infoDetail:'',
                //模拟后台返回的数据
                stepData:  [
					{
						name: '等待快递员上门揽件',
						time: '2023-06-09 20:01:49',
						isNow: 1,
						type: 1,
						desc: '已发货'
					},
					{
						name: '您的包裹正在打包',
						time: '2023-06-09 20:01:49',
						isNow: 0,
						type: 1,
						desc: '待发货'
					},
					{
						name: '订单支付成功，等待商家发货',
						time: '2023-06-09 20:13:49',
						isNow: 0,
						type: 1,
						desc: '已支付'
					},
					{
						name: '订单提交成功',
						time: '2023-06-09 20:13:49',
						isNow: 0,
						type: 1,
						desc: '已下单'
					}
				]
			};
		},
		 onLoad(option) {
			 this.logId = option.id;
            // 步骤数组数据反序
            //this.stepData.reverse()
			this.getInfoByIds();
			this.getExpressMsg();
        },
		methods:{
			getInfoByIds() {
				let that = this
				const  data  = uni.$u.http.get('/course/getInfoById',{params:{id: this.logId}}).then(rest=>{	
					that.logInfo  = rest.data.logArr;
					that.infoDetail  = rest.data.data;
					
				});		
			},
			getExpressMsg() {
				let that = this
				const  data  = uni.$u.http.get('/course/getExpressMsg',{params:{id: this.logId}}).then(rest=>{	
					if(rest.data.data == null) {
						that.stepData  = [];
					} else{
						that.stepData = rest.data.data.list 
						
					}
					
				});		
			}
		},
		components:{
			ccDefineStep
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.meterial-list{
			padding: 0 30rpx;
		.title-name {
		
			display: flex;
			align-items: center;
			justify-content: flex-start;
			padding: 30rpx 28rpx;
		
			.left {
				color: #4A4A4C;
				font-size: 28rpx;
				font-weight: bold;
		
				.green-block {
					display: inline-block;
					width: 14rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #01997A;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
					margin-right: 6rpx;
				}
			}
			.content{
				margin-left: 24rpx;
				color:#4C5370;
				font-size: 26rpx;
			}
		}
		.item-list{
			display: flex;
			align-items: center;
			justify-content:space-between;
			padding: 28rpx;
			background-color: #EEFAF6;
			border-radius: 16rpx;
			flex-wrap: wrap;
		}
		.item{
			padding: 20rpx 2rpx;
			margin-bottom: 30rpx;
			background-color: #fff;
			width: 30%;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			image{
				width: 80%;
			}
			text.title{
				font-size: 24rpx;
				color: #1F1F27;
				margin-top: 16rpx;
				margin-bottom: 6rpx;
				width: 80%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}			
		}
		}
	}
</style>