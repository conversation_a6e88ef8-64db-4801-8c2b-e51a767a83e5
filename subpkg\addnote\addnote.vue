<template>
	<view class="container">
		<view class="text-input">
			<u-textarea height='120' border='surround' v-model="value" placeholder="请输入内容"></u-textarea>
		</view>
		
		<view class="img-upload">
			<view class="notice">
				<text >*最多上传30张,单次最多9张同时上传</text>
			</view>
			<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" :multiple="true"
					:maxCount="30"
					width="180rpx"
					height="180rpx"
					:uploadText="'上传照片'"
				></u-upload>
		</view>
		<view class="submit-container">
			<view class="submit-btn" @click="submitNote">
				 确定
			</view>
		</view>
	</view>
</template>

<script>
	import {uploadFile} from "@/api/common/common.js"
	export default {
		data() {
			return {
				fileList1: [],
				value:"",
				disflag:false,
				id:'',
				submitFlag:false,
			}
		},
		onLoad(option){
			if(option.id != undefined) {
				this.id = option.id
			}
			this.getNote()
		},
		methods:{
			getNote(){
				let that = this
				let  data  = uni.$u.http.post('/student/getNote',{id:that.id}).then(res=>{
					console.log(res)
					if(res.code == 1) {						
						that.fileList1 = res.data.image;
						that.value  = res.data.data.text;
						that.id = res.data.data.id
					} else if(res.code == 0){
						uni.tip(res.msg);
						uni.navigateBack({delta:1})
					} else  {
						
					}
				});
			},
			submitNote() {
				if(this.submitFlag) {
					return '';
				}
				if(this.disflag) {
					uni.tip('图片上传中');
					return false;
				}
				this.submitFlag = true;
				let that = this
				let  data  = uni.$u.http.post('/student/addNote',{id:that.id,imageList:that.fileList1,text:that.value}).then(res=>{
					console.log(res)
					that.submitFlag = false;
					if(res.code == 1) {
						uni.$u.toast('操作成功')
						setTimeout(function(){
							uni.navigateBack({delta:1})
						},2000)
						
					} else {
						uni.$u.toast(res.msg)
					}
				});
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				uni.showLoading({
					title:'图片上传中,请稍等...'
				})
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				console.log(lists);
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				for (let i = 0; i < lists.length; i++) {
					this.disflag = true;
					const result = await uploadFile(lists[i].url)
					
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {		
						url: result.fullurl
					}))
					fileListLen++
					if(i+1  == lists.length) {
						this.disflag = false;
						uni.hideLoading()
						uni.tip('图片上传完成');
					}
				}
			}
		}

	}
</script>

<style lang="scss" scoped>
	.container{
		box-sizing: border-box;
		background-color: #F6F7FB;
		padding: 0 30rpx;
		padding-top: 30rpx;
		
		height: 120vh;
		.notice{
			font-size: 20rpx;
			margin-bottom: 16rpx;
			margin-left:30rpx;
			color:#f00;
		}
		.text-input{
			background-color: #fff;
			height: 240rpx;
			border-radius: 20rpx;
		}
		.img-upload{
			border-radius: 20rpx;
			margin-top: 32rpx;
			background-color: #fff;
			padding: 24rpx 28rpx;
		}
		
		.submit-container{
			display: flex;
			align-items: center;
			justify-content: center;
			.submit-btn{
				margin-top: 80rpx;
				width: 400rpx;
				height: 80rpx;
				line-height: 80rpx;
				background: linear-gradient( 268deg, #01997A 0%, #08AB8A 100%);
				border-radius: 40rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				text-align: center
				
			}
		}
		
	}
</style>