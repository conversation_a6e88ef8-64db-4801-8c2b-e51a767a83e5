<template>
	<view class="container">
		<scroll-view :scroll-y="true" :style="{height:height}" :show-scrollbar="false">
		<view class="item" v-for="(item,index) in list" :key="item.id" :class="{active:item.id==currentSel}" @click="change(item.id,index)">
			<view class="left-block" v-show="item.id==currentSel">
				
			</view>
			<text>{{item.name}}</text>
		</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		name:"SideBar",
		data() {
			return {
				
			};
		},
		props:{
			height:{
				default:0
			},
			list:{
				type:Array,
				default:[
				
				],
				
			},
			currentSel:{
				default:1
			}
		},
		methods:{
			change(id,index){
				this.$emit("changeItem", {id:id,index:index})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container{
		box-sizing: border-box;
		width: 162rpx;
		background: #fff !important;
		.item{
			height: 92rpx;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #0A0A0A;
			font-size: 28rpx;
			margin-left: 4rpx;
		}
		.active{
			background-color: #EEFAF6;
			color: #01997A;
		}
		.left-block{
			height: 52rpx;
			width: 10rpx;
			position: absolute;
			background-color: #01997A;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			border-radius: 0rpx 16rpx 16rpx 0rpx;
		}
	}
</style>