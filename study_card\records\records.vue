<template>
	<view class="container">
		<view class="head">
		 <uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button" activeColor="#009c7b"></uni-segmented-control>
		</view>
		
		<view class="all-course-h1">
			<view class="left">
				<view class="green-block-big">
					
				</view>
				<text>所有记录</text>
			</view>
			<image @click="changeSort()" :src="sort==1?'../../static/png/asort.png':'../../static/png/sort.png'" mode="widthFix"></image>
		</view>
		<template v-if="current == 0" v-for="item in listMorn">
			<records-item :item="item" :key="item.id"></records-item>
		</template>
		
		<template v-if="current == 1" v-for="item in listNight">
			<records-item :item="item" :key="item.id"></records-item>
		</template>
		
		<view class="more" @click="getMore" v-if="show">
			<image src="../../static/png/down-arrow-double.png" mode="widthFix"></image>
			<text>加载更多</text>
		</view>
	</view>
</template>
<!-- {
						id:1,
						title:'第12天',
						date:'2024-03-12  07:00:23',
						imgNum:"共3张",
						info:"努力是很难，但永远记住，如果不努力，就会一直很难。你不一定要逆风翻盘，但一定要向阳而生。夜色难免黑凉，前行必有曙光",
						imgSrc:"https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png"
					}, -->
<script>
	import recordsItem from "../components/records_item/records_item.vue"
	export default {
		data() {
			return {
				items: ['早打卡', '晚打卡'],
				current: 0,
				listMorn:[],
				listNight:[],
				mornTotal:0,
				nightTotal:0,
				mornPage:1,
				nightPage:1,
				show:true,
				sort:1
			};
		},
		onLoad(){
			this.getList(1);
			this.getList(2);
		},
		methods:{
			changeSort() {
				if(this.sort == 1) {
					this.sort  = 0;
				} else {
					this.sort  = 1;
				}
				this.listMorn  = [];
				this.listNight = [];
				this.getList(1);
				this.getList(2);
			},
			getList (type){				
				let that = this
				let curlData = {page:this.mornPage,type:type,sort:that.sort}
				if(this.current != 0) {
					let curlData = {page:this.nightPage,type:type}
				} 
				console.log(curlData)
				let data  = uni.$u.http.get('/student/getColockList',{params:curlData}).then(res=>{
					if(res.code == 1) {	
						if(type == 1) {
							that.mornTotal = res.data.data.total;
							that.listMorn = [...that.listMorn, ...res.data.data.rows]
								console.log(that.listMorn.length,that.mornTotal);
							if(that.listMorn.length >= that.mornTotal) {
							
								that.show = false;
							}
						} else  {
							that.nightTotal = res.data.data.total;
							that.listNight = [...that.listNight, ...res.data.data.rows]
							if(that.listNight.length >= that.nightTotal && that.current == 1) {
								that.show = false;
							}
						}
					} else {
						
					}
				});
						
			},
			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
				let that  = this;
				if(this.current == 0) {
					if(that.listMorn.length >= that.mornTotal) {
						that.show = false;
					} else  {
						that.show = true; 
					}
				} else  {
					if(that.listNight.length >= that.nightTotal) {
						that.show = false;
					} else  {
						that.show = true; 
					}
				}
			},
			getMore() {
				if(this.current == 0) {
					this.mornPage++;
					this.getList(1)
				} else  {
					this.nightPage++;
					this.getList(2)
				}
			}
		},
		components:{
			recordsItem
		}
	}
</script>

<style lang="scss" scoped>
.container{
	padding: 0 30rpx;
	background-color: $container-bg-color;
	padding-top: 30rpx;
	height: 100vh;
	::v-deep .segmented-control__item{
		&:first-child{
			border-top-left-radius: 60rpx;
			border-bottom-left-radius: 60rpx;
			
		}
		&:last-child{
			border-top-right-radius: 60rpx;
			border-bottom-right-radius: 60rpx;
		}
	}
	.all-course-h1 {
		background-color: #F6F7FB;
		padding: 26rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.left{
			display: flex;
			align-items: center;
			justify-content: flex-start;
			.green-block-big {
				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;
				
			}
				
			text {
				padding-left: 10rpx;
				width: 128rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}
		image{
			width: 40rpx;
		}

	}
	.more{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		image{
			width: 20rpx;
		}
		text{
			margin-left: 6rpx;
			font-size: 30rpx;
			color: #A2A2A2;
			font-weight: bold;
		}
		
	}
}
</style>
