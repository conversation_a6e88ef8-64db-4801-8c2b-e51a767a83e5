<template>
  <view class="container">
    <image
      class="top-bg"
      src="https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/login_bg.png"
      mode="widthFix"
    ></image>
    <view class="login-btn">
      <button class="login" @click="comfirmLogin">确认登陆</button>
      <view class="author-info">
        <u-checkbox-group @change="checkboxChange">
          <u-checkbox
            size="10px"
            activeColor="#009c7b"
            iconSize="10px"
            name="1"
            shape="circle"
          ></u-checkbox>
        </u-checkbox-group>
        <text>我已认真阅读并同意 </text>
        <view>
          <text class="useragreement" @click.stop="privacy(4)"
            >《用户协议》</text
          >与 <text @click.stop="privacy(3)">《隐私协议》</text></view
        >
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from "vuex";
export default {
  data() {
    return {
      check: 0,
      codeKey: "",
    };
  },
  onLoad(options) {
    this.codeKey = options.key;
    this.userScanCode();
  },
  methods: {
    privacy(type) {
      uni.navigateTo({
        url: "/subpkg/scan_login/agree_index?type=" + type,
      });
    },

    async userScanCode() {
      await uni.$u.http
        .post("/mini_user/userScanCode", { key: this.codeKey })
        .then((res) => {});
    },
    comfirmLogin() {
      uni.$u.http
        .post("/mini_user/confirmLogin", { key: this.codeKey })
        .then((res) => {
          if (res.code == 1) {
            uni.tip("登陆成功");
            setTimeout(function () {
              uni.switchTab({
                url: "/pages/index/index",
              });
            }, 1000);
          } else {
            uni.tip(res.msg);
            setTimeout(function () {
              uni.navigateBack({
                delta: 1,
              });
            }, 1000);
          }
        });
    },

    ...mapMutations("user", ["setUser"]),
    checkboxChange(n) {
      this.check = n[0] == 1 ? n[0] : 0;
    },
    tip() {
      uni.showToast({
        icon: "none",
        title: "请同意协议",
      });
    },
  },
  computed: {
    showTipButton() {
      return this.check == 1;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  image {
    width: 750rpx;
  }

  box-sizing: border-box;
  padding-bottom: 60rpx;

  .login-btn {
    padding: 0 30rpx;

    .logo {
      display: block;
      width: 240rpx;
      margin: 0 auto;
      margin-bottom: 50rpx;
    }
  }

  .login {
    box-sizing: border-box;
    width: 520rpx;
    height: 80rpx;
    border-radius: 40rpx;
    background-color: $main-color;
    font-size: 24rpx;
    color: #fff;
    text-align: center;
    line-height: 80rpx;
  }

  .author-info {
    padding-left: 24rpx;
    margin-top: 54rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    text {
      font-size: 24rpx;
      margin-left: 10rpx;

      &:first-child {
        color: $main-color;
      }
      &:nth-child(3) {
        color: $main-color;
      }

      &:last-child {
        color: $main-color;
      }
    }
  }
}
</style>
