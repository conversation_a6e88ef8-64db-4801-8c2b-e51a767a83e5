{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": false, "es6": false, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": false, "useStaticServer": true, "condition": false}, "compileType": "miniprogram", "libVersion": "2.24.0", "appid": "wx6f5394eef6c66ca3", "projectname": "demo", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}