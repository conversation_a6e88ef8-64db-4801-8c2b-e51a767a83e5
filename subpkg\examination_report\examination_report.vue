<template>
	<view class="container">
		<!-- 头部 -->
		<view class="head">
			<u-navbar :placeholder='true' :bgColor='"transparent"' @leftClick="toList" title="答题报告" :titleStyle='{ color: "#fff" }'>
				<view class="u-nav-slot" slot="left">
					<u-icon name="arrow-left" size="38"></u-icon>
				</view>

			</u-navbar>


			<view class="grid-container">
				<view class="data-report-grid">
					<canvas style="width: 100%; text-align: center; height: 120px;" canvas-id="dashboard" id="dashboard"></canvas>
					<view class="extra-info u-border-top">
						<view class="info">
							<text>{{ reportData.type == 0 ? "题库名称:" : "试卷名称:" }}</text>
							<text class="point-value">{{ reportData.subject }}</text>
						</view>
						<view class="info">
							<text>交卷时间:</text>
							<text class="point-value">{{ reportData.submit_time }}</text>
						</view>
						<view class="info">
							<text>考试时长:</text>
							<text class="point-value">{{ totalTime }}</text>
						</view>
					</view>
				</view>
				<view class="info-board">
					<view class="item">
						<view class="top-text">
							<text>{{ hasContainSubjective ? 0 : reportData.rightRate }}</text>
							<text>%</text>
						</view>
						<text>正确率</text>
					</view>
					<view class="item u-border-left">
						<view class="top-text">
							<text>{{ hasContainSubjective ? 0 : reportData.avgScore }}</text>
							<text></text>
						</view>
						<text>平均分</text>
					</view>
					<view class="item u-border-left">
						<view class="top-text">
							<text>{{ hasContainSubjective ? 0 : reportData.maxScore }}</text>
							<text></text>
						</view>
						<text>最高分</text>
					</view>
				</view>
			</view>
		</view>
		<view class="content">

			<view class="q-answer">
				<view class="all-course-h1">
					<view class="green-block-big">

					</view>
					<text>答题卡</text>
				</view>

				<view class="list">
					<template v-for="(item, index) in reportData.answer_card">



						<view class="material " v-if="item.MATERIAL">
							<view class="item-container" v-for="obj in item.MATERIAL.answerList" :key="obj.index">
								<view class="item" :class='{ "item-sel": obj.isRight != 1 }'>
									{{ (index - 0 + 1) + '.' + (obj.index - 0 + 1) }}
								</view>
							</view>
						</view>

						<view class="material " v-if="item.is_new == 3">
							<view class="item-container" v-for="obj in item.FILL.answerList" :key="obj.index">
								<view class="item" :class='{ "item-sel": obj.isRight != 1 }'>
									{{ (index - 0 + 1) + '.' + (obj.index - 0 + 1) }}
								</view>
							</view>
						</view>

						<view class="item-container" v-if="!item.MATERIAL && item.is_new != 3" :key="item.index">
							<view class="item" :class='{ "item-sel": item.isRight != 1 }'>
								{{ item.index - 0 + 1 }}
							</view>
						</view>
					</template>

				</view>
				<view class="bottom-info">
					<view class="bottom-info-item">
						<view class="round first">

						</view>
						<text>正确</text>
					</view>

					<view class="bottom-info-item ">
						<view class="round third">

						</view>
						<text>错误</text>
					</view>
				</view>
			</view>



		</view>
		<view class="fix-bottom">
			<view class="bottom-btn" @click="toAnswer" v-if="hideBtn === 0">
				{{ submitText }}
			</view>
		</view>


	</view>
</template>

<script>
import {
	formatTime
} from '@/utils/tool'
import {
	selectTemplate,
	selectTemplateAnswer,
	formatLocalTime
} from "@/utils/tool.js"
import {
	getReportData,
	sendTaskSubmitTemplateMessage
} from "@/api/professional/index.js"
import {
	mapState
} from "vuex"
export default {
	data() {
		return {
			reportId: 0,
			canvas: "",
			ctx: '',
			hasContainSubjective: false,
			reportData: {},
			hideBtn: 0
		}
	},
	components: {},
	computed: {
		//获取答题总时间
		totalTime() {
			if (typeof this.reportData.used_time === "number") {
				return formatTime(this.reportData.used_time)
			} else {
				return "0"
			}
		},
		submitText() {
			console.log('reportData', this.reportData)
			if (this.reportData.type == 2) {
				if (this.reportData.isCorrection == 1) {
					return "返回个人中心"
				} else {
					return "返回上一页"
				}
			}
			return '错题解析'
		},
		...mapState('professionalConfig', ['currentTypes', 'currentIndex']),
	},
	methods: {
		//点击左侧提示用户 跳转会 刷题列表
		toList() {
			uni.showModal({
				title: '提示',
				content: '退回上级界面',
				success: (res) => {
					if (res.confirm) {
						// uni.reLaunch({
						// 	url:"/subpkg/allquestion/allquestion"
						// })
						this.$store.commit('professionalConfig/enterCurrentStartPage')
					}
				}
			})
		},

		toAnswer() {
			console.log(this.reportData.type)
			//模考试卷 跳转到刷题界面
			if (this.reportData.type == 2) {
				if (this.reportData.isCorrection == 1) {
					uni.navigateBack()
				} else {
					this.$store.commit('professionalConfig/enterCurrentStartPage')
					// uni.showModal({
					// 	title: '提示',
					// 	content: '确认通知老师批改',
					// 	success: async (res) => {
					// 		if (res.confirm) {
					// 			await sendTaskSubmitTemplateMessage({
					// 				report_id: this.reportId
					// 			})
					// 			uni.reLaunch({
					// 				url: "/subpkg/allquestion/allquestion?type=2"
					// 			})
					// 		}
					// 	}
					// })
				}
			} else {
				//进入当前的答案模板 从第一道题开始
				this.$store.commit("professionalConfig/setCurrentIndex", 0)
				selectTemplateAnswer(this.currentTypes[this.currentIndex], false)
			}
		},
		drawMeter(value, allScore, score) {
			const ctx = uni.createCanvasContext('dashboard', this);
			// 仪表盘参数

			const centerY = 90;
			const radius = 70;
			let wInfo = uni.getWindowInfo()
			//  const query = uni.createSelectorQuery().in(this);
			// let rect =  await  query.select('.data-report-grid').boundingClientRect().exec();
			// console.log(rect)
			//计算起始点     
			const centerX = parseInt((wInfo.screenWidth - radius) / 2)


			// 绘制仪表盘背景
			ctx.beginPath();
			ctx.arc(centerX, centerY, radius, Math.PI, 2 * Math.PI, false);
			ctx.lineWidth = 8;
			ctx.strokeStyle = '#CDF3E7';
			ctx.stroke();

			// 动态绘制仪表盘
			let endAngle = Math.PI + value * Math.PI / 100;

			ctx.beginPath();
			ctx.arc(centerX, centerY, radius, Math.PI, endAngle, false);
			ctx.lineWidth = 8;
			ctx.strokeStyle = '#009c7b';
			ctx.stroke();

			// 计算尾端圆的坐标
			if (value < 4) {
				endAngle = Math.PI + 4 * Math.PI / 100;
			}
			if (value >= 96) {
				endAngle = Math.PI + 96 * Math.PI / 100;
			}
			const endX = centerX + radius * Math.cos(endAngle);
			const endY = centerY + radius * Math.sin(endAngle);

			// 绘制尾端的实心圆
			ctx.beginPath();
			ctx.arc(endX, endY, 10, 0, 2 * Math.PI, false);
			ctx.fillStyle = '#009c7b';
			ctx.fill();

			// 绘制尾端的空心圆环
			ctx.beginPath();
			ctx.arc(endX, endY, 14, 0, 2 * Math.PI, false);
			ctx.lineWidth = 5;
			ctx.strokeStyle = '#fff';
			ctx.stroke();

			// 绘制文本
			ctx.font = '20px Arial';
			ctx.fillStyle = '#201E2E';
			ctx.textAlign = 'center';
			ctx.fillText(score, centerX - 8, centerY - 20);


			ctx.font = '12px Arial';
			ctx.fillStyle = '#201E2E';
			ctx.textAlign = 'center';
			let x_distance = score > 100 ? 14 : 8
			if (!Number.isInteger(score)) {
				x_distance += 10
			}
			ctx.fillText('分', centerX + x_distance, centerY - 20);


			ctx.font = '12px Arial';

			ctx.fillStyle = '#777777';
			ctx.textAlign = 'center';
			ctx.fillText(`总分${allScore}分`, centerX, centerY);

			// 将以上绘图操作立即渲染到 canvas
			ctx.draw();
		}
	},
	mounted() {
		//this.drawMeter(48)
	},
	onLoad(option) {
		let {
			report_id,
		} = option
		this.reportId = report_id
		this.hideBtn = option.hideBtn ?? 0
		//获取报告数据
		getReportData({
			report_id
		}).then(res => {
			if (res.has_contain_subjective == 1 && res.type != 2) {
				this.hasContainSubjective = true
				this.reportData = res
				this.drawMeter(0, 0, 0);
			} else {
				this.reportData = res
				console.log('reportData', this.reportData)
				//绘制仪表盘
				const cicleAngle = parseInt(this.reportData.score * 100 / this.reportData.total_score)
				this.drawMeter(cicleAngle, this.reportData.total_score, this.reportData.score);
			}
		})
	},
}
</script>

<style lang="scss" scoped>
.container {
	position: relative;
	min-height: 100vh;
	background-color: $container-bg-color;
	padding-bottom: 120rpx;

	.head {
		background-image: url("https://yanqu-online-mini-1300870289.cos.ap-nanjing.myqcloud.com/%E7%BB%84%20224%402x.png");
		background-size: cover;
		height: 490rpx;
		padding-top: 136rpx;
		position: relative;

		.title {
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.avatar {
					width: 100rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 47rpx;
						height: 66rpx;
					}
				}

				.info {
					margin-left: 4rpx;

					.top-text {
						color: #201E2E;
						font-size: 32rpx;
						font-weight: bold;
						display: flex;
						align-items: center;
						justify-content: flex-start;

						image {
							width: 40rpx;
							height: 40rpx;
						}

						.coin {
							color: #323232;
							font-size: 22rpx;
						}
					}

					.bottom-text {
						margin-top: 12rpx;
						color: #6D6D6D;
						font-size: 22rpx;
					}
				}
			}

			.right {
				width: 235rpx;
				height: 107rpx;
				background: #E0F9F1;
				opacity: 0.42;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				text {
					color: #4D504F;

					&:last-child {
						font-size: 30rpx;
						padding: 4rpx 0;
					}
				}
			}
		}

		.data-report-grid {
			width: 100%;
			padding: 0 36rpx;
			display: flex;
			align-items: center;
			background-color: #fff;
			justify-content: center;

			.extra-info {
				width: 100%;

				.info {}
			}
		}
	}

	.content {
		padding: 0 36rpx;
		background-color: transparent;
		margin-top: 303rpx;

	}

	.grid-container {
		padding: 0 36rpx;
		position: absolute;
		width: 100%;
		top: 184rpx;
	}

	.data-report-grid {
		width: 100%;
		background-color: #fff;
		border-radius: 28rpx;
		display: flex;
		flex-direction: column;
		align-content: center;
		box-sizing: border-box;
		justify-content: center;

		.extra-info {
			font-size: 26rpx;
			color: #5A5A5A;
			padding-bottom: 24rpx;

			.info {
				margin-top: 16rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 28rpx;
			}
		}
	}

	.info-board {
		background: #F6F7FB;
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 130rpx;
		background-color: #fff;
		margin-top: 33rpx;

		.item {
			text-align: center;
			flex: 1;
			color: #777;
			font-size: 18rpx;

			.top-text {
				text {
					font-size: 32rpx;
					color: #201E2E;

					&:last-child {
						color: #777;
						font-size: 18rpx;
					}
				}
			}
		}
	}

	.q-answer {
		margin-top: 36rpx;
		padding: 0 36rpx;
		background-color: #fff;
		border-radius: 28rpx;

		.all-course-h1 {
			background-color: #FFF;
			padding: 28rpx 0;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			.green-block-big {

				width: 18rpx;
				height: 37rpx;
				background: #01997A;
				border-radius: 0rpx 10rpx 0rpx 10rpx;

			}

			text {
				padding-left: 10rpx;
				height: 45rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1F1F27;
			}
		}

		.list {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			align-content: space-between;
			padding-bottom: 70rpx;

			.material {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				align-content: space-between;
			}

			.item-container {
				width: 20%;
				display: flex;
				align-items: center;
				justify-content: center;

				.item {
					background-color: $main-color;
					color: #fff;
					height: 74rpx;
					width: 74rpx;
					border-radius: 50%;
					line-height: 74rpx;
					text-align: center;
					font-size: 28rpx;
					margin-bottom: 20rpx;
				}

				.item-sel {
					background-color: #EE7878;
				}

			}

		}

		.bottom-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			padding: 0 80rpx;
			padding-bottom: 80rpx;

			.bottom-info-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 18rpx;
				color: #777777;
				text-align: center;

				view {
					height: 20rpx;
					width: 20rpx;
					border-radius: 50%;

					// &:n {
					// 	background-color: #01997A;
					// }

					// &:last-child{
					// 	background-color: #EE7878;
					// }
				}

				.first {
					background-color: #01997A;
				}

				.second {
					background: linear-gradient(to right, #EE7878 50%, #01997A 50%);
				}

				.third {
					background-color: #EE7878;
				}

				text {
					height: 20rpx;
					text-align: center;
					line-height: 20rpx;
					margin-left: 4rpx;
				}
			}
		}
	}

	.fix-bottom {
		padding: 20rpx 0;
		width: 100%;
		position: fixed;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: $container-bg-color;
	}

	.bottom-btn {
		width: 400rpx;
		height: 80rpx;
		background: linear-gradient(268deg, #01997A 0%, #08AB8A 100%);
		border-radius: 40rpx;
		text-align: center;
		line-height: 80rpx;
		color: #fff;
	}
}
</style>